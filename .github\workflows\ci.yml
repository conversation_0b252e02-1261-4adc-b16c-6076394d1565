name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  lint-and-type-check:
    name: <PERSON><PERSON> and <PERSON> Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'packages/frontend/package-lock.json'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Run ESLint
        run: |
          cd packages/frontend
          npm run lint:airbnb

      - name: Run Prettier check
        run: |
          cd packages/frontend
          npm run format:check

      - name: Run TypeScript type check
        run: |
          cd packages/frontend
          npm run type-check

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: lint-and-type-check

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'packages/frontend/package-lock.json'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Build application
        run: |
          cd packages/frontend
          npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: packages/frontend/.next/
          retention-days: 1

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: lint-and-type-check

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'packages/frontend/package-lock.json'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Run unit tests
        run: |
          cd packages/frontend
          npm run test:ci

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: packages/frontend/coverage/
          retention-days: 7

  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: build

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'packages/frontend/package-lock.json'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Install Playwright browsers
        run: |
          cd packages/frontend
          npm run playwright:install

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: packages/frontend/.next/

      - name: Run Playwright tests
        run: |
          cd packages/frontend
          npm run test:e2e

      - name: Upload Playwright report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: packages/frontend/test-results/
          retention-days: 7

  lighthouse-audit:
    name: Lighthouse Performance Audit
    runs-on: ubuntu-latest
    needs: build

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'packages/frontend/package-lock.json'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: packages/frontend/.next/

      - name: Start application
        run: |
          cd packages/frontend
          npm run start &
          sleep 30

      - name: Run Lighthouse CI
        run: |
          cd packages/frontend
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Upload Lighthouse reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-report
          path: packages/frontend/.lighthouseci/
          retention-days: 7

  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    needs: lint-and-type-check

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'packages/frontend/package-lock.json'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Run code quality check
        run: |
          chmod +x scripts/code-quality-check.sh
          ./scripts/code-quality-check.sh --report-only

      - name: Upload quality reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: quality-reports
          path: reports/
          retention-days: 7

  dependency-check:
    name: Dependency Security Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'packages/frontend/package-lock.json'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Run dependency audit
        run: |
          cd packages/frontend
          npm audit --audit-level=moderate

      - name: Check for outdated dependencies
        run: |
          cd packages/frontend
          npm outdated || true

      - name: License compliance check
        run: |
          cd packages/frontend
          npx license-checker --summary || true

  # Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run npm audit
        run: |
          cd packages/frontend
          npm audit --audit-level=high || true

  # Unit and Integration Tests
  test:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/frontend/package-lock.json'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Run unit tests
        run: |
          cd packages/frontend
          npm run test || echo "Tests not configured yet"
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: Run integration tests
        run: |
          cd packages/frontend
          npm run test:integration || echo "Integration tests not configured yet"
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

  # Performance Testing
  performance:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Build application
        run: |
          cd packages/frontend
          npm run build

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          cd packages/frontend
          lhci autorun || echo "Lighthouse CI not configured yet"
