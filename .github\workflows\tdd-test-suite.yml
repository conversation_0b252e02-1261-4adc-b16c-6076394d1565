name: TDD Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'

jobs:
  # Job 1: Unit and Integration Tests
  test-backend:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ondc_seller_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup test database
        run: |
          cd packages/prisma
          npx prisma generate
          npx prisma db push --force-reset
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ondc_seller_test

      - name: Run backend unit tests
        run: |
          cd packages/backend
          npm run test:unit
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ondc_seller_test
          REDIS_URL: redis://localhost:6379

      - name: Run backend integration tests
        run: |
          cd packages/backend
          npm run test:integration
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ondc_seller_test
          REDIS_URL: redis://localhost:6379

      - name: Run multi-tenant tests
        run: |
          cd packages/backend
          npm run test:multi-tenant
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ondc_seller_test
          REDIS_URL: redis://localhost:6379

      - name: Generate backend coverage report
        run: |
          cd packages/backend
          npm run test:ci
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ondc_seller_test
          REDIS_URL: redis://localhost:6379

      - name: Upload backend coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # Job 2: Frontend Tests
  test-frontend:
    name: Frontend Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run frontend unit tests
        run: |
          cd packages/frontend
          npm run test:unit

      - name: Run frontend integration tests
        run: |
          cd packages/frontend
          npm run test:integration

      - name: Generate frontend coverage report
        run: |
          cd packages/frontend
          npm run test:ci

      - name: Upload frontend coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # Job 3: E2E Tests
  test-e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ondc_seller_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: |
          cd packages/frontend
          npx playwright install --with-deps

      - name: Setup test database
        run: |
          cd packages/prisma
          npx prisma generate
          npx prisma db push --force-reset
          npm run seed:test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ondc_seller_test

      - name: Start backend server
        run: |
          cd packages/backend
          npm run dev &
          sleep 30
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ondc_seller_test
          PORT: 9000

      - name: Start frontend server
        run: |
          cd packages/frontend
          npm run dev &
          sleep 30
        env:
          NODE_ENV: test
          NEXT_PUBLIC_API_URL: http://localhost:9000
          PORT: 3000

      - name: Run E2E tests
        run: |
          cd packages/frontend
          npx playwright test
        env:
          NODE_ENV: test

      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: packages/frontend/test-results/
          retention-days: 7

      - name: Upload E2E test report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-report
          path: packages/frontend/playwright-report/
          retention-days: 7

  # Job 4: Security and Performance Tests
  test-security-performance:
    name: Security & Performance Tests
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=moderate

      - name: Run dependency check
        run: |
          npx audit-ci --moderate
        continue-on-error: true

      - name: Run performance tests
        run: |
          if [ -f "package.json" ] && grep -q "test:performance" package.json; then
            npm run test:performance
          else
            echo "Performance tests not configured"
          fi

  # Job 5: Coverage Analysis and Reporting
  coverage-analysis:
    name: Coverage Analysis
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download backend coverage
        uses: actions/download-artifact@v3
        with:
          name: backend-coverage
          path: ./packages/backend/coverage/

      - name: Download frontend coverage
        uses: actions/download-artifact@v3
        with:
          name: frontend-coverage
          path: ./packages/frontend/coverage/

      - name: Generate combined coverage report
        run: |
          # Install lcov if not available
          sudo apt-get update && sudo apt-get install -y lcov
          
          # Create combined coverage directory
          mkdir -p coverage/combined
          
          # Combine coverage files
          lcov \
            -a packages/backend/coverage/lcov.info \
            -a packages/frontend/coverage/lcov.info \
            -o coverage/combined/lcov.info
          
          # Generate HTML report
          genhtml coverage/combined/lcov.info -o coverage/combined/html
        continue-on-error: true

      - name: Upload combined coverage report
        uses: actions/upload-artifact@v3
        with:
          name: combined-coverage-report
          path: coverage/combined/html/
          retention-days: 30

      - name: Check coverage thresholds
        run: |
          # Check if coverage meets minimum thresholds
          node -e "
            const fs = require('fs');
            
            const checkCoverage = (packageName, threshold) => {
              const coverageFile = \`packages/\${packageName}/coverage/coverage-summary.json\`;
              if (!fs.existsSync(coverageFile)) {
                console.log(\`⚠️ No coverage file found for \${packageName}\`);
                return true;
              }
              
              const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
              const { lines, branches, functions, statements } = coverage.total;
              
              const passed = lines.pct >= threshold && 
                            branches.pct >= threshold && 
                            functions.pct >= threshold && 
                            statements.pct >= threshold;
              
              console.log(\`\${packageName}: Lines: \${lines.pct}%, Branches: \${branches.pct}%, Functions: \${functions.pct}%, Statements: \${statements.pct}%\`);
              
              if (passed) {
                console.log(\`✅ \${packageName} coverage meets threshold (\${threshold}%)\`);
              } else {
                console.log(\`❌ \${packageName} coverage below threshold (\${threshold}%)\`);
              }
              
              return passed;
            };
            
            const backendPassed = checkCoverage('backend', 90);
            const frontendPassed = checkCoverage('frontend', 85);
            
            if (!backendPassed || !frontendPassed) {
              process.exit(1);
            }
          "

  # Job 6: Test Results Summary
  test-summary:
    name: Test Results Summary
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend, test-e2e, test-security-performance, coverage-analysis]
    if: always()

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Generate test summary
        run: |
          echo "# TDD Test Suite Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## Test Status" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.test-backend.result }}" == "success" ]; then
            echo "✅ Backend Tests: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Backend Tests: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.test-frontend.result }}" == "success" ]; then
            echo "✅ Frontend Tests: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Frontend Tests: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.test-e2e.result }}" == "success" ]; then
            echo "✅ E2E Tests: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ E2E Tests: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.test-security-performance.result }}" == "success" ]; then
            echo "✅ Security & Performance Tests: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Security & Performance Tests: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.coverage-analysis.result }}" == "success" ]; then
            echo "✅ Coverage Analysis: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Coverage Analysis: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## Artifacts" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- [E2E Test Results](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
          echo "- [Coverage Report](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Triggered by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
