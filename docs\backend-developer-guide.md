# Backend Developer Guide

This guide provides information for backend developers working on the ONDC Seller Platform.

## Technology Stack

- **Framework**: Medusa Commerce
- **Language**: TypeScript
- **Database**: PostgreSQL (via Prisma)
- **Cache**: Redis
- **Testing**: Jest

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm (v9 or later)
- PostgreSQL (v14 or later)
- Redis (v6 or later)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-username/ondc-seller.git
cd ondc-seller
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

Copy the example environment file and update it with your configuration:

```bash
cp packages/backend/.env.example packages/backend/.env
```

4. Start the development server:

```bash
npm run dev
```

5. The server will be running at [http://localhost:9000](http://localhost:9000).

## Project Structure

```
packages/backend/
├── data/                # Seed data
├── src/
│   ├── api/             # Custom API routes
│   ├── services/        # Custom services
│   └── subscribers/     # Event subscribers
├── .env                 # Environment variables
├── medusa-config.js     # Medusa configuration
├── package.json         # Package dependencies
└── tsconfig.json        # TypeScript configuration
```

## Development Workflow

### Creating a Custom API Route

1. Create a new file in `src/api` or modify the existing `index.ts`
2. Export a function that returns a router

Example:

```typescript
// src/api/custom-route.ts
import { Router } from "express";

export default (rootDirectory) => {
  const router = Router();

  router.get("/custom-endpoint", (req, res) => {
    res.json({ message: "Custom endpoint" });
  });

  return router;
};
```

### Creating a Custom Service

1. Create a new file in `src/services`
2. Export a class that extends `TransactionBaseService`

Example:

```typescript
// src/services/custom-service.ts
import { TransactionBaseService } from "@medusajs/medusa";
import { EntityManager } from "typeorm";

class CustomService extends TransactionBaseService {
  constructor(container) {
    super(container);
  }

  async customMethod(data) {
    // Implementation
    return { success: true };
  }

  async withTransaction(transactionManager?: EntityManager): Promise<CustomService> {
    if (!transactionManager) {
      return this;
    }

    const cloned = new CustomService({
      ...this.container,
      manager: transactionManager,
    });

    cloned.transactionManager_ = transactionManager;
    return cloned;
  }
}

export default CustomService;
```

### Creating an Event Subscriber

1. Create a new file in `src/subscribers`
2. Export a class that extends `BaseSubscriber`

Example:

```typescript
// src/subscribers/order-subscriber.ts
import { EventBusService } from "@medusajs/medusa";
import { BaseSubscriber } from "medusa-interfaces";

class OrderSubscriber extends BaseSubscriber {
  constructor({ eventBusService }) {
    super();
    this.eventBus = eventBusService;
  }

  subscribe() {
    this.eventBus.subscribe("order.placed", this.handleOrderPlaced);
  }

  handleOrderPlaced = async (data) => {
    // Handle order placed event
    console.log("Order placed:", data);
  };
}

export default OrderSubscriber;
```

## Database Access

This project uses Prisma for database access. The Prisma client is available in the `@ondc-seller/prisma` package:

```typescript
import { prisma } from "@ondc-seller/prisma";

// Query the database
const users = await prisma.user.findMany();
```

## Authentication

In development mode, the app uses hardcoded credentials:

- Username: `demo`
- Password: `demo`

In production, you'll need to integrate with oneSSO (Keycloak).

## Testing

### Running Tests

```bash
npm run test
```

### Writing Tests

Tests are written using Jest. Create a test file with the `.spec.ts` extension:

```typescript
// src/services/custom-service.spec.ts
import CustomService from "./custom-service";

describe("CustomService", () => {
  let customService;

  beforeEach(() => {
    customService = new CustomService({});
  });

  it("should return success", async () => {
    const result = await customService.customMethod({});
    expect(result.success).toBe(true);
  });
});
```

## Deployment

### Building for Production

```bash
npm run build
```

### Starting the Production Server

```bash
npm run start
```

### Environment Variables

- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `JWT_SECRET`: Secret for JWT tokens
- `COOKIE_SECRET`: Secret for cookies
- `STORE_CORS`: CORS origin for the store
- `ADMIN_CORS`: CORS origin for the admin
- `NODE_ENV`: Environment (development, production)

## Customization

### Multi-Tenancy Support

The ONDC Seller Platform uses Medusa's multi-tenancy plugin to support multiple sellers on the ONDC network.

#### Setting Up Multi-Tenancy

1. Prerequisites:
   - Ensure you have Node.js (v18+) and npm installed
   - Make sure you're in the backend package directory

2. Initialize a new Medusa project with sample data:
   ```bash
   npx @medusajs/medusa-cli new . --seed
   ```
   This command will install all required dependencies and seed the database with initial data.

3. Configure the multi-tenancy plugin in `medusa-config.js`:
   ```javascript
   const plugins = [
     `medusa-fulfillment-manual`,
     `medusa-payment-manual`,
     {
       resolve: "@medusajs/medusa/dist/plugins/multi-tenancy",
       options: {
         // Configure tenant-specific metadata fields for ONDC
         tenant_metadata_fields: ["ondcId", "sellerCategory", "region"]
       }
     }
   ];
   ```

4. Restart your Medusa server and verify the installation by accessing the admin panel at http://localhost:9000/admin

#### Working with Tenants in Your Code

```typescript
// Creating a new tenant (seller)
const tenantService = req.scope.resolve("tenantService");
const tenant = await tenantService.create({
  name: "Seller Name",
  metadata: {
    ondcId: "seller-ondc-id",
    sellerCategory: "F&B",
    region: "Delhi-NCR"
  }
});

// Switching context to a specific tenant
const tenantManager = req.scope.resolve("tenantManager");
await tenantManager.setCurrentTenant(tenantId);

// Getting the current tenant
const currentTenant = await tenantManager.getCurrentTenant();

// Listing all tenants
const tenants = await tenantService.list();

// Updating a tenant
await tenantService.update(tenantId, {
  name: "Updated Seller Name",
  metadata: {
    sellerCategory: "Electronics"
  }
});

// Deleting a tenant
await tenantService.delete(tenantId);
```

#### Tenant-Specific API Routes

You can create tenant-specific API routes by checking the current tenant in your route handlers:

```typescript
router.get("/tenant-specific-endpoint", async (req, res) => {
  const tenantManager = req.scope.resolve("tenantManager");
  const currentTenant = await tenantManager.getCurrentTenant();

  if (!currentTenant) {
    return res.status(400).json({ error: "No tenant context" });
  }

  // Tenant-specific logic here
  res.json({
    tenant: currentTenant.name,
    metadata: currentTenant.metadata
  });
});
```

### Adding Plugins

You can add Medusa plugins by installing them and adding them to the `plugins` array in `medusa-config.js`:

```javascript
const plugins = [
  `medusa-fulfillment-manual`,
  `medusa-payment-manual`,
  // Add your plugins here
];
```

### Adding Dependencies

```bash
npm install package-name
```

## Troubleshooting

### Common Issues

- **Database Connection Issues**: Make sure PostgreSQL is running and the `DATABASE_URL` environment variable is set correctly.
- **Redis Connection Issues**: Make sure Redis is running and the `REDIS_URL` environment variable is set correctly.
- **TypeScript Errors**: Run `npm run lint` to check for TypeScript errors.
- **Build Errors**: Make sure all dependencies are installed and the environment variables are set correctly.
