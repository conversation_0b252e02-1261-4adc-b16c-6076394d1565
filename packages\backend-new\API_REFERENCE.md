# Medusa v2 API Reference for ONDC Seller App

## Base URLs
- **Admin API:** `http://localhost:9000/admin`
- **Store API:** `http://localhost:9000/store`
- **Auth API:** `http://localhost:9000/auth`
- **Admin Panel:** `http://localhost:9000/app`

## Authentication

### Admin Login
```bash
POST /auth/user/emailpass
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "supersecret"
}

# Response
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Using JWT Token
Include the token in all admin API requests:
```bash
Authorization: Bearer YOUR_JWT_TOKEN
```

## Admin API Endpoints

### Users Management

#### Get Current User
```bash
GET /admin/users/me
Authorization: Bearer YOUR_TOKEN

# Response
{
  "user": {
    "id": "user_01JZ4VVGEJX6KTQ3RZMB78Y5M1",
    "email": "<EMAIL>",
    "first_name": null,
    "last_name": null,
    "created_at": "2025-07-02T06:05:45.810Z"
  }
}
```

#### List All Users
```bash
GET /admin/users
Authorization: Bearer YOUR_TOKEN

# Response
{
  "users": [...],
  "count": 1,
  "offset": 0,
  "limit": 50
}
```

### Customer Management

#### List Customers
```bash
GET /admin/customers
Authorization: Bearer YOUR_TOKEN

# Response
{
  "customers": [...],
  "count": 0,
  "offset": 0,
  "limit": 50
}
```

#### Create Customer
```bash
POST /admin/customers
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+**********"
}

# Response
{
  "customer": {
    "id": "cus_01JZ4W260PPK0PW0R9EK9D3D5F",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "has_account": false,
    "created_at": "2025-07-02T06:09:24.503Z"
  }
}
```

#### Get Customer by ID
```bash
GET /admin/customers/{customer_id}
Authorization: Bearer YOUR_TOKEN
```

#### Update Customer
```bash
POST /admin/customers/{customer_id}
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "first_name": "Updated Name"
}
```

#### Delete Customer
```bash
DELETE /admin/customers/{customer_id}
Authorization: Bearer YOUR_TOKEN
```

### Product Management

#### List Products
```bash
GET /admin/products
Authorization: Bearer YOUR_TOKEN

# Query Parameters
?limit=20&offset=0&q=search_term

# Response
{
  "products": [...],
  "count": 0,
  "offset": 0,
  "limit": 50
}
```

#### Create Product
```bash
POST /admin/products
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "title": "Sample Product",
  "handle": "sample-product",
  "description": "A sample product for ONDC",
  "status": "published",
  "options": [
    {
      "title": "Size",
      "values": ["S", "M", "L"]
    }
  ],
  "variants": [
    {
      "title": "Small",
      "prices": [
        {
          "amount": 1000,
          "currency_code": "INR"
        }
      ],
      "options": {
        "Size": "S"
      }
    }
  ]
}
```

#### Get Product by ID
```bash
GET /admin/products/{product_id}
Authorization: Bearer YOUR_TOKEN
```

#### Update Product
```bash
POST /admin/products/{product_id}
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "title": "Updated Product Title"
}
```

#### Delete Product
```bash
DELETE /admin/products/{product_id}
Authorization: Bearer YOUR_TOKEN
```

### Order Management

#### List Orders
```bash
GET /admin/orders
Authorization: Bearer YOUR_TOKEN

# Query Parameters
?limit=15&offset=0&status=pending

# Response
{
  "orders": [...],
  "count": 0,
  "offset": 0,
  "limit": 15
}
```

#### Get Order by ID
```bash
GET /admin/orders/{order_id}
Authorization: Bearer YOUR_TOKEN
```

#### Update Order
```bash
POST /admin/orders/{order_id}
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "status": "fulfilled"
}
```

### Region Management

#### List Regions
```bash
GET /admin/regions
Authorization: Bearer YOUR_TOKEN

# Response
{
  "regions": [...],
  "count": 0,
  "offset": 0,
  "limit": 50
}
```

#### Create Region
```bash
POST /admin/regions
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "name": "India",
  "currency_code": "INR",
  "countries": ["IN"]
}
```

### Inventory Management

#### List Stock Locations
```bash
GET /admin/stock-locations
Authorization: Bearer YOUR_TOKEN
```

#### Create Stock Location
```bash
POST /admin/stock-locations
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "name": "Main Warehouse",
  "address": {
    "address_1": "123 Main St",
    "city": "Mumbai",
    "country_code": "IN",
    "postal_code": "400001"
  }
}
```

### Sales Channel Management

#### List Sales Channels
```bash
GET /admin/sales-channels
Authorization: Bearer YOUR_TOKEN
```

#### Create Sales Channel
```bash
POST /admin/sales-channels
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "name": "ONDC Channel",
  "description": "Sales channel for ONDC marketplace"
}
```

## Store API Endpoints

### Public Endpoints (No Authentication Required)

#### Get Store Information
```bash
GET /store
x-publishable-api-key: YOUR_PUBLISHABLE_KEY

# Note: Requires publishable API key
```

#### List Products (Store)
```bash
GET /store/products
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
```

#### Get Product by Handle
```bash
GET /store/products/{handle}
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
```

### Cart Management

#### Create Cart
```bash
POST /store/carts
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "region_id": "reg_0**********"
}
```

#### Add Item to Cart
```bash
POST /store/carts/{cart_id}/line-items
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "variant_id": "variant_0**********",
  "quantity": 1
}
```

## Error Responses

### Common Error Formats
```json
{
  "type": "invalid_data",
  "message": "Validation error message"
}

{
  "type": "not_found",
  "message": "Resource not found"
}

{
  "type": "unauthorized",
  "message": "Authentication required"
}

{
  "type": "not_allowed",
  "message": "Insufficient permissions"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Unprocessable Entity
- `500` - Internal Server Error

## Rate Limiting
- No rate limiting configured in development
- Implement rate limiting for production deployment

## Pagination
Most list endpoints support pagination:
```bash
?limit=20&offset=40
```

## Filtering and Search
Many endpoints support filtering:
```bash
?q=search_term&status=published&created_at[gte]=2025-01-01
```

## CORS Configuration
- **Admin CORS:** `http://localhost:5173,http://localhost:9000,https://docs.medusajs.com`
- **Store CORS:** `http://localhost:8000,https://docs.medusajs.com`
- **Auth CORS:** `http://localhost:5173,http://localhost:9000,http://localhost:8000,https://docs.medusajs.com`

## Testing with cURL

### Complete Example: Create and List Customer
```bash
# 1. Login
TOKEN=$(curl -s -X POST http://localhost:9000/auth/user/emailpass \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "supersecret"}' | \
  grep -o '"token":"[^"]*"' | cut -d'"' -f4)

# 2. Create customer
curl -X POST -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "first_name": "Test", "last_name": "User"}' \
  http://localhost:9000/admin/customers

# 3. List customers
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:9000/admin/customers
```

## Next Steps for ONDC Integration
1. Configure publishable API keys
2. Set up ONDC-specific product attributes
3. Implement ONDC order workflows
4. Add ONDC-compliant pricing structures
5. Configure region-specific settings for India
