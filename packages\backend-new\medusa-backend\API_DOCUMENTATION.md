# ONDC Seller App - Medusa Backend API Documentation

## Overview

This document provides comprehensive documentation for the ONDC Seller App backend API built with Medusa v2. The API supports multi-tenant architecture and provides complete e-commerce functionality including products, orders, customers, analytics, and ONDC integration.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL database
- Redis (for caching and sessions)
- Medusa backend service running on port 9000

### Base URLs

- **Development**: `http://localhost:9000`
- **Production**: `https://api.ondc-seller.com`

## 🔐 Authentication

### Admin Authentication

```bash
POST /auth/user/emailpass
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "supersecret"
}
```

Response:

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Store API Authentication

Store endpoints require a publishable API key in the header:

```bash
x-publishable-api-key: pk_test_123
```

## 🏢 Multi-Tenancy

All endpoints support multi-tenancy via the `x-tenant-id` header:

```bash
x-tenant-id: tenant-electronics-001
```

### Available Tenants

- `tenant-electronics-001` - Electronics Store
- `tenant-fashion-002` - Fashion Store
- `tenant-books-003` - Books Store
- `default` - Default Store

## 📊 Key API Endpoints

### Store Information

```bash
GET /store/test-info
x-tenant-id: tenant-electronics-001
x-publishable-api-key: pk_test_123
```

### Sales Analytics

```bash
GET /admin/analytics/sales?from=2024-01-01&to=2024-01-31&interval=day
Authorization: Bearer {admin_token}
x-tenant-id: tenant-electronics-001
```

### Create Order

```bash
POST /store/orders/create
x-tenant-id: tenant-electronics-001
x-publishable-api-key: pk_test_123
Content-Type: application/json

{
  "cart_id": "cart_01JZA9Z1QEZQD1ENMJ20K32FJV",
  "email": "<EMAIL>",
  "shipping_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address_1": "123 Tech Street",
    "city": "Mumbai",
    "postal_code": "400001",
    "country_code": "IN",
    "phone": "+91-9876543210"
  },
  "payment_method": "cod"
}
```

### Get Orders

```bash
GET /store/orders/simple?email=<EMAIL>&limit=10
x-tenant-id: tenant-electronics-001
x-publishable-api-key: pk_test_123
```

### Tenant Configuration

```bash
GET /admin/tenant
Authorization: Bearer {admin_token}
x-tenant-id: tenant-electronics-001
```

## 📋 OpenAPI Specification

The complete OpenAPI 3.0 specification is available in:

- `openapi-updated.yaml` - Updated specification with new endpoints
- `openapi.yaml` - Original comprehensive specification

### Viewing the Specification

You can view the API documentation using:

1. **Swagger UI**: Import the YAML file into [Swagger Editor](https://editor.swagger.io/)
2. **Postman**: Use the generated Postman collection
3. **Insomnia**: Import the OpenAPI specification directly

## 🧪 Testing the API

### Automated Testing

Run the test script to verify all endpoints:

```bash
node test-api-endpoints.js
```

### Generate Postman Collection

Generate a Postman collection from the OpenAPI spec:

```bash
# Install dependencies first
npm install js-yaml

# Generate collection
node generate-postman-collection.js
```

This creates:

- `postman/ONDC-Seller-Backend-API.postman_collection.json`
- `postman/ONDC-Seller-Backend-Development.postman_environment.json`

### Manual Testing Examples

#### 1. Test Store Information

```bash
curl -X GET "http://localhost:9000/store/test-info" \
  -H "x-tenant-id: tenant-electronics-001" \
  -H "x-publishable-api-key: pk_test_123"
```

#### 2. Test Analytics

```bash
curl -X GET "http://localhost:9000/admin/analytics/sales?from=2024-01-01&to=2024-01-31" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "x-tenant-id: tenant-electronics-001"
```

#### 3. Test Order Creation

```bash
curl -X POST "http://localhost:9000/store/orders/create" \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: tenant-electronics-001" \
  -H "x-publishable-api-key: pk_test_123" \
  -d '{
    "cart_id": "cart_test_123",
    "email": "<EMAIL>",
    "shipping_address": {
      "first_name": "John",
      "last_name": "Doe",
      "address_1": "123 Tech Street",
      "city": "Mumbai",
      "postal_code": "400001",
      "country_code": "IN",
      "phone": "+91-9876543210"
    },
    "payment_method": "cod"
  }'
```

## 🏗️ API Structure

### Admin Endpoints (`/admin/*`)

- **Authentication**: Requires JWT token
- **Purpose**: Backend management, analytics, configuration
- **Key Features**:
  - User management
  - Product management
  - Order management
  - Analytics and reporting
  - Tenant configuration

### Store Endpoints (`/store/*`)

- **Authentication**: Requires publishable API key
- **Purpose**: Customer-facing operations
- **Key Features**:
  - Product catalog
  - Cart management
  - Order placement
  - Customer profiles

### Authentication Endpoints (`/auth/*`)

- **Purpose**: Login and token management
- **Types**:
  - Admin user authentication
  - Customer authentication

## 🔧 Configuration

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/medusa_db

# Redis
REDIS_URL=redis://localhost:6379

# Server
PORT=9000
NODE_ENV=development

# JWT
JWT_SECRET=your_jwt_secret_here

# CORS
STORE_CORS=http://localhost:3000,http://localhost:8000
ADMIN_CORS=http://localhost:7001,http://localhost:9000
```

### Multi-Tenant Configuration

Each tenant has its own configuration including:

- Store name and branding
- ONDC participant details
- Currency and timezone settings
- Feature flags
- Contact information

## 📈 Analytics Features

The analytics endpoint provides:

- **Revenue Metrics**: Gross revenue, net revenue, growth rates
- **Order Statistics**: Order count, average order value, growth trends
- **Product Performance**: Top-selling products by revenue and units
- **Customer Insights**: New vs returning customers
- **Time-based Trends**: Daily, weekly, monthly aggregations
- **Recent Orders**: Latest order details

## 🛡️ Security Features

- **JWT Authentication**: Secure admin access
- **API Key Authentication**: Store endpoint protection
- **Multi-tenant Isolation**: Data separation by tenant
- **CORS Configuration**: Cross-origin request protection
- **Input Validation**: Request data validation
- **Error Handling**: Secure error responses

## 🚨 Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "Error type",
  "message": "Human-readable error message",
  "details": "Additional details (development only)"
}
```

Common HTTP status codes:

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

## 📚 Additional Resources

- [Medusa v2 Documentation](https://docs.medusajs.com/)
- [OpenAPI 3.0 Specification](https://swagger.io/specification/)
- [ONDC Network Documentation](https://ondc.org/)
- [Postman API Testing](https://www.postman.com/)

## 🤝 Contributing

When adding new endpoints:

1. Update the OpenAPI specification (`openapi-updated.yaml`)
2. Add tests to `test-api-endpoints.js`
3. Update this documentation
4. Regenerate Postman collection
5. Test all endpoints thoroughly

## 📞 Support

For API support and questions:

- Email: <EMAIL>
- Documentation: This file and OpenAPI spec
- Testing: Use provided test scripts and Postman collection
