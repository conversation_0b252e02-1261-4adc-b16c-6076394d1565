# Medusa v2 API Quick Reference

## 🚀 **Base URLs**
- **Admin API**: `http://localhost:9000/admin`
- **Store API**: `http://localhost:9000/store`
- **Auth API**: `http://localhost:9000/auth`

## 🔑 **Authentication**
```bash
# Get admin token
curl -X POST http://localhost:9000/auth/user/emailpass \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "supersecret"}'

# Use token in requests
curl -H "Authorization: Bearer $TOKEN" \
     -H "x-tenant-id: tenant-electronics-001" \
     http://localhost:9000/admin/products
```

## 📋 **Core Endpoints**

### Products
- `GET /admin/products` - List products
- `POST /admin/products` - Create product
- `GET /admin/products/{id}` - Get product
- `POST /admin/products/{id}` - Update product
- `DELETE /admin/products/{id}` - Delete product

### Customers
- `GET /admin/customers` - List customers
- `POST /admin/customers` - Create customer
- `GET /admin/customers/{id}` - Get customer
- `POST /admin/customers/{id}` - Update customer

### Orders
- `GET /admin/orders` - List orders
- `POST /admin/orders` - Create order
- `GET /admin/orders/{id}` - Get order
- `POST /admin/orders/{id}` - Update order

### Inventory
- `GET /admin/inventory-items` - List inventory
- `POST /admin/inventory-items` - Create inventory item
- `GET /admin/inventory-items/{id}` - Get inventory item

### Store (Customer-facing)
- `GET /store/products` - Public product catalog
- `POST /store/carts` - Create cart
- `GET /store/carts/{id}` - Get cart
- `POST /store/carts/{id}/line-items` - Add to cart

## 🏢 **Multi-Tenancy**

### Available Tenants
- `tenant-electronics-001` - Electronics Store
- `tenant-fashion-002` - Fashion Store
- `tenant-books-003` - Books Store
- `default` - Default Store

### Tenant Headers
```bash
# Use x-tenant-id header for tenant-specific operations
-H "x-tenant-id: tenant-electronics-001"
```

### Tenant Configuration
```bash
# Get tenant info
curl -H "Authorization: Bearer $TOKEN" \
     -H "x-tenant-id: tenant-electronics-001" \
     http://localhost:9000/admin/tenant
```

## 🗄️ **Database Tables with Tenant Support**

✅ **Ready for Multi-Tenancy:**
- `product` (has tenant_id)
- `customer` (has tenant_id)
- `order` (has tenant_id)
- `cart` (has tenant_id)
- `product_category` (has tenant_id)
- `inventory_item` (has tenant_id)

⚠️ **Needs Implementation:**
- Default endpoints don't filter by tenant yet
- Data creation uses 'default' tenant_id

## 📊 **Status Summary**

| Component | Status | Description |
|-----------|--------|-------------|
| **Database Schema** | ✅ Complete | tenant_id columns added |
| **Tenant Detection** | ✅ Complete | x-tenant-id header working |
| **Tenant Config** | ✅ Complete | /admin/tenant endpoint |
| **CORS Support** | ✅ Complete | Headers configured |
| **Endpoint Filtering** | ⚠️ Partial | Needs implementation |
| **Data Isolation** | ⚠️ Partial | Needs implementation |

## 🔗 **Full Documentation**
See [MEDUSA_ENDPOINTS_DATABASE_MAPPING.md](./MEDUSA_ENDPOINTS_DATABASE_MAPPING.md) for complete details.
