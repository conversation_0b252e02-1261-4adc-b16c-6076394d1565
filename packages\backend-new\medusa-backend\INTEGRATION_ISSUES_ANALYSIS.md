# Frontend-Backend Integration Issues Analysis

## Overview

This document analyzes critical integration issues between the frontend (`packages/frontend/lib/medusa-backend-api.ts`) and the backend API implementation, providing specific fixes and recommendations.

## Critical Integration Issues

### 1. Missing Tenant Context in Frontend

**Issue:** Frontend API client doesn't send tenant identification
```typescript
// CURRENT FRONTEND CODE (PROBLEMATIC)
export class MedusaBackendAPI {
  private headers: Record<string, string>;

  constructor() {
    this.headers = {
      'Content-Type': 'application/json',
      'x-publishable-api-key': `${process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY}`,
      // MISSING: 'x-tenant-id': tenantId
    };
  }
}
```

**Impact:**
- All requests default to 'default' tenant
- Multi-tenancy completely broken in frontend
- Users see wrong data from different tenants

**Solution:**
```typescript
// FIXED FRONTEND IMPLEMENTATION
export class TenantAwareMedusaAPI {
  private headers: Record<string, string>;
  private tenantId: string;

  constructor(tenantId: string = 'default') {
    this.tenantId = tenantId;
    this.headers = {
      'Content-Type': 'application/json',
      'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY!,
      'x-tenant-id': tenantId
    };
  }

  setTenant(tenantId: string): void {
    this.tenantId = tenantId;
    this.headers['x-tenant-id'] = tenantId;
  }

  getTenant(): string {
    return this.tenantId;
  }
}
```

### 2. Currency Configuration Mismatch

**Issue:** Backend uses INR, frontend expects EUR
```typescript
// FRONTEND HARDCODED CONFIG (WRONG)
const API_CONFIG = {
  REGION_ID: 'reg_01K0FKS6YRP1287PJF4HZ3SAPK', // Europe region with EUR
} as const;

// BACKEND TENANT CONFIG (CORRECT)
settings: {
  currency: 'INR',
  timezone: 'Asia/Kolkata'
}
```

**Impact:**
- Price calculation errors
- Wrong currency display
- Cart and order processing failures

**Solution:**
```typescript
// DYNAMIC CURRENCY CONFIGURATION
interface TenantConfig {
  regionId: string;
  currency: string;
  timezone: string;
}

export class CurrencyAwareMedusaAPI extends TenantAwareMedusaAPI {
  private tenantConfig: TenantConfig | null = null;

  async initializeTenantConfig(): Promise<void> {
    try {
      const response = await this.request<{tenant: any}>('/admin/tenant');
      this.tenantConfig = {
        regionId: response.tenant.regionId || 'reg_01INDIA_INR',
        currency: response.tenant.settings.currency,
        timezone: response.tenant.settings.timezone
      };
    } catch (error) {
      console.warn('Failed to load tenant config, using defaults');
      this.tenantConfig = {
        regionId: 'reg_01INDIA_INR',
        currency: 'INR',
        timezone: 'Asia/Kolkata'
      };
    }
  }

  async createCart(data?: {email?: string}): Promise<CartResponse> {
    if (!this.tenantConfig) {
      await this.initializeTenantConfig();
    }

    const cartData = {
      region_id: this.tenantConfig!.regionId,
      currency_code: this.tenantConfig!.currency,
      ...data
    };

    return await this.request<CartResponse>('/store/carts', {
      method: 'POST',
      body: JSON.stringify(cartData),
    });
  }
}
```

### 3. Response Format Inconsistencies

**Issue:** Backend returns additional fields not handled by frontend
```typescript
// FRONTEND INTERFACE (INCOMPLETE)
interface ProductsResponse {
  products: MedusaProduct[];
  count: number;
  offset: number;
  limit: number;
}

// BACKEND ACTUAL RESPONSE (EXTENDED)
{
  products: [...],
  count: 10,
  offset: 0,
  limit: 20,
  total: 50,           // Missing in frontend interface
  metadata: {          // Missing in frontend interface
    tenantId: "tenant-electronics-001",
    filtering: {
      totalBeforeFiltering: 100,
      totalAfterFiltering: 50,
      returned: 10
    },
    timestamp: "2025-01-23T10:30:00Z"
  }
}
```

**Solution:**
```typescript
// UPDATED FRONTEND INTERFACES
interface EnhancedProductsResponse {
  products: MedusaProduct[];
  count: number;
  offset: number;
  limit: number;
  total: number;
  metadata?: {
    tenantId: string;
    filtering?: {
      totalBeforeFiltering: number;
      totalAfterFiltering: number;
      returned: number;
    };
    timestamp: string;
  };
}

interface StandardAPIResponse<T> {
  success?: boolean;
  data?: T;
  metadata?: {
    tenantId: string;
    timestamp: string;
    pagination?: {
      offset: number;
      limit: number;
      total: number;
    };
  };
  error?: string;
}
```

### 4. Deprecated Endpoint Usage

**Issue:** Frontend uses non-standard HTTP methods and endpoints
```typescript
// PROBLEMATIC FRONTEND CODE
async getOrder(orderId: string): Promise<OrderResponse> {
  return await this.request<OrderResponse>('/store/orders/simple', {
    method: 'POST',  // Using POST for GET operation
    body: JSON.stringify({ order_id: orderId }),
  });
}
```

**Solution:**
```typescript
// CORRECTED IMPLEMENTATION
async getOrder(orderId: string): Promise<OrderResponse> {
  return await this.request<OrderResponse>(`/store/orders/${orderId}`, {
    method: 'GET'
  });
}

async getOrdersByEmail(email: string): Promise<OrdersResponse> {
  const searchParams = new URLSearchParams({ email });
  return await this.request<OrdersResponse>(`/store/orders?${searchParams}`);
}
```

### 5. Missing Error Handling for Tenant-Specific Errors

**Issue:** Frontend doesn't handle tenant-specific error responses
```typescript
// BACKEND TENANT ERROR FORMAT
{
  type: 'tenant_error',
  code: 'INVALID_TENANT',
  message: 'Tenant not found or inactive',
  details: { tenantId: 'invalid-tenant' }
}
```

**Solution:**
```typescript
// ENHANCED ERROR HANDLING
export class TenantAPIError extends MedusaAPIError {
  public tenantId?: string;
  public code?: string;

  constructor(
    message: string,
    status?: number,
    response?: any,
    tenantId?: string
  ) {
    super(message, status, response);
    this.name = 'TenantAPIError';
    this.tenantId = tenantId;
    this.code = response?.code;
  }
}

// Updated request method with tenant error handling
private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  try {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: { ...this.headers, ...options.headers },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // Handle tenant-specific errors
      if (errorData.type === 'tenant_error') {
        throw new TenantAPIError(
          errorData.message,
          response.status,
          errorData,
          this.tenantId
        );
      }
      
      throw new MedusaAPIError(
        errorData.message || `HTTP ${response.status}`,
        response.status,
        errorData
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof TenantAPIError || error instanceof MedusaAPIError) {
      throw error;
    }
    throw new MedusaAPIError(`Network error: ${error.message}`);
  }
}
```

## Authentication Flow Issues

### 6. Inconsistent Authentication Patterns

**Issue:** Different auth patterns for admin vs store endpoints
```typescript
// CURRENT INCONSISTENT PATTERNS
// Admin: Bearer token in Authorization header
// Store: API key in x-publishable-api-key header
// Missing: Tenant context in both flows
```

**Solution:**
```typescript
// UNIFIED AUTHENTICATION SYSTEM
export class UnifiedAuthMedusaAPI extends TenantAwareMedusaAPI {
  private authToken?: string;
  private authType: 'admin' | 'store' = 'store';

  setAdminAuth(token: string): void {
    this.authType = 'admin';
    this.authToken = token;
    this.headers['Authorization'] = `Bearer ${token}`;
    delete this.headers['x-publishable-api-key'];
  }

  setStoreAuth(apiKey: string): void {
    this.authType = 'store';
    this.headers['x-publishable-api-key'] = apiKey;
    delete this.headers['Authorization'];
  }

  getAuthType(): 'admin' | 'store' {
    return this.authType;
  }

  isAuthenticated(): boolean {
    return this.authType === 'admin' 
      ? !!this.authToken 
      : !!this.headers['x-publishable-api-key'];
  }
}
```

### 7. Missing Session Management

**Issue:** No tenant context preservation across requests
```typescript
// SOLUTION: SESSION-AWARE API CLIENT
export class SessionAwareMedusaAPI extends UnifiedAuthMedusaAPI {
  private sessionStorage: Storage;

  constructor(tenantId?: string) {
    super(tenantId);
    this.sessionStorage = typeof window !== 'undefined' 
      ? window.sessionStorage 
      : new Map() as any; // Server-side fallback
    
    this.loadSessionData();
  }

  private loadSessionData(): void {
    try {
      const sessionData = this.sessionStorage.getItem('medusa-session');
      if (sessionData) {
        const { tenantId, authToken, authType } = JSON.parse(sessionData);
        
        if (tenantId) this.setTenant(tenantId);
        if (authToken && authType === 'admin') this.setAdminAuth(authToken);
      }
    } catch (error) {
      console.warn('Failed to load session data:', error);
    }
  }

  private saveSessionData(): void {
    try {
      const sessionData = {
        tenantId: this.tenantId,
        authToken: this.authToken,
        authType: this.authType
      };
      
      this.sessionStorage.setItem('medusa-session', JSON.stringify(sessionData));
    } catch (error) {
      console.warn('Failed to save session data:', error);
    }
  }

  setTenant(tenantId: string): void {
    super.setTenant(tenantId);
    this.saveSessionData();
  }

  setAdminAuth(token: string): void {
    super.setAdminAuth(token);
    this.saveSessionData();
  }
}
```

## Data Flow Corrections

### 8. Cart and Order Flow Fixes

**Issue:** Cart creation doesn't consider tenant-specific configurations
```typescript
// CORRECTED CART FLOW
export class TenantCartManager extends SessionAwareMedusaAPI {
  async createTenantAwareCart(customerEmail?: string): Promise<CartResponse> {
    // Ensure tenant config is loaded
    if (!this.tenantConfig) {
      await this.initializeTenantConfig();
    }

    const cartData = {
      region_id: this.tenantConfig!.regionId,
      currency_code: this.tenantConfig!.currency,
      email: customerEmail
    };

    const cart = await this.createCart(cartData);
    
    // Store cart ID in session with tenant context
    this.sessionStorage.setItem(
      `cart-${this.tenantId}`, 
      cart.cart.id
    );

    return cart;
  }

  async getTenantCart(): Promise<CartResponse | null> {
    const cartId = this.sessionStorage.getItem(`cart-${this.tenantId}`);
    
    if (!cartId) return null;

    try {
      return await this.getCart(cartId);
    } catch (error) {
      // Cart might be expired or invalid
      this.sessionStorage.removeItem(`cart-${this.tenantId}`);
      return null;
    }
  }
}
```

## Implementation Roadmap

### Phase 1: Critical Fixes (Week 1)
1. ✅ Add tenant context to all frontend API calls
2. ✅ Fix currency configuration mismatch
3. ✅ Update response interfaces to match backend

### Phase 2: Enhanced Integration (Week 2)
1. ✅ Implement unified authentication system
2. ✅ Add proper error handling for tenant errors
3. ✅ Fix deprecated endpoint usage

### Phase 3: Advanced Features (Week 3)
1. ✅ Add session management with tenant context
2. ✅ Implement tenant-aware cart management
3. ✅ Add comprehensive integration testing

## Testing Integration Fixes

```typescript
// INTEGRATION TEST EXAMPLE
describe('Tenant-Aware API Integration', () => {
  let api: TenantCartManager;

  beforeEach(() => {
    api = new TenantCartManager('tenant-electronics-001');
  });

  test('should include tenant header in all requests', async () => {
    const spy = jest.spyOn(global, 'fetch');
    
    await api.getProducts();
    
    expect(spy).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        headers: expect.objectContaining({
          'x-tenant-id': 'tenant-electronics-001'
        })
      })
    );
  });

  test('should handle tenant-specific errors', async () => {
    // Mock tenant error response
    global.fetch = jest.fn().mockResolvedValue({
      ok: false,
      status: 403,
      json: () => Promise.resolve({
        type: 'tenant_error',
        code: 'INVALID_TENANT',
        message: 'Tenant not found'
      })
    });

    await expect(api.getProducts()).rejects.toThrow(TenantAPIError);
  });
});
```

---

*These integration fixes should be implemented incrementally with thorough testing at each step.*
