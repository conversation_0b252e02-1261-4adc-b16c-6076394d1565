# ONDC-Specific Endpoints Analysis

## Overview

This document analyzes the remaining custom endpoints to determine which should be kept for ONDC-specific functionality vs. which should be removed in favor of native Medusa APIs.

## ✅ **KEEP: ONDC-Specific Endpoints**

### 1. **Cash on Delivery (COD) Endpoint** - ✅ **KEEP**

**File:** `src/api/store/carts/[id]/complete-cod/route.ts`

**Justification:**
- **ONDC Requirement:** Cash on Delivery is a critical payment method for Indian e-commerce
- **Missing in Medusa:** Medusa doesn't have native COD workflow that bypasses payment sessions
- **Custom Logic:** Creates orders directly without complex payment processing
- **Indian Market:** Essential for ONDC compliance and Indian customer expectations

**Functionality:**
- Bypasses Medusa's payment session complexity
- Creates orders with "awaiting" payment status
- Handles COD-specific order metadata
- Integrates with manual payment provider

**Native Alternative:** None - Medusa's native order creation requires payment sessions

**Recommendation:** ✅ **KEEP** - This is genuine ONDC-specific functionality

### 2. **Store Customers Endpoint** - 🔄 **EVALUATE**

**File:** `src/api/store/customers/route.ts`

**Current Implementation:**
- Uses real Medusa customer service
- Adds tenant-specific filtering
- Provides customer registration functionality

**Analysis:**
- **Tenant Filtering:** Can be replaced with sales channel filtering
- **Customer Service:** Uses native Medusa customer service
- **Registration:** Medusa has native customer registration

**Recommendation:** ❌ **REMOVE** - Replace with native `/store/customers` with publishable keys

## ❌ **REMOVE: Replaceable with Native APIs**

### 1. **Disabled Cart Endpoints** - ❌ **REMOVE**

**Files:** 
- `src/api/store/carts.disabled/route.ts`
- `src/api/store/carts.disabled/[id]/route.ts`

**Justification:**
- **Already Disabled:** Files are in `.disabled` directory
- **Mock Data:** Uses mock data instead of real Medusa services
- **Native Alternative:** Medusa has comprehensive native cart APIs

**Recommendation:** ❌ **REMOVE** - Use native `/store/carts` with publishable keys

### 2. **Store Customers Endpoint** - ❌ **REMOVE**

**File:** `src/api/store/customers/route.ts`

**Justification:**
- **Tenant Headers:** Uses custom `x-tenant-id` headers
- **Native Alternative:** Medusa has native customer APIs with sales channel context
- **No ONDC-Specific Logic:** Just wraps native Medusa customer service

**Recommendation:** ❌ **REMOVE** - Use native `/store/customers` with publishable keys

## 🔄 **REMAINING ENDPOINTS TO ANALYZE**

Let me check what other endpoints exist:

### Analytics Endpoints
- `src/api/admin/analytics/*` - Likely custom analytics, evaluate if ONDC-specific

### Sales Endpoints  
- `src/api/admin/sales/*` - Likely custom sales reporting, evaluate if ONDC-specific

### Stock Location Endpoints
- `src/api/admin/stock-locations/*` - May have ONDC-specific location logic

### Categories Endpoints
- `src/api/store/categories/*` - May have Indian market category structures

## 📋 **EVALUATION CRITERIA**

### ✅ **KEEP Criteria:**
1. **ONDC Compliance:** Required for ONDC protocol compliance
2. **Indian Market:** Specific to Indian e-commerce requirements
3. **Missing in Medusa:** Functionality not available in native Medusa
4. **Custom Business Logic:** Unique workflows not supported natively

### ❌ **REMOVE Criteria:**
1. **Tenant Headers:** Uses `x-tenant-id` instead of publishable keys
2. **Wrapper Only:** Just wraps native Medusa services
3. **Mock Data:** Uses mock data instead of real services
4. **Native Alternative:** Medusa has equivalent native functionality

## 🎯 **FINAL RECOMMENDATIONS**

### **Immediate Actions:**

1. **✅ KEEP:**
   - `src/api/store/carts/[id]/complete-cod/route.ts` - Essential ONDC COD functionality

2. **❌ REMOVE:**
   - `src/api/store/carts.disabled/*` - Disabled mock implementations
   - `src/api/store/customers/route.ts` - Replaceable with native APIs

3. **🔄 EVALUATE NEXT:**
   - Analytics endpoints - Check if ONDC-specific
   - Sales endpoints - Check if ONDC-specific  
   - Categories endpoints - Check if Indian market specific
   - Stock location endpoints - Check if ONDC location logic

### **Updated Native API Client:**

The `MedusaNativeAPI` class should be extended to support the COD endpoint:

```typescript
/**
 * Complete cart with Cash on Delivery (ONDC-specific)
 */
async completeCartWithCOD(cartId: string): Promise<OrderResponse> {
  return await this.request<OrderResponse>(`/store/carts/${cartId}/complete-cod`, {
    method: 'POST',
    headers: {
      'x-publishable-api-key': this.publishableKey
    }
  })
}
```

### **Environment Variables:**

The COD endpoint should also respect tenant isolation through publishable keys, so it may need to be updated to use publishable keys instead of tenant headers.

## 🔧 **Next Steps:**

1. Remove the identified endpoints that are replaceable
2. Analyze remaining endpoints (analytics, sales, categories, stock-locations)
3. Update COD endpoint to use publishable keys instead of tenant headers
4. Extend native API client with ONDC-specific methods
5. Test all remaining functionality

This analysis ensures we keep only the endpoints that provide genuine ONDC-specific value while maximizing the use of Medusa's native, tested, and maintained APIs.
