# Medusa Native Multi-Tenancy Refactoring Implementation Guide

## Overview

This guide provides step-by-step instructions to refactor the current custom multi-tenancy implementation to use Medusa's native Sales Channels and Publishable API Keys approach.

## 🎯 **Refactoring Goals**

### **Replace Custom Implementations:**
- ❌ Custom `/admin/tenant` endpoint → ✅ Native `/admin/sales-channels`
- ❌ Custom product filtering → ✅ Native sales channel filtering
- ❌ Custom tenant middleware → ✅ Native publishable key context
- ❌ Custom service wrappers → ✅ Native Medusa service patterns

### **Preserve All Functionality:**
- ✅ Complete tenant isolation
- ✅ ONDC-specific configurations
- ✅ All existing API capabilities
- ✅ Frontend integration patterns

## 📋 **Pre-Implementation Checklist**

### **1. Backup Current Implementation**
```bash
# Create backup of current custom implementation
cp -r src/api/admin/tenant backup/
cp -r src/api/admin/test-multi-tenant backup/
cp -r src/api/store/test-info backup/
cp -r src/middleware backup/
cp src/api/middlewares.ts backup/
```

### **2. Verify Medusa Version**
```bash
# Ensure you're using Medusa v2.8.6 or later
npm list @medusajs/framework
```

### **3. Database Backup**
```bash
# Backup your database before migration
pg_dump your_database > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 🚀 **Implementation Steps**

### **Step 1: Setup Tenant Sales Channels (30 minutes)**

#### **1.1 Run the Setup Script**
```bash
cd ondc-seller-app/packages/backend-new/medusa-backend

# Install dependencies if needed
npm install

# Run the tenant setup script
npx ts-node src/scripts/setup-tenant-sales-channels.ts
```

**Expected Output:**
```
🚀 Starting tenant sales channels setup...
📍 Setting up India region...
✅ India region created: reg_india_123
🏪 Creating tenant sales channels...
✅ Created 3 sales channels
🔑 Creating publishable API keys...
✅ Tenant: tenant-electronics-001
   Sales Channel ID: sc_electronics_456
   API Key: pk_electronics_789
   Domain: electronics.ondc-seller.com
✅ Tenant: tenant-fashion-002
   Sales Channel ID: sc_fashion_abc
   API Key: pk_fashion_def
   Domain: fashion.ondc-seller.com
🎉 TENANT SALES CHANNELS SETUP COMPLETE!
```

#### **1.2 Save Generated Environment Variables**
Copy the generated environment variables to your frontend `.env.local`:

```bash
# Frontend environment variables
NEXT_PUBLIC_TENANT_ELECTRONICS_001_API_KEY=pk_electronics_generated_key
NEXT_PUBLIC_TENANT_FASHION_002_API_KEY=pk_fashion_generated_key
NEXT_PUBLIC_DEFAULT_API_KEY=pk_default_generated_key

# Sales Channel IDs for admin operations
TENANT_ELECTRONICS_001_SALES_CHANNEL_ID=sc_electronics_id
TENANT_FASHION_002_SALES_CHANNEL_ID=sc_fashion_id
DEFAULT_SALES_CHANNEL_ID=sc_default_id
```

### **Step 2: Migrate Existing Data (45 minutes)**

#### **2.1 Run the Migration Script**
```bash
# Migrate existing tenant data to sales channels
npx ts-node src/scripts/migrate-tenant-data.ts
```

**Expected Output:**
```
🔄 Starting tenant data migration to sales channels...
📋 Building tenant to sales channel mapping...
   • tenant-electronics-001 → sc_electronics_456 (Electronics Store)
   • tenant-fashion-002 → sc_fashion_abc (Fashion Store)
📦 Migrating products to sales channels...
   Found 150 products to process
   Linking 75 products to tenant-electronics-001
   Linking 50 products to tenant-fashion-002
   ✅ Linked 125 products total
👥 Migrating customers to sales channels...
   ✅ Migrated 45 customers
📋 Migrating orders to sales channels...
   ✅ Migrated 23 orders
🎉 TENANT DATA MIGRATION COMPLETE!
```

#### **2.2 Verify Migration**
```bash
# Test that products are properly linked to sales channels
curl -H "x-publishable-api-key: pk_electronics_generated_key" \
     http://localhost:9000/store/products

# Should return only electronics products
```

### **Step 3: Update Frontend Integration (60 minutes)**

#### **3.1 Replace API Client**
```bash
# Copy the new native API client
cp src/lib/medusa-native-api.ts ../frontend/lib/

# Update frontend imports
# Replace: import { MedusaBackendAPI } from './medusa-backend-api'
# With: import { MedusaNativeAPI } from './medusa-native-api'
```

#### **3.2 Update Frontend Usage**
```typescript
// Before (Custom Implementation)
const api = new MedusaBackendAPI()
api.setTenant('tenant-electronics-001')
const products = await api.getProducts()

// After (Native Implementation)
const api = new MedusaNativeAPI('tenant-electronics-001')
const products = await api.getProducts()
```

#### **3.3 Test Frontend Integration**
```bash
# Start frontend and test tenant switching
cd ../frontend
npm run dev

# Verify that:
# 1. Products load correctly for each tenant
# 2. Cart operations work
# 3. Order creation functions
# 4. Admin operations filter by sales channel
```

### **Step 4: Remove Custom Endpoints (30 minutes)**

#### **4.1 Remove Custom API Files**
```bash
# Remove custom admin endpoints
rm -rf src/api/admin/tenant
rm -rf src/api/admin/test-multi-tenant
rm -rf src/api/admin/products/route.ts  # Custom implementation
rm -rf src/api/admin/customers/route.ts # Custom implementation
rm -rf src/api/admin/orders/route.ts    # Custom implementation

# Remove custom store endpoints
rm -rf src/api/store/test-info
rm -rf src/api/store/test-products
rm -rf src/api/store/products/route.ts  # Custom implementation
rm -rf src/api/store/orders/create      # Custom implementation

# Remove custom middleware
rm src/api/middlewares.ts
rm -rf src/middleware/
```

#### **4.2 Update Medusa Configuration**
```typescript
// medusa-config.ts - Remove custom configurations
export default defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      // Remove custom tenant headers - use standard Medusa headers only
      cors: {
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'x-medusa-access-token',
          'x-publishable-api-key', // Standard Medusa header
          'Accept',
          'Origin'
        ]
      }
    }
  },
  // Use standard Medusa modules only
  modules: {
    cart: { resolve: '@medusajs/cart' },
    order: { resolve: '@medusajs/order' },
    customer: { resolve: '@medusajs/customer' },
    salesChannel: { resolve: '@medusajs/sales-channel' }
  }
})
```

### **Step 5: Test Native Implementation (45 minutes)**

#### **5.1 Test Store APIs**
```bash
# Test electronics tenant
curl -H "x-publishable-api-key: pk_electronics_generated_key" \
     http://localhost:9000/store/products

# Test fashion tenant
curl -H "x-publishable-api-key: pk_fashion_generated_key" \
     http://localhost:9000/store/products

# Verify different products are returned for each tenant
```

#### **5.2 Test Admin APIs**
```bash
# Get admin token
ADMIN_TOKEN=$(curl -X POST http://localhost:9000/admin/auth/token \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"supersecret"}' | jq -r '.access_token')

# List all sales channels (tenants)
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     http://localhost:9000/admin/sales-channels

# Get products for specific sales channel
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "http://localhost:9000/admin/products?sales_channel_id=sc_electronics_456"
```

#### **5.3 Test Cart and Order Flow**
```bash
# Create cart with electronics API key
CART_RESPONSE=$(curl -X POST -H "x-publishable-api-key: pk_electronics_generated_key" \
  -H "Content-Type: application/json" \
  http://localhost:9000/store/carts \
  -d '{}')

CART_ID=$(echo $CART_RESPONSE | jq -r '.cart.id')

# Add product to cart
curl -X POST -H "x-publishable-api-key: pk_electronics_generated_key" \
  -H "Content-Type: application/json" \
  "http://localhost:9000/store/carts/$CART_ID/line-items" \
  -d '{"variant_id":"variant_123","quantity":1}'

# Create order
curl -X POST -H "x-publishable-api-key: pk_electronics_generated_key" \
  -H "Content-Type: application/json" \
  http://localhost:9000/store/orders \
  -d "{\"cart_id\":\"$CART_ID\"}"
```

## 🔍 **Verification Checklist**

### **✅ Store API Verification**
- [ ] Products are filtered by tenant (different results for different API keys)
- [ ] Cart creation works with publishable keys
- [ ] Order creation works from cart
- [ ] Product details load correctly
- [ ] Search and filtering work within tenant scope

### **✅ Admin API Verification**
- [ ] Sales channels list shows all tenants
- [ ] Products can be filtered by sales channel ID
- [ ] Customers can be filtered by sales channel ID
- [ ] Orders can be filtered by sales channel ID
- [ ] Sales channel metadata contains ONDC configuration

### **✅ Frontend Integration Verification**
- [ ] Tenant switching works correctly
- [ ] Product listings show tenant-specific products
- [ ] Cart operations work for each tenant
- [ ] Order creation completes successfully
- [ ] Admin panel shows tenant-filtered data

### **✅ ONDC Configuration Verification**
- [ ] Sales channel metadata contains ONDC configs
- [ ] Tenant-specific ONDC settings are preserved
- [ ] Currency and timezone settings are maintained
- [ ] Domain and branding information is stored

## 🚨 **Troubleshooting**

### **Common Issues and Solutions**

#### **Issue: "No publishable API key found for tenant"**
```bash
# Solution: Re-run the setup script
npx ts-node src/scripts/setup-tenant-sales-channels.ts
```

#### **Issue: "Products not filtered by tenant"**
```bash
# Solution: Verify migration completed successfully
npx ts-node src/scripts/migrate-tenant-data.ts --verify
```

#### **Issue: "Sales channel not found"**
```bash
# Solution: Check that sales channels were created
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     http://localhost:9000/admin/sales-channels
```

#### **Issue: "Frontend shows wrong products"**
```bash
# Solution: Verify environment variables are set correctly
echo $NEXT_PUBLIC_TENANT_ELECTRONICS_001_API_KEY
```

## 📊 **Success Metrics**

### **Before Refactoring:**
- ~2000+ lines of custom multi-tenancy code
- Custom middleware and service wrappers
- Manual tenant validation and filtering
- Custom API endpoints for tenant management

### **After Refactoring:**
- ~200 lines of configuration and integration code
- Native Medusa multi-tenancy through sales channels
- Automatic tenant isolation via publishable keys
- Standard Medusa API endpoints with built-in filtering

### **Performance Improvements:**
- ✅ Faster API responses (native Medusa optimizations)
- ✅ Better caching (Medusa's built-in caching)
- ✅ Reduced memory usage (no custom middleware overhead)
- ✅ Improved scalability (Medusa's proven architecture)

## 🎉 **Completion**

Once all verification steps pass, you have successfully refactored the custom multi-tenancy implementation to use Medusa's native capabilities while preserving all existing functionality and ONDC-specific requirements.

The refactored implementation is now:
- ✅ **Standards-compliant** with Medusa best practices
- ✅ **Future-proof** for Medusa updates
- ✅ **Maintainable** with reduced custom code
- ✅ **Performant** with native optimizations
- ✅ **Scalable** with proven architecture patterns
