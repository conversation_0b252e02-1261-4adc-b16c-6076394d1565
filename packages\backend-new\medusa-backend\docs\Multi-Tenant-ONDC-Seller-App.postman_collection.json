{"info": {"name": "Multi-Tenant ONDC Seller App API", "description": "Multi-tenant e-commerce API built on Medusa v2 for ONDC (Open Network for Digital Commerce).\n\n## Multi-Tenancy\nThis API supports multi-tenancy through the `x-tenant-id` header. Each tenant has isolated data and configurations.\n\n## Authentication\n- Admin endpoints require Bearer token authentication\n- Store endpoints require publishable API key in `x-publishable-api-key` header\n\n## Tenant Isolation\nAll data is isolated by tenant including products, customers, orders, and configurations.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "multi-tenant-ondc-seller-app", "version": "2.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:9000", "type": "string"}, {"key": "admin_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKWjRWVkdKSlRLVzNSWUdNTUUwRVg0NDMiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEifSwiaWF0IjoxNzUxNDQ5NDA2LCJleHAiOjE3NTE1MzU4MDZ9.8UR1_CpBrJFgkdNeU2iU-_8x2EvcMidMTP8bt8Ok9pA", "type": "string"}, {"key": "publishable_api_key", "value": "pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0", "type": "string"}, {"key": "electronics_tenant_id", "value": "tenant-electronics-001", "type": "string"}, {"key": "fashion_tenant_id", "value": "tenant-fashion-002", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"supersecret\"\n}"}, "url": {"raw": "{{base_url}}/auth/user/emailpass", "host": ["{{base_url}}"], "path": ["auth", "user", "emailpass"]}, "description": "Authenticate admin user and get JWT token"}, "response": []}]}, {"name": "Admin - Tenant Management", "item": [{"name": "Get Electronics Tenant Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "x-tenant-id", "value": "{{electronics_tenant_id}}"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "Get configuration for Electronics tenant"}, "response": []}, {"name": "Get Fashion Tenant Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "x-tenant-id", "value": "{{fashion_tenant_id}}"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "Get configuration for Fashion tenant"}, "response": []}]}, {"name": "Admin - Testing", "item": [{"name": "Test Multi-Tenant Isolation - Electronics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "x-tenant-id", "value": "{{electronics_tenant_id}}"}], "url": {"raw": "{{base_url}}/admin/test-multi-tenant", "host": ["{{base_url}}"], "path": ["admin", "test-multi-tenant"]}, "description": "Test multi-tenant data isolation for Electronics tenant"}, "response": []}, {"name": "Test Multi-Tenant Isolation - Fashion", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "x-tenant-id", "value": "{{fashion_tenant_id}}"}], "url": {"raw": "{{base_url}}/admin/test-multi-tenant", "host": ["{{base_url}}"], "path": ["admin", "test-multi-tenant"]}, "description": "Test multi-tenant data isolation for Fashion tenant"}, "response": []}]}, {"name": "Store - Information", "item": [{"name": "Get Electronics Store Info", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable_api_key}}"}, {"key": "x-tenant-id", "value": "{{electronics_tenant_id}}"}], "url": {"raw": "{{base_url}}/store/test-info", "host": ["{{base_url}}"], "path": ["store", "test-info"]}, "description": "Get store information for Electronics tenant"}, "response": []}, {"name": "Get Fashion Store Info", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable_api_key}}"}, {"key": "x-tenant-id", "value": "{{fashion_tenant_id}}"}], "url": {"raw": "{{base_url}}/store/test-info", "host": ["{{base_url}}"], "path": ["store", "test-info"]}, "description": "Get store information for Fashion tenant"}, "response": []}]}, {"name": "Store - Products", "item": [{"name": "Get Electronics Store Products", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable_api_key}}"}, {"key": "x-tenant-id", "value": "{{electronics_tenant_id}}"}], "url": {"raw": "{{base_url}}/store/test-products", "host": ["{{base_url}}"], "path": ["store", "test-products"]}, "description": "Get product catalog for Electronics tenant"}, "response": []}, {"name": "Get Fashion Store Products", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable_api_key}}"}, {"key": "x-tenant-id", "value": "{{fashion_tenant_id}}"}], "url": {"raw": "{{base_url}}/store/test-products", "host": ["{{base_url}}"], "path": ["store", "test-products"]}, "description": "Get product catalog for Fashion tenant"}, "response": []}, {"name": "Get Electronics Product Details", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable_api_key}}"}, {"key": "x-tenant-id", "value": "{{electronics_tenant_id}}"}], "url": {"raw": "{{base_url}}/store/test-products/prod_electronics_001", "host": ["{{base_url}}"], "path": ["store", "test-products", "prod_electronics_001"]}, "description": "Get details for iPhone 15 Pro from Electronics tenant"}, "response": []}, {"name": "Get Fashion Product Details", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable_api_key}}"}, {"key": "x-tenant-id", "value": "{{fashion_tenant_id}}"}], "url": {"raw": "{{base_url}}/store/test-products/prod_fashion_001", "host": ["{{base_url}}"], "path": ["store", "test-products", "prod_fashion_001"]}, "description": "Get details for Men's Casual Shirt from Fashion tenant"}, "response": []}, {"name": "Cross-Tenant Access Test (Should Fail)", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable_api_key}}"}, {"key": "x-tenant-id", "value": "{{electronics_tenant_id}}"}], "url": {"raw": "{{base_url}}/store/test-products/prod_fashion_001", "host": ["{{base_url}}"], "path": ["store", "test-products", "prod_fashion_001"]}, "description": "Test cross-tenant access - Electronics tenant trying to access Fashion product (should fail)"}, "response": []}]}]}