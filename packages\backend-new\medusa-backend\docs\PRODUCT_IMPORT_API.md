# Product Import API Documentation

## Overview

The Product Import API provides comprehensive Excel-based bulk product import functionality for the ONDC Seller App. It supports multi-tenant architecture and includes validation, template generation, and error handling capabilities.

## Base URL
```
http://localhost:9000
```

## Authentication

### Production Endpoints
Production endpoints (`/admin/products/import/*`) require JWT authentication:
```
Authorization: Bearer <jwt_token>
x-tenant-id: <tenant_id>
```

### Test Endpoints
Test endpoints (`/test/products/import/*`) do not require authentication and are designed for development use:
```
x-tenant-id: <tenant_id>  # Optional, defaults to 'default'
```

## Endpoints

### 1. Download Excel Template

**Test Endpoint:** `GET /test/products/import/template`
**Production Endpoint:** `GET /admin/products/import/template`

**Description:** Downloads an Excel template file with predefined columns and sample data for product import.

**Response:** Excel file (.xlsx) with sample product data

**Example:**
```bash
curl -X GET "http://localhost:9000/test/products/import/template" -o product-template.xlsx
```

### 2. Validate Excel File

**Test Endpoint:** `POST /test/products/import/validate`
**Production Endpoint:** `POST /admin/products/import/validate`

**Description:** Validates an Excel file for product import without creating products. Returns validation results, errors, and preview data.

**Request:**
- Content-Type: `multipart/form-data`
- Body: Excel file as `file` parameter

**Response:**
```json
{
  "message": "File validation completed",
  "valid": true,
  "total_rows": 2,
  "valid_rows": 2,
  "invalid_rows": 0,
  "errors": [],
  "warnings": [],
  "preview_data": [
    {
      "row_number": 2,
      "title": "Sample Product 1",
      "description": "This is a sample product description",
      "status": "draft",
      "variant_sku": "SAMPLE-001",
      "variant_price": 999,
      "errors": [],
      "warnings": []
    }
  ]
}
```

**Example:**
```bash
curl -X POST "http://localhost:9000/test/products/import/validate" \
  -F "file=@product-data.xlsx"
```

### 3. Import Products from Excel

**Test Endpoint:** `POST /test/products/import`
**Production Endpoint:** `POST /admin/products/import`

**Description:** Imports products from an Excel file. Creates products and variants using Medusa's native APIs with proper multi-tenant isolation.

**Request:**
- Content-Type: `multipart/form-data`
- Body: Excel file as `file` parameter

**Response:**
```json
{
  "message": "Product import completed",
  "transaction_id": "33129a99-7707-4bfc-9a20-c3d983a86009",
  "success": true,
  "total_rows": 2,
  "successful_imports": 2,
  "failed_imports": 0,
  "errors": [],
  "created_products": [
    "test-product-ea5086f6-7df3-4815-af25-b0c802c53a9c",
    "test-product-abee2197-e6ba-457e-abc2-4bc47cad9909"
  ]
}
```

**Example:**
```bash
curl -X POST "http://localhost:9000/test/products/import" \
  -F "file=@product-data.xlsx" \
  -H "x-tenant-id: default"
```

## Excel Template Structure

The Excel template includes the following columns:

### Basic Product Fields
- `title` (required): Product name
- `description`: Product description
- `handle`: URL slug (auto-generated if not provided)
- `thumbnail`: Main product image URL
- `status`: Product status (draft, proposed, published, rejected)

### Custom Metadata Fields
- `product_prices`: JSON string with currency pricing `{"INR": 999.00}`
- `product_features`: JSON array of features `["Feature 1", "Feature 2"]`
- `product_overview`: Product overview text
- `product_quantity`: Available quantity
- `product_specifications`: JSON object with specifications `{"color": "Red"}`
- `product_inventory_status`: Inventory status (in_stock, out_of_stock, etc.)

### Product Images
- `images`: Comma-separated list of image URLs

### Variant Fields
- `variant_title`: Variant name
- `variant_sku`: Stock Keeping Unit
- `variant_material`: Material description
- `variant_weight`: Weight in kg
- `variant_width`: Width in cm
- `variant_length`: Length in cm
- `variant_height`: Height in cm
- `variant_price`: Variant price in INR
- `variant_compare_price`: Compare at price
- `variant_cost_price`: Cost price
- `variant_inventory_quantity`: Available quantity
- `variant_allow_backorder`: Allow backorders (true/false)
- `variant_manage_inventory`: Manage inventory (true/false)

### Categorization
- `category`: Product category
- `tags`: Comma-separated tags

## Error Handling

### Validation Errors
The API provides detailed validation errors with:
- Row number
- Field name
- Error message
- Invalid value

### Common Errors
1. **Missing Title**: Product title is required
2. **Invalid Status**: Status must be one of the allowed values
3. **Invalid JSON**: Malformed JSON in metadata fields
4. **Invalid Numbers**: Non-numeric values in numeric fields
5. **File Type**: Only Excel (.xlsx, .xls) and CSV files allowed
6. **File Size**: Maximum 10MB file size

## Multi-Tenant Support

All endpoints support multi-tenant architecture:
- Products are automatically isolated by tenant
- Use `x-tenant-id` header to specify tenant
- Default tenant is 'default' if not specified

## Rate Limits

- Maximum file size: 10MB
- Maximum rows per import: No hard limit (performance dependent)
- Concurrent imports: Limited by server resources

## Development vs Production

### Test Endpoints (`/test/products/import/*`)
- No authentication required
- Mock product creation (doesn't create real products)
- Detailed logging and debugging
- Suitable for development and testing

### Production Endpoints (`/admin/products/import/*`)
- JWT authentication required
- Creates real products in Medusa
- Production-level error handling
- Audit logging enabled

## Frontend Integration

The frontend uses the Bulk Import API service:

```typescript
import { BulkImportService } from '@/lib/api/bulk-import'

const bulkImportService = new BulkImportService()

// Download template
const template = await bulkImportService.downloadTemplate()

// Validate file
const validation = await bulkImportService.validateProductImportFile(file)

// Import products
const result = await bulkImportService.uploadProductImport(file)
```

## Monitoring and Logging

All import operations are logged with:
- Transaction IDs for tracking
- Detailed error messages
- Performance metrics
- Tenant isolation verification

## Security Considerations

1. **File Validation**: Strict MIME type and extension checking
2. **Size Limits**: 10MB maximum file size
3. **Tenant Isolation**: Automatic tenant-based data separation
4. **Authentication**: JWT-based authentication for production endpoints
5. **Input Sanitization**: All input data is validated and sanitized
