# Product Import System - Developer Guide

## Overview

This guide provides comprehensive instructions for developers to understand, customize, and extend the Excel-based product import system in the ONDC Seller App.

## Architecture

### Backend Components

```
src/api/
├── admin/products/import/
│   ├── route.ts                 # Production import endpoint (auth required)
│   └── validate/route.ts        # Production validation endpoint
└── test/products/import/
    ├── route.ts                 # Test import endpoint (no auth)
    ├── template/route.ts        # Test template download
    └── validate/route.ts        # Test validation endpoint
```

### Frontend Components

```
lib/api/
└── bulk-import.ts              # API service for import functionality

components/
└── [existing upload components] # UI components for file upload
```

## Development Setup

### Prerequisites

1. **Backend Dependencies**
   ```bash
   cd ondc-seller-app/packages/backend-new/medusa-backend
   npm install xlsx multer @types/multer uuid @types/uuid --legacy-peer-deps
   ```

2. **Frontend Dependencies**
   ```bash
   cd ondc-seller-app/packages/frontend
   # Dependencies already included in existing setup
   ```

### Running the System

1. **Start Backend**
   ```bash
   cd ondc-seller-app/packages/backend-new/medusa-backend
   npm run dev
   ```

2. **Start Frontend**
   ```bash
   cd ondc-seller-app/packages/frontend
   npm run dev
   ```

3. **Test Endpoints**
   - Template Download: `http://localhost:9000/test/products/import/template`
   - File Validation: `POST http://localhost:9000/test/products/import/validate`
   - Product Import: `POST http://localhost:9000/test/products/import`

## Development vs Production Modes

### Development Mode (Test Endpoints)

**Features:**
- No authentication required
- Mock product creation (doesn't create real products)
- Detailed logging and debugging
- Accessible at `/test/products/import/*`

**Usage:**
```bash
# Download template
curl -X GET "http://localhost:9000/test/products/import/template" -o template.xlsx

# Validate file
curl -X POST "http://localhost:9000/test/products/import/validate" -F "file=@data.xlsx"

# Import products (mock)
curl -X POST "http://localhost:9000/test/products/import" -F "file=@data.xlsx"
```

### Production Mode (Admin Endpoints)

**Features:**
- JWT authentication required
- Creates real products in Medusa
- Production-level error handling
- Accessible at `/admin/products/import/*`

**Usage:**
```bash
# Requires authentication header
curl -X POST "http://localhost:9000/admin/products/import" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "x-tenant-id: <tenant_id>" \
  -F "file=@data.xlsx"
```

## Customization Guide

### Adding New Fields

1. **Update TypeScript Interface**
   ```typescript
   // In route.ts files
   interface ProductImportRow {
     // Existing fields...
     new_custom_field?: string
   }
   ```

2. **Update Excel Template**
   ```typescript
   // In generateExcelTemplate() function
   const sampleData = [
     {
       // Existing fields...
       new_custom_field: 'Sample Value'
     }
   ]
   ```

3. **Update Validation Logic**
   ```typescript
   // In validateProductRow() function
   if (row.new_custom_field && !isValidCustomField(row.new_custom_field)) {
     errors.push({
       row: rowNumber,
       field: 'new_custom_field',
       message: 'Invalid custom field value',
       value: row.new_custom_field
     })
   }
   ```

4. **Update Product Creation**
   ```typescript
   // In buildProductData() function
   if (row.new_custom_field) {
     metadata.new_custom_field = row.new_custom_field
   }
   ```

### Extending Validation Rules

```typescript
function validateProductRow(row: ProductImportRow, rowNumber: number): ImportValidationError[] {
  const errors: ImportValidationError[] = []

  // Add custom validation
  if (row.custom_field && !customValidationLogic(row.custom_field)) {
    errors.push({
      row: rowNumber,
      field: 'custom_field',
      message: 'Custom validation failed',
      value: row.custom_field
    })
  }

  return errors
}
```

### Adding New File Types

```typescript
// Update file filter in multer configuration
fileFilter: (req, file, cb) => {
  const allowedTypes = [
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/json', // Add new type
  ]
  
  const allowedExtensions = ['.xlsx', '.xls', '.csv', '.json'] // Add new extension
  
  // Validation logic...
}
```

## Testing

### Unit Testing

Create test files for each component:

```typescript
// tests/product-import.test.ts
import { validateProductRow, buildProductData } from '../src/api/test/products/import/route'

describe('Product Import', () => {
  test('should validate required fields', () => {
    const row = { title: '' }
    const errors = validateProductRow(row, 1)
    expect(errors).toHaveLength(1)
    expect(errors[0].field).toBe('title')
  })

  test('should build product data correctly', async () => {
    const row = { title: 'Test Product', description: 'Test Description' }
    const productData = await buildProductData(row, 'test-tenant')
    expect(productData.title).toBe('Test Product')
    expect(productData.metadata.tenant_id).toBe('test-tenant')
  })
})
```

### Integration Testing

```bash
# Test complete workflow
npm run test:integration

# Test specific endpoints
curl -X GET "http://localhost:9000/test/products/import/template" -o test-template.xlsx
curl -X POST "http://localhost:9000/test/products/import/validate" -F "file=@test-template.xlsx"
curl -X POST "http://localhost:9000/test/products/import" -F "file=@test-template.xlsx"
```

## Error Handling

### Common Issues and Solutions

1. **File Upload Errors**
   ```typescript
   // Check MIME type detection
   console.log('File details:', {
     originalname: file.originalname,
     mimetype: file.mimetype,
     size: file.size
   })
   ```

2. **Excel Parsing Errors**
   ```typescript
   try {
     const workbook = XLSX.readFile(filePath)
     const sheetName = workbook.SheetNames[0]
     const worksheet = workbook.Sheets[sheetName]
     const rawData = XLSX.utils.sheet_to_json(worksheet)
   } catch (error) {
     console.error('Excel parsing error:', error)
     throw new Error(`Failed to parse Excel file: ${error.message}`)
   }
   ```

3. **Validation Errors**
   ```typescript
   // Add detailed error context
   errors.push({
     row: rowNumber,
     field: fieldName,
     message: `Detailed error message with context`,
     value: fieldValue,
     expectedFormat: 'Expected format description'
   })
   ```

## Performance Optimization

### Large File Handling

1. **Streaming Processing**
   ```typescript
   // For very large files, consider streaming
   const stream = XLSX.stream.read(filePath, {
     sheetRows: 1000 // Process in chunks
   })
   ```

2. **Batch Processing**
   ```typescript
   // Process products in batches
   const batchSize = 50
   for (let i = 0; i < rawData.length; i += batchSize) {
     const batch = rawData.slice(i, i + batchSize)
     await processBatch(batch)
   }
   ```

3. **Memory Management**
   ```typescript
   // Clean up resources
   fs.unlinkSync(filePath) // Remove uploaded file
   delete workbook // Clear memory
   ```

## Security Considerations

### File Validation

```typescript
// Strict file validation
const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ALLOWED_EXTENSIONS = ['.xlsx', '.xls', '.csv']
const ALLOWED_MIME_TYPES = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
  'text/csv'
]
```

### Input Sanitization

```typescript
// Sanitize input data
function sanitizeInput(value: string): string {
  return value
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000) // Limit length
}
```

### Tenant Isolation

```typescript
// Ensure tenant isolation
const tenantId = req.headers['x-tenant-id'] as string || 'default'
metadata.tenant_id = tenantId // Always set tenant ID
```

## Monitoring and Logging

### Structured Logging

```typescript
console.log('[PRODUCT IMPORT]', {
  action: 'file_uploaded',
  tenant_id: tenantId,
  file_name: file.originalname,
  file_size: file.size,
  timestamp: new Date().toISOString()
})
```

### Error Tracking

```typescript
// Track errors with context
console.error('[PRODUCT IMPORT ERROR]', {
  error: error.message,
  stack: error.stack,
  tenant_id: tenantId,
  file_name: file?.originalname,
  row_number: rowNumber
})
```

## Deployment

### Environment Variables

```bash
# .env
MAX_IMPORT_FILE_SIZE=10485760  # 10MB
IMPORT_UPLOAD_DIR=/tmp/imports
ENABLE_IMPORT_LOGGING=true
```

### Production Checklist

- [ ] Authentication middleware enabled
- [ ] File size limits configured
- [ ] Upload directory permissions set
- [ ] Error logging configured
- [ ] Rate limiting implemented
- [ ] Security headers added
- [ ] CORS configured properly

## Troubleshooting

### Common Issues

1. **Port 9000 already in use**
   ```bash
   lsof -ti:9000 | xargs kill -9
   ```

2. **Excel file not recognized**
   - Check MIME type in logs
   - Verify file extension
   - Test with different Excel versions

3. **Authentication errors**
   - Verify JWT token
   - Check tenant ID header
   - Use test endpoints for development

4. **Memory issues with large files**
   - Implement streaming
   - Add batch processing
   - Increase Node.js memory limit
