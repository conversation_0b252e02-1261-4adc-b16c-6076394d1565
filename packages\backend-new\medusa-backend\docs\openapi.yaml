openapi: 3.0.3
info:
  title: ONDC Seller App - Medusa v2 Backend API
  description: |
    Production-ready API specification for the ONDC Seller App built on Medusa v2.

    ## Multi-Tenancy
    This API supports multi-tenancy through sales channels and publishable API keys for automatic tenant isolation.

    ## Authentication
    - **Admin endpoints**: Require Bearer token authentication via `Authorization: Bearer {token}`
    - **Store endpoints**: Require publishable API key via `x-publishable-api-key` header
    - **Customer endpoints**: Require customer JWT token for authenticated operations

    ## Base URLs
    - **Admin API**: `http://localhost:9000/admin`
    - **Store API**: `http://localhost:9000/store`
    - **Auth API**: `http://localhost:9000/auth`

    ## ONDC Integration
    The API includes ONDC-specific extensions including Cash on Delivery support and multi-tenant configurations.

  version: 2.8.6
  contact:
    name: ONDC Seller App Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:9000
    description: Development server
  - url: https://api.ondc-seller.com
    description: Production server

security:
  - bearerAuth: []
  - publishableApiKey: []

tags:
  - name: Authentication
    description: User authentication and session management
  - name: Admin - Users
    description: Admin user management
  - name: Admin - Products
    description: Product management and catalog operations
  - name: Admin - Customers
    description: Customer management and administration
  - name: Admin - Orders
    description: Order management and fulfillment
  - name: Admin - Inventory
    description: Inventory and stock management
  - name: Admin - Analytics
    description: Analytics and reporting endpoints
  - name: Admin - Sales Channels
    description: Sales channel and tenant management
  - name: Store - Products
    description: Public product catalog
  - name: Store - Carts
    description: Shopping cart operations
  - name: Store - Customers
    description: Customer registration and account management
  - name: Store - Orders
    description: Order placement and tracking

paths:
  # Authentication Endpoints
  /auth/user/emailpass:
    post:
      summary: Admin user login
      description: Authenticate admin user and receive JWT token
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  example: supersecret
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/user/me:
    get:
      summary: Get current user
      description: Get current authenticated user information
      tags:
        - Authentication
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/user/logout:
    post:
      summary: Logout user
      description: Logout current user and invalidate token
      tags:
        - Authentication
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Logout successful
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Admin User Management
  /admin/users:
    get:
      summary: List admin users
      description: Get list of all admin users
      tags:
        - Admin - Users
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          description: Number of users to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of users to skip
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create admin user
      description: Create a new admin user
      tags:
        - Admin - Users
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                first_name:
                  type: string
                last_name:
                  type: string
                password:
                  type: string
                  minLength: 8
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          description: User already exists
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/users/{id}:
    get:
      summary: Get user by ID
      description: Get specific admin user by ID
      tags:
        - Admin - Users
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: string
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Update user
      description: Update admin user information
      tags:
        - Admin - Users
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                first_name:
                  type: string
                last_name:
                  type: string
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete user
      description: Delete admin user
      tags:
        - Admin - Users
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: string
      responses:
        '200':
          description: User deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  deleted:
                    type: boolean
                    example: true
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Admin Product Management
  /admin/products:
    get:
      summary: List products
      description: Get list of products with filtering and pagination
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          description: Number of products to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of products to skip
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: q
          in: query
          description: Search query for product title or description
          schema:
            type: string
        - name: status
          in: query
          description: Filter by product status
          schema:
            type: string
            enum: [draft, proposed, published, rejected]
        - name: sales_channel_id
          in: query
          description: Filter by sales channel (tenant)
          schema:
            type: string
        - name: category_id
          in: query
          description: Filter by product category
          schema:
            type: string
        - name: collection_id
          in: query
          description: Filter by product collection
          schema:
            type: string
      responses:
        '200':
          description: Products retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create product
      description: Create a new product
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
              properties:
                title:
                  type: string
                  example: "Premium Smartphone"
                subtitle:
                  type: string
                  example: "Latest model with advanced features"
                description:
                  type: string
                  example: "High-performance smartphone with excellent camera"
                handle:
                  type: string
                  example: "premium-smartphone"
                status:
                  type: string
                  enum: [draft, proposed, published, rejected]
                  default: draft
                thumbnail:
                  type: string
                  example: "https://example.com/image.jpg"
                weight:
                  type: number
                  example: 200
                length:
                  type: number
                  example: 15
                height:
                  type: number
                  example: 0.8
                width:
                  type: number
                  example: 7
                origin_country:
                  type: string
                  example: "IN"
                material:
                  type: string
                  example: "Aluminum"
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      summary: Get product analytics
      description: Product performance and inventory insights
      tags:
        - Admin - Analytics
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantHeader'
        - name: period
          in: query
          description: Time period for analytics
          schema:
            type: string
            enum: ['7d', '30d', '90d', '1y']
            default: '30d'
        - name: limit
          in: query
          description: Number of products to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: sort_by
          in: query
          description: Sort products by metric
          schema:
            type: string
            enum: ['revenue', 'units_sold', 'views', 'conversion_rate']
            default: 'revenue'
        - name: category_id
          in: query
          description: Filter by product category
          schema:
            type: string
        - name: sales_channel_id
          in: query
          description: Filter by sales channel
          schema:
            type: string
      responses:
        '200':
          description: Product analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductAnalytics'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/products/{id}:
    get:
      summary: Get product by ID
      description: Get specific product by ID
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
        - name: expand
          in: query
          description: Comma-separated list of relations to expand
          schema:
            type: string
            example: "variants,categories,collections"
      responses:
        '200':
          description: Product retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Update product
      description: Update product information
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                subtitle:
                  type: string
                description:
                  type: string
                handle:
                  type: string
                status:
                  type: string
                  enum: [draft, proposed, published, rejected]
                thumbnail:
                  type: string
                weight:
                  type: number
                length:
                  type: number
                height:
                  type: number
                width:
                  type: number
                origin_country:
                  type: string
                material:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '200':
          description: Product updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete product
      description: Delete product
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
      responses:
        '200':
          description: Product deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  deleted:
                    type: boolean
                    example: true
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Admin Product Variants
  /admin/products/{id}/variants:
    get:
      summary: List product variants
      description: Get list of variants for a specific product
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
        - name: limit
          in: query
          description: Number of variants to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of variants to skip
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: Product variants retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  variants:
                    type: array
                    items:
                      $ref: '#/components/schemas/ProductVariant'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create product variant
      description: Create a new variant for a product
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
              properties:
                title:
                  type: string
                  example: "128GB Black"
                sku:
                  type: string
                  example: "PHONE-128GB-BLACK"
                barcode:
                  type: string
                  example: "1234567890123"
                ean:
                  type: string
                  example: "1234567890123"
                upc:
                  type: string
                  example: "123456789012"
                inventory_quantity:
                  type: integer
                  example: 50
                allow_backorder:
                  type: boolean
                  example: false
                manage_inventory:
                  type: boolean
                  example: true
                weight:
                  type: number
                  example: 200
                length:
                  type: number
                  example: 15
                height:
                  type: number
                  example: 0.8
                width:
                  type: number
                  example: 7
                origin_country:
                  type: string
                  example: "IN"
                material:
                  type: string
                  example: "Aluminum"
                metadata:
                  type: object
                  additionalProperties: true
                prices:
                  type: array
                  items:
                    type: object
                    properties:
                      currency_code:
                        type: string
                        example: "INR"
                      amount:
                        type: number
                        example: 47199
                      region_id:
                        type: string
                options:
                  type: array
                  items:
                    type: object
                    properties:
                      option_id:
                        type: string
                      value:
                        type: string
      responses:
        '201':
          description: Product variant created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  variant:
                    $ref: '#/components/schemas/ProductVariant'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/products/{id}/variants/{variant_id}:
    post:
      summary: Update product variant
      description: Update a specific product variant
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
        - name: variant_id
          in: path
          required: true
          description: Variant ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                sku:
                  type: string
                barcode:
                  type: string
                ean:
                  type: string
                upc:
                  type: string
                inventory_quantity:
                  type: integer
                allow_backorder:
                  type: boolean
                manage_inventory:
                  type: boolean
                weight:
                  type: number
                length:
                  type: number
                height:
                  type: number
                width:
                  type: number
                origin_country:
                  type: string
                material:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '200':
          description: Product variant updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  variant:
                    $ref: '#/components/schemas/ProductVariant'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete product variant
      description: Delete a specific product variant
      tags:
        - Admin - Products
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
        - name: variant_id
          in: path
          required: true
          description: Variant ID
          schema:
            type: string
      responses:
        '200':
          description: Product variant deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  deleted:
                    type: boolean
                    example: true
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Admin Customer Management
  /admin/customers:
    get:
      summary: List customers
      description: Get list of customers with filtering and pagination
      tags:
        - Admin - Customers
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          description: Number of customers to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of customers to skip
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: q
          in: query
          description: Search query for customer email or name
          schema:
            type: string
        - name: sales_channel_id
          in: query
          description: Filter by sales channel (tenant)
          schema:
            type: string
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customers:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create customer
      description: Create a new customer
      tags:
        - Admin - Customers
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                first_name:
                  type: string
                  example: John
                last_name:
                  type: string
                  example: Doe
                phone:
                  type: string
                  example: "+91-9876543210"
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customer:
                    $ref: '#/components/schemas/Customer'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          description: Customer already exists
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      summary: Get inventory analytics
      description: Inventory management and stock analysis
      tags:
        - Admin - Analytics
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantHeader'
        - name: location_id
          in: query
          description: Filter by stock location
          schema:
            type: string
        - name: low_stock_threshold
          in: query
          description: Threshold for low stock alerts
          schema:
            type: integer
            default: 10
        - name: category_id
          in: query
          description: Filter by product category
          schema:
            type: string
      responses:
        '200':
          description: Inventory analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryAnalytics'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/orders/{id}:
    get:
      summary: Get order by ID
      description: Get specific order by ID with detailed information
      tags:
        - Admin - Orders
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
        - name: expand
          in: query
          description: Comma-separated list of relations to expand
          schema:
            type: string
            example: "customer,items,shipping_address,billing_address,payments,fulfillments"
      responses:
        '200':
          description: Order retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  order:
                    $ref: '#/components/schemas/Order'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Update order
      description: Update order information
      tags:
        - Admin - Orders
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                shipping_address:
                  $ref: '#/components/schemas/Address'
                billing_address:
                  $ref: '#/components/schemas/Address'
                status:
                  type: string
                  enum: [pending, completed, archived, canceled, requires_action]
                payment_status:
                  type: string
                  enum: [not_paid, awaiting, captured, partially_refunded, refunded, canceled, requires_action]
                fulfillment_status:
                  type: string
                  enum: [not_fulfilled, partially_fulfilled, fulfilled, partially_shipped, shipped, partially_returned, returned, canceled, requires_action]
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '200':
          description: Order updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  order:
                    $ref: '#/components/schemas/Order'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/orders/{id}/fulfillments:
    get:
      summary: List order fulfillments
      description: Get list of fulfillments for an order
      tags:
        - Admin - Orders
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
      responses:
        '200':
          description: Fulfillments retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  fulfillments:
                    type: array
                    items:
                      $ref: '#/components/schemas/Fulfillment'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create fulfillment
      description: Create a new fulfillment for an order
      tags:
        - Admin - Orders
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - items
              properties:
                items:
                  type: array
                  items:
                    type: object
                    required:
                      - id
                      - quantity
                    properties:
                      id:
                        type: string
                        description: Order line item ID
                      quantity:
                        type: integer
                        minimum: 1
                        description: Quantity to fulfill
                location_id:
                  type: string
                  description: Stock location ID
                provider_id:
                  type: string
                  description: Fulfillment provider ID
                shipping_option_id:
                  type: string
                  description: Shipping option ID
                tracking_numbers:
                  type: array
                  items:
                    type: string
                  description: Tracking numbers
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '201':
          description: Fulfillment created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  fulfillment:
                    $ref: '#/components/schemas/Fulfillment'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/orders/{id}/payments:
    get:
      summary: List order payments
      description: Get list of payments for an order
      tags:
        - Admin - Orders
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
      responses:
        '200':
          description: Payments retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  payments:
                    type: array
                    items:
                      $ref: '#/components/schemas/Payment'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/orders/{id}/capture:
    post:
      summary: Capture payment
      description: Capture payment for an order
      tags:
        - Admin - Orders
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: number
                  description: Amount to capture (optional, defaults to full amount)
      responses:
        '200':
          description: Payment captured successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  order:
                    $ref: '#/components/schemas/Order'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Admin Inventory Management
  /admin/inventory-items:
    get:
      summary: List inventory items
      description: Get list of inventory items with filtering and pagination
      tags:
        - Admin - Inventory
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          description: Number of inventory items to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of inventory items to skip
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: q
          in: query
          description: Search query for inventory items
          schema:
            type: string
        - name: sku
          in: query
          description: Filter by SKU
          schema:
            type: string
        - name: origin_country
          in: query
          description: Filter by origin country
          schema:
            type: string
        - name: mid_code
          in: query
          description: Filter by MID code
          schema:
            type: string
        - name: material
          in: query
          description: Filter by material
          schema:
            type: string
        - name: weight
          in: query
          description: Filter by weight
          schema:
            type: object
            properties:
              lt:
                type: number
              gt:
                type: number
              gte:
                type: number
              lte:
                type: number
        - name: length
          in: query
          description: Filter by length
          schema:
            type: object
            properties:
              lt:
                type: number
              gt:
                type: number
              gte:
                type: number
              lte:
                type: number
        - name: height
          in: query
          description: Filter by height
          schema:
            type: object
            properties:
              lt:
                type: number
              gt:
                type: number
              gte:
                type: number
              lte:
                type: number
        - name: width
          in: query
          description: Filter by width
          schema:
            type: object
            properties:
              lt:
                type: number
              gt:
                type: number
              gte:
                type: number
              lte:
                type: number
      responses:
        '200':
          description: Inventory items retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  inventory_items:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryItem'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create inventory item
      description: Create a new inventory item
      tags:
        - Admin - Inventory
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sku:
                  type: string
                  example: "PHONE-128GB-BLACK"
                origin_country:
                  type: string
                  example: "IN"
                mid_code:
                  type: string
                  example: "MID123"
                material:
                  type: string
                  example: "Aluminum"
                weight:
                  type: number
                  example: 200
                length:
                  type: number
                  example: 15
                height:
                  type: number
                  example: 0.8
                width:
                  type: number
                  example: 7
                requires_shipping:
                  type: boolean
                  example: true
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '201':
          description: Inventory item created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  inventory_item:
                    $ref: '#/components/schemas/InventoryItem'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/inventory-items/{id}:
    get:
      summary: Get inventory item by ID
      description: Get specific inventory item by ID
      tags:
        - Admin - Inventory
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Inventory item ID
          schema:
            type: string
      responses:
        '200':
          description: Inventory item retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  inventory_item:
                    $ref: '#/components/schemas/InventoryItem'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Update inventory item
      description: Update inventory item information
      tags:
        - Admin - Inventory
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Inventory item ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sku:
                  type: string
                origin_country:
                  type: string
                mid_code:
                  type: string
                material:
                  type: string
                weight:
                  type: number
                length:
                  type: number
                height:
                  type: number
                width:
                  type: number
                requires_shipping:
                  type: boolean
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '200':
          description: Inventory item updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  inventory_item:
                    $ref: '#/components/schemas/InventoryItem'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/inventory-items/{id}/location-levels:
    get:
      summary: Get inventory levels
      description: Get stock levels for an inventory item across locations
      tags:
        - Admin - Inventory
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Inventory item ID
          schema:
            type: string
        - name: location_id
          in: query
          description: Filter by specific location
          schema:
            type: string
      responses:
        '200':
          description: Inventory levels retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  inventory_levels:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryLevel'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Update inventory levels
      description: Update stock levels for an inventory item at specific locations
      tags:
        - Admin - Inventory
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Inventory item ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - location_id
              properties:
                location_id:
                  type: string
                  description: Stock location ID
                stocked_quantity:
                  type: integer
                  description: Available stock quantity
                  example: 100
                incoming_quantity:
                  type: integer
                  description: Incoming stock quantity
                  example: 50
      responses:
        '200':
          description: Inventory levels updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  inventory_level:
                    $ref: '#/components/schemas/InventoryLevel'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Admin Analytics Endpoints
  /admin/analytics:
    get:
      summary: Get analytics overview
      description: Provides information about available analytics endpoints
      tags:
        - Admin - Analytics
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Analytics overview retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsOverview'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/analytics/dashboard:
    get:
      summary: Get dashboard analytics
      description: Comprehensive dashboard analytics with key metrics and trends
      tags:
        - Admin - Analytics
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          description: Time period for analytics
          schema:
            type: string
            enum: ['7d', '30d', '90d', '1y']
            default: '30d'
        - name: sales_channel_id
          in: query
          description: Filter by sales channel
          schema:
            type: string
        - name: currency
          in: query
          description: Currency code
          schema:
            type: string
            default: 'INR'
      responses:
        '200':
          description: Dashboard analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardAnalytics'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/analytics/sales:
    get:
      summary: Get sales analytics
      description: Detailed sales performance analysis with trends and breakdowns
      tags:
        - Admin - Analytics
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          description: Time period for analytics
          schema:
            type: string
            enum: ['7d', '30d', '90d', '1y']
            default: '30d'
        - name: group_by
          in: query
          description: Group sales data by time interval
          schema:
            type: string
            enum: ['day', 'week', 'month']
            default: 'day'
        - name: sales_channel_id
          in: query
          description: Filter by sales channel
          schema:
            type: string
        - name: currency
          in: query
          description: Currency code
          schema:
            type: string
            default: 'INR'
        - name: category_id
          in: query
          description: Filter by product category
          schema:
            type: string
      responses:
        '200':
          description: Sales analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAnalytics'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/analytics/customers:
    get:
      summary: Get customer analytics
      description: Customer behavior analysis and segmentation
      tags:
        - Admin - Analytics
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          description: Time period for analytics
          schema:
            type: string
            enum: ['7d', '30d', '90d', '1y']
            default: '30d'
        - name: segment
          in: query
          description: Customer segment filter
          schema:
            type: string
            enum: ['all', 'new', 'repeat', 'vip', 'inactive']
            default: 'all'
        - name: sales_channel_id
          in: query
          description: Filter by sales channel
          schema:
            type: string
      responses:
        '200':
          description: Customer analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAnalytics'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/analytics/products:
    get:
      summary: Get product analytics
      description: Product performance and inventory insights
      tags:
        - Admin - Analytics
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          description: Time period for analytics
          schema:
            type: string
            enum: ['7d', '30d', '90d', '1y']
            default: '30d'
        - name: limit
          in: query
          description: Number of products to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: sort_by
          in: query
          description: Sort products by metric
          schema:
            type: string
            enum: ['revenue', 'units_sold', 'views', 'conversion_rate']
            default: 'revenue'
        - name: category_id
          in: query
          description: Filter by product category
          schema:
            type: string
        - name: sales_channel_id
          in: query
          description: Filter by sales channel
          schema:
            type: string
      responses:
        '200':
          description: Product analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductAnalytics'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      summary: Get KPI analytics
      description: Key Performance Indicators with targets and scoring
      tags:
        - Admin - Analytics
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantHeader'
        - name: period
          in: query
          description: Time period for KPI analysis
          schema:
            type: string
            enum: ['7d', '30d', '90d', '1y']
            default: '30d'
        - name: sales_channel_id
          in: query
          description: Filter by sales channel
          schema:
            type: string
        - name: compare_previous
          in: query
          description: Compare with previous period
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: KPI analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KPIAnalytics'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/customers/{id}:
    get:
      summary: Get customer by ID
      description: Get specific customer by ID
      tags:
        - Admin - Customers
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: string
      responses:
        '200':
          description: Customer retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customer:
                    $ref: '#/components/schemas/Customer'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Update customer
      description: Update customer information
      tags:
        - Admin - Customers
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                first_name:
                  type: string
                last_name:
                  type: string
                phone:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customer:
                    $ref: '#/components/schemas/Customer'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete customer
      description: Delete customer
      tags:
        - Admin - Customers
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: string
      responses:
        '200':
          description: Customer deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  deleted:
                    type: boolean
                    example: true
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Admin Order Management
  /admin/orders:
    get:
      summary: List orders
      description: Get list of orders with filtering and pagination
      tags:
        - Admin - Orders
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          description: Number of orders to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of orders to skip
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: q
          in: query
          description: Search query for order
          schema:
            type: string
        - name: status
          in: query
          description: Filter by order status
          schema:
            type: string
            enum: [pending, completed, archived, canceled, requires_action]
        - name: payment_status
          in: query
          description: Filter by payment status
          schema:
            type: string
            enum: [not_paid, awaiting, captured, partially_refunded, refunded, canceled, requires_action]
        - name: fulfillment_status
          in: query
          description: Filter by fulfillment status
          schema:
            type: string
            enum: [not_fulfilled, partially_fulfilled, fulfilled, partially_shipped, shipped, partially_returned, returned, canceled, requires_action]
        - name: sales_channel_id
          in: query
          description: Filter by sales channel (tenant)
          schema:
            type: string
        - name: customer_id
          in: query
          description: Filter by customer ID
          schema:
            type: string
        - name: email
          in: query
          description: Filter by customer email
          schema:
            type: string
        - name: region_id
          in: query
          description: Filter by region
          schema:
            type: string
        - name: currency_code
          in: query
          description: Filter by currency
          schema:
            type: string
        - name: created_at
          in: query
          description: Filter by creation date
          schema:
            type: object
            properties:
              lt:
                type: string
                format: date-time
              gt:
                type: string
                format: date-time
              gte:
                type: string
                format: date-time
              lte:
                type: string
                format: date-time
      responses:
        '200':
          description: Orders retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create order
      description: Create a new order
      tags:
        - Admin - Orders
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - region_id
                - email
                - items
              properties:
                region_id:
                  type: string
                  description: Region ID
                email:
                  type: string
                  format: email
                  description: Customer email
                customer_id:
                  type: string
                  description: Customer ID (optional)
                sales_channel_id:
                  type: string
                  description: Sales channel ID
                currency_code:
                  type: string
                  description: Currency code
                  example: INR
                items:
                  type: array
                  items:
                    type: object
                    required:
                      - variant_id
                      - quantity
                    properties:
                      variant_id:
                        type: string
                      quantity:
                        type: integer
                        minimum: 1
                      unit_price:
                        type: number
                      metadata:
                        type: object
                        additionalProperties: true
                shipping_address:
                  $ref: '#/components/schemas/Address'
                billing_address:
                  $ref: '#/components/schemas/Address'
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '201':
          description: Order created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  order:
                    $ref: '#/components/schemas/Order'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/sales-channels:
    get:
      summary: List sales channels
      description: Get list of sales channels (tenants)
      tags:
        - Admin - Sales Channels
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          description: Number of sales channels to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of sales channels to skip
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: Sales channels retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  sales_channels:
                    type: array
                    items:
                      $ref: '#/components/schemas/SalesChannel'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create sales channel
      description: Create a new sales channel (tenant)
      tags:
        - Admin - Sales Channels
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  example: "Electronics Store"
                description:
                  type: string
                  example: "Premium electronics and gadgets"
                is_disabled:
                  type: boolean
                  default: false
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '201':
          description: Sales channel created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  sales_channel:
                    $ref: '#/components/schemas/SalesChannel'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Store Product Endpoints
  /store/products:
    get:
      summary: List products
      description: Get public product catalog (automatically filtered by publishable API key)
      tags:
        - Store - Products
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: limit
          in: query
          description: Number of products to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of products to skip
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: q
          in: query
          description: Search query for product title or description
          schema:
            type: string
        - name: category_id
          in: query
          description: Filter by product category
          schema:
            type: string
        - name: collection_id
          in: query
          description: Filter by product collection
          schema:
            type: string
        - name: region_id
          in: query
          description: Region ID for pricing
          schema:
            type: string
      responses:
        '200':
          description: Products retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /store/products/{id}:
    get:
      summary: Get product details
      description: Get detailed information for a specific product
      tags:
        - Store - Products
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
        - name: region_id
          in: query
          description: Region ID for pricing
          schema:
            type: string
      responses:
        '200':
          description: Product details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Store Cart Endpoints
  /store/carts:
    post:
      summary: Create cart
      description: Create a new shopping cart
      tags:
        - Store - Carts
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                region_id:
                  type: string
                  description: Region ID for the cart
                  example: "reg_01JZ7RPY072WGWKTJ6Q2YE46V7"
                sales_channel_id:
                  type: string
                  description: Sales channel ID (optional, auto-detected from API key)
                email:
                  type: string
                  format: email
                  description: Customer email (optional)
                currency_code:
                  type: string
                  description: Currency code
                  example: "INR"
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '201':
          description: Cart created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /store/carts/{id}:
    get:
      summary: Get cart
      description: Get cart details
      tags:
        - Store - Carts
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: id
          in: path
          required: true
          description: Cart ID
          schema:
            type: string
      responses:
        '200':
          description: Cart retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # ONDC-Specific Store Endpoints
  /store/customers/register:
    post:
      summary: Register new customer
      description: Simple customer registration without complex authentication flows
      tags:
        - Store - Customers
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                  description: Customer email address
                  example: <EMAIL>
                first_name:
                  type: string
                  description: Customer first name
                  example: John
                last_name:
                  type: string
                  description: Customer last name
                  example: Doe
                phone:
                  type: string
                  description: Customer phone number
                  example: "+91-9876543210"
      responses:
        '201':
          description: Customer registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerRegistrationResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          description: Customer already exists
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Customer exists"
                  message:
                    type: string
                    example: "Customer with this email already exists"
                  customer:
                    $ref: '#/components/schemas/Customer'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /store/carts/{id}/line-items/{line_id}:
    post:
      summary: Update cart line item
      description: Update quantity or other properties of a cart line item
      tags:
        - Store - Carts
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: id
          in: path
          required: true
          description: Cart ID
          schema:
            type: string
        - name: line_id
          in: path
          required: true
          description: Line item ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: integer
                  minimum: 1
                  description: New quantity
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '200':
          description: Cart line item updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Remove cart line item
      description: Remove a line item from the cart
      tags:
        - Store - Carts
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: id
          in: path
          required: true
          description: Cart ID
          schema:
            type: string
        - name: line_id
          in: path
          required: true
          description: Line item ID
          schema:
            type: string
      responses:
        '200':
          description: Cart line item removed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Store Order Endpoints
  /store/orders:
    get:
      summary: List customer orders
      description: Get list of orders for the authenticated customer
      tags:
        - Store - Orders
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: limit
          in: query
          description: Number of orders to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of orders to skip
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: status
          in: query
          description: Filter by order status
          schema:
            type: string
            enum: [pending, completed, archived, canceled, requires_action]
        - name: payment_status
          in: query
          description: Filter by payment status
          schema:
            type: string
            enum: [not_paid, awaiting, captured, partially_refunded, refunded, canceled, requires_action]
        - name: fulfillment_status
          in: query
          description: Filter by fulfillment status
          schema:
            type: string
            enum: [not_fulfilled, partially_fulfilled, fulfilled, partially_shipped, shipped, partially_returned, returned, canceled, requires_action]
        - name: email
          in: query
          description: Filter by customer email
          schema:
            type: string
            format: email
        - name: created_at
          in: query
          description: Filter by creation date
          schema:
            type: object
            properties:
              lt:
                type: string
                format: date-time
              gt:
                type: string
                format: date-time
              gte:
                type: string
                format: date-time
              lte:
                type: string
                format: date-time
      responses:
        '200':
          description: Orders retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /store/orders/{id}:
    get:
      summary: Get order details
      description: Get detailed information for a specific order
      tags:
        - Store - Orders
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
        - name: expand
          in: query
          description: Comma-separated list of relations to expand
          schema:
            type: string
            example: "items,shipping_address,billing_address,payments,fulfillments"
      responses:
        '200':
          description: Order details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  order:
                    $ref: '#/components/schemas/Order'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Store Region Endpoints
  /store/regions:
    get:
      summary: List regions
      description: Get list of available regions for the store
      tags:
        - Store - Regions
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: limit
          in: query
          description: Number of regions to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of regions to skip
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: Regions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  regions:
                    type: array
                    items:
                      $ref: '#/components/schemas/Region'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /store/regions/{id}:
    get:
      summary: Get region details
      description: Get detailed information for a specific region
      tags:
        - Store - Regions
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: id
          in: path
          required: true
          description: Region ID
          schema:
            type: string
      responses:
        '200':
          description: Region details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  region:
                    $ref: '#/components/schemas/Region'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /store/carts/{id}/complete-cod:
    post:
      summary: Complete cart with Cash on Delivery
      description: Complete cart checkout using Cash on Delivery payment method (ONDC-specific)
      tags:
        - Store - Carts
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: id
          in: path
          required: true
          description: Cart ID
          schema:
            type: string
            example: cart_01JZA9Z1QEZQD1ENMJ20K32FJV
      responses:
        '200':
          description: Order created successfully with COD payment
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CODOrderResponse'
        '400':
          description: Invalid cart or empty cart
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Empty cart"
                  message:
                    type: string
                    example: "Cannot create order from empty cart"
        '404':
          description: Cart not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Cart not found"
                  message:
                    type: string
                    example: "Cart with ID cart_123 not found"
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for admin authentication
    publishableApiKey:
      type: apiKey
      in: header
      name: x-publishable-api-key
      description: Publishable API key for store endpoints

  parameters:
    PublishableApiKeyHeader:
      name: x-publishable-api-key
      in: header
      required: true
      description: Publishable API key for store endpoints
      schema:
        type: string
        example: 'pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b'

  responses:
    BadRequest:
      description: Bad request - invalid parameters or request body
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Unauthorized:
      description: Unauthorized - invalid or missing authentication
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error type or code
        message:
          type: string
          description: Human-readable error message
        details:
          type: string
          description: Additional error details (development only)
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
      required:
        - error
        - message

    User:
      type: object
      properties:
        id:
          type: string
          example: user_01JZ7RPY072WGWKTJ6Q2YE46V7
        email:
          type: string
          format: email
          example: <EMAIL>
        first_name:
          type: string
          example: John
        last_name:
          type: string
          example: Doe
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    Product:
      type: object
      properties:
        id:
          type: string
          example: prod_01JZ7RPY072WGWKTJ6Q2YE46V7
        title:
          type: string
          example: Premium Smartphone
        subtitle:
          type: string
          example: Latest model with advanced features
        description:
          type: string
          example: High-performance smartphone with excellent camera
        handle:
          type: string
          example: premium-smartphone
        status:
          type: string
          enum: [draft, proposed, published, rejected]
          example: published
        thumbnail:
          type: string
          example: https://example.com/image.jpg
        weight:
          type: number
          example: 200
        length:
          type: number
          example: 15
        height:
          type: number
          example: 0.8
        width:
          type: number
          example: 7
        origin_country:
          type: string
          example: IN
        material:
          type: string
          example: Aluminum
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true
        variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariant'
        categories:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategory'
        collections:
          type: array
          items:
            $ref: '#/components/schemas/ProductCollection'

    Customer:
      type: object
      properties:
        id:
          type: string
          example: cus_01JZ7RPY072WGWKTJ6Q2YE46V7
        email:
          type: string
          format: email
          example: <EMAIL>
        first_name:
          type: string
          example: John
        last_name:
          type: string
          example: Doe
        phone:
          type: string
          example: "+91-9876543210"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    Cart:
      type: object
      properties:
        id:
          type: string
          example: cart_01JZA9Z1QEZQD1ENMJ20K32FJV
        email:
          type: string
          format: email
          example: <EMAIL>
        currency_code:
          type: string
          example: INR
        region_id:
          type: string
          example: reg_01JZ7RPY072WGWKTJ6Q2YE46V7
        sales_channel_id:
          type: string
          example: sc_01JZ7RPY072WGWKTJ6Q2YE46V7
        total:
          type: number
          example: 94398
        subtotal:
          type: number
          example: 94398
        tax_total:
          type: number
          example: 0
        discount_total:
          type: number
          example: 0
        shipping_total:
          type: number
          example: 0
        gift_card_total:
          type: number
          example: 0
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true
        items:
          type: array
          items:
            $ref: '#/components/schemas/CartLineItem'
        shipping_address:
          $ref: '#/components/schemas/Address'
        billing_address:
          $ref: '#/components/schemas/Address'

    Address:
      type: object
      properties:
        id:
          type: string
          example: addr_01JZ7RPY072WGWKTJ6Q2YE46V7
        first_name:
          type: string
          example: John
        last_name:
          type: string
          example: Doe
        phone:
          type: string
          example: "+91-9876543210"
        company:
          type: string
          example: Acme Corp
        address_1:
          type: string
          example: 123 Main Street
        address_2:
          type: string
          example: Apt 4B
        city:
          type: string
          example: Mumbai
        country_code:
          type: string
          example: IN
        province:
          type: string
          example: Maharashtra
        postal_code:
          type: string
          example: "400001"
        metadata:
          type: object
          additionalProperties: true

    SalesChannel:
      type: object
      properties:
        id:
          type: string
          example: sc_01JZ7RPY072WGWKTJ6Q2YE46V7
        name:
          type: string
          example: Electronics Store
        description:
          type: string
          example: Premium electronics and gadgets
        is_disabled:
          type: boolean
          example: false
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    AnalyticsOverview:
      type: object
      properties:
        message:
          type: string
          example: "ONDC Seller Analytics API"
        version:
          type: string
          example: "1.0.0"
        description:
          type: string
          example: "Comprehensive analytics API system for multi-tenant e-commerce operations"
        endpoints:
          type: object
          additionalProperties:
            type: object
            properties:
              path:
                type: string
              method:
                type: string
              description:
                type: string
              parameters:
                type: array
                items:
                  type: string
        authentication:
          type: object
          properties:
            required:
              type: boolean
            type:
              type: string
            header:
              type: string
        features:
          type: array
          items:
            type: string

    DashboardAnalytics:
      type: object
      properties:
        stats:
          type: object
          properties:
            totalRevenue:
              type: number
              example: 1250000
            totalOrders:
              type: integer
              example: 150
            totalCustomers:
              type: integer
              example: 89
            totalProducts:
              type: integer
              example: 45
            averageOrderValue:
              type: number
              example: 8333.33
            conversionRate:
              type: number
              example: 3.2
            revenueGrowth:
              type: number
              example: 15.5
            orderGrowth:
              type: number
              example: 12.3
            customerGrowth:
              type: number
              example: 8.7

    SalesAnalytics:
      type: object
      properties:
        period:
          type: string
          example: "30d"
        salesData:
          type: array
          items:
            type: object
            properties:
              period:
                type: string
              revenue:
                type: number
              orders:
                type: integer
              units_sold:
                type: integer
              average_order_value:
                type: number
              growth_rate:
                type: number

    CustomerAnalytics:
      type: object
      properties:
        period:
          type: string
          example: "30d"
        customerSegments:
          type: array
          items:
            type: object
            properties:
              segment:
                type: string
              count:
                type: integer
              revenue:
                type: number
              average_order_value:
                type: number
              percentage:
                type: number

    ProductAnalytics:
      type: object
      properties:
        period:
          type: string
          example: "30d"
        products:
          type: array
          items:
            type: object
            properties:
              product_id:
                type: string
              title:
                type: string
              sku:
                type: string
              revenue:
                type: number
              units_sold:
                type: integer
              orders:
                type: integer
              views:
                type: integer
              conversion_rate:
                type: number
              stock_level:
                type: integer
              category:
                type: string
              growth_rate:
                type: number

    ProductVariant:
      type: object
      properties:
        id:
          type: string
          example: variant_01JZ7RPY072WGWKTJ6Q2YE46V7
        title:
          type: string
          example: 128GB Black
        sku:
          type: string
          example: PHONE-128GB-BLACK
        inventory_quantity:
          type: integer
          example: 50
        allow_backorder:
          type: boolean
          example: false
        manage_inventory:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    ProductCategory:
      type: object
      properties:
        id:
          type: string
          example: pcat_01JZ7RPY072WGWKTJ6Q2YE46V7
        name:
          type: string
          example: Electronics
        description:
          type: string
          example: Electronic devices and accessories
        handle:
          type: string
          example: electronics
        is_active:
          type: boolean
          example: true
        is_internal:
          type: boolean
          example: false
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    ProductCollection:
      type: object
      properties:
        id:
          type: string
          example: pcol_01JZ7RPY072WGWKTJ6Q2YE46V7
        title:
          type: string
          example: Premium Collection
        handle:
          type: string
          example: premium-collection
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    CartLineItem:
      type: object
      properties:
        id:
          type: string
          example: item_01JZ7RPY072WGWKTJ6Q2YE46V7
        cart_id:
          type: string
          example: cart_01JZA9Z1QEZQD1ENMJ20K32FJV
        variant_id:
          type: string
          example: variant_01JZ7RPY072WGWKTJ6Q2YE46V7
        product_id:
          type: string
          example: prod_01JZ7RPY072WGWKTJ6Q2YE46V7
        product_title:
          type: string
          example: Premium Smartphone
        product_description:
          type: string
          example: High-performance smartphone
        product_subtitle:
          type: string
          example: Latest model
        product_type:
          type: string
          example: Electronics
        product_collection:
          type: string
          example: Premium Collection
        product_handle:
          type: string
          example: premium-smartphone
        variant_sku:
          type: string
          example: PHONE-128GB-BLACK
        variant_barcode:
          type: string
          example: "1234567890123"
        variant_title:
          type: string
          example: 128GB Black
        title:
          type: string
          example: Premium Smartphone
        quantity:
          type: integer
          example: 2
        unit_price:
          type: number
          example: 47199
        total:
          type: number
          example: 94398
        original_total:
          type: number
          example: 94398
        original_tax_total:
          type: number
          example: 0
        tax_total:
          type: number
          example: 0
        discount_total:
          type: number
          example: 0
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    CustomerRegistrationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        customer:
          $ref: '#/components/schemas/Customer'
        message:
          type: string
          example: "Customer registered successfully"

    CODOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        order:
          type: object
          properties:
            id:
              type: string
              example: "order_01JZA9Z1QEZQD1ENMJ20K32FJV"
            display_id:
              type: string
              example: "1001"
            status:
              type: string
              example: "pending"
            payment_status:
              type: string
              example: "awaiting"
            payment_method:
              type: string
              example: "cash_on_delivery"
            total:
              type: number
              example: 94398
            currency_code:
              type: string
              example: "INR"
            created_at:
              type: string
              format: date-time
        message:
          type: string
          example: "Order created successfully with COD payment"

    Order:
      type: object
      properties:
        id:
          type: string
          example: order_01JZA9Z1QEZQD1ENMJ20K32FJV
        display_id:
          type: string
          example: "1001"
        status:
          type: string
          enum: [pending, completed, archived, canceled, requires_action]
          example: pending
        payment_status:
          type: string
          enum: [not_paid, awaiting, captured, partially_refunded, refunded, canceled, requires_action]
          example: awaiting
        fulfillment_status:
          type: string
          enum: [not_fulfilled, partially_fulfilled, fulfilled, partially_shipped, shipped, partially_returned, returned, canceled, requires_action]
          example: not_fulfilled
        email:
          type: string
          format: email
          example: <EMAIL>
        currency_code:
          type: string
          example: INR
        total:
          type: number
          example: 94398
        subtotal:
          type: number
          example: 94398
        tax_total:
          type: number
          example: 0
        discount_total:
          type: number
          example: 0
        shipping_total:
          type: number
          example: 0
        gift_card_total:
          type: number
          example: 0
        refunded_total:
          type: number
          example: 0
        region_id:
          type: string
          example: reg_01JZ7RPY072WGWKTJ6Q2YE46V7
        sales_channel_id:
          type: string
          example: sc_01JZ7RPY072WGWKTJ6Q2YE46V7
        customer_id:
          type: string
          example: cus_01JZ7RPY072WGWKTJ6Q2YE46V7
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        canceled_at:
          type: string
          format: date-time
          nullable: true
        metadata:
          type: object
          additionalProperties: true
        customer:
          $ref: '#/components/schemas/Customer'
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderLineItem'
        shipping_address:
          $ref: '#/components/schemas/Address'
        billing_address:
          $ref: '#/components/schemas/Address'
        payments:
          type: array
          items:
            $ref: '#/components/schemas/Payment'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'

    OrderLineItem:
      type: object
      properties:
        id:
          type: string
          example: item_01JZ7RPY072WGWKTJ6Q2YE46V7
        order_id:
          type: string
          example: order_01JZA9Z1QEZQD1ENMJ20K32FJV
        variant_id:
          type: string
          example: variant_01JZ7RPY072WGWKTJ6Q2YE46V7
        product_id:
          type: string
          example: prod_01JZ7RPY072WGWKTJ6Q2YE46V7
        product_title:
          type: string
          example: Premium Smartphone
        product_description:
          type: string
          example: High-performance smartphone
        product_subtitle:
          type: string
          example: Latest model
        product_type:
          type: string
          example: Electronics
        product_collection:
          type: string
          example: Premium Collection
        product_handle:
          type: string
          example: premium-smartphone
        variant_sku:
          type: string
          example: PHONE-128GB-BLACK
        variant_barcode:
          type: string
          example: "1234567890123"
        variant_title:
          type: string
          example: 128GB Black
        title:
          type: string
          example: Premium Smartphone
        quantity:
          type: integer
          example: 2
        fulfilled_quantity:
          type: integer
          example: 0
        returned_quantity:
          type: integer
          example: 0
        shipped_quantity:
          type: integer
          example: 0
        unit_price:
          type: number
          example: 47199
        total:
          type: number
          example: 94398
        original_total:
          type: number
          example: 94398
        original_tax_total:
          type: number
          example: 0
        tax_total:
          type: number
          example: 0
        discount_total:
          type: number
          example: 0
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    Payment:
      type: object
      properties:
        id:
          type: string
          example: pay_01JZ7RPY072WGWKTJ6Q2YE46V7
        amount:
          type: number
          example: 94398
        currency_code:
          type: string
          example: INR
        amount_authorized:
          type: number
          example: 94398
        amount_captured:
          type: number
          example: 0
        amount_refunded:
          type: number
          example: 0
        provider_id:
          type: string
          example: cash_on_delivery
        data:
          type: object
          additionalProperties: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        captured_at:
          type: string
          format: date-time
          nullable: true
        canceled_at:
          type: string
          format: date-time
          nullable: true
        metadata:
          type: object
          additionalProperties: true

    Fulfillment:
      type: object
      properties:
        id:
          type: string
          example: ful_01JZ7RPY072WGWKTJ6Q2YE46V7
        location_id:
          type: string
          example: sloc_01JZ7RPY072WGWKTJ6Q2YE46V7
        packed_at:
          type: string
          format: date-time
          nullable: true
        shipped_at:
          type: string
          format: date-time
          nullable: true
        delivered_at:
          type: string
          format: date-time
          nullable: true
        canceled_at:
          type: string
          format: date-time
          nullable: true
        data:
          type: object
          additionalProperties: true
        provider_id:
          type: string
          example: manual
        shipping_option_id:
          type: string
          example: so_01JZ7RPY072WGWKTJ6Q2YE46V7
        tracking_numbers:
          type: array
          items:
            type: string
          example: ["TRK123456789"]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true
        items:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              line_item_id:
                type: string
              quantity:
                type: integer

    InventoryItem:
      type: object
      properties:
        id:
          type: string
          example: iitem_01JZ7RPY072WGWKTJ6Q2YE46V7
        sku:
          type: string
          example: PHONE-128GB-BLACK
        origin_country:
          type: string
          example: IN
        mid_code:
          type: string
          example: MID123
        material:
          type: string
          example: Aluminum
        weight:
          type: number
          example: 200
        length:
          type: number
          example: 15
        height:
          type: number
          example: 0.8
        width:
          type: number
          example: 7
        requires_shipping:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true

    InventoryLevel:
      type: object
      properties:
        id:
          type: string
          example: ilev_01JZ7RPY072WGWKTJ6Q2YE46V7
        inventory_item_id:
          type: string
          example: iitem_01JZ7RPY072WGWKTJ6Q2YE46V7
        location_id:
          type: string
          example: sloc_01JZ7RPY072WGWKTJ6Q2YE46V7
        stocked_quantity:
          type: integer
          example: 100
        reserved_quantity:
          type: integer
          example: 5
        incoming_quantity:
          type: integer
          example: 50
        metadata:
          type: object
          additionalProperties: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    Region:
      type: object
      properties:
        id:
          type: string
          example: reg_01JZ7RPY072WGWKTJ6Q2YE46V7
        name:
          type: string
          example: India
        currency_code:
          type: string
          example: INR
        tax_rate:
          type: number
          example: 18.0
        tax_code:
          type: string
          example: GST
        gift_cards_taxable:
          type: boolean
          example: true
        automatic_taxes:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties: true
        countries:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              iso_2:
                type: string
                example: IN
              iso_3:
                type: string
                example: IND
              num_code:
                type: integer
                example: 356
              name:
                type: string
                example: INDIA
              display_name:
                type: string
                example: India
        payment_providers:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              is_enabled:
                type: boolean
        fulfillment_providers:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              is_enabled:
                type: boolean
