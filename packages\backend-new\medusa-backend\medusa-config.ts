import { loadEnv, defineConfig } from '@medusajs/framework/utils';

loadEnv(process.env.NODE_ENV || 'development', process.cwd());

export default defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || 'supersecret',
      cookieSecret: process.env.COOKIE_SECRET || 'supersecret',
    },
  },

  // ✅ REGISTER CORE MODULES HERE
  modules: {
    // Authentication module - required for user signup/login
    auth: {
      resolve: '@medusajs/medusa/auth',
      options: {
        providers: [
          {
            resolve: '@medusajs/medusa/auth-emailpass',
            id: 'emailpass',
            options: {},
          },
        ],
      },
    },
    // User module - required for user management
    user: {
      resolve: '@medusajs/medusa/user',
      options: {
        jwt_secret: process.env.JWT_SECRET || 'supersecret',
      },
    },
    cart: {
      resolve: '@medusajs/cart',
      options: {},
    },
    order: {
      resolve: '@medusajs/order',
      options: {},
    },
    customer: {
      resolve: '@medusajs/customer',
      options: {},
    },
    payment: {
      resolve: '@medusajs/payment',
      options: {},
    },
    inventory: {
      resolve: '@medusajs/inventory',
      options: {},
    },
    promotion: { resolve: '@medusajs/medusa/promotion' },
    analytics: {
      resolve: '@medusajs/medusa/analytics',
      options: {
        providers: [
          /* Logs events to the Medusa console – great for dev */
          { resolve: '@medusajs/medusa/analytics-local', id: 'local' },

          /* Switch to PostHog later:
          {
            resolve: "@medusajs/analytics-posthog",
            id: "posthog",
            options: {
              posthogEventsKey: process.env.POSTHOG_EVENTS_API_KEY,
              posthogHost:      process.env.POSTHOG_HOST
            }
          }
          */
        ],
      },
    },
  },

  // ✅ REGISTER PAYMENT PROVIDERS
  plugins: [
    {
      resolve: 'medusa-payment-manual',
      options: {
        // Cash on Delivery configuration
        name: 'Cash on Delivery',
        description: 'Pay with cash when your order is delivered',
        id: 'manual',
      },
    },
    {
      resolve: '@rsc-labs/medusa-store-analytics-v2',
      options: {},
    },
  ],
});
