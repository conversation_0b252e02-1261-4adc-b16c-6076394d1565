{"openapi": "3.0.3", "info": {"title": "ONDC Seller App - Medusa v2 Backend API", "description": "Comprehensive e-commerce backend API built with Medusa v2, providing complete endpoints and services for the ONDC Seller App ecosystem with multi-tenant support.\n\n## Features\n- Multi-tenant architecture with tenant isolation\n- Complete e-commerce functionality (products, orders, customers, carts)\n- ONDC integration ready\n- Analytics and reporting\n- Admin and Store APIs\n- Real-time order management\n\n## Authentication\n- Admin endpoints require JWT token obtained from `/auth/user/emailpass`\n- Store endpoints require publishable API key in `x-publishable-api-key` header\n- Customer endpoints require customer JWT token\n- Multi-tenant endpoints require `x-tenant-id` header\n\n## Base URLs\n- Admin API: `http://localhost:9000/admin`\n- Store API: `http://localhost:9000/store`\n- Auth API: `http://localhost:9000/auth`\n\n## Multi-Tenancy\nAll endpoints support multi-tenancy via the `x-tenant-id` header.\nAvailable tenants:\n- `tenant-electronics-001` - Electronics Store\n- `tenant-fashion-002` - Fashion Store\n- `tenant-books-003` - Books Store\n- `default` - Default Store", "version": "2.8.6", "contact": {"name": "ONDC Seller App Team", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:9000", "description": "Development server"}, {"url": "https://api.ondc-seller.com", "description": "Production server"}], "security": [{"BearerAuth": []}, {"PublishableApiKey": []}], "paths": {"/auth/user/emailpass": {"post": {"tags": ["Authentication"], "summary": "Admin user login", "description": "Authenticate admin user and receive JWT token", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "example": "supersecret"}}}}}}, "responses": {"200": {"description": "Successful authentication", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT token for admin API access", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/customer/emailpass": {"post": {"tags": ["Authentication"], "summary": "Customer login", "description": "Authenticate customer and receive JWT token", "security": [{"PublishableApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "example": "customerpassword"}}}}}}, "responses": {"200": {"description": "Successful authentication", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT token for customer API access"}}}}}}}}}, "/admin/users/me": {"get": {"tags": ["Admin Users"], "summary": "Get current admin user", "description": "Retrieve details of the currently authenticated admin user", "responses": {"200": {"description": "Current user details", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/AdminUser"}}}}}}}}}, "/admin/analytics/sales": {"get": {"tags": ["Admin - Analytics"], "summary": "Get sales analytics", "description": "Retrieve comprehensive sales analytics including revenue trends, top products, and order statistics", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"name": "from", "in": "query", "description": "Start date for analytics (ISO 8601 format)", "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "to", "in": "query", "description": "End date for analytics (ISO 8601 format)", "schema": {"type": "string", "format": "date", "example": "2024-01-31"}}, {"name": "interval", "in": "query", "description": "Data aggregation interval", "schema": {"type": "string", "enum": ["day", "week", "month"], "default": "day"}}], "responses": {"200": {"description": "Sales analytics data", "content": {"application/json": {"schema": {"type": "object", "properties": {"stats": {"type": "object", "properties": {"grossRevenue": {"type": "number", "description": "Total gross revenue"}, "netRevenue": {"type": "number", "description": "Net revenue after refunds"}, "revenueGrowth": {"type": "number", "description": "Revenue growth percentage"}, "orders": {"type": "integer", "description": "Total number of orders"}, "orderGrowth": {"type": "number", "description": "Order growth percentage"}, "unitsSold": {"type": "integer", "description": "Total units sold"}, "aov": {"type": "number", "description": "Average order value"}, "aovGrowth": {"type": "number", "description": "AOV growth percentage"}, "totalCustomers": {"type": "integer", "description": "Total number of customers"}}}, "revenueTrend": {"type": "array", "items": {"type": "object", "properties": {"bucket": {"type": "string", "description": "Time bucket (ISO date)"}, "gross": {"type": "number"}, "net": {"type": "number"}, "orders": {"type": "integer"}}}}, "topProducts": {"type": "array", "items": {"type": "object", "properties": {"product_id": {"type": "string"}, "title": {"type": "string"}, "units": {"type": "integer"}, "gross": {"type": "number"}}}}, "topOrders": {"type": "array", "items": {"type": "object", "properties": {"order_display_id": {"type": "string"}, "order_id": {"type": "string"}, "customer_name": {"type": "string"}, "customer_email": {"type": "string"}, "order_date": {"type": "string", "format": "date-time"}, "total_order_amount": {"type": "number"}, "order_status": {"type": "string"}}}}, "customerSplit": {"type": "object", "properties": {"new": {"type": "integer"}, "returning": {"type": "integer"}}}, "refundRate": {"type": "object", "properties": {"refunded": {"type": "number"}, "gross": {"type": "number"}, "rate": {"type": "number"}}}}}}}}}}}, "/admin/tenant": {"get": {"tags": ["Admin - Tenant"], "summary": "Get tenant information", "description": "Retrieve tenant configuration and settings", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"description": "Tenant information", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "tenant": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "domain": {"type": "string"}, "settings": {"type": "object", "properties": {"currency": {"type": "string"}, "timezone": {"type": "string"}, "features": {"type": "array", "items": {"type": "string"}}, "ondcConfig": {"type": "object", "properties": {"participantId": {"type": "string"}, "subscriberId": {"type": "string"}, "bppId": {"type": "string"}}}}}, "status": {"type": "string"}}}, "multi_tenancy": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "available_tenants": {"type": "array", "items": {"type": "string"}}}}}}}}}}}, "post": {"tags": ["Admin - Tenant"], "summary": "Update tenant configuration", "description": "Update tenant settings and configuration", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "settings": {"type": "object", "additionalProperties": true}}}}}}, "responses": {"200": {"description": "Tenant configuration updated", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "tenant_id": {"type": "string"}, "updated_config": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}, "/store/test-info": {"get": {"tags": ["Store"], "summary": "Get tenant-specific store information", "description": "Retrieve detailed tenant-specific store information including ONDC configuration", "security": [{"PublishableApiKey": []}], "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"description": "Tenant store information", "content": {"application/json": {"schema": {"type": "object", "properties": {"store": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "domain": {"type": "string"}, "currency": {"type": "string"}, "timezone": {"type": "string"}, "region": {"type": "string"}, "features": {"type": "array", "items": {"type": "string"}}, "branding": {"type": "object", "properties": {"primaryColor": {"type": "string"}, "logo": {"type": "string"}}}, "contact": {"type": "object", "properties": {"email": {"type": "string"}, "phone": {"type": "string"}, "address": {"type": "object"}}}, "ondcConfig": {"type": "object", "properties": {"participantId": {"type": "string"}, "subscriberId": {"type": "string"}, "bppId": {"type": "string"}, "domain": {"type": "string"}, "region": {"type": "string"}}}}}, "tenant_id": {"type": "string"}, "api_version": {"type": "string"}, "multi_tenant": {"type": "boolean"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}, "/store/orders/create": {"post": {"tags": ["Store - Orders"], "summary": "Create order from cart", "description": "Create a new order from an existing cart with shipping details and payment method", "security": [{"PublishableApiKey": []}], "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["cart_id", "email", "shipping_address"], "properties": {"cart_id": {"type": "string", "description": "ID of the cart to create order from", "example": "cart_01JZA9Z1QEZQD1ENMJ20K32FJV"}, "customer_id": {"type": "string", "description": "Customer ID (optional)"}, "email": {"type": "string", "format": "email", "description": "Customer email address", "example": "<EMAIL>"}, "shipping_address": {"$ref": "#/components/schemas/Address"}, "billing_address": {"$ref": "#/components/schemas/Address"}, "payment_method": {"type": "string", "enum": ["cod", "card", "upi"], "default": "cod", "description": "Payment method", "example": "cod"}}}}}}, "responses": {"201": {"description": "Order created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "order": {"$ref": "#/components/schemas/OrderResponse"}, "message": {"type": "string", "example": "Order created successfully"}, "tenant_id": {"type": "string", "example": "tenant-electronics-001"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "Bad request - validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "<PERSON><PERSON> not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/store/orders/simple": {"get": {"tags": ["Store - Orders"], "summary": "Get orders (simplified)", "description": "Retrieve orders for a customer by email (no authentication required for testing)", "security": [{"PublishableApiKey": []}], "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"name": "email", "in": "query", "required": true, "schema": {"type": "string", "format": "email"}, "description": "Customer email to filter orders", "example": "<EMAIL>"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 10}, "description": "Number of orders to return"}, {"name": "offset", "in": "query", "schema": {"type": "integer", "default": 0}, "description": "Number of orders to skip"}, {"name": "id", "in": "query", "schema": {"type": "string"}, "description": "Specific order ID to retrieve"}], "responses": {"200": {"description": "Orders retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "count": {"type": "integer", "example": 1}, "total": {"type": "integer", "example": 1}, "metadata": {"type": "object", "properties": {"tenantId": {"type": "string", "example": "tenant-electronics-001"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}, "404": {"description": "Order not found (when specific ID is requested)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["Store - Orders"], "summary": "Get single order by ID", "description": "Retrieve a specific order by ID", "security": [{"PublishableApiKey": []}], "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["order_id"], "properties": {"order_id": {"type": "string", "description": "Order ID to retrieve"}}}}}}, "responses": {"200": {"description": "Order retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "order": {"$ref": "#/components/schemas/Order"}}}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Order not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for admin authentication"}, "PublishableApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-publishable-api-key", "description": "Publishable API key for store endpoints"}}, "parameters": {"TenantId": {"name": "x-tenant-id", "in": "header", "description": "Tenant ID for multi-tenant operations", "schema": {"type": "string", "enum": ["tenant-electronics-001", "tenant-fashion-002", "tenant-books-003", "default"], "default": "default"}, "example": "tenant-electronics-001"}, "Limit": {"name": "limit", "in": "query", "description": "Number of items to return", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, "Offset": {"name": "offset", "in": "query", "description": "Number of items to skip", "schema": {"type": "integer", "minimum": 0, "default": 0}}}, "schemas": {"Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error type"}, "message": {"type": "string", "description": "Error message"}, "details": {"type": "string", "description": "Additional error details (development only)"}}}, "AdminUser": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string", "format": "email"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "Order": {"type": "object", "properties": {"id": {"type": "string"}, "display_id": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "completed", "archived", "canceled", "requires_action"]}, "fulfillment_status": {"type": "string", "enum": ["not_fulfilled", "partially_fulfilled", "fulfilled", "partially_shipped", "shipped", "partially_returned", "returned", "canceled", "requires_action"]}, "payment_status": {"type": "string", "enum": ["not_paid", "awaiting", "captured", "partially_refunded", "refunded", "canceled", "requires_action"]}, "currency_code": {"type": "string"}, "email": {"type": "string", "format": "email"}, "customer_id": {"type": "string"}, "total": {"type": "integer"}, "subtotal": {"type": "integer"}, "tax_total": {"type": "integer"}, "shipping_total": {"type": "integer"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/LineItem"}}, "shipping_address": {"$ref": "#/components/schemas/Address"}, "billing_address": {"$ref": "#/components/schemas/Address"}, "metadata": {"type": "object", "additionalProperties": true}}}, "OrderResponse": {"type": "object", "properties": {"id": {"type": "string", "example": "order_01JZAA3CY399FE355JDTHFDRKX"}, "display_id": {"type": "string", "example": "4"}, "status": {"type": "string", "example": "pending"}, "currency_code": {"type": "string", "example": "EUR"}, "email": {"type": "string", "example": "<EMAIL>"}, "total": {"type": "number", "example": 5102}, "subtotal": {"type": "number", "example": 3900}, "tax_total": {"type": "number", "example": 702}, "shipping_total": {"type": "number", "example": 500}, "payment_method": {"type": "string", "example": "cod"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/LineItem"}}, "shipping_address": {"$ref": "#/components/schemas/Address"}, "billing_address": {"$ref": "#/components/schemas/Address"}, "created_at": {"type": "string", "format": "date-time"}, "metadata": {"type": "object"}}}, "LineItem": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "quantity": {"type": "integer"}, "unit_price": {"type": "integer"}, "total": {"type": "integer"}, "variant_id": {"type": "string"}, "product_id": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": true}}}, "Address": {"type": "object", "required": ["first_name", "last_name", "address_1", "city", "postal_code", "country_code", "phone"], "properties": {"id": {"type": "string"}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "address_1": {"type": "string", "example": "123 Tech Street"}, "address_2": {"type": "string", "example": "Apt 4B"}, "city": {"type": "string", "example": "Mumbai"}, "postal_code": {"type": "string", "example": "400001"}, "province": {"type": "string", "example": "Maharashtra"}, "country_code": {"type": "string", "example": "IN"}, "phone": {"type": "string", "example": "+91-9876543210"}, "company": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": true}}}}}, "tags": [{"name": "Authentication", "description": "Authentication endpoints for admin and customer login"}, {"name": "Admin Users", "description": "Admin user management"}, {"name": "Admin - Analytics", "description": "Analytics and reporting (Admin)"}, {"name": "Admin - Tenant", "description": "Multi-tenant configuration (Admin)"}, {"name": "Store", "description": "Store information and configuration"}, {"name": "Store - Orders", "description": "Order placement and retrieval"}]}