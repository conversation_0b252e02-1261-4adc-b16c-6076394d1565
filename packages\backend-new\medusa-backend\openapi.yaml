openapi: 3.0.3
info:
  title: Medusa v2 Backend API for ONDC Seller App
  description: |
    Comprehensive e-commerce backend API built with Medusa v2, providing complete 
    endpoints and services for the ONDC Seller App ecosystem.
    
    ## Authentication
    - Admin endpoints require JWT token obtained from `/auth/user/emailpass`
    - Store endpoints require publishable API key in `x-publishable-api-key` header
    - Customer endpoints require customer JWT token
    
    ## Base URLs
    - Admin API: `http://localhost:9000/admin`
    - Store API: `http://localhost:9000/store`
    - Auth API: `http://localhost:9000/auth`
  version: 2.8.6
  contact:
    name: ONDC Seller App Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:9000
    description: Development server
  - url: https://api.ondc-seller.com
    description: Production server

security:
  - BearerAuth: []
  - PublishableApiKey: []

paths:
  # Authentication Endpoints
  /auth/user/emailpass:
    post:
      tags:
        - Authentication
      summary: Admin user login
      description: Authenticate admin user and receive JWT token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  example: supersecret
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT token for admin API access
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/customer/emailpass:
    post:
      tags:
        - Authentication
      summary: Customer login
      description: Authenticate customer and receive JWT token
      security:
        - PublishableApiKey: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  example: customerpassword
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT token for customer API access

  # Admin User Management
  /admin/users/me:
    get:
      tags:
        - Admin Users
      summary: Get current admin user
      description: Retrieve details of the currently authenticated admin user
      responses:
        '200':
          description: Current user details
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/AdminUser'

  /admin/users:
    get:
      tags:
        - Admin Users
      summary: List admin users
      description: Retrieve a list of all admin users
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Offset'
        - $ref: '#/components/parameters/Query'
      responses:
        '200':
          description: List of admin users
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/AdminUser'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer

    post:
      tags:
        - Admin Users
      summary: Create admin user
      description: Create a new admin user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                first_name:
                  type: string
                last_name:
                  type: string
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/AdminUser'

  # Customer Management
  /admin/customers:
    get:
      tags:
        - Customers
      summary: List customers
      description: Retrieve a list of customers
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Offset'
        - $ref: '#/components/parameters/Query'
        - name: email
          in: query
          description: Filter by customer email
          schema:
            type: string
            format: email
      responses:
        '200':
          description: List of customers
          content:
            application/json:
              schema:
                type: object
                properties:
                  customers:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer

    post:
      tags:
        - Customers
      summary: Create customer
      description: Create a new customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                first_name:
                  type: string
                last_name:
                  type: string
                phone:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customer:
                    $ref: '#/components/schemas/Customer'

  /admin/customers/{customer_id}:
    get:
      tags:
        - Customers
      summary: Get customer by ID
      description: Retrieve a specific customer by ID
      parameters:
        - name: customer_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Customer details
          content:
            application/json:
              schema:
                type: object
                properties:
                  customer:
                    $ref: '#/components/schemas/Customer'

    post:
      tags:
        - Customers
      summary: Update customer
      description: Update an existing customer
      parameters:
        - name: customer_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                last_name:
                  type: string
                phone:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customer:
                    $ref: '#/components/schemas/Customer'

    delete:
      tags:
        - Customers
      summary: Delete customer
      description: Delete a customer
      parameters:
        - name: customer_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Customer deleted successfully

  # Product Management
  /admin/products:
    get:
      tags:
        - Products
      summary: List products
      description: Retrieve a list of products
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Offset'
        - $ref: '#/components/parameters/Query'
        - name: status
          in: query
          description: Filter by product status
          schema:
            type: string
            enum: [draft, proposed, published, rejected]
        - name: category_id
          in: query
          description: Filter by category ID
          schema:
            type: string
      responses:
        '200':
          description: List of products
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer

    post:
      tags:
        - Products
      summary: Create product
      description: Create a new product
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProductRequest'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'

  /admin/products/{product_id}:
    get:
      tags:
        - Products
      summary: Get product by ID
      description: Retrieve a specific product by ID
      parameters:
        - name: product_id
          in: path
          required: true
          schema:
            type: string
        - name: fields
          in: query
          description: Specify which relations to include
          schema:
            type: string
            example: "*variants,*options,*categories"
      responses:
        '200':
          description: Product details
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'

    post:
      tags:
        - Products
      summary: Update product
      description: Update an existing product
      parameters:
        - name: product_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProductRequest'
      responses:
        '200':
          description: Product updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'

    delete:
      tags:
        - Products
      summary: Delete product
      description: Delete a product
      parameters:
        - name: product_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Product deleted successfully

  # Order Management
  /admin/orders:
    get:
      tags:
        - Orders
      summary: List orders
      description: Retrieve a list of orders
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Offset'
        - name: status
          in: query
          description: Filter by order status
          schema:
            type: string
            enum: [pending, completed, archived, canceled, requires_action]
        - name: payment_status
          in: query
          description: Filter by payment status
          schema:
            type: string
            enum: [not_paid, awaiting, captured, partially_refunded, refunded, canceled, requires_action]
        - name: customer_id
          in: query
          description: Filter by customer ID
          schema:
            type: string
      responses:
        '200':
          description: List of orders
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer

  /admin/orders/{order_id}:
    get:
      tags:
        - Orders
      summary: Get order by ID
      description: Retrieve a specific order by ID
      parameters:
        - name: order_id
          in: path
          required: true
          schema:
            type: string
        - name: fields
          in: query
          description: Specify which relations to include
          schema:
            type: string
            example: "*customer,*items,*shipping_address"
      responses:
        '200':
          description: Order details
          content:
            application/json:
              schema:
                type: object
                properties:
                  order:
                    $ref: '#/components/schemas/Order'

  # Store API Endpoints
  /store:
    get:
      tags:
        - Store
      summary: Get store information
      description: Retrieve store configuration and details
      security:
        - PublishableApiKey: []
      responses:
        '200':
          description: Store information
          content:
            application/json:
              schema:
                type: object
                properties:
                  store:
                    $ref: '#/components/schemas/Store'

  /store/products:
    get:
      tags:
        - Store
      summary: List products (Store)
      description: Retrieve a list of published products for customers
      security:
        - PublishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Offset'
        - name: category_id
          in: query
          description: Filter by category ID
          schema:
            type: string
        - name: region_id
          in: query
          description: Filter by region ID
          schema:
            type: string
      responses:
        '200':
          description: List of products
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer

  /store/products/{handle}:
    get:
      tags:
        - Store
      summary: Get product by handle
      description: Retrieve a specific product by its handle
      security:
        - PublishableApiKey: []
      parameters:
        - name: handle
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Product details
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'

  # Cart Management
  /store/carts:
    post:
      tags:
        - Cart
      summary: Create cart
      description: Create a new shopping cart
      security:
        - PublishableApiKey: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                region_id:
                  type: string
                  description: Region ID for the cart
                sales_channel_id:
                  type: string
                  description: Sales channel ID
      responses:
        '201':
          description: Cart created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'

  /store/carts/{cart_id}:
    get:
      tags:
        - Cart
      summary: Get cart
      description: Retrieve cart details
      security:
        - PublishableApiKey: []
      parameters:
        - name: cart_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Cart details
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'

  /store/carts/{cart_id}/line-items:
    post:
      tags:
        - Cart
      summary: Add item to cart
      description: Add a product variant to the cart
      security:
        - PublishableApiKey: []
      parameters:
        - name: cart_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - variant_id
                - quantity
              properties:
                variant_id:
                  type: string
                  description: Product variant ID
                quantity:
                  type: integer
                  minimum: 1
                  description: Quantity to add
                metadata:
                  type: object
                  additionalProperties: true
      responses:
        '200':
          description: Item added to cart
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'

  /store/carts/{cart_id}/complete:
    post:
      tags:
        - Cart
      summary: Complete cart (Place order)
      description: Complete the cart and create an order
      security:
        - PublishableApiKey: []
      parameters:
        - name: cart_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order placed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  order:
                    $ref: '#/components/schemas/Order'

  # Order Management
  /store/orders/create:
    post:
      tags:
        - Orders
      summary: Create order from cart
      description: Create a new order from an existing cart with shipping details and payment method
      security:
        - PublishableApiKey: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - cart_id
                - email
                - shipping_address
              properties:
                cart_id:
                  type: string
                  description: ID of the cart to create order from
                  example: "cart_01JZA9Z1QEZQD1ENMJ20K32FJV"
                email:
                  type: string
                  format: email
                  description: Customer email address
                  example: "<EMAIL>"
                shipping_address:
                  type: object
                  required:
                    - first_name
                    - last_name
                    - address_1
                    - city
                    - postal_code
                    - country_code
                    - phone
                  properties:
                    first_name:
                      type: string
                      example: "John"
                    last_name:
                      type: string
                      example: "Doe"
                    address_1:
                      type: string
                      example: "123 Tech Street"
                    address_2:
                      type: string
                      example: "Apt 4B"
                    city:
                      type: string
                      example: "Mumbai"
                    postal_code:
                      type: string
                      example: "400001"
                    country_code:
                      type: string
                      example: "IN"
                    phone:
                      type: string
                      example: "+91-9876543210"
                billing_address:
                  type: object
                  description: Billing address (optional, uses shipping address if not provided)
                payment_method:
                  type: string
                  enum: [cod, card, upi]
                  default: cod
                  description: Payment method
                  example: "cod"
      responses:
        '201':
          description: Order created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  order:
                    type: object
                    properties:
                      id:
                        type: string
                        example: "order_01JZAA3CY399FE355JDTHFDRKX"
                      display_id:
                        type: string
                        example: "4"
                      status:
                        type: string
                        example: "pending"
                      currency_code:
                        type: string
                        example: "EUR"
                      email:
                        type: string
                        example: "<EMAIL>"
                      total:
                        type: number
                        example: 5102
                      subtotal:
                        type: number
                        example: 3900
                      tax_total:
                        type: number
                        example: 702
                      shipping_total:
                        type: number
                        example: 500
                      payment_method:
                        type: string
                        example: "cod"
                      items:
                        type: array
                        items:
                          type: object
                      shipping_address:
                        type: object
                      billing_address:
                        type: object
                      created_at:
                        type: string
                        format: date-time
                      metadata:
                        type: object
                  message:
                    type: string
                    example: "Order created successfully"
                  tenant_id:
                    type: string
                    example: "tenant-electronics-001"
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Validation error"
                  message:
                    type: string
                    example: "cart_id is required"
        '404':
          description: Cart not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Cart not found"
                  message:
                    type: string
                    example: "Cart with ID cart_123 not found"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Order creation failed"
                  message:
                    type: string
                    example: "Failed to create order"

  /store/orders/simple:
    get:
      tags:
        - Orders
      summary: Get orders (simplified)
      description: Retrieve orders for a customer by email (no authentication required for testing)
      security:
        - PublishableApiKey: []
      parameters:
        - name: email
          in: query
          required: true
          schema:
            type: string
            format: email
          description: Customer email to filter orders
          example: "<EMAIL>"
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
          description: Number of orders to return
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
          description: Number of orders to skip
        - name: id
          in: query
          schema:
            type: string
          description: Specific order ID to retrieve
      responses:
        '200':
          description: Orders retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  count:
                    type: integer
                    example: 1
                  total:
                    type: integer
                    example: 1
                  metadata:
                    type: object
                    properties:
                      tenantId:
                        type: string
                        example: "tenant-electronics-001"
                      filtering:
                        type: object
                        properties:
                          totalBeforeFiltering:
                            type: integer
                          totalAfterFiltering:
                            type: integer
                          returned:
                            type: integer
                      timestamp:
                        type: string
                        format: date-time
        '404':
          description: Order not found (when specific ID is requested)
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Order not found"
                  message:
                    type: string
                    example: "Order with ID order_123 not found"

  # Customer Profile Management
  /store/customers/me:
    get:
      tags:
        - Customer Profile
      summary: Get customer profile
      description: Retrieve customer profile data by email or customer ID
      security:
        - PublishableApiKey: []
      parameters:
        - name: email
          in: query
          schema:
            type: string
            format: email
          description: Customer email address
          example: "<EMAIL>"
        - name: customer_id
          in: query
          schema:
            type: string
          description: Customer ID
      responses:
        '200':
          description: Customer profile retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customer:
                    $ref: '#/components/schemas/Customer'
                  tenant_id:
                    type: string
                  timestamp:
                    type: string
                    format: date-time
        '404':
          description: Customer not found
    put:
      tags:
        - Customer Profile
      summary: Update customer profile
      description: Update customer profile data
      security:
        - PublishableApiKey: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                customer_id:
                  type: string
                email:
                  type: string
                  format: email
                first_name:
                  type: string
                last_name:
                  type: string
                phone:
                  type: string
                metadata:
                  type: object
      responses:
        '200':
          description: Customer profile updated successfully
        '404':
          description: Customer not found
        '409':
          description: Email already exists

  # Admin Customer Management
  /admin/customers:
    get:
      tags:
        - Admin - Customers
      summary: List customers (Admin)
      description: Retrieve a list of customers with filtering and pagination
      security:
        - BearerAuth: []
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
        - name: q
          in: query
          schema:
            type: string
          description: Search query for email, first_name, or last_name
        - name: email
          in: query
          schema:
            type: string
          description: Filter by email
        - name: has_account
          in: query
          schema:
            type: boolean
          description: Filter by account status
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customers:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'
                  count:
                    type: integer
                  total:
                    type: integer
                  metadata:
                    type: object
    post:
      tags:
        - Admin - Customers
      summary: Create customer (Admin)
      description: Create a new customer
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - first_name
                - last_name
              properties:
                email:
                  type: string
                  format: email
                first_name:
                  type: string
                last_name:
                  type: string
                phone:
                  type: string
                has_account:
                  type: boolean
                  default: false
                metadata:
                  type: object
      responses:
        '201':
          description: Customer created successfully
        '409':
          description: Customer already exists

  /admin/customers/{id}:
    get:
      tags:
        - Admin - Customers
      summary: Get customer by ID (Admin)
      description: Retrieve a specific customer by ID
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Customer retrieved successfully
        '404':
          description: Customer not found
    put:
      tags:
        - Admin - Customers
      summary: Update customer (Admin)
      description: Update a specific customer
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                first_name:
                  type: string
                last_name:
                  type: string
                phone:
                  type: string
                has_account:
                  type: boolean
                metadata:
                  type: object
      responses:
        '200':
          description: Customer updated successfully
        '404':
          description: Customer not found
        '409':
          description: Email already exists
    delete:
      tags:
        - Admin - Customers
      summary: Delete customer (Admin)
      description: Soft delete a customer by updating metadata
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Customer deleted successfully
        '404':
          description: Customer not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from admin login
    PublishableApiKey:
      type: apiKey
      in: header
      name: x-publishable-api-key
      description: Publishable API key for store endpoints

  parameters:
    Limit:
      name: limit
      in: query
      description: Number of items to return
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 50
    Offset:
      name: offset
      in: query
      description: Number of items to skip
      schema:
        type: integer
        minimum: 0
        default: 0
    Query:
      name: q
      in: query
      description: Search query string
      schema:
        type: string

  schemas:
    Error:
      type: object
      properties:
        type:
          type: string
          description: Error type
          example: invalid_data
        message:
          type: string
          description: Error message
          example: Validation error occurred

    AdminUser:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the user
          example: user_01JZ4VVGEJX6KTQ3RZMB78Y5M1
        email:
          type: string
          format: email
          description: User's email address
          example: <EMAIL>
        first_name:
          type: string
          nullable: true
          description: User's first name
        last_name:
          type: string
          nullable: true
          description: User's last name
        created_at:
          type: string
          format: date-time
          description: User creation timestamp
        updated_at:
          type: string
          format: date-time
          description: User last update timestamp

    Customer:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the customer
          example: cus_01JZ4W260PPK0PW0R9EK9D3D5F
        email:
          type: string
          format: email
          description: Customer's email address
          example: <EMAIL>
        first_name:
          type: string
          nullable: true
          description: Customer's first name
        last_name:
          type: string
          nullable: true
          description: Customer's last name
        phone:
          type: string
          nullable: true
          description: Customer's phone number
        has_account:
          type: boolean
          description: Whether customer has an account
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata
        created_at:
          type: string
          format: date-time
          description: Customer creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Customer last update timestamp

    Product:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the product
          example: prod_01234567890
        title:
          type: string
          description: Product title
          example: ONDC Sample Product
        handle:
          type: string
          description: Product handle (URL slug)
          example: ondc-sample-product
        description:
          type: string
          nullable: true
          description: Product description
        status:
          type: string
          enum: [draft, proposed, published, rejected]
          description: Product status
        thumbnail:
          type: string
          nullable: true
          description: Product thumbnail URL
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata
        variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariant'
          description: Product variants
        options:
          type: array
          items:
            $ref: '#/components/schemas/ProductOption'
          description: Product options
        categories:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategory'
          description: Product categories
        created_at:
          type: string
          format: date-time
          description: Product creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Product last update timestamp

    ProductVariant:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the variant
        title:
          type: string
          description: Variant title
        sku:
          type: string
          nullable: true
          description: Stock keeping unit
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
          description: Variant prices
        options:
          type: object
          additionalProperties: true
          description: Variant option values
        inventory_quantity:
          type: integer
          description: Available inventory quantity
        manage_inventory:
          type: boolean
          description: Whether inventory is managed

    ProductOption:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the option
        title:
          type: string
          description: Option title
          example: Size
        values:
          type: array
          items:
            type: string
          description: Available option values
          example: ["S", "M", "L", "XL"]

    ProductCategory:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the category
        name:
          type: string
          description: Category name
        handle:
          type: string
          description: Category handle
        description:
          type: string
          nullable: true
          description: Category description

    Price:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the price
        amount:
          type: integer
          description: Price amount in smallest currency unit
          example: 2999
        currency_code:
          type: string
          description: Currency code
          example: INR

    Order:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the order
          example: order_01234567890
        status:
          type: string
          enum: [pending, completed, archived, canceled, requires_action]
          description: Order status
        payment_status:
          type: string
          enum: [not_paid, awaiting, captured, partially_refunded, refunded, canceled, requires_action]
          description: Payment status
        fulfillment_status:
          type: string
          enum: [not_fulfilled, partially_fulfilled, fulfilled, partially_shipped, shipped, partially_returned, returned, canceled, requires_action]
          description: Fulfillment status
        customer_id:
          type: string
          nullable: true
          description: Customer ID
        email:
          type: string
          format: email
          description: Order email
        total:
          type: integer
          description: Order total amount
        subtotal:
          type: integer
          description: Order subtotal amount
        tax_total:
          type: integer
          description: Total tax amount
        shipping_total:
          type: integer
          description: Total shipping amount
        items:
          type: array
          items:
            $ref: '#/components/schemas/LineItem'
          description: Order items
        shipping_address:
          $ref: '#/components/schemas/Address'
        billing_address:
          $ref: '#/components/schemas/Address'
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata
        created_at:
          type: string
          format: date-time
          description: Order creation timestamp

    LineItem:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the line item
        title:
          type: string
          description: Item title
        description:
          type: string
          nullable: true
          description: Item description
        thumbnail:
          type: string
          nullable: true
          description: Item thumbnail URL
        quantity:
          type: integer
          description: Item quantity
        unit_price:
          type: integer
          description: Unit price
        total:
          type: integer
          description: Line item total
        variant_id:
          type: string
          description: Product variant ID
        product_id:
          type: string
          description: Product ID
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata

    Cart:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the cart
          example: cart_01234567890
        customer_id:
          type: string
          nullable: true
          description: Customer ID
        region_id:
          type: string
          description: Region ID
        sales_channel_id:
          type: string
          description: Sales channel ID
        items:
          type: array
          items:
            $ref: '#/components/schemas/LineItem'
          description: Cart items
        total:
          type: integer
          description: Cart total amount
        subtotal:
          type: integer
          description: Cart subtotal amount
        tax_total:
          type: integer
          description: Total tax amount
        shipping_total:
          type: integer
          description: Total shipping amount
        shipping_address:
          $ref: '#/components/schemas/Address'
        billing_address:
          $ref: '#/components/schemas/Address'
        created_at:
          type: string
          format: date-time
          description: Cart creation timestamp

    Address:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the address
        first_name:
          type: string
          description: First name
        last_name:
          type: string
          description: Last name
        company:
          type: string
          nullable: true
          description: Company name
        address_1:
          type: string
          description: Address line 1
        address_2:
          type: string
          nullable: true
          description: Address line 2
        city:
          type: string
          description: City
        postal_code:
          type: string
          description: Postal code
        province:
          type: string
          nullable: true
          description: Province/State
        country_code:
          type: string
          description: Country code
          example: IN
        phone:
          type: string
          nullable: true
          description: Phone number

    Store:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the store
        name:
          type: string
          description: Store name
          example: ONDC Store
        default_currency_code:
          type: string
          description: Default currency code
          example: INR
        default_sales_channel_id:
          type: string
          description: Default sales channel ID
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata

    CreateProductRequest:
      type: object
      required:
        - title
      properties:
        title:
          type: string
          description: Product title
        handle:
          type: string
          description: Product handle (auto-generated if not provided)
        description:
          type: string
          description: Product description
        status:
          type: string
          enum: [draft, proposed, published, rejected]
          default: draft
        thumbnail:
          type: string
          description: Product thumbnail URL
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata
        options:
          type: array
          items:
            type: object
            properties:
              title:
                type: string
              values:
                type: array
                items:
                  type: string
        variants:
          type: array
          items:
            type: object
            properties:
              title:
                type: string
              prices:
                type: array
                items:
                  type: object
                  properties:
                    amount:
                      type: integer
                    currency_code:
                      type: string
              options:
                type: object
                additionalProperties: true
              inventory_quantity:
                type: integer
              manage_inventory:
                type: boolean

    UpdateProductRequest:
      type: object
      properties:
        title:
          type: string
          description: Product title
        description:
          type: string
          description: Product description
        status:
          type: string
          enum: [draft, proposed, published, rejected]
        thumbnail:
          type: string
          description: Product thumbnail URL
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata

tags:
  - name: Authentication
    description: Authentication endpoints for admin and customer login
  - name: Admin Users
    description: Admin user management endpoints
  - name: Customers
    description: Customer management endpoints
  - name: Products
    description: Product management endpoints
  - name: Orders
    description: Order management endpoints
  - name: Store
    description: Store-facing endpoints for customers
  - name: Cart
    description: Shopping cart management endpoints
