{"info": {"name": "03 - Product Management", "description": "Product CRUD operations with multi-tenant support", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Check if auth token exists", "if (!pm.environment.get('auth_token')) {", "    console.log('Warning: No auth token found. Please run authentication first.');", "}"]}}], "variable": [], "item": [{"name": "List All Products", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has products array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('products');", "    pm.expect(jsonData.products).to.be.an('array');", "});", "", "pm.test(\"Products have tenant_id\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.products.length > 0) {", "        pm.expect(jsonData.products[0]).to.have.property('tenant_id');", "    }", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/products", "host": ["{{base_url}}"], "path": ["admin", "products"]}, "description": "Get all products (shows tenant isolation in database)"}}, {"name": "Get Product by ID", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has product data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('product');", "    pm.expect(jsonData.product).to.have.property('id');", "    pm.expect(jsonData.product.id).to.eql(pm.environment.get('product_id'));", "});", "", "pm.test(\"Product has correct tenant\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.product).to.have.property('tenant_id');", "    pm.expect(jsonData.product.tenant_id).to.eql(pm.environment.get('tenant_id'));", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["admin", "products", "{{product_id}}"]}, "description": "Get specific product by ID with tenant validation"}}, {"name": "Create New Product", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test(\"Product created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('product');", "    pm.expect(jsonData.product).to.have.property('id');", "    ", "    // Save product ID for future requests", "    pm.environment.set('new_product_id', jsonData.product.id);", "});", "", "pm.test(\"Product has correct tenant\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.product).to.have.property('tenant_id');", "    pm.expect(jsonData.product.tenant_id).to.eql(pm.environment.get('tenant_id'));", "});"]}}], "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Product - {{tenant_name}}\",\n  \"handle\": \"test-product-{{$randomInt}}\",\n  \"description\": \"A test product created via Postman for {{tenant_name}}\",\n  \"status\": \"published\",\n  \"weight\": 500,\n  \"length\": 20,\n  \"height\": 15,\n  \"width\": 10,\n  \"hs_code\": \"1234.56.78\",\n  \"origin_country\": \"{{region}}\",\n  \"material\": \"Test Material\",\n  \"thumbnail\": \"/images/test-product.jpg\"\n}"}, "url": {"raw": "{{base_url}}/admin/products", "host": ["{{base_url}}"], "path": ["admin", "products"]}, "description": "Create a new product with tenant context"}}, {"name": "Update Product", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Product updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('product');", "    pm.expect(jsonData.product).to.have.property('description');", "    pm.expect(jsonData.product.description).to.include('Updated');", "});"]}}], "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Updated product description for {{tenant_name}} - {{$timestamp}}\",\n  \"status\": \"published\"\n}"}, "url": {"raw": "{{base_url}}/admin/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["admin", "products", "{{product_id}}"]}, "description": "Update existing product with tenant validation"}}, {"name": "Search Products by Title", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Search results returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('products');", "    pm.expect(jsonData.products).to.be.an('array');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/products?q=iPhone", "host": ["{{base_url}}"], "path": ["admin", "products"], "query": [{"key": "q", "value": "iPhone"}]}, "description": "Search products by title with tenant filtering"}}, {"name": "Get Products with Pagination", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Pagination data present\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('count');", "    pm.expect(jsonData).to.have.property('offset');", "    pm.expect(jsonData).to.have.property('limit');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/products?limit=5&offset=0", "host": ["{{base_url}}"], "path": ["admin", "products"], "query": [{"key": "limit", "value": "5"}, {"key": "offset", "value": "0"}]}, "description": "Get products with pagination parameters"}}]}