{"info": {"name": "06 - Store API", "description": "Customer-facing store endpoints with multi-tenant support", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "<PERSON><PERSON><PERSON>"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Store API typically doesn't require authentication for browsing"]}}], "variable": [], "item": [{"name": "Get Store Information", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has store data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('store');", "    pm.expect(jsonData.store).to.have.property('name');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/store", "host": ["{{base_url}}"], "path": ["store"]}, "description": "Get store information with tenant context"}}, {"name": "Browse Products", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has products array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('products');", "    pm.expect(jsonData.products).to.be.an('array');", "});", "", "pm.test(\"Products are published\", function () {", "    var jsonData = pm.response.json();", "    jsonData.products.forEach(function(product) {", "        pm.expect(product.status).to.eql('published');", "    });", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/store/products", "host": ["{{base_url}}"], "path": ["store", "products"]}, "description": "Browse published products in store with tenant filtering"}}, {"name": "Get Product by Handle", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has product data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('product');", "    pm.expect(jsonData.product).to.have.property('handle');", "    pm.expect(jsonData.product.handle).to.eql(pm.environment.get('product_handle'));", "});", "", "pm.test(\"Product is published\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.product.status).to.eql('published');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/store/products/{{product_handle}}", "host": ["{{base_url}}"], "path": ["store", "products", "{{product_handle}}"]}, "description": "Get product details by handle with tenant context"}}, {"name": "Search Products", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Search results returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('products');", "    pm.expect(jsonData.products).to.be.an('array');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/store/products?q=phone", "host": ["{{base_url}}"], "path": ["store", "products"], "query": [{"key": "q", "value": "phone"}]}, "description": "Search products in store with tenant filtering"}}, {"name": "Create <PERSON><PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test(\"<PERSON><PERSON> created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('cart');", "    pm.expect(jsonData.cart).to.have.property('id');", "    ", "    // Save cart ID for future requests", "    pm.environment.set('cart_id', jsonData.cart.id);", "});"]}}], "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"currency_code\": \"{{currency}}\"\n}"}, "url": {"raw": "{{base_url}}/store/carts", "host": ["{{base_url}}"], "path": ["store", "carts"]}, "description": "Create a new shopping cart with tenant context"}}, {"name": "Add Item to Cart", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Item added to cart\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('cart');", "    pm.expect(jsonData.cart).to.have.property('items');", "    pm.expect(jsonData.cart.items.length).to.be.greaterThan(0);", "});"]}}], "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"variant_id\": \"variant_01H1VDJHYRXF2V93KXHH4HEADY\",\n  \"quantity\": 1\n}"}, "url": {"raw": "{{base_url}}/store/carts/{{cart_id}}/line-items", "host": ["{{base_url}}"], "path": ["store", "carts", "{{cart_id}}", "line-items"]}, "description": "Add product variant to cart with tenant validation"}}, {"name": "Get Cart", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Cart data returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('cart');", "    pm.expect(jsonData.cart).to.have.property('id');", "    pm.expect(jsonData.cart.id).to.eql(pm.environment.get('cart_id'));", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/store/carts/{{cart_id}}", "host": ["{{base_url}}"], "path": ["store", "carts", "{{cart_id}}"]}, "description": "Get cart details with tenant context"}}, {"name": "Get Regions", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Regions returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('regions');", "    pm.expect(jsonData.regions).to.be.an('array');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/store/regions", "host": ["{{base_url}}"], "path": ["store", "regions"]}, "description": "Get available regions for store with tenant context"}}]}