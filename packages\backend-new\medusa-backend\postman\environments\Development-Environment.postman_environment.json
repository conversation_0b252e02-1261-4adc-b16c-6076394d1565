{"id": "development-env", "name": "Development Environment", "values": [{"key": "base_url", "value": "http://localhost:9000", "type": "default", "enabled": true}, {"key": "tenant_id", "value": "default", "type": "default", "enabled": true}, {"key": "tenant_name", "value": "Default Store", "type": "default", "enabled": true}, {"key": "tenant_domain", "value": "localhost", "type": "default", "enabled": true}, {"key": "admin_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "admin_password", "value": "supersecret", "type": "secret", "enabled": true}, {"key": "auth_token", "value": "", "type": "secret", "enabled": true}, {"key": "customer_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "customer_id", "value": "", "type": "default", "enabled": true}, {"key": "product_id", "value": "", "type": "default", "enabled": true}, {"key": "product_handle", "value": "", "type": "default", "enabled": true}, {"key": "category_id", "value": "", "type": "default", "enabled": true}, {"key": "order_id", "value": "", "type": "default", "enabled": true}, {"key": "cart_id", "value": "", "type": "default", "enabled": true}, {"key": "currency", "value": "USD", "type": "default", "enabled": true}, {"key": "region", "value": "US", "type": "default", "enabled": true}, {"key": "timezone", "value": "America/New_York", "type": "default", "enabled": true}, {"key": "api_version", "value": "v2", "type": "default", "enabled": true}, {"key": "content_type", "value": "application/json", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-02T08:30:00.000Z", "_postman_exported_using": "Postman/10.20.0"}