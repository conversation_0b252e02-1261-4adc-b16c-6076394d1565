#!/usr/bin/env node

/**
 * Seed Core Data for Medusa v2 E-commerce Functionality
 * This script populates the essential configuration data needed for
 * cart, checkout, and order operations to work properly.
 */

const { Client } = require('pg');
require('dotenv').config();

const client = new Client({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/medusa_backend'
});

async function seedCoreData() {
  try {
    await client.connect();
    console.log('🔗 Connected to database');

    // Check if we already have the basic configuration
    const existingRegion = await client.query('SELECT id FROM region LIMIT 1');
    if (existingRegion.rows.length > 0) {
      console.log('✅ Core data already exists, skipping seeding');
      return;
    }

    console.log('🌱 Starting core data seeding...');

    // Use Medusa CLI approach - create a minimal region first
    console.log('🌍 Creating default region...');
    await client.query(`
      INSERT INTO region (id, name, currency_code, created_at, updated_at)
      VALUES ('reg_01', 'Default Region', 'usd', NOW(), NOW())
      ON CONFLICT (id) DO NOTHING;
    `);

    // Create a simple store
    console.log('🏪 Creating default store...');
    await client.query(`
      INSERT INTO store (id, name, default_currency_code, created_at, updated_at)
      VALUES ('store_01', 'Default Store', 'usd', NOW(), NOW())
      ON CONFLICT (id) DO NOTHING;
    `);

    console.log('🎉 Basic core data seeding completed!');
    console.log('✅ Created: Default region and store');
    console.log('🚀 Cart APIs should now work with basic configuration!');



  } catch (error) {
    console.error('❌ Error seeding core data:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the seeding
if (require.main === module) {
  seedCoreData()
    .then(() => {
      console.log('✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedCoreData };
