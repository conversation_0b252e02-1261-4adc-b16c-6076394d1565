#!/usr/bin/env node

/**
 * Setup Inventory Configuration for Medusa v2
 * This script creates stock locations, associates them with sales channels,
 * and sets up inventory items for products to enable cart functionality.
 */

const { Client } = require('pg');
require('dotenv').config();

const client = new Client({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/medusa_backend'
});

async function setupInventory() {
  try {
    await client.connect();
    console.log('🔗 Connected to database');

    console.log('🏭 Setting up inventory configuration...');

    // Step 1: Create a default stock location
    console.log('📍 Creating default stock location...');
    const stockLocationResult = await client.query(`
      INSERT INTO stock_location (id, name, created_at, updated_at)
      VALUES ('sloc_01JZ85WAREHOUSE', 'Main Warehouse', NOW(), NOW())
      ON CONFLICT (id) DO NOTHING
      RETURNING id;
    `);

    const stockLocationId = stockLocationResult.rows[0]?.id || 'sloc_01JZ85WAREHOUSE';
    console.log(`✅ Stock location created: ${stockLocationId}`);

    // Step 2: Get the sales channel ID
    const salesChannelResult = await client.query('SELECT id FROM sales_channel LIMIT 1');
    if (salesChannelResult.rows.length === 0) {
      throw new Error('No sales channel found. Please ensure the backend is properly seeded.');
    }
    const salesChannelId = salesChannelResult.rows[0].id;
    console.log(`📺 Found sales channel: ${salesChannelId}`);

    // Step 3: Associate stock location with sales channel
    console.log('🔗 Associating stock location with sales channel...');
    const relationshipId = `scsl_${Date.now()}`;
    await client.query(`
      INSERT INTO sales_channel_stock_location (id, sales_channel_id, stock_location_id, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
      ON CONFLICT (sales_channel_id, stock_location_id) DO NOTHING;
    `, [relationshipId, salesChannelId, stockLocationId]);

    console.log('✅ Sales channel associated with stock location');

    // Step 4: Get all product variants
    const variantsResult = await client.query('SELECT id, sku FROM product_variant');
    console.log(`📦 Found ${variantsResult.rows.length} product variants`);

    // Step 5: Create inventory items for each variant
    for (const variant of variantsResult.rows) {
      const inventoryItemId = `inv_${variant.id.slice(8)}`;

      try {
        // Check if inventory item already exists
        const existingItem = await client.query('SELECT id FROM inventory_item WHERE id = $1', [inventoryItemId]);
        if (existingItem.rows.length === 0) {
          // Create inventory item
          await client.query(`
            INSERT INTO inventory_item (id, sku, created_at, updated_at)
            VALUES ($1, $2, NOW(), NOW());
          `, [inventoryItemId, variant.sku || variant.id]);
        }

        // Check if variant-inventory link already exists
        const existingLink = await client.query('SELECT id FROM product_variant_inventory_item WHERE variant_id = $1 AND inventory_item_id = $2', [variant.id, inventoryItemId]);
        if (existingLink.rows.length === 0) {
          // Link inventory item to product variant
          const linkId = `pvii_${variant.id.slice(8)}_${inventoryItemId.slice(4)}`;
          await client.query(`
            INSERT INTO product_variant_inventory_item (id, variant_id, inventory_item_id, required_quantity, created_at, updated_at)
            VALUES ($1, $2, $3, 1, NOW(), NOW());
          `, [linkId, variant.id, inventoryItemId]);
        }

        // Check if inventory level already exists
        const existingLevel = await client.query('SELECT inventory_item_id FROM inventory_level WHERE inventory_item_id = $1 AND location_id = $2', [inventoryItemId, stockLocationId]);
        if (existingLevel.rows.length === 0) {
          // Create inventory level (stock quantity)
          const levelId = `invlvl_${inventoryItemId.slice(4)}_${stockLocationId.slice(5)}`;
          await client.query(`
            INSERT INTO inventory_level (id, inventory_item_id, location_id, stocked_quantity, reserved_quantity, incoming_quantity, created_at, updated_at)
            VALUES ($1, $2, $3, 100, 0, 0, NOW(), NOW());
          `, [levelId, inventoryItemId, stockLocationId]);
        }

        console.log(`✅ Inventory set up for variant: ${variant.id} (SKU: ${variant.sku || 'N/A'})`);
      } catch (error) {
        console.log(`⚠️ Skipping variant ${variant.id}: ${error.message}`);
      }
    }

    console.log('🎉 Inventory setup completed!');
    console.log('✅ Created stock location, associated with sales channel, and set up inventory for all variants');
    console.log('🛒 Cart functionality should now work without inventory errors!');

  } catch (error) {
    console.error('❌ Error setting up inventory:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the setup
if (require.main === module) {
  setupInventory()
    .then(() => {
      console.log('✅ Inventory setup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Inventory setup failed:', error);
      process.exit(1);
    });
}

module.exports = { setupInventory };
