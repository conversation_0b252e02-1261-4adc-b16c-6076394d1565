import { MedusaRequest, MedusaResponse } from '@medusajs/medusa';
import { z } from 'zod';

// Query parameter validation schema
const DashboardQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
  sales_channel_id: z.string().optional(),
  tenant_id: z.string().optional(),
  currency: z.string().default('INR'),
});

// Response interfaces
interface DashboardOverview {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  averageOrderValue: number;
  conversionRate: number;
  revenueGrowth: number;
  orderGrowth: number;
  customerGrowth: number;
}

interface SalesTrend {
  date: string;
  revenue: number;
  orders: number;
  customers: number;
}

interface TopProduct {
  productId: string;
  title: string;
  sku: string;
  revenue: number;
  units: number;
  stock: number;
  gross: number;
}

interface RecentOrder {
  order_id: string;
  order_display_id: string;
  customer_name: string;
  customer_email: string;
  total_order_amount: number;
  order_status: string;
  created_at: string;
}

interface DashboardAnalytics {
  stats: DashboardOverview;
  revenueTrend: SalesTrend[];
  topProducts: TopProduct[];
  topOrders: RecentOrder[];
  refundRate: Array<{ name: string; value: number; color: string }>;
  customerSplit: Array<{ segment: string; count: number; percentage: number }>;
}

/**
 * GET /admin/analytics/dashboard
 *
 * Comprehensive dashboard analytics endpoint that provides:
 * - Overview statistics (revenue, orders, customers, products)
 * - Sales trends over time
 * - Top performing products
 * - Recent orders
 * - Refund rate analysis
 * - Customer segmentation
 */
export async function GET(req: MedusaRequest, res: MedusaResponse): Promise<void> {
  try {
    console.log('🔍 Dashboard API called with query:', req.query);

    // Validate query parameters
    const query = DashboardQuerySchema.parse(req.query);
    const { period, sales_channel_id, tenant_id, currency } = query;

    console.log('✅ Query validation passed:', query);

    // Get services using proper Medusa v2 service resolution
    const orderService = req.scope.resolve('order');
    const productService = req.scope.resolve('product');
    const customerService = req.scope.resolve('customer');

    // Calculate date range based on period
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    // Build filters for multi-tenant support (Medusa v2 format)
    const filters: any = {};

    // Note: Medusa v2 might not support custom date filtering in the same way
    // For now, we'll fetch all orders and filter in memory
    if (tenant_id) {
      filters.tenant_id = tenant_id;
    }

    if (sales_channel_id) {
      filters.sales_channel_id = sales_channel_id;
    }

    // Fetch orders data using Medusa v2 API
    const ordersResult = await orderService.listAndCountOrders(filters, {
      relations: ['items', 'shipping_address', 'billing_address'],
      order: { created_at: 'DESC' },
    });
    const allOrders = ordersResult[0] || [];

    // Filter orders by date range (client-side filtering)
    const orders = allOrders.filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= startDate && orderDate <= endDate;
    });

    // Fetch products data using Medusa v2 API
    const productsResult = await productService.listAndCountProducts(
      tenant_id ? { tenant_id } : {},
      { relations: ['variants', 'categories', 'images'] }
    );
    const products = productsResult[0] || [];

    // For customers, we'll use a simplified approach since the API is different
    // In a real implementation, you'd need to query the customer module properly
    const customers = [];

    // Calculate overview statistics
    const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
    const totalOrders = orders.length;
    const totalCustomers = customers.length;
    const totalProducts = products.length;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate growth rates (comparing with previous period)
    const previousStartDate = new Date(startDate);
    const previousEndDate = new Date(startDate);
    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    previousStartDate.setDate(previousStartDate.getDate() - periodDays);

    // Filter previous period orders (client-side filtering)
    const previousOrders = allOrders.filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= previousStartDate && orderDate <= previousEndDate;
    });

    const previousRevenue = previousOrders.reduce((sum, order) => sum + (order.total || 0), 0);
    const revenueGrowth =
      previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const orderGrowth =
      previousOrders.length > 0
        ? ((totalOrders - previousOrders.length) / previousOrders.length) * 100
        : 0;

    // Generate sales trend data
    const revenueTrend: SalesTrend[] = [];
    const days = Math.min(periodDays, 30); // Limit to 30 data points for performance

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStart = new Date(date.setHours(0, 0, 0, 0));
      const dayEnd = new Date(date.setHours(23, 59, 59, 999));

      const dayOrders = orders.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= dayStart && orderDate <= dayEnd;
      });

      revenueTrend.push({
        date: dayStart.toISOString().split('T')[0],
        revenue: dayOrders.reduce((sum, order) => sum + (order.total || 0), 0),
        orders: dayOrders.length,
        customers: new Set(dayOrders.map(order => order.customer_id)).size,
      });
    }

    // Calculate top products
    const productSales = new Map<string, { revenue: number; units: number; product: any }>();

    orders.forEach(order => {
      order.items?.forEach(item => {
        const productId = item.variant?.product_id;
        if (productId) {
          const existing = productSales.get(productId) || {
            revenue: 0,
            units: 0,
            product: item.variant.product,
          };
          existing.revenue += (item.unit_price || 0) * (item.quantity || 0);
          existing.units += item.quantity || 0;
          productSales.set(productId, existing);
        }
      });
    });

    const topProducts: TopProduct[] = Array.from(productSales.entries())
      .sort((a, b) => b[1].revenue - a[1].revenue)
      .slice(0, 10)
      .map(([productId, data]) => ({
        productId,
        title: data.product?.title || 'Unknown Product',
        sku: data.product?.variants?.[0]?.sku || '',
        revenue: data.revenue,
        units: data.units,
        stock: data.product?.variants?.[0]?.inventory_quantity || 0,
        gross: data.revenue,
      }));

    // Get recent orders
    const topOrders: RecentOrder[] = orders.slice(0, 10).map(order => ({
      order_id: order.id,
      order_display_id: order.display_id?.toString() || order.id.slice(-6),
      customer_name:
        `${order.customer?.first_name || ''} ${order.customer?.last_name || ''}`.trim() || 'Guest',
      customer_email: order.customer?.email || order.email || '',
      total_order_amount: order.total || 0,
      order_status: order.status || 'pending',
      created_at: order.created_at,
    }));

    // Calculate refund rate
    const completedOrders = orders.filter(order => order.status === 'completed');
    const refundedOrders = orders.filter(
      order => order.status === 'canceled' || order.status === 'refunded'
    );
    const refundRate =
      completedOrders.length > 0 ? (refundedOrders.length / completedOrders.length) * 100 : 0;

    const refundRateData = [
      { name: 'Completed', value: completedOrders.length, color: '#10B981' },
      { name: 'Refunded', value: refundedOrders.length, color: '#EF4444' },
    ];

    // Customer segmentation (simplified)
    const customerSplit = [
      { segment: 'New Customers', count: Math.floor(totalCustomers * 0.4), percentage: 40 },
      { segment: 'Repeat Customers', count: Math.floor(totalCustomers * 0.35), percentage: 35 },
      { segment: 'VIP Customers', count: Math.floor(totalCustomers * 0.25), percentage: 25 },
    ];

    // Build response
    const analytics: DashboardAnalytics = {
      stats: {
        totalRevenue,
        totalOrders,
        totalCustomers,
        totalProducts,
        averageOrderValue,
        conversionRate: 3.2, // This would need web analytics integration
        revenueGrowth,
        orderGrowth,
        customerGrowth: 0, // Would need historical customer data
      },
      revenueTrend,
      topProducts,
      topOrders,
      refundRate: refundRateData,
      customerSplit,
    };

    res.status(200).json(analytics);
  } catch (error) {
    console.error('Dashboard analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch dashboard analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
