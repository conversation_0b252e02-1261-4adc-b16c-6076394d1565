import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";

/**
 * GET /admin/analytics
 * 
 * Analytics API overview endpoint that provides information about available analytics endpoints.
 * This serves as a discovery endpoint for the analytics API system.
 */
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const analyticsOverview = {
      message: "ONDC Seller Analytics API",
      version: "1.0.0",
      description: "Comprehensive analytics API system for multi-tenant e-commerce operations",
      endpoints: {
        dashboard: {
          path: "/admin/analytics/dashboard",
          method: "GET",
          description: "Complete dashboard overview with key metrics and trends",
          parameters: ["period", "sales_channel_id", "tenant_id", "currency"]
        },
        sales: {
          path: "/admin/analytics/sales",
          method: "GET", 
          description: "Detailed sales performance analysis with trends and breakdowns",
          parameters: ["period", "group_by", "sales_channel_id", "tenant_id", "category_id"]
        },
        customers: {
          path: "/admin/analytics/customers",
          method: "GET",
          description: "Customer behavior analysis and segmentation",
          parameters: ["period", "segment", "tenant_id", "sales_channel_id"]
        },
        products: {
          path: "/admin/analytics/products", 
          method: "GET",
          description: "Product performance and inventory insights",
          parameters: ["period", "limit", "sort_by", "category_id", "tenant_id", "sales_channel_id"]
        },
        inventory: {
          path: "/admin/analytics/inventory",
          method: "GET",
          description: "Inventory management and stock analysis",
          parameters: ["location_id", "tenant_id", "low_stock_threshold", "category_id"]
        },
        kpi: {
          path: "/admin/analytics/kpi",
          method: "GET",
          description: "Key Performance Indicators with targets and scoring",
          parameters: ["period", "tenant_id", "sales_channel_id", "compare_previous"]
        }
      },
      authentication: {
        required: true,
        type: "Bearer Token",
        header: "Authorization: Bearer <jwt_token>"
      },
      features: [
        "Multi-tenant data isolation",
        "Sales channel filtering", 
        "Real-time analytics",
        "Period-over-period comparisons",
        "Target tracking and scoring",
        "Inventory alerts and monitoring",
        "Customer segmentation",
        "Product performance analysis"
      ],
      response_format: {
        success: true,
        timestamp: "ISO 8601 timestamp",
        data: "Endpoint-specific data structure",
        metadata: "Additional response metadata"
      },
      error_format: {
        success: false,
        error: {
          code: "Error code",
          message: "Human-readable error message",
          details: "Additional error details"
        }
      }
    };

    res.status(200).json(analyticsOverview);
  } catch (error) {
    console.error('Analytics overview error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'ANALYTICS_OVERVIEW_ERROR',
        message: 'Failed to fetch analytics overview',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      timestamp: new Date().toISOString()
    });
  }
}
