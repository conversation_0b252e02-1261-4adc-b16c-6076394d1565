import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";

/**
 * GET /admin/debug/methods
 * 
 * Debug endpoint to explore available methods on Medusa services
 */
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    // Get services
    const orderService = req.scope.resolve('order');
    const productService = req.scope.resolve('product');
    const customerService = req.scope.resolve('customer');

    // Explore available methods
    const orderMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(orderService))
      .filter(name => typeof orderService[name] === 'function');
    
    const productMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(productService))
      .filter(name => typeof productService[name] === 'function');
    
    const customerMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(customerService))
      .filter(name => typeof customerService[name] === 'function');

    const response = {
      message: "Available methods on Medusa services",
      services: {
        order: {
          type: typeof orderService,
          constructor: orderService.constructor.name,
          methods: orderMethods,
          sample_properties: Object.keys(orderService).slice(0, 10)
        },
        product: {
          type: typeof productService,
          constructor: productService.constructor.name,
          methods: productMethods,
          sample_properties: Object.keys(productService).slice(0, 10)
        },
        customer: {
          type: typeof customerService,
          constructor: customerService.constructor.name,
          methods: customerMethods,
          sample_properties: Object.keys(customerService).slice(0, 10)
        }
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Method discovery error:', error);
    res.status(500).json({
      error: 'Failed to discover methods',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
