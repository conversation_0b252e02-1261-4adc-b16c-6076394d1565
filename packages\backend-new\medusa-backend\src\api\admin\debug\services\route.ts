import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";

/**
 * GET /admin/debug/services
 * 
 * Debug endpoint to discover available services in the Medusa container
 */
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    // Get all available services from the container
    const availableServices = Object.keys(req.scope.cradle);
    
    // Filter for relevant services
    const coreServices = availableServices.filter(name => 
      name.includes('Service') || 
      name.includes('Module') ||
      name.includes('order') ||
      name.includes('product') ||
      name.includes('customer') ||
      name.includes('inventory')
    );

    const response = {
      message: "Available Medusa services",
      total_services: availableServices.length,
      core_services: coreServices,
      all_services: availableServices.sort(),
      container_info: {
        has_orderService: 'orderService' in req.scope.cradle,
        has_productService: 'productService' in req.scope.cradle,
        has_customerService: 'customerService' in req.scope.cradle,
        has_orderModuleService: 'orderModuleService' in req.scope.cradle,
        has_productModuleService: 'productModuleService' in req.scope.cradle,
        has_customerModuleService: 'customerModuleService' in req.scope.cradle,
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Service discovery error:', error);
    res.status(500).json({
      error: 'Failed to discover services',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
