import { MedusaRequest, MedusaResponse } from '@medusajs/medusa';
import { z } from 'zod';

// Query parameter validation schema
const DashboardQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
  sales_channel_id: z.string().optional(),
  tenant_id: z.string().optional(),
  currency: z.string().default('INR'),
});

// Response interfaces
interface DashboardOverview {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  averageOrderValue: number;
  conversionRate: number;
  revenueGrowth: number;
  orderGrowth: number;
  customerGrowth: number;
}

interface SalesTrend {
  date: string;
  revenue: number;
  orders: number;
  customers: number;
}

interface TopProduct {
  productId: string;
  title: string;
  sku: string;
  revenue: number;
  units: number;
  stock: number;
  gross: number;
}

interface RecentOrder {
  order_id: string;
  order_display_id: string;
  customer_name: string;
  customer_email: string;
  total_order_amount: number;
  order_status: string;
  created_at: string;
}

interface DashboardAnalytics {
  stats: DashboardOverview;
  revenueTrend: SalesTrend[];
  topProducts: TopProduct[];
  topOrders: RecentOrder[];
  refundRate: Array<{ name: string; value: number; color: string }>;
  customerSplit: Array<{ segment: string; count: number; percentage: number }>;
}

/**
 * GET /test/dashboard
 *
 * Test dashboard analytics endpoint (NO AUTHENTICATION REQUIRED)
 * This is a test endpoint to verify the dashboard API structure and response format
 */
export async function GET(req: MedusaRequest, res: MedusaResponse): Promise<void> {
  try {
    console.log('🧪 TEST Dashboard API called with query:', req.query);

    // Validate query parameters
    const query = DashboardQuerySchema.parse(req.query);
    const { period, sales_channel_id, tenant_id, currency } = query;

    console.log('✅ Query validation passed:', query);

    // Get services using proper Medusa v2 service resolution
    let orderService, productService, customerService;

    try {
      orderService = req.scope.resolve('order');
      productService = req.scope.resolve('product');
      customerService = req.scope.resolve('customer');
      console.log('✅ Services resolved successfully');
    } catch (serviceError) {
      console.error('❌ Service resolution failed:', serviceError);
      // Return mock data if services fail
      const mockAnalytics: DashboardAnalytics = {
        stats: {
          totalRevenue: 125000,
          totalOrders: 45,
          totalCustomers: 32,
          totalProducts: 150,
          averageOrderValue: 2777.78,
          conversionRate: 3.2,
          revenueGrowth: 15.5,
          orderGrowth: 12.3,
          customerGrowth: 8.7,
        },
        revenueTrend: [
          { date: '2025-01-01', revenue: 15000, orders: 5, customers: 4 },
          { date: '2025-01-02', revenue: 18000, orders: 6, customers: 5 },
          { date: '2025-01-03', revenue: 22000, orders: 8, customers: 6 },
        ],
        topProducts: [
          {
            productId: 'prod_01',
            title: 'Premium Smartphone',
            sku: 'PHONE-001',
            revenue: 45000,
            units: 15,
            stock: 25,
            gross: 50000,
          },
        ],
        topOrders: [
          {
            order_id: 'order_01',
            order_display_id: 'ORD-001',
            customer_name: 'John Doe',
            customer_email: '<EMAIL>',
            total_order_amount: 5500,
            order_status: 'completed',
            created_at: '2025-01-20T10:30:00Z',
          },
        ],
        refundRate: [
          { name: 'Successful', value: 95, color: '#10B981' },
          { name: 'Refunded', value: 5, color: '#EF4444' },
        ],
        customerSplit: [
          { segment: 'New', count: 20, percentage: 62.5 },
          { segment: 'Returning', count: 12, percentage: 37.5 },
        ],
      };

      res.status(200).json({
        success: true,
        message: 'Test dashboard API working (mock data)',
        data: mockAnalytics,
        meta: {
          period,
          tenant_id,
          sales_channel_id,
          currency,
          timestamp: new Date().toISOString(),
          isTestEndpoint: true,
          serviceError: serviceError.message,
        },
      });
      return;
    }

    // Try to fetch real data
    try {
      // Calculate date range based on period
      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      console.log('📅 Date range:', { startDate, endDate });

      // Build filters for multi-tenant support
      const filters: any = {
        created_at: {
          $gte: startDate,
          $lte: endDate,
        },
      };

      // Add tenant filtering if specified
      if (tenant_id) {
        filters.metadata = {
          tenant_id: tenant_id,
        };
      }

      // Add sales channel filtering if specified
      if (sales_channel_id) {
        filters.sales_channel_id = sales_channel_id;
      }

      console.log('🔍 Filters applied:', filters);

      // Fetch orders with relations using correct method name and supported relations
      const ordersResult = await orderService.listAndCountOrders(filters, {
        relations: ['items'],
        order: { created_at: 'DESC' },
      });

      const orders = ordersResult[0]; // First element is the orders array
      const totalOrdersCount = ordersResult[1]; // Second element is the count

      console.log(`📦 Found ${orders.length} orders (total: ${totalOrdersCount})`);

      // Calculate summary metrics
      const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
      const totalOrders = orders.length;
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      // For customers, count unique customer IDs from orders since CustomerModuleService doesn't have list methods
      const uniqueCustomerIds = new Set();
      orders.forEach(order => {
        if (order.customer_id) {
          uniqueCustomerIds.add(order.customer_id);
        }
      });
      const totalCustomers = uniqueCustomerIds.size;

      const productsResult = await productService.listProducts({}, { select: ['id'] });
      const totalProducts = Array.isArray(productsResult)
        ? productsResult.length
        : productsResult[1] || 0;

      console.log('📊 Calculated metrics:', {
        totalRevenue,
        totalOrders,
        totalCustomers,
        totalProducts,
        averageOrderValue,
      });

      console.log('📊 Calculated metrics:', {
        totalRevenue,
        totalOrders,
        totalCustomers,
        totalProducts,
        averageOrderValue,
      });

      // Build response
      const analytics: DashboardAnalytics = {
        stats: {
          totalRevenue,
          totalOrders,
          totalCustomers,
          totalProducts,
          averageOrderValue,
          conversionRate: 3.2, // This would need web analytics integration
          revenueGrowth: 0, // Would need historical comparison
          orderGrowth: 0, // Would need historical comparison
          customerGrowth: 0, // Would need historical customer data
        },
        revenueTrend: [], // Simplified for test
        topProducts: [], // Simplified for test
        topOrders: orders.slice(0, 5).map(order => ({
          order_id: order.id,
          order_display_id: order.display_id || order.id,
          customer_name: 'Guest', // Customer relation not available, would need separate lookup
          customer_email: order.email || 'N/A',
          total_order_amount: order.total || 0,
          order_status: order.status || 'unknown',
          created_at: order.created_at,
        })),
        refundRate: [
          { name: 'Successful', value: 95, color: '#10B981' },
          { name: 'Refunded', value: 5, color: '#EF4444' },
        ],
        customerSplit: [
          { segment: 'New', count: Math.floor(totalCustomers * 0.6), percentage: 60 },
          { segment: 'Returning', count: Math.floor(totalCustomers * 0.4), percentage: 40 },
        ],
      };

      res.status(200).json({
        success: true,
        message: 'Test dashboard API working (real data)',
        data: analytics,
        meta: {
          period,
          tenant_id,
          sales_channel_id,
          currency,
          timestamp: new Date().toISOString(),
          isTestEndpoint: true,
          dataSource: 'real',
        },
      });
    } catch (dataError) {
      console.error('❌ Data fetching failed:', dataError);
      throw dataError;
    }
  } catch (error) {
    console.error('❌ Test Dashboard analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch test dashboard analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
      isTestEndpoint: true,
    });
  }
}
