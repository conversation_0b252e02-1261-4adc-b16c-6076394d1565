import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";

/**
 * GET /test/debug
 * 
 * Debug endpoint to explore available methods on Medusa services (NO AUTH)
 */
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    console.log('🔍 Debug endpoint called');
    
    // Get services
    let orderService, productService, customerService;
    
    try {
      orderService = req.scope.resolve('order');
      productService = req.scope.resolve('product');
      customerService = req.scope.resolve('customer');
      console.log('✅ Services resolved successfully');
    } catch (error) {
      console.error('❌ Service resolution failed:', error);
      return res.status(500).json({
        error: 'Failed to resolve services',
        details: error.message,
      });
    }

    // Explore available methods
    const orderMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(orderService))
      .filter(name => typeof orderService[name] === 'function');
    
    const productMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(productService))
      .filter(name => typeof productService[name] === 'function');
    
    const customerMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(customerService))
      .filter(name => typeof customerService[name] === 'function');

    // Also check direct properties
    const orderProps = Object.keys(orderService);
    const productProps = Object.keys(productService);
    const customerProps = Object.keys(customerService);

    const response = {
      message: "Available methods on Medusa services",
      services: {
        order: {
          type: typeof orderService,
          constructor: orderService.constructor.name,
          methods: orderMethods,
          properties: orderProps.slice(0, 20),
          hasListMethod: typeof orderService.list === 'function',
          hasListAndCountMethod: typeof orderService.listAndCount === 'function',
          hasListOrdersMethod: typeof orderService.listOrders === 'function',
          hasListAndCountOrdersMethod: typeof orderService.listAndCountOrders === 'function',
        },
        product: {
          type: typeof productService,
          constructor: productService.constructor.name,
          methods: productMethods,
          properties: productProps.slice(0, 20),
          hasListMethod: typeof productService.list === 'function',
          hasListAndCountMethod: typeof productService.listAndCount === 'function',
          hasListProductsMethod: typeof productService.listProducts === 'function',
        },
        customer: {
          type: typeof customerService,
          constructor: customerService.constructor.name,
          methods: customerMethods,
          properties: customerProps.slice(0, 20),
          hasListMethod: typeof customerService.list === 'function',
          hasListAndCountMethod: typeof customerService.listAndCount === 'function',
          hasListCustomersMethod: typeof customerService.listCustomers === 'function',
        }
      },
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);

  } catch (error) {
    console.error('❌ Debug endpoint error:', error);
    res.status(500).json({
      error: 'Debug endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
