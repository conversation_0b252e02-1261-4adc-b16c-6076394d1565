import type { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';

/**
 * GET /test/services
 * 
 * Test endpoint to check which services are available in the container
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log('🔍 Testing service resolution...');
    
    const serviceTests = [];
    
    // Test auth service resolution
    const authServiceNames = ['auth', 'authModuleService', 'authModule'];
    for (const serviceName of authServiceNames) {
      try {
        const service = req.scope.resolve(serviceName);
        serviceTests.push({
          name: serviceName,
          status: 'available',
          methods: Object.getOwnPropertyNames(Object.getPrototypeOf(service))
            .filter(name => typeof service[name] === 'function' && name !== 'constructor')
            .slice(0, 10) // Limit to first 10 methods
        });
        console.log(`✅ ${serviceName} service available`);
      } catch (error) {
        serviceTests.push({
          name: serviceName,
          status: 'not_available',
          error: error.message
        });
        console.log(`❌ ${serviceName} service not available:`, error.message);
      }
    }
    
    // Test user service resolution
    const userServiceNames = ['user', 'userModuleService', 'userModule'];
    for (const serviceName of userServiceNames) {
      try {
        const service = req.scope.resolve(serviceName);
        serviceTests.push({
          name: serviceName,
          status: 'available',
          methods: Object.getOwnPropertyNames(Object.getPrototypeOf(service))
            .filter(name => typeof service[name] === 'function' && name !== 'constructor')
            .slice(0, 10) // Limit to first 10 methods
        });
        console.log(`✅ ${serviceName} service available`);
      } catch (error) {
        serviceTests.push({
          name: serviceName,
          status: 'not_available',
          error: error.message
        });
        console.log(`❌ ${serviceName} service not available:`, error.message);
      }
    }
    
    // Test other common services
    const commonServices = ['product', 'order', 'customer', 'cart', 'payment', 'inventory'];
    for (const serviceName of commonServices) {
      try {
        const service = req.scope.resolve(serviceName);
        serviceTests.push({
          name: serviceName,
          status: 'available',
          type: 'common_service'
        });
        console.log(`✅ ${serviceName} service available`);
      } catch (error) {
        serviceTests.push({
          name: serviceName,
          status: 'not_available',
          error: error.message,
          type: 'common_service'
        });
        console.log(`❌ ${serviceName} service not available:`, error.message);
      }
    }
    
    // Get all registered services in the container
    let allServices = [];
    try {
      // Try to get container registration info
      const container = req.scope;
      if (container && container.registrations) {
        allServices = Object.keys(container.registrations);
      }
    } catch (error) {
      console.log('Could not get all services:', error.message);
    }
    
    return res.json({
      message: 'Service resolution test completed',
      serviceTests,
      totalTested: serviceTests.length,
      availableServices: serviceTests.filter(s => s.status === 'available').length,
      allRegisteredServices: allServices.slice(0, 50), // Limit output
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Service test error:', error);
    return res.status(500).json({
      error: 'Service test failed',
      message: error.message
    });
  }
};
