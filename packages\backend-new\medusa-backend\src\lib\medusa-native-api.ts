/**
 * Medusa Native API Client
 * 
 * Refactored API client that uses Medusa's native multi-tenancy
 * through Sales Channels and Publishable API Keys instead of
 * custom tenant implementations.
 */

// Types
export interface ProductFilters {
  limit?: number
  offset?: number
  q?: string
  category_id?: string
  collection_id?: string
  tags?: string[]
  region_id?: string
  currency_code?: string
}

export interface CreateCartData {
  region_id?: string
  currency_code?: string
  customer_id?: string
  email?: string
}

export interface LineItem {
  variant_id: string
  quantity: number
  metadata?: Record<string, any>
}

export interface ProductsResponse {
  products: any[]
  count: number
  offset: number
  limit: number
}

export interface ProductResponse {
  product: any
}

export interface CartResponse {
  cart: any
}

export interface OrderResponse {
  order: any
}

export interface SalesChannelsResponse {
  sales_channels: any[]
  count: number
  offset: number
  limit: number
}

export interface SalesChannelResponse {
  sales_channel: any
}

export interface CustomersResponse {
  customers: any[]
  count: number
  offset: number
  limit: number
}

export interface OrdersResponse {
  orders: any[]
  count: number
  offset: number
  limit: number
}

/**
 * Medusa Native API Client
 * 
 * Uses Medusa's built-in multi-tenancy through Sales Channels
 * and Publishable API Keys for proper tenant isolation.
 */
export class MedusaNativeAPI {
  private baseURL: string
  private publishableKey: string
  private adminToken?: string
  private salesChannelId?: string

  constructor(tenantId: string) {
    this.baseURL = process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000'
    this.publishableKey = this.getTenantPublishableKey(tenantId)
    this.salesChannelId = this.getTenantSalesChannelId(tenantId)
  }

  /**
   * Map tenant ID to publishable API key
   * These keys are generated by the setup-tenant-sales-channels script
   */
  private getTenantPublishableKey(tenantId: string): string {
    const keyMap: Record<string, string> = {
      'tenant-electronics-001': process.env.NEXT_PUBLIC_TENANT_ELECTRONICS_001_API_KEY || '',
      'tenant-fashion-002': process.env.NEXT_PUBLIC_TENANT_FASHION_002_API_KEY || '',
      'default': process.env.NEXT_PUBLIC_DEFAULT_API_KEY || ''
    }
    
    const key = keyMap[tenantId] || keyMap['default']
    
    if (!key) {
      throw new Error(`No publishable API key found for tenant: ${tenantId}. Please run setup-tenant-sales-channels script.`)
    }
    
    return key
  }

  /**
   * Map tenant ID to sales channel ID (for admin operations)
   */
  private getTenantSalesChannelId(tenantId: string): string {
    const channelMap: Record<string, string> = {
      'tenant-electronics-001': process.env.TENANT_ELECTRONICS_001_SALES_CHANNEL_ID || '',
      'tenant-fashion-002': process.env.TENANT_FASHION_002_SALES_CHANNEL_ID || '',
      'default': process.env.DEFAULT_SALES_CHANNEL_ID || ''
    }
    
    return channelMap[tenantId] || channelMap['default']
  }

  // ==========================================
  // STORE API METHODS (Customer-facing)
  // ==========================================

  /**
   * Get products (automatically filtered by sales channel via publishable key)
   */
  async getProducts(params?: ProductFilters): Promise<ProductsResponse> {
    const searchParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => searchParams.append(key, v.toString()))
          } else {
            searchParams.append(key, value.toString())
          }
        }
      })
    }
    
    return await this.request<ProductsResponse>(`/store/products?${searchParams}`, {
      headers: {
        'x-publishable-api-key': this.publishableKey
      }
    })
  }

  /**
   * Get specific product
   */
  async getProduct(id: string): Promise<ProductResponse> {
    return await this.request<ProductResponse>(`/store/products/${id}`, {
      headers: {
        'x-publishable-api-key': this.publishableKey
      }
    })
  }

  /**
   * Create cart (automatically scoped to sales channel)
   */
  async createCart(data?: CreateCartData): Promise<CartResponse> {
    return await this.request<CartResponse>('/store/carts', {
      method: 'POST',
      headers: {
        'x-publishable-api-key': this.publishableKey
      },
      body: JSON.stringify(data || {})
    })
  }

  /**
   * Get cart
   */
  async getCart(cartId: string): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}`, {
      headers: {
        'x-publishable-api-key': this.publishableKey
      }
    })
  }

  /**
   * Add item to cart
   */
  async addToCart(cartId: string, item: LineItem): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}/line-items`, {
      method: 'POST',
      headers: {
        'x-publishable-api-key': this.publishableKey
      },
      body: JSON.stringify(item)
    })
  }

  /**
   * Update cart item
   */
  async updateCartItem(cartId: string, itemId: string, quantity: number): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}/line-items/${itemId}`, {
      method: 'POST',
      headers: {
        'x-publishable-api-key': this.publishableKey
      },
      body: JSON.stringify({ quantity })
    })
  }

  /**
   * Remove item from cart
   */
  async removeFromCart(cartId: string, itemId: string): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}/line-items/${itemId}`, {
      method: 'DELETE',
      headers: {
        'x-publishable-api-key': this.publishableKey
      }
    })
  }

  /**
   * Create order from cart
   */
  async createOrder(cartId: string): Promise<OrderResponse> {
    return await this.request<OrderResponse>('/store/orders', {
      method: 'POST',
      headers: {
        'x-publishable-api-key': this.publishableKey
      },
      body: JSON.stringify({ cart_id: cartId })
    })
  }

  /**
   * Get order
   */
  async getOrder(orderId: string): Promise<OrderResponse> {
    return await this.request<OrderResponse>(`/store/orders/${orderId}`, {
      headers: {
        'x-publishable-api-key': this.publishableKey
      }
    })
  }

  // ==========================================
  // ADMIN API METHODS (Management)
  // ==========================================

  /**
   * Set admin authentication token
   */
  setAdminToken(token: string): void {
    this.adminToken = token
  }

  /**
   * Get all sales channels (tenants)
   */
  async getSalesChannels(): Promise<SalesChannelsResponse> {
    return await this.request<SalesChannelsResponse>('/admin/sales-channels', {
      headers: {
        'Authorization': `Bearer ${this.adminToken}`
      }
    })
  }

  /**
   * Get specific sales channel (tenant configuration)
   */
  async getSalesChannel(id?: string): Promise<SalesChannelResponse> {
    const channelId = id || this.salesChannelId
    
    if (!channelId) {
      throw new Error('No sales channel ID available')
    }
    
    return await this.request<SalesChannelResponse>(`/admin/sales-channels/${channelId}`, {
      headers: {
        'Authorization': `Bearer ${this.adminToken}`
      }
    })
  }

  /**
   * Update sales channel (tenant configuration)
   */
  async updateSalesChannel(id: string, data: any): Promise<SalesChannelResponse> {
    return await this.request<SalesChannelResponse>(`/admin/sales-channels/${id}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.adminToken}`
      },
      body: JSON.stringify(data)
    })
  }

  /**
   * Get admin products (optionally filtered by sales channel)
   */
  async getAdminProducts(salesChannelId?: string, params?: ProductFilters): Promise<ProductsResponse> {
    const searchParams = new URLSearchParams()
    
    if (salesChannelId) {
      searchParams.append('sales_channel_id', salesChannelId)
    }
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => searchParams.append(key, v.toString()))
          } else {
            searchParams.append(key, value.toString())
          }
        }
      })
    }
    
    return await this.request<ProductsResponse>(`/admin/products?${searchParams}`, {
      headers: {
        'Authorization': `Bearer ${this.adminToken}`
      }
    })
  }

  /**
   * Get admin customers (optionally filtered by sales channel)
   */
  async getAdminCustomers(salesChannelId?: string): Promise<CustomersResponse> {
    const params = salesChannelId ? `?sales_channel_id=${salesChannelId}` : ''
    
    return await this.request<CustomersResponse>(`/admin/customers${params}`, {
      headers: {
        'Authorization': `Bearer ${this.adminToken}`
      }
    })
  }

  /**
   * Get admin orders (optionally filtered by sales channel)
   */
  async getAdminOrders(salesChannelId?: string): Promise<OrdersResponse> {
    const params = salesChannelId ? `?sales_channel_id=${salesChannelId}` : ''
    
    return await this.request<OrdersResponse>(`/admin/orders${params}`, {
      headers: {
        'Authorization': `Bearer ${this.adminToken}`
      }
    })
  }

  // ==========================================
  // UTILITY METHODS
  // ==========================================

  /**
   * Get tenant configuration from sales channel metadata
   */
  async getTenantConfig(): Promise<any> {
    const response = await this.getSalesChannel()
    return {
      id: response.sales_channel.metadata?.tenantId,
      name: response.sales_channel.name,
      domain: response.sales_channel.metadata?.domain,
      settings: {
        currency: response.sales_channel.metadata?.currency,
        timezone: response.sales_channel.metadata?.timezone,
        features: response.sales_channel.metadata?.features,
        ondcConfig: response.sales_channel.metadata?.ondcConfig,
        branding: response.sales_channel.metadata?.branding
      },
      status: response.sales_channel.is_disabled ? 'inactive' : 'active'
    }
  }

  /**
   * Generic request method
   */
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  }
}

/**
 * Factory function to create API client for specific tenant
 */
export function createMedusaAPI(tenantId: string): MedusaNativeAPI {
  return new MedusaNativeAPI(tenantId)
}

/**
 * Default export for backward compatibility
 */
export default MedusaNativeAPI
