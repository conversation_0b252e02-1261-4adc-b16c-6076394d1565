import { MedusaContainer } from "@medusajs/medusa"
import { TenantService } from "../modules/tenant/service"

/**
 * Tenant Service Loader
 * Registers the tenant service in the Medusa container
 */
export default async (container: MedusaContainer): Promise<void> => {
  try {
    // Register tenant service in container
    container.register({
      tenantModuleService: {
        resolve: () => new TenantService(container),
        lifetime: "SINGLETON"
      }
    })

    console.log("✅ Tenant service registered successfully")
  } catch (error) {
    console.error("❌ Failed to register tenant service:", error)
    throw error
  }
}
