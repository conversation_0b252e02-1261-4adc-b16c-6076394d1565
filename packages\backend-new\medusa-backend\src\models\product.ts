import { Product as MedusaProduct } from "@medusajs/medusa"
import { Entity, Property } from "@mikro-orm/core"

/**
 * Extends Medusa's default Product entity to include a JSONB column
 * `additional_data` used for storing arbitrary product-specific data.
 *
 * A migration is provided to add the column at the database level.
 */
@Entity({ tableName: "product" })
export class Product extends MedusaProduct {
  @Property({ type: "jsonb", nullable: true })
  additional_data?: Record<string, unknown>
}
