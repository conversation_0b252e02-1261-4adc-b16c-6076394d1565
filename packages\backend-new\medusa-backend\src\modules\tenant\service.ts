import { TenantConfig } from "../../middleware/tenant"

interface TenantServiceOptions {
  // Add any dependencies here
}

/**
 * Tenant Service for Medusa v2
 *
 * Provides multi-tenant functionality including:
 * - Tenant validation and configuration
 * - ONDC-specific tenant settings
 * - Feature management per tenant
 * - Tenant-aware data filtering
 */
export class TenantService {
  private tenantCache: Map<string, TenantConfig> = new Map()
  private cacheTimeout: number = 5 * 60 * 1000 // 5 minutes

  constructor(container: any, options: TenantServiceOptions = {}) {
    // Initialize service without extending MedusaService
  }

  /**
   * Get tenant configuration by ID
   */
  async getTenantConfig(tenantId: string): Promise<TenantConfig | null> {
    // Check cache first
    if (this.tenantCache.has(tenantId)) {
      const cached = this.tenantCache.get(tenantId)!
      const isExpired = Date.now() - cached.updatedAt.getTime() > this.cacheTimeout
      
      if (!isExpired) {
        return cached
      }
    }

    // Fetch tenant configuration
    const config = await this.fetchTenantConfig(tenantId)
    
    if (config) {
      this.tenantCache.set(tenantId, config)
    }

    return config
  }

  /**
   * Validate if tenant exists and is active
   */
  async validateTenant(tenantId: string): Promise<boolean> {
    const config = await this.getTenantConfig(tenantId)
    return config !== null && config.status === 'active'
  }

  /**
   * Check if tenant has specific feature enabled
   */
  async hasTenantFeature(tenantId: string, feature: string): Promise<boolean> {
    const config = await this.getTenantConfig(tenantId)
    
    if (!config) {
      return false
    }

    return config.settings.features.includes(feature) || 
           config.settings.features.includes('all')
  }

  /**
   * Get ONDC configuration for tenant
   */
  async getOndcConfig(tenantId: string) {
    const config = await this.getTenantConfig(tenantId)
    return config?.settings.ondcConfig || null
  }

  /**
   * Get all active tenants
   */
  async getActiveTenants(): Promise<TenantConfig[]> {
    // In development, return mock tenants
    if (process.env.NODE_ENV === 'development') {
      return [
        await this.getTenantConfig('tenant-electronics-001'),
        await this.getTenantConfig('tenant-fashion-002'),
        await this.getTenantConfig('tenant-books-003'),
      ].filter(Boolean) as TenantConfig[]
    }

    // In production, fetch from database
    const validTenants = process.env.VALID_TENANTS?.split(',') || ['default']
    const configs = await Promise.all(
      validTenants.map(id => this.getTenantConfig(id))
    )

    return configs.filter(Boolean) as TenantConfig[]
  }

  /**
   * Create tenant-aware database query filter
   */
  createTenantFilter(tenantId: string) {
    return {
      tenant_id: tenantId
    }
  }

  /**
   * Add tenant context to data
   */
  addTenantContext<T extends Record<string, any>>(
    data: T, 
    tenantId: string
  ): T & { tenant_id: string } {
    return {
      ...data,
      tenant_id: tenantId
    }
  }

  /**
   * Validate tenant access to resource
   */
  async validateTenantAccess(
    tenantId: string, 
    resourceTenantId: string
  ): Promise<boolean> {
    // Basic validation - resource must belong to the same tenant
    return tenantId === resourceTenantId
  }

  /**
   * Get tenant-specific settings
   */
  async getTenantSettings(tenantId: string) {
    const config = await this.getTenantConfig(tenantId)
    return config?.settings || null
  }

  /**
   * Update tenant configuration (admin only)
   */
  async updateTenantConfig(
    tenantId: string, 
    updates: Partial<TenantConfig>
  ): Promise<TenantConfig | null> {
    const existing = await this.getTenantConfig(tenantId)
    
    if (!existing) {
      throw new Error(`Tenant ${tenantId} not found`)
    }

    const updated: TenantConfig = {
      ...existing,
      ...updates,
      id: tenantId, // Ensure ID cannot be changed
      updatedAt: new Date()
    }

    // In production, save to database
    // For now, update cache
    this.tenantCache.set(tenantId, updated)

    return updated
  }

  /**
   * Clear tenant cache
   */
  clearTenantCache(tenantId?: string): void {
    if (tenantId) {
      this.tenantCache.delete(tenantId)
    } else {
      this.tenantCache.clear()
    }
  }

  /**
   * Private method to fetch tenant configuration
   */
  private async fetchTenantConfig(tenantId: string): Promise<TenantConfig | null> {
    // For development mode, return mock configuration
    if (process.env.NODE_ENV === 'development') {
      return this.createMockTenantConfig(tenantId)
    }

    // In production, this would fetch from database
    const validTenants = process.env.VALID_TENANTS?.split(',') || ['default']
    
    if (!validTenants.includes(tenantId)) {
      return null
    }

    return this.createMockTenantConfig(tenantId)
  }

  /**
   * Create mock tenant configuration for development
   */
  private createMockTenantConfig(tenantId: string): TenantConfig {
    const tenantConfigs: Record<string, Partial<TenantConfig>> = {
      'tenant-electronics-001': {
        name: 'Electronics Store',
        domain: 'electronics.ondc-seller.com',
        settings: {
          currency: 'INR',
          timezone: 'Asia/Kolkata',
          features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
          ondcConfig: {
            participantId: 'electronics-participant-001',
            subscriberId: 'electronics-subscriber-001',
            bppId: 'ondc-bpp-electronics-001',
          },
        },
      },
      'tenant-fashion-002': {
        name: 'Fashion Store',
        domain: 'fashion.ondc-seller.com',
        settings: {
          currency: 'INR',
          timezone: 'Asia/Kolkata',
          features: ['products', 'orders', 'customers', 'promotions'],
          ondcConfig: {
            participantId: 'fashion-participant-002',
            subscriberId: 'fashion-subscriber-002',
            bppId: 'ondc-bpp-fashion-002',
          },
        },
      },
      'tenant-books-003': {
        name: 'Books Store',
        domain: 'books.ondc-seller.com',
        settings: {
          currency: 'INR',
          timezone: 'Asia/Kolkata',
          features: ['products', 'orders', 'customers'],
          ondcConfig: {
            participantId: 'books-participant-003',
            subscriberId: 'books-subscriber-003',
            bppId: 'ondc-bpp-books-003',
          },
        },
      },
    }

    const baseConfig: TenantConfig = {
      id: tenantId,
      name: `Tenant ${tenantId}`,
      domain: `${tenantId}.ondc-seller.com`,
      settings: {
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        features: ['products', 'orders', 'customers'],
        ondcConfig: {
          participantId: process.env[`ONDC_PARTICIPANT_ID_${tenantId.toUpperCase()}`] || process.env.ONDC_PARTICIPANT_ID,
          subscriberId: process.env[`ONDC_SUBSCRIBER_ID_${tenantId.toUpperCase()}`] || process.env.ONDC_SUBSCRIBER_ID,
          bppId: process.env[`ONDC_BPP_ID_${tenantId.toUpperCase()}`] || `${process.env.ONDC_BPP_ID || 'ondc-bpp'}-${tenantId}`,
        },
      },
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    const specificConfig = tenantConfigs[tenantId] || {}
    
    return {
      ...baseConfig,
      ...specificConfig,
      id: tenantId,
      settings: {
        ...baseConfig.settings,
        ...specificConfig.settings,
      },
    }
  }
}

export default TenantService
