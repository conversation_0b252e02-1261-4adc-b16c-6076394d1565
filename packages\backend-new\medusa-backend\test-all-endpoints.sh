#!/bin/bash

# Complete E-commerce API Testing Script with Cash on Delivery
# Tests all endpoints from product browsing to order completion

echo "🚀 Testing Complete E-commerce API Flow with Cash on Delivery"
echo "============================================================="

# Configuration
API_KEY="pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0"
BASE_URL="http://localhost:9000"
REGION_ID="reg_01JZ7RPY072WGWKTJ6Q2YE46V7"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -H "x-publishable-api-key: $API_KEY" "$BASE_URL$endpoint")
    else
        response=$(curl -s -X $method -H "x-publishable-api-key: $API_KEY" -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    fi
    
    if [[ $response == *"error"* ]] || [[ $response == *"Error"* ]] || [[ $response == *"Unauthorized"* ]]; then
        echo -e "${RED}❌ FAILED${NC}"
        echo "Response: $response"
        return 1
    else
        echo -e "${GREEN}✅ SUCCESS${NC}"
        echo "Response: ${response:0:200}..."
        return 0
    fi
}

# Test 1: Health Check
echo -e "\n${YELLOW}=== 1. HEALTH CHECK ===${NC}"
test_endpoint "GET" "/health" "" "Server Health Check"

# Test 2: Regions
echo -e "\n${YELLOW}=== 2. REGIONS ===${NC}"
test_endpoint "GET" "/store/regions" "" "Get Available Regions"

# Test 3: Products
echo -e "\n${YELLOW}=== 3. PRODUCTS ===${NC}"
test_endpoint "GET" "/store/products?limit=1" "" "Get Products List"

# Get first product and variant for cart testing
echo -e "\n${YELLOW}Getting product details for cart testing...${NC}"
PRODUCT_RESPONSE=$(curl -s -H "x-publishable-api-key: $API_KEY" "$BASE_URL/store/products?limit=1")
VARIANT_ID=$(echo "$PRODUCT_RESPONSE" | jq -r '.products[0].variants[0].id // empty')
PRODUCT_ID=$(echo "$PRODUCT_RESPONSE" | jq -r '.products[0].id // empty')

if [ -n "$VARIANT_ID" ] && [ "$VARIANT_ID" != "null" ]; then
    echo "✅ Found variant ID: $VARIANT_ID"
    echo "✅ Found product ID: $PRODUCT_ID"
else
    echo "❌ Could not get variant ID from products"
    exit 1
fi

# Test 4: Product Categories
echo -e "\n${YELLOW}=== 4. PRODUCT CATEGORIES ===${NC}"
test_endpoint "GET" "/store/product-categories" "" "Get Product Categories"

# Test 5: Single Product
echo -e "\n${YELLOW}=== 5. SINGLE PRODUCT ===${NC}"
test_endpoint "GET" "/store/products/$PRODUCT_ID" "" "Get Single Product Details"

# Test 6: Cart Creation
echo -e "\n${YELLOW}=== 6. CART CREATION ===${NC}"
CART_DATA='{"region_id": "'$REGION_ID'"}'
CART_RESPONSE=$(curl -s -X POST -H "x-publishable-api-key: $API_KEY" -H "Content-Type: application/json" -d "$CART_DATA" "$BASE_URL/store/carts")

if [[ $CART_RESPONSE == *"cart"* ]]; then
    CART_ID=$(echo "$CART_RESPONSE" | jq -r '.cart.id // empty')
    echo -e "${GREEN}✅ Cart created successfully${NC}"
    echo "Cart ID: $CART_ID"
else
    echo -e "${RED}❌ Cart creation failed${NC}"
    echo "Response: $CART_RESPONSE"
    exit 1
fi

# Test 7: Cart Retrieval
echo -e "\n${YELLOW}=== 7. CART RETRIEVAL ===${NC}"
test_endpoint "GET" "/store/carts/$CART_ID" "" "Get Cart Details"

# Test 8: Add to Cart
echo -e "\n${YELLOW}=== 8. ADD TO CART ===${NC}"
ADD_TO_CART_DATA='{"variant_id": "'$VARIANT_ID'", "quantity": 1}'
test_endpoint "POST" "/store/carts/$CART_ID/line-items" "$ADD_TO_CART_DATA" "Add Product to Cart"

# Test 9: Customer Registration (Simplified)
echo -e "\n${YELLOW}=== 9. CUSTOMER REGISTRATION (SIMPLIFIED) ===${NC}"
CUSTOMER_DATA='{
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+1234567890"
}'
test_endpoint "POST" "/store/customers/register" "$CUSTOMER_DATA" "Customer Registration (Simplified)"

# Test 10: Complete Cart with Cash on Delivery
echo -e "\n${YELLOW}=== 10. COMPLETE CART WITH CASH ON DELIVERY ===${NC}"
ORDER_RESPONSE=$(curl -s -X POST -H "x-publishable-api-key: $API_KEY" -H "Content-Type: application/json" "$BASE_URL/store/carts/$CART_ID/complete-cod")

if [[ $ORDER_RESPONSE == *"order"* ]] && [[ $ORDER_RESPONSE == *"success"* ]]; then
    ORDER_ID=$(echo "$ORDER_RESPONSE" | jq -r '.order.id // empty')
    echo -e "${GREEN}✅ COD Order created successfully${NC}"
    echo "Order ID: $ORDER_ID"
    echo "Payment Method: Cash on Delivery"
    echo "Response: ${ORDER_RESPONSE:0:300}..."
else
    echo -e "${RED}❌ COD Order creation failed${NC}"
    echo "Response: $ORDER_RESPONSE"
fi

# Test 11: Get Orders (Simplified)
echo -e "\n${YELLOW}=== 11. GET ORDERS (SIMPLIFIED) ===${NC}"
test_endpoint "GET" "/store/orders/simple?email=<EMAIL>&limit=5" "" "Get Orders for Customer"

# Test 12: Get Single Order (if order was created)
if [ -n "$ORDER_ID" ] && [ "$ORDER_ID" != "null" ]; then
    echo -e "\n${YELLOW}=== 12. GET SINGLE ORDER ===${NC}"
    SINGLE_ORDER_DATA='{"order_id": "'$ORDER_ID'"}'
    test_endpoint "POST" "/store/orders/simple" "$SINGLE_ORDER_DATA" "Get Single Order Details"
fi

# Summary
echo -e "\n${YELLOW}=============================================================${NC}"
echo -e "${YELLOW}🎉 COMPLETE E-COMMERCE API TESTING FINISHED${NC}"
echo -e "${YELLOW}=============================================================${NC}"

if [ -n "$ORDER_ID" ] && [ "$ORDER_ID" != "null" ]; then
    echo -e "${GREEN}✅ FULL E-COMMERCE FLOW SUCCESSFUL!${NC}"
    echo -e "${GREEN}   - Product browsing: ✅ Working${NC}"
    echo -e "${GREEN}   - Cart creation: ✅ Working${NC}"
    echo -e "${GREEN}   - Add to cart: ✅ Working${NC}"
    echo -e "${GREEN}   - Payment setup: ✅ Working${NC}"
    echo -e "${GREEN}   - Order creation: ✅ Working${NC}"
    echo -e "${GREEN}   - Order ID: $ORDER_ID${NC}"
else
    echo -e "${RED}❌ Some endpoints may need additional configuration${NC}"
fi

echo -e "\n${YELLOW}Ready to update Postman collection with working endpoints!${NC}"
