#!/bin/bash

# API Endpoints Verification Script
# Tests all core e-commerce endpoints with real database operations

BASE_URL="http://localhost:9000"
PUBLISHABLE_KEY="pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to print test results
print_test_result() {
    local test_name="$1"
    local status="$2"
    local details="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC} - $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ FAIL${NC} - $test_name"
        [ -n "$details" ] && echo -e "   ${RED}Error: $details${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  WARN${NC} - $test_name"
        [ -n "$details" ] && echo -e "   ${YELLOW}Warning: $details${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local method="$1"
    local url="$2"
    local headers="$3"
    local data="$4"
    local expected_status="$5"
    local test_name="$6"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -H "$headers" "$url")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -X POST -H "$headers" -H "Content-Type: application/json" -d "$data" "$url")
    fi
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "$expected_status" ]; then
        print_test_result "$test_name" "PASS"
        return 0
    else
        print_test_result "$test_name" "FAIL" "Expected $expected_status, got $http_code"
        return 1
    fi
}

echo -e "${BLUE}🚀 Starting API Endpoints Verification${NC}"
echo "=================================================="

# Get admin token
echo -e "\n${BLUE}🔐 Authentication Tests${NC}"
echo "----------------------------"

TOKEN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d '{"email": "<EMAIL>", "password": "supersecret"}' "$BASE_URL/auth/user/emailpass")
TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.token // empty')

if [ -n "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
    print_test_result "Admin Authentication" "PASS"
else
    print_test_result "Admin Authentication" "FAIL" "Could not get admin token"
    exit 1
fi

# Test invalid credentials
INVALID_RESPONSE=$(curl -s -w "%{http_code}" -X POST -H "Content-Type: application/json" -d '{"email": "<EMAIL>", "password": "wrong"}' "$BASE_URL/auth/user/emailpass")
INVALID_CODE="${INVALID_RESPONSE: -3}"

if [ "$INVALID_CODE" = "401" ] || [ "$INVALID_CODE" = "500" ]; then
    print_test_result "Invalid Credentials Rejection" "PASS"
else
    print_test_result "Invalid Credentials Rejection" "FAIL" "Expected 401/500, got $INVALID_CODE"
fi

# Test admin user info
test_endpoint "GET" "$BASE_URL/admin/users/me" "Authorization: Bearer $TOKEN" "" "200" "Get Admin User Info"

echo -e "\n${BLUE}🏢 Multi-Tenant Configuration Tests${NC}"
echo "----------------------------------------"

# Test default tenant
test_endpoint "GET" "$BASE_URL/admin/tenant" "Authorization: Bearer $TOKEN" "" "200" "Default Tenant Configuration"

# Test electronics tenant
ELECTRONICS_RESPONSE=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" -H "x-tenant-id: tenant-electronics-001" "$BASE_URL/admin/tenant")
ELECTRONICS_CODE="${ELECTRONICS_RESPONSE: -3}"
if [ "$ELECTRONICS_CODE" = "200" ]; then
    print_test_result "Electronics Tenant Configuration" "PASS"
else
    print_test_result "Electronics Tenant Configuration" "FAIL" "Expected 200, got $ELECTRONICS_CODE"
fi

# Test fashion tenant
FASHION_RESPONSE=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" -H "x-tenant-id: tenant-fashion-002" "$BASE_URL/admin/tenant")
FASHION_CODE="${FASHION_RESPONSE: -3}"
if [ "$FASHION_CODE" = "200" ]; then
    print_test_result "Fashion Tenant Configuration" "PASS"
else
    print_test_result "Fashion Tenant Configuration" "FAIL" "Expected 200, got $FASHION_CODE"
fi

# Test books tenant
BOOKS_RESPONSE=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" -H "x-tenant-id: tenant-books-003" "$BASE_URL/admin/tenant")
BOOKS_CODE="${BOOKS_RESPONSE: -3}"
if [ "$BOOKS_CODE" = "200" ]; then
    print_test_result "Books Tenant Configuration" "PASS"
else
    print_test_result "Books Tenant Configuration" "FAIL" "Expected 200, got $BOOKS_CODE"
fi

echo -e "\n${BLUE}🏪 Store Information Tests${NC}"
echo "-------------------------------"

# Test store info
STORE_RESPONSE=$(curl -s -w "%{http_code}" -H "x-publishable-api-key: $PUBLISHABLE_KEY" "$BASE_URL/store")
STORE_CODE="${STORE_RESPONSE: -3}"

if [ "$STORE_CODE" = "200" ]; then
    print_test_result "Store Information" "PASS"
elif [ "$STORE_CODE" = "404" ]; then
    print_test_result "Store Information" "WARN" "Store endpoint returns 404 - may need configuration"
else
    print_test_result "Store Information" "FAIL" "Expected 200, got $STORE_CODE"
fi

echo -e "\n${BLUE}📦 Products API Tests${NC}"
echo "------------------------"

# Test admin products list
test_endpoint "GET" "$BASE_URL/admin/products" "Authorization: Bearer $TOKEN" "" "200" "Admin Products List"

# Test store products list
test_endpoint "GET" "$BASE_URL/store/products" "x-publishable-api-key: $PUBLISHABLE_KEY" "" "200" "Store Products List"

echo -e "\n${BLUE}👥 Customer API Tests${NC}"
echo "------------------------"

# Test admin customers list
test_endpoint "GET" "$BASE_URL/admin/customers" "Authorization: Bearer $TOKEN" "" "200" "Admin Customers List"

echo -e "\n${BLUE}📋 Orders API Tests${NC}"
echo "---------------------"

# Test admin orders list
test_endpoint "GET" "$BASE_URL/admin/orders" "Authorization: Bearer $TOKEN" "" "200" "Admin Orders List"

echo -e "\n${BLUE}🛒 Cart API Tests${NC}"
echo "-------------------"

# Test cart creation
CART_RESPONSE=$(curl -s -w "%{http_code}" -X POST -H "x-publishable-api-key: $PUBLISHABLE_KEY" -H "Content-Type: application/json" -d '{}' "$BASE_URL/store/carts")
CART_CODE="${CART_RESPONSE: -3}"

if [ "$CART_CODE" = "200" ] || [ "$CART_CODE" = "201" ]; then
    print_test_result "Cart Creation" "PASS"
    CART_BODY="${CART_RESPONSE%???}"
    CART_ID=$(echo "$CART_BODY" | jq -r '.cart.id // empty')
    
    if [ -n "$CART_ID" ] && [ "$CART_ID" != "null" ]; then
        # Test cart retrieval
        test_endpoint "GET" "$BASE_URL/store/carts/$CART_ID" "x-publishable-api-key: $PUBLISHABLE_KEY" "" "200" "Cart Retrieval"
    fi
else
    print_test_result "Cart Creation" "FAIL" "Expected 200, got $CART_CODE"
fi

echo -e "\n${BLUE}🏷️ Categories API Tests${NC}"
echo "---------------------------"

# Test store categories
test_endpoint "GET" "$BASE_URL/store/product-categories" "x-publishable-api-key: $PUBLISHABLE_KEY" "" "200" "Store Categories List"

echo -e "\n${BLUE}🔒 Multi-Tenant Isolation Tests${NC}"
echo "-----------------------------------"

# Test tenant isolation by trying to access data across tenants
ELECTRONICS_PRODUCTS=$(curl -s -H "Authorization: Bearer $TOKEN" -H "x-tenant-id: tenant-electronics-001" "$BASE_URL/admin/products" | jq -r '.products[0].id // empty')

if [ -n "$ELECTRONICS_PRODUCTS" ] && [ "$ELECTRONICS_PRODUCTS" != "null" ]; then
    # Try to access electronics product from fashion tenant
    CROSS_TENANT_RESPONSE=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" -H "x-tenant-id: tenant-fashion-002" "$BASE_URL/admin/products/$ELECTRONICS_PRODUCTS")
    CROSS_TENANT_CODE="${CROSS_TENANT_RESPONSE: -3}"
    
    if [ "$CROSS_TENANT_CODE" = "404" ]; then
        print_test_result "Cross-Tenant Data Isolation" "PASS"
    else
        print_test_result "Cross-Tenant Data Isolation" "FAIL" "Expected 404, got $CROSS_TENANT_CODE"
    fi
else
    print_test_result "Cross-Tenant Data Isolation" "WARN" "No products found to test isolation"
fi

echo -e "\n${BLUE}⚠️ Error Handling Tests${NC}"
echo "---------------------------"

# Test unauthorized access
UNAUTH_RESPONSE=$(curl -s -w "%{http_code}" "$BASE_URL/admin/products")
UNAUTH_CODE="${UNAUTH_RESPONSE: -3}"

if [ "$UNAUTH_CODE" = "401" ]; then
    print_test_result "Unauthorized Access Rejection" "PASS"
else
    print_test_result "Unauthorized Access Rejection" "FAIL" "Expected 401, got $UNAUTH_CODE"
fi

# Test non-existent resource
NOTFOUND_RESPONSE=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" "$BASE_URL/admin/products/non-existent-id")
NOTFOUND_CODE="${NOTFOUND_RESPONSE: -3}"

if [ "$NOTFOUND_CODE" = "404" ] || [ "$NOTFOUND_CODE" = "500" ]; then
    print_test_result "Non-existent Resource Handling" "PASS"
else
    print_test_result "Non-existent Resource Handling" "FAIL" "Expected 404/500, got $NOTFOUND_CODE"
fi

echo -e "\n${BLUE}📊 Test Summary${NC}"
echo "=================================================="
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! API endpoints are working correctly.${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️ Some tests failed. Please check the issues above.${NC}"
    exit 1
fi
