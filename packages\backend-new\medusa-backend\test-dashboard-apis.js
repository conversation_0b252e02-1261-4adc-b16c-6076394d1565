#!/usr/bin/env node

/**
 * Test script for Dashboard Analytics APIs
 * Tests all the dashboard analytics endpoints to ensure they're working correctly
 */

const http = require('http');

const BASE_URL = 'http://localhost:9000';

// Test endpoints
const endpoints = [
  {
    name: 'Test Dashboard API (7d)',
    url: '/test/dashboard?period=7d',
    method: 'GET',
    requiresAuth: false
  },
  {
    name: 'Test Dashboard API (30d)',
    url: '/test/dashboard?period=30d',
    method: 'GET',
    requiresAuth: false
  },
  {
    name: 'Test Debug API',
    url: '/test/debug',
    method: 'GET',
    requiresAuth: false
  },
  {
    name: 'Admin Dashboard API',
    url: '/admin/analytics/dashboard?period=30d',
    method: 'GET',
    requiresAuth: true
  },
  {
    name: 'Admin KPI API',
    url: '/admin/analytics/kpi?period=30d',
    method: 'GET',
    requiresAuth: true
  },
  {
    name: 'Admin Sales API',
    url: '/admin/analytics/sales?period=30d',
    method: 'GET',
    requiresAuth: true
  },
  {
    name: 'Admin Products API',
    url: '/admin/analytics/products?period=30d',
    method: 'GET',
    requiresAuth: true
  },
  {
    name: 'Admin Inventory API',
    url: '/admin/analytics/inventory',
    method: 'GET',
    requiresAuth: true
  }
];

function makeRequest(endpoint) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 9000,
      path: endpoint.url,
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (endpoint.requiresAuth) {
      options.headers['Authorization'] = 'Bearer test-token';
    }

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testEndpoint(endpoint) {
  console.log(`\n🧪 Testing: ${endpoint.name}`);
  console.log(`   URL: ${endpoint.url}`);
  
  try {
    const response = await makeRequest(endpoint);
    
    if (response.statusCode === 200) {
      console.log(`   ✅ SUCCESS (${response.statusCode})`);
      
      try {
        const jsonData = JSON.parse(response.data);
        if (jsonData.success !== undefined) {
          console.log(`   📊 Success: ${jsonData.success}`);
        }
        if (jsonData.message) {
          console.log(`   💬 Message: ${jsonData.message}`);
        }
        if (jsonData.data && jsonData.data.stats) {
          const stats = jsonData.data.stats;
          console.log(`   📈 Stats: Orders=${stats.totalOrders}, Revenue=${stats.totalRevenue}, Customers=${stats.totalCustomers}`);
        }
      } catch (parseError) {
        console.log(`   📄 Response length: ${response.data.length} characters`);
      }
    } else if (response.statusCode === 401 && endpoint.requiresAuth) {
      console.log(`   🔒 EXPECTED AUTH REQUIRED (${response.statusCode})`);
    } else {
      console.log(`   ❌ FAILED (${response.statusCode})`);
      console.log(`   📄 Response: ${response.data.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`   💥 ERROR: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Starting Dashboard Analytics API Tests');
  console.log('=' .repeat(50));
  
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('✨ Test completed!');
  console.log('\n📝 Summary:');
  console.log('   - Test endpoints (no auth): Should return 200 with real data');
  console.log('   - Admin endpoints (auth required): Should return 401 Unauthorized');
  console.log('   - All endpoints should be accessible and not crash');
}

// Run the tests
runTests().catch(console.error);
