#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9000';

async function getAdminToken() {
  try {
    const response = await axios.post(`${BASE_URL}/auth/user/emailpass`, {
      email: '<EMAIL>',
      password: 'supersecret'
    }, {
      headers: { 'Content-Type': 'application/json' }
    });

    return response.data?.token || null;
  } catch (error) {
    console.log('❌ Failed to get admin token:', error.response?.data || error.message);
    return null;
  }
}

async function testDetailedResponse(endpoint, token) {
  try {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    console.log(`\n🔍 Testing detailed response for: ${endpoint.name}`);
    console.log(`   URL: ${endpoint.url}`);

    const response = await axios.get(`${BASE_URL}${endpoint.url}`, { headers });

    console.log(`   ✅ SUCCESS (${response.status})`);
    console.log('\n📊 DETAILED RESPONSE:');
    console.log('='.repeat(50));
    console.log(JSON.stringify(response.data, null, 2));
    console.log('='.repeat(50));

    return response.data;
  } catch (error) {
    console.log(`   ❌ FAILED (${error.response?.status})`);
    console.log(`   💬 Error: ${error.response?.data?.error || error.message}`);
    return null;
  }
}

async function main() {
  console.log('🚀 Dashboard API Detailed Response Testing');
  console.log('==================================================');

  // Get admin token
  const adminToken = await getAdminToken();
  
  if (!adminToken) {
    console.log('❌ Could not obtain admin token. Exiting...');
    return;
  }

  console.log('✅ Admin token obtained successfully');

  // Test endpoints with detailed responses
  const endpoints = [
    {
      name: 'Test Dashboard API (No Auth)',
      url: '/test/dashboard?period=30d'
    },
    {
      name: 'Admin KPI API',
      url: '/admin/analytics/kpi?period=30d'
    },
    {
      name: 'Admin Dashboard API',
      url: '/admin/analytics/dashboard?period=30d'
    }
  ];

  for (const endpoint of endpoints) {
    await testDetailedResponse(endpoint, adminToken);
    console.log('\n' + '='.repeat(80) + '\n');
  }

  console.log('🎉 Detailed testing completed!');
}

main().catch(console.error);
