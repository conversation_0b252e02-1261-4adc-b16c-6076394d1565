const axios = require('axios');

/**
 * Product Creation API Test Script
 *
 * This script demonstrates the correct way to create products using the Medusa API
 * after fixing the issues identified in the product creation process.
 *
 * Usage: node test-original-payload.js
 */

const testOriginalPayload = async () => {
  try {
    console.log('🔐 Step 1: Authenticating admin user...');

    // First, authenticate to get a token
    const authResponse = await axios.post(
      'http://localhost:9000/auth/user/emailpass',
      {
        email: '<EMAIL>',
        password: 'supersecret',
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Authentication successful!');

    // Extract the token from the response
    const token = authResponse.data.token;

    if (!token) {
      console.error('❌ No token found in auth response');
      return;
    }

    console.log('🚀 Step 2: Testing with original payload structure (with fixes)...');

    // Original payload with fixes applied
    const productData = {
      title: 'product 2',
      handle: 'product-2',
      description: 'test product description',
      status: 'draft',
      // Remove categories for now - they need to exist in the system first
      // categories: [{"id": "pcat_01JZ5E2TWTJPB9CNSYHYJJX934"}],
      // Remove empty collection_id - it causes circular reference issues
      // collection_id: "",
      tags: [],
      // Keep options as they're required
      options: [{ title: 'Variant', values: ['6gb/12gb', '8gb/128gb'] }],
      metadata: {
        additional_data: {
          images: [],
          product_prices: [{ sale_price: 699, original_price: 800 }],
          product_quantity: 50,
          product_inventory_status: 'in_stock',
          product_overview: 'Test Product Overview',
          product_features: 'Test Product Features',
          product_specifications: 'Test Product Specification',
        },
      },
      // Remove empty thumbnail - it can cause issues
      // thumbnail: ""
    };

    console.log('📦 Product data:', JSON.stringify(productData, null, 2));

    const response = await axios.post('http://localhost:9000/admin/products', productData, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    });

    console.log('✅ Success! Product created with original payload structure:');
    console.log('Product ID:', response.data.product.id);
    console.log('Product Title:', response.data.product.title);
    console.log('Product Handle:', response.data.product.handle);
    console.log('Product Status:', response.data.product.status);
    console.log('Product Metadata:', JSON.stringify(response.data.product.metadata, null, 2));
    console.log('Product Options:', JSON.stringify(response.data.product.options, null, 2));
  } catch (error) {
    console.error('❌ Error:');
    console.error('Status:', error.response?.status);
    console.error('Status Text:', error.response?.statusText);
    console.error('Error Data:', JSON.stringify(error.response?.data, null, 2));
    console.error('Full Error:', error.message);
  }
};

testOriginalPayload();
