# Strapi CMS Error Log

This file documents errors encountered during the development and implementation of the Strapi CMS in the ONDC Seller Platform, along with their resolutions.

## [2025-05-19] Port Conflict

### Error
```
[2025-05-19 12:22:13.939] error: The port 1337 is already used by another application.
```

### Resolution
Changed the port in the `.env` file from 1337 to 1339. Also updated the frontend environment variables to match the new port.

## [2025-05-19] TypeScript Compilation Errors

### Error
TypeScript compilation errors related to missing type definitions for content types.

### Resolution
Created and updated type definitions in the following files:
- `/types/generated/contentTypes.d.ts`
- `/types/generated/api-types.d.ts`
- Created `/types/generated/api-types-additional.d.ts` for new content types

## [2025-05-19] Relation Field Issues

### Error
When trying to add a relation field to a collection type in the Strapi admin panel, the dropdown only showed a limited number of collection types instead of all available ones.

### Resolution
1. Fixed incorrect relation definitions in schema files
2. Added missing content types
3. Ensured proper bidirectional relations with correct inversedBy/mappedBy properties
4. Updated TypeScript type definitions

Detailed changes:
- Updated Product schema to include proper Seller relation
- Updated Seller schema to include the inverse Products relation
- Fixed relation types (oneToOne → manyToOne, etc.)
- Created missing content types (Customer, Banner, Page)

## [2025-05-19] API Access Forbidden

### Error
```
{"data":null,"error":{"status":403,"name":"ForbiddenError","message":"Forbidden","details":{}}}
```

### Resolution
Updated permissions in the Strapi admin panel:
1. Went to Settings > Roles
2. Selected the "Public" role
3. Enabled find and findOne permissions for all content types
4. Saved the changes

## [2025-05-19] Media Field Configuration Issues

### Error
Media fields were configured to allow all types of files, which could lead to security issues and inconsistent data.

### Resolution
Updated media field configurations to restrict to appropriate file types:
- For images (logo, banner, product images): restricted to images only
- Changed multiple settings to single where appropriate (logo, banner)

## [2025-05-19] Field Type Issues

### Error
Some fields had incorrect types, such as phone numbers as biginteger and pincodes as integer.

### Resolution
Updated field types to match the requirements:
- Changed phone from biginteger to string
- Changed pincode from integer to string
- Changed product weight from integer to decimal
- Changed order item total from integer to decimal

## [2025-05-18] Database Connection Issues

### Error
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```

### Resolution
1. Ensured PostgreSQL was running
2. Created the database if it didn't exist:
   ```sql
   CREATE DATABASE ondc_seller;
   ```
3. Updated database configuration in `.env` file with correct credentials

## Best Practices to Prevent Future Errors

1. **Schema Validation**: Regularly validate schema definitions to ensure they are correct and consistent
2. **Relation Definitions**: Always define relations on both sides with proper inversedBy/mappedBy properties
3. **TypeScript Types**: Keep TypeScript type definitions up to date with content types
4. **Testing**: Test relation fields in the admin UI after making changes
5. **Documentation**: Maintain comprehensive documentation of content types and relations
6. **Port Configuration**: Use consistent port configuration and document it
7. **Permission Management**: Regularly review and update API permissions
