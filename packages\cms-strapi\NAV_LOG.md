# Strapi CMS Navigation Log

This file documents navigation patterns and page access in the Strapi CMS for the ONDC Seller Platform.

## Admin Panel Navigation

### Content Manager
- **Collection Types**
  - Seller: `/admin/content-manager/collectionType/api::seller.seller`
  - Product Category: `/admin/content-manager/collectionType/api::product-category.product-category`
  - Product: `/admin/content-manager/collectionType/api::product.product`
  - Order: `/admin/content-manager/collectionType/api::order.order`
  - Order Item: `/admin/content-manager/collectionType/api::order-item.order-item`
  - Customer: `/admin/content-manager/collectionType/api::customer.customer`
  - Banner: `/admin/content-manager/collectionType/api::banner.banner`
  - Page: `/admin/content-manager/collectionType/api::page.page`

- **Single Types**
  - About: `/admin/content-manager/singleType/api::about.about`
  - Global: `/admin/content-manager/singleType/api::global.global`

### Content Type Builder
- **Collection Types**
  - Seller: `/admin/plugins/content-type-builder/content-types/api::seller.seller`
  - Product Category: `/admin/plugins/content-type-builder/content-types/api::product-category.product-category`
  - Product: `/admin/plugins/content-type-builder/content-types/api::product.product`
  - Order: `/admin/plugins/content-type-builder/content-types/api::order.order`
  - Order Item: `/admin/plugins/content-type-builder/content-types/api::order-item.order-item`
  - Customer: `/admin/plugins/content-type-builder/content-types/api::customer.customer`
  - Banner: `/admin/plugins/content-type-builder/content-types/api::banner.banner`
  - Page: `/admin/plugins/content-type-builder/content-types/api::page.page`

- **Components**
  - Address: `/admin/plugins/content-type-builder/component-categories/address`
  - SEO: `/admin/plugins/content-type-builder/component-categories/seo`
  - Dimensions: `/admin/plugins/content-type-builder/component-categories/dimensions`
  - Attribute: `/admin/plugins/content-type-builder/component-categories/attribute`

### Settings
- **Global Settings**
  - API Tokens: `/admin/settings/api-tokens`
  - Roles: `/admin/settings/users-permissions/roles`
  - Users: `/admin/settings/users-permissions/users`
  - Media Library: `/admin/settings/media-library`
  - Webhooks: `/admin/settings/webhooks`

## API Endpoints Navigation

### Public Endpoints
- **Sellers**
  - Get all sellers: `/api/sellers`
  - Get a specific seller: `/api/sellers/:id`
  - Get sellers with related products: `/api/sellers?populate=products`

- **Products**
  - Get all products: `/api/products`
  - Get a specific product: `/api/products/:id`
  - Get products with related seller and categories: `/api/products?populate[0]=seller&populate[1]=categories`
  - Get products by seller: `/api/products?filters[seller][id][$eq]=1&populate=*`
  - Get products by category: `/api/products?filters[categories][id][$eq]=1`
  - Get featured products: `/api/products?filters[featured][$eq]=true`

- **Product Categories**
  - Get all product categories: `/api/product-categories`
  - Get a specific product category: `/api/product-categories/:id`
  - Get product categories with related products: `/api/product-categories?populate=products`
  - Get product categories with parent/children hierarchy: `/api/product-categories?populate[0]=parent&populate[1]=children`

- **Banners**
  - Get all banners: `/api/banners`
  - Get a specific banner: `/api/banners/:id`
  - Get active banners: `/api/banners?filters[active][$eq]=true`

- **Pages**
  - Get all pages: `/api/pages`
  - Get a specific page: `/api/pages/:id`
  - Get a page by slug: `/api/pages?filters[slug][$eq]=about-us`

### Protected Endpoints
- **Orders**
  - Get all orders: `/api/orders`
  - Get a specific order: `/api/orders/:id`
  - Get orders with related customer and order items: `/api/orders?populate[0]=customer&populate[1]=order_items`
  - Get orders by customer: `/api/orders?filters[customer][id][$eq]=1`
  - Get orders by status: `/api/orders?filters[order_status][$eq]=Processing`

- **Order Items**
  - Get all order items: `/api/order-items`
  - Get a specific order item: `/api/order-items/:id`
  - Get order items with related product and order: `/api/order-items?populate[0]=product&populate[1]=order`
  - Get order items by order: `/api/order-items?filters[order][id][$eq]=1`

- **Customers**
  - Get all customers: `/api/customers`
  - Get a specific customer: `/api/customers/:id`
  - Get customers with related orders: `/api/customers?populate=orders`

## Navigation Flow for Common Tasks

### Creating a New Product
1. Navigate to Content Manager: `/admin/content-manager`
2. Select Product collection type: `/admin/content-manager/collectionType/api::product.product`
3. Click "Create new entry": `/admin/content-manager/collectionType/api::product.product/create`
4. Fill in product details
5. Save and publish

### Managing Orders
1. Navigate to Content Manager: `/admin/content-manager`
2. Select Order collection type: `/admin/content-manager/collectionType/api::order.order`
3. View list of orders: `/admin/content-manager/collectionType/api::order.order`
4. Click on an order to view details: `/admin/content-manager/collectionType/api::order.order/:id`
5. Update order status
6. Save changes

### Configuring API Permissions
1. Navigate to Settings: `/admin/settings`
2. Select Roles: `/admin/settings/users-permissions/roles`
3. Select Public role: `/admin/settings/users-permissions/roles/1`
4. Configure permissions for each content type
5. Save changes
