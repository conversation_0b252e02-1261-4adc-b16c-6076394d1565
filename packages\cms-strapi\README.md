# ONDC Seller Platform - Strapi CMS

This package contains the Strapi CMS for the ONDC Seller Platform. It provides content management capabilities for the platform, including product management, seller profiles, banners, and more.

## Features

- Content management for the ONDC Seller Platform
- RESTful API for accessing content
- Admin panel for managing content
- Role-based access control
- Media library for managing images and other media
- Customizable content types and components

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database

### Installation

1. Install dependencies:

```bash
npm install
```

2. Configure the database connection in `.env`:

```
DATABASE_CLIENT=postgres
DATABASE_HOST=127.0.0.1
DATABASE_PORT=5432
DATABASE_NAME=ondc_seller
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_SSL=false
```

3. Start the development server:

```bash
npm run develop
```

4. Access the admin panel at http://localhost:1337/admin

## Content Types

The CMS includes the following content types:

- Seller
- Product Category
- Product
- Order
- Order Item
- Customer
- Banner
- Page

See the `content-types.md` file for detailed information about each content type.

## API

The CMS provides a RESTful API for accessing content. The API is available at http://localhost:1337/api.

### Authentication

The API uses JWT authentication. To authenticate, send a POST request to `/api/auth/local` with the following body:

```json
{
  "identifier": "<EMAIL>",
  "password": "your-password"
}
```

The response will include a JWT token that you can use to authenticate subsequent requests by including it in the `Authorization` header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

### Example API Endpoints

- GET `/api/products`: Get all products
- GET `/api/products/:id`: Get a specific product
- GET `/api/sellers`: Get all sellers
- GET `/api/sellers/:id`: Get a specific seller

## Development

### Creating Content Types

You can create content types through the admin panel or by creating schema files in the `src/api` directory.

### Customizing the Admin Panel

You can customize the admin panel by modifying the files in the `src/admin` directory.

## Documentation

### Project Documentation

- `content-types.md`: Detailed information about each content type
- `api-endpoints.md`: Comprehensive list of all available API endpoints
- `sample-data-guide.md`: Step-by-step guide for creating sample data
- `strapi-verification.md`: Verification of the Strapi implementation against specifications

### Logs

- `CHANGELOG.md`: Documents all notable changes to the Strapi CMS implementation
- `ERROR_LOG.md`: Documents errors encountered and their resolutions
- `NAV_LOG.md`: Documents navigation patterns and page access

### Strapi Documentation

For more information about Strapi, check out the [official Strapi documentation](https://docs.strapi.io).
