{"data": [{"id": 2, "documentId": "xcjgtt8scjnbeios0xh73ht9", "name": "Organic Apples", "description": [{"type": "paragraph", "children": [{"text": "Fresh organic apples from the farm", "type": "text"}]}], "short_description": "Organic apples", "price": 150, "sale_price": 120, "sku": "ORG-APL-001", "inventory_quantity": 100, "product_status": "Published", "featured": true, "tags": "Organic, Fruit, Healthy", "weight": 1, "createdAt": "2025-05-19T08:03:20.895Z", "updatedAt": "2025-05-19T08:03:20.895Z", "publishedAt": "2025-05-19T08:03:21.148Z"}, {"id": 4, "documentId": "ibxz1kyhrv2tbccxeutnos8j", "name": "Smartphone X", "description": [{"type": "paragraph", "children": [{"text": "Latest smartphone with advanced features", "type": "text"}]}], "short_description": "Premium smartphone", "price": 50000, "sale_price": 45000, "sku": "TECH-SPX-001", "inventory_quantity": 50, "product_status": "Published", "featured": true, "tags": "Smartphone, Tech, Premium", "weight": 0.2, "createdAt": "2025-05-19T08:16:00.855Z", "updatedAt": "2025-05-19T08:16:03.028Z", "publishedAt": "2025-05-19T08:16:03.054Z"}], "meta": {"pagination": {"page": 1, "pageSize": 25, "pageCount": 1, "total": 2}}}