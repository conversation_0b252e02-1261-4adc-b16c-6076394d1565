/**
 * Bootstrap function to set up initial permissions and data
 */

module.exports = async () => {
  console.log('🚀 Setting up API permissions...');

  try {
    // Get the public role
    const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
      where: { type: 'public' },
    });

    if (!publicRole) {
      console.error('❌ Public role not found');
      return;
    }

    // Set permissions for Page API
    const pagePermissions = ['find', 'findOne'];

    for (const action of pagePermissions) {
      const existingPermission = await strapi
        .query('plugin::users-permissions.permission')
        .findOne({
          where: {
            role: publicRole.id,
            action: `api::page.page.${action}`,
          },
        });

      if (!existingPermission) {
        await strapi.query('plugin::users-permissions.permission').create({
          data: {
            action: `api::page.page.${action}`,
            role: publicRole.id,
            enabled: true,
          },
        });
        console.log(`✅ Page API permission set for ${action}`);
      } else {
        // Update existing permission to be enabled
        await strapi.query('plugin::users-permissions.permission').update({
          where: { id: existingPermission.id },
          data: { enabled: true },
        });
        console.log(`✅ Page API permission updated for ${action}`);
      }
    }

    // Get the authenticated role
    const authenticatedRole = await strapi.query('plugin::users-permissions.role').findOne({
      where: { type: 'authenticated' },
    });

    if (authenticatedRole) {
      // Set full CRUD permissions for authenticated users
      const authenticatedPermissions = ['find', 'findOne', 'create', 'update', 'delete'];

      for (const action of authenticatedPermissions) {
        const existingPermission = await strapi
          .query('plugin::users-permissions.permission')
          .findOne({
            where: {
              role: authenticatedRole.id,
              action: `api::page.page.${action}`,
            },
          });

        if (!existingPermission) {
          await strapi.query('plugin::users-permissions.permission').create({
            data: {
              action: `api::page.page.${action}`,
              role: authenticatedRole.id,
              enabled: true,
            },
          });
          console.log(`✅ Page API permission set for authenticated users: ${action}`);
        } else {
          await strapi.query('plugin::users-permissions.permission').update({
            where: { id: existingPermission.id },
            data: { enabled: true },
          });
          console.log(`✅ Page API permission updated for authenticated users: ${action}`);
        }
      }
    }

    // Set up Store Configuration API permissions
    console.log('🚀 Setting up Store Configuration API permissions...');

    // Set permissions for Store Configuration API (public access for create, find, findOne, update)
    const storeConfigPermissions = ['find', 'findOne', 'create', 'update', 'delete'];

    for (const action of storeConfigPermissions) {
      const existingPermission = await strapi
        .query('plugin::users-permissions.permission')
        .findOne({
          where: {
            role: publicRole.id,
            action: `api::store-configuration.store-configuration.${action}`,
          },
        });

      if (!existingPermission) {
        await strapi.query('plugin::users-permissions.permission').create({
          data: {
            action: `api::store-configuration.store-configuration.${action}`,
            role: publicRole.id,
            enabled: true,
          },
        });
        console.log(`✅ Store Configuration API permission set for ${action}`);
      } else {
        // Update existing permission to be enabled
        await strapi.query('plugin::users-permissions.permission').update({
          where: { id: existingPermission.id },
          data: { enabled: true },
        });
        console.log(`✅ Store Configuration API permission updated for ${action}`);
      }
    }

    // Also set permissions for authenticated users
    if (authenticatedRole) {
      for (const action of storeConfigPermissions) {
        const existingPermission = await strapi
          .query('plugin::users-permissions.permission')
          .findOne({
            where: {
              role: authenticatedRole.id,
              action: `api::store-configuration.store-configuration.${action}`,
            },
          });

        if (!existingPermission) {
          await strapi.query('plugin::users-permissions.permission').create({
            data: {
              action: `api::store-configuration.store-configuration.${action}`,
              role: authenticatedRole.id,
              enabled: true,
            },
          });
          console.log(
            `✅ Store Configuration API permission set for authenticated users: ${action}`
          );
        } else {
          await strapi.query('plugin::users-permissions.permission').update({
            where: { id: existingPermission.id },
            data: { enabled: true },
          });
          console.log(
            `✅ Store Configuration API permission updated for authenticated users: ${action}`
          );
        }
      }
    }

    console.log('🎉 API permissions setup completed!');
  } catch (error) {
    console.error('❌ Error setting up API permissions:', error);
  }
};
