module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS') || [
      'n5zNAhRgtYnUL/GiKsIqqseS/5i8BMw7q3zCOtt53GI=',
      'T2E6kp2cqgq7l2wTEICwU1DwayKVmo9Uq/VA0ZzJZhg=',
      '5HiyEX5CFzotZ7QFAq61XIgAtul4MJm0cuZOjFDOT4A=',
      'd+0hKVwrm55JWnznZVFrmihkz7aceSTh5EJeMgED5is='
    ],
  },
  webhooks: {
    populateRelations: env.bool('WEBHOOKS_POPULATE_RELATIONS', false),
  },
  // Custom MCP server configuration
  mcp: {
    enabled: true,
    port: env.int('MCP_PORT', 3001),
  },
});
