version: '3'

services:
  postgres:
    image: postgres:15-alpine
    container_name: strapi-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: strapi_cms
      POSTGRES_USER: strapi
      POSTGRES_PASSWORD: strapi_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U strapi"]
      interval: 10s
      timeout: 5s
      retries: 5

  strapi:
    image: node:18-alpine
    container_name: strapi-cms
    restart: unless-stopped
    working_dir: /app
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      DATABASE_CLIENT: postgres
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: strapi_cms
      DATABASE_USERNAME: strapi
      DATABASE_PASSWORD: strapi_password
      NODE_ENV: development
      HOST: 0.0.0.0
      PORT: 1337
    ports:
      - "1339:1337"
    depends_on:
      postgres:
        condition: service_healthy
    command: tail -f /dev/null

volumes:
  postgres_data:
    name: strapi-postgres-data 