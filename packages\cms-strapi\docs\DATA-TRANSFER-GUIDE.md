# 🚀 Strapi Data Transfer Guide

Complete guide for accessing and transferring Strapi data to different machines.

## 🎯 Quick Start

### Option 1: Network Access (Easiest)
```bash
cd packages/cms-strapi
node scripts/setup-network-access.js
```
Then access from any device on the same network using the provided IP address.

### Option 2: Export & Import Data
```bash
# On source machine
cd packages/cms-strapi
node scripts/export-data.js

# Copy exports folder to new machine, then:
cd packages/cms-strapi
node scripts/import-data.js
```

### Option 3: Copy Entire Database
```bash
# On source machine
cd packages/cms-strapi
node scripts/copy-database.js

# Copy database-backup folder to new machine and follow instructions
```

## 📋 All Available Scripts

| Script | Purpose | When to Use |
|--------|---------|-------------|
| `data-transfer-manager.js` | Interactive menu for all options | First time users |
| `export-data.js` | Export data to JSON files | Different Strapi versions |
| `import-data.js` | Import data from JSON files | After export |
| `copy-database.js` | Copy entire SQLite database | Identical setups |
| `setup-network-access.js` | Configure network access | Real-time access |

## 🔧 Usage Examples

### Interactive Manager
```bash
cd packages/cms-strapi
node scripts/data-transfer-manager.js
```

### Direct Export
```bash
cd packages/cms-strapi
node scripts/export-data.js
```

### Direct Import
```bash
cd packages/cms-strapi
node scripts/import-data.js
```

### Network Setup
```bash
cd packages/cms-strapi
node scripts/setup-network-access.js
```

## 📁 Generated Files

### After Export
- `exports/complete-export.json` - All data
- `exports/banners.json` - Banner data only
- `exports/categories.json` - Category data only
- `exports/product-categories.json` - Product category data
- `exports/products.json` - Product data only
- `exports/pages.json` - Page data only
- `exports/IMPORT-INSTRUCTIONS.md` - Import guide

### After Database Copy
- `database-backup/.tmp/` - Complete database
- `database-backup/uploads/` - Media files
- `database-backup/RESTORE-INSTRUCTIONS.md` - Restore guide

### After Network Setup
- `NETWORK-ACCESS-GUIDE.md` - Network access guide

## 🌐 Network Access URLs

After running network setup, you'll get URLs like:
- Admin Panel: `http://*************:1337/admin`
- API Base: `http://*************:1337/api`
- Banners: `http://*************:1337/api/banners`

## 🔒 Security Notes

⚠️ **Development Only**: Network access is configured for development environments only.

For production:
- Use HTTPS with SSL certificates
- Configure proper authentication
- Use environment variables
- Implement rate limiting
- Consider VPN or SSH tunneling

## 🐛 Troubleshooting

### Export/Import Issues
- Ensure Strapi is running: `http://localhost:1337`
- Check if collections exist
- Verify network connectivity

### Network Access Issues
- Check firewall settings
- Ensure devices are on same network
- Try disabling firewall temporarily
- Verify Strapi is accessible locally first

### Database Copy Issues
- Ensure Strapi is stopped before copying
- Check file permissions
- Verify paths are correct

## 📞 Support

If you encounter issues:
1. Check the generated instruction files
2. Verify Strapi is running properly
3. Test with simple API calls first
4. Check console logs for errors

## 🎉 Success Verification

After any transfer method:
1. ✅ Admin panel loads: `http://localhost:1337/admin`
2. ✅ API responds: `http://localhost:1337/api`
3. ✅ Data is present: `http://localhost:1337/api/banners`
4. ✅ Collections show correct counts
5. ✅ Relationships work properly

---

**Happy data transferring! 🚀**
