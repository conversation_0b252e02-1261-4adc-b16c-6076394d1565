# 🔐 Strapi CMS Permission Setup Guide

## **URGENT: Configure API Permissions First**

Before populating data, we need to configure API permissions for public access to categories and products.

### **Step 1: Access Strapi Admin Panel**

1. **Open Strapi Admin Panel**: http://localhost:1339/admin
2. **Login** with your admin credentials

### **Step 2: Navigate to Permissions**

1. Click on **Settings** (gear icon) in the left sidebar
2. Under **Users & Permissions Plugin**, click on **Roles**
3. Click on the **Public** role

### **Step 3: Configure Content Type Permissions**

In the **Permissions** section, you'll see a list of content types. Configure the following:

#### **📂 PRODUCT-CATEGORY Permissions**
- ✅ **find** - Allows `GET /api/product-categories`
- ✅ **findOne** - Allows `GET /api/product-categories/:id`

#### **🛍️ PRODUCT Permissions**
- ✅ **find** - Allows `GET /api/products`
- ✅ **findOne** - Allows `GET /api/products/:id`

#### **🏷️ CATEGORY Permissions (if exists)**
- ✅ **find** - Allows `GET /api/categories`
- ✅ **findOne** - Allows `GET /api/categories/:id`

### **Step 4: Save Configuration**

1. Click the **Save** button at the top right
2. Wait for the "Saved successfully" confirmation

### **Step 5: Verify Permissions**

Run the permission test script:
```bash
node scripts/configure-permissions.js
```

You should see ✅ success messages for all endpoints.

---

## **Next Steps After Permission Configuration**

Once permissions are configured, proceed with data population:

### **Option 1: Automated Data Population**
```bash
# Run the comprehensive data population script
node scripts/populate-comprehensive-data.js
```

### **Option 2: Manual Data Entry**
Use the Strapi admin panel to manually add:
- 8+ main categories with subcategories
- 10-15 products per category
- Proper category-product relationships

---

## **Expected Results**

After successful configuration and data population:

- ✅ **8+ Categories**: Electronics, Fashion, Home & Garden, Health & Beauty, Sports & Outdoors, Books & Media, Automotive, Food & Beverages
- ✅ **80+ Products**: 10-15 products per category with realistic data
- ✅ **API Access**: Public endpoints working without authentication
- ✅ **Relationships**: Products properly linked to categories

---

## **Troubleshooting**

### **403 Forbidden Errors**
- Ensure Public role has correct permissions
- Check that content types exist
- Verify Strapi server is running on port 1339

### **Empty API Responses**
- Check if data exists in Strapi admin panel
- Verify content is published (not draft)
- Ensure relationships are properly configured

### **Permission Changes Not Applied**
- Clear browser cache
- Restart Strapi server if needed
- Check for any error messages in Strapi logs

---

## **Quick Verification Commands**

```bash
# Test API permissions
curl http://localhost:1339/api/product-categories

# Test with population
curl http://localhost:1339/api/product-categories?populate=*

# Test products
curl http://localhost:1339/api/products?populate=*

# Test featured categories
curl "http://localhost:1339/api/product-categories?filters[featured][\$eq]=true"
```

All commands should return JSON data instead of 403 errors.
