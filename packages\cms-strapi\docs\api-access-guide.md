# Accessing Strapi API Endpoints - Comprehensive Guide

This guide provides detailed instructions on how to access the Strapi API endpoints for the ONDC Seller Platform.

## 1. Configuring API Permissions

Before you can access the API endpoints, you need to configure the permissions in the Strapi admin panel:

1. **Access the Roles Settings**:
   - Go to Settings > Roles
   - Click on the "Public" role

2. **Configure Permissions for Each Content Type**:
   - For each content type (Seller, Product, Product Category, etc.), enable the following permissions:
     - find (GET) - to get all entries
     - findOne (GET) - to get a specific entry
   - Make sure to check both the "Enabled" checkbox and the specific action checkboxes

3. **Save the Changes**:
   - Click the "Save" button at the top right
   - Wait for the confirmation message

4. **Restart the Strapi Server**:
   - Stop the current Strapi server (Ctrl+C in the terminal)
   - Start it again with `npm run develop`
   - This ensures that the permission changes take effect

## 2. Creating an API Token (Alternative Method)

If you're still having issues with public access, you can create an API token:

1. **Access the API Tokens Settings**:
   - Go to Settings > API Tokens
   - Click "Create new API Token"

2. **Configure the Token**:
   - Name: `full-access-token`
   - Description: `Token with full access to all API endpoints`
   - Token duration: Unlimited
   - Token type: Full access

3. **Save the Token**:
   - Click "Save"
   - Copy the generated token (you won't be able to see it again)

4. **Use the Token in API Requests**:
   - Include the token in the Authorization header:
   ```
   Authorization: Bearer YOUR_API_TOKEN
   ```

## 3. Testing API Endpoints with API Token

Here's an updated test script that uses an API token:

```javascript
const axios = require('axios');
const fs = require('fs');

// Configuration
const BASE_URL = 'http://localhost:1339';
const API_TOKEN = 'YOUR_API_TOKEN'; // Replace with your actual token
const OUTPUT_DIR = './api-responses';

// Create output directory if it doesn't exist
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR);
}

// Helper function to make API requests
async function makeRequest(endpoint, params = {}) {
  try {
    const url = `${BASE_URL}${endpoint}`;
    console.log(`Making request to: ${url}`);
    
    const headers = {
      Authorization: `Bearer ${API_TOKEN}`
    };
    
    const response = await axios.get(url, { params, headers });
    return response.data;
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error.response ? error.response.data : error.message);
    return null;
  }
}

// Helper function to save response to file
function saveResponse(endpoint, data) {
  if (!data) return;
  
  const filename = endpoint.replace(/\//g, '_').replace(/\?/g, '_').replace(/=/g, '_');
  const filePath = `${OUTPUT_DIR}/${filename}.json`;
  
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  console.log(`Response saved to ${filePath}`);
}

// Test all endpoints
async function testAllEndpoints() {
  console.log('Testing API endpoints...');
  
  // Test endpoints
  const endpoints = [
    // Sellers
    '/api/sellers',
    '/api/sellers?populate=products',
    
    // Product Categories
    '/api/product-categories',
    '/api/product-categories?populate[0]=parent&populate[1]=children',
    
    // Products
    '/api/products',
    '/api/products?populate[0]=seller&populate[1]=categories',
    '/api/products?populate=*',
    '/api/products?filters[featured][$eq]=true',
    
    // Customers
    '/api/customers',
    '/api/customers?populate=orders',
    
    // Orders
    '/api/orders',
    '/api/orders?populate[0]=customer&populate[1]=order_items',
    
    // Order Items
    '/api/order-items',
    '/api/order-items?populate[0]=product&populate[1]=order',
    
    // Banners
    '/api/banners',
    '/api/banners?filters[active][$eq]=true&populate=image',
    
    // Pages
    '/api/pages',
    '/api/pages?filters[slug][$eq]=about-us'
  ];
  
  // Test each endpoint
  for (const endpoint of endpoints) {
    const data = await makeRequest(endpoint);
    saveResponse(endpoint, data);
  }
  
  console.log('All endpoints tested!');
}

// Run the tests
testAllEndpoints();
```

Save this script as `test-api-with-token.js` and run it with:
```
node test-api-with-token.js
```

## 4. Using the API in Your Frontend Application

Once you've confirmed that the API endpoints are working, you can use them in your frontend application:

### React Example:

```jsx
import { useState, useEffect } from 'react';
import axios from 'axios';

function ProductList() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await axios.get('http://localhost:1339/api/products?populate=*');
        setProducts(response.data.data);
        setLoading(false);
      } catch (error) {
        setError('Error fetching products');
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div>
      <h1>Products</h1>
      <div className="product-grid">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <h2>{product.attributes.name}</h2>
            <p>{product.attributes.short_description}</p>
            <p>Price: ${product.attributes.price}</p>
            <p>Sale Price: ${product.attributes.sale_price}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

export default ProductList;
```

### Next.js Example:

```jsx
// pages/products.js
import { useState, useEffect } from 'react';
import axios from 'axios';

export default function Products({ products }) {
  return (
    <div>
      <h1>Products</h1>
      <div className="product-grid">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <h2>{product.attributes.name}</h2>
            <p>{product.attributes.short_description}</p>
            <p>Price: ${product.attributes.price}</p>
            <p>Sale Price: ${product.attributes.sale_price}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

export async function getServerSideProps() {
  try {
    const response = await axios.get('http://localhost:1339/api/products?populate=*');
    return {
      props: {
        products: response.data.data
      }
    };
  } catch (error) {
    return {
      props: {
        products: []
      }
    };
  }
}
```

## 5. Understanding the Strapi API Response Format

Strapi API responses follow a specific format:

```json
{
  "data": [
    {
      "id": 1,
      "attributes": {
        "name": "Product Name",
        "description": "Product Description",
        "price": 100,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z",
        "publishedAt": "2023-01-01T00:00:00.000Z",
        "seller": {
          "data": {
            "id": 1,
            "attributes": {
              "name": "Seller Name",
              // other seller attributes
            }
          }
        }
      }
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 1
    }
  }
}
```

When working with this data in your frontend:
- Use `data` to access the array of items
- Use `data[0].attributes` to access the attributes of an item
- Use `data[0].attributes.seller.data` to access related items
- Use `meta.pagination` for pagination information

## 6. Troubleshooting API Access Issues

If you're still having issues accessing the API:

1. **Check Server Logs**:
   - Look at the Strapi server logs for any error messages
   - Pay attention to permission-related errors

2. **Verify Content Exists**:
   - Make sure you've actually created and published the content
   - Unpublished content won't be accessible via the API

3. **Check API URL Format**:
   - Make sure you're using the correct URL format
   - Collection types use plural names (e.g., `/api/products`)
   - Single types use singular names (e.g., `/api/homepage`)

4. **Test with Postman or Insomnia**:
   - Use a tool like Postman or Insomnia to test the API endpoints
   - This can help identify if the issue is with your code or the API itself

5. **Check CORS Settings**:
   - If you're accessing the API from a frontend application, make sure CORS is properly configured
   - You may need to add your frontend URL to the allowed origins in the Strapi settings

## 7. Additional Resources

- [Strapi REST API Documentation](https://docs.strapi.io/dev-docs/api/rest)
- [Strapi Authentication Documentation](https://docs.strapi.io/dev-docs/plugins/users-permissions)
- [Strapi Permissions Guide](https://docs.strapi.io/user-docs/users-roles-permissions)
- [Strapi API Tokens Guide](https://docs.strapi.io/user-docs/settings/API-tokens)
