# Strapi API Endpoints for ONDC Seller Platform

This document lists all available API endpoints for accessing content from the Strapi CMS.

## Authentication

### Get JWT Token

```
POST /api/auth/local
```

Request body:
```json
{
  "identifier": "<EMAIL>",
  "password": "your-password"
}
```

Response:
```json
{
  "jwt": "your-jwt-token",
  "user": {
    "id": 1,
    "username": "your-username",
    "email": "<EMAIL>",
    ...
  }
}
```

Use the JWT token in subsequent requests by including it in the Authorization header:
```
Authorization: Bearer your-jwt-token
```

## Content Type Endpoints

### Sellers

#### Get all sellers
```
GET /api/sellers
```

#### Get a specific seller
```
GET /api/sellers/:id
```

#### Get sellers with related products
```
GET /api/sellers?populate=products
```

### Product Categories

#### Get all product categories
```
GET /api/product-categories
```

#### Get a specific product category
```
GET /api/product-categories/:id
```

#### Get product categories with related products
```
GET /api/product-categories?populate=products
```

#### Get product categories with parent/children hierarchy
```
GET /api/product-categories?populate[0]=parent&populate[1]=children
```

### Products

#### Get all products
```
GET /api/products
```

#### Get a specific product
```
GET /api/products/:id
```

#### Get products with related seller and categories
```
GET /api/products?populate[0]=seller&populate[1]=categories
```

#### Get products with all relations and components
```
GET /api/products?populate=*
```

#### Get products by seller
```
GET /api/products?filters[seller][id][$eq]=1
```

#### Get products by category
```
GET /api/products?filters[categories][id][$eq]=1
```

#### Get featured products
```
GET /api/products?filters[featured][$eq]=true
```

### Customers

#### Get all customers
```
GET /api/customers
```

#### Get a specific customer
```
GET /api/customers/:id
```

#### Get customers with related orders
```
GET /api/customers?populate=orders
```

### Orders

#### Get all orders
```
GET /api/orders
```

#### Get a specific order
```
GET /api/orders/:id
```

#### Get orders with related customer and order items
```
GET /api/orders?populate[0]=customer&populate[1]=order_items
```

#### Get orders by customer
```
GET /api/orders?filters[customer][id][$eq]=1
```

#### Get orders by status
```
GET /api/orders?filters[order_status][$eq]=Processing
```

### Order Items

#### Get all order items
```
GET /api/order-items
```

#### Get a specific order item
```
GET /api/order-items/:id
```

#### Get order items with related product and order
```
GET /api/order-items?populate[0]=product&populate[1]=order
```

#### Get order items by order
```
GET /api/order-items?filters[order][id][$eq]=1
```

### Banners

#### Get all banners
```
GET /api/banners
```

#### Get a specific banner
```
GET /api/banners/:id
```

#### Get active banners
```
GET /api/banners?filters[active][$eq]=true
```

### Pages

#### Get all pages
```
GET /api/pages
```

#### Get a specific page
```
GET /api/pages/:id
```

#### Get a page by slug
```
GET /api/pages?filters[slug][$eq]=about-us
```

## Example API Requests

### Get products by seller with all relations

```
GET /api/products?filters[seller][id][$eq]=1&populate=*
```

### Get orders by customer with order items and products

```
GET /api/orders?filters[customer][id][$eq]=1&populate[0]=customer&populate[1]=order_items.product
```

### Get active banners with images

```
GET /api/banners?filters[active][$eq]=true&populate=image
```

### Get product categories with hierarchical structure

```
GET /api/product-categories?populate[0]=parent&populate[1]=children&populate[2]=products
```

## Pagination, Sorting, and Filtering

### Pagination

```
GET /api/products?pagination[page]=1&pagination[pageSize]=10
```

### Sorting

```
GET /api/products?sort=price:asc
```

### Filtering

```
GET /api/products?filters[price][$gt]=100&filters[price][$lt]=500
```

## Authentication Requirements

All API endpoints require authentication except for those specifically configured for public access. By default, the following endpoints are typically public:

- GET requests for published content
- POST requests to /api/auth/local

For all other endpoints, you need to include the JWT token in the Authorization header:

```
Authorization: Bearer your-jwt-token
```

To configure public access to specific endpoints, you need to update the permissions in the Strapi admin panel:

1. Go to Settings > Roles
2. Select the "Public" role
3. Configure permissions for each content type
4. Save the changes

## API Documentation

For more detailed information about the Strapi API, refer to the official documentation:

- [Strapi REST API Documentation](https://docs.strapi.io/dev-docs/api/rest)
- [Strapi Authentication Documentation](https://docs.strapi.io/dev-docs/plugins/users-permissions)
