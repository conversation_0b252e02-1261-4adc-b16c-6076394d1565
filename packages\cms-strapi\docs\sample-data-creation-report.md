# Sample Data Creation Report

This document summarizes the process of creating sample entries in the Strapi CMS for the ONDC Seller Platform.

## Sample Entries Created

### 1. Seller Entries

Created 2 seller entries:
- **Organic Farms**
  - Description: Premium organic produce directly from farms
  - Email: <EMAIL>
  - Phone: **********
  - Address: 123 Farm Road
  - City: Bangalore
  - State: Karnataka
  - Pincode: 560001
  - ONDC Seller ID: ONDC-ORG-001
  - Seller Status: Active

- **Tech Gadgets**
  - Description: Latest technology gadgets and accessories
  - Email: <EMAIL>
  - Phone: **********
  - Address: 456 Tech Park
  - City: Mumbai
  - State: Maharashtra
  - Pincode: 400001
  - ONDC Seller ID: ONDC-TECH-002
  - Seller Status: Active

### 2. Product Category Entries

Created 3 product category entries:
- **Electronics**
  - Description: Electronic devices and gadgets
  - Slug: electronics
  - Featured: Yes

- **Smartphones**
  - Description: Mobile phones and accessories
  - Slug: smartphones
  - Parent Category: Electronics
  - Featured: Yes

- **Organic Food**
  - Description: Fresh organic produce
  - Slug: organic-food
  - Featured: Yes

### 3. Product Entries

Created 5 product entries:
- **Organic Apples**
  - Description: Fresh organic apples from the farm
  - Short Description: Organic apples
  - Price: 150
  - Sale Price: 120
  - SKU: ORG-APL-001
  - Inventory Quantity: 100
  - Categories: Organic Food
  - Seller: Organic Farms
  - Product Status: Published
  - Featured: Yes
  - Tags: Organic, Fruit, Healthy
  - Weight: 1.0
  - Dimensions: Length: 10, Width: 10, Height: 10, Unit: cm
  - Attributes: Color: Red, Variety: Fuji

- **Smartphone X**
  - Description: Latest smartphone with advanced features
  - Short Description: Premium smartphone
  - Price: 50000
  - Sale Price: 45000
  - SKU: TECH-SPX-001
  - Inventory Quantity: 50
  - Categories: Smartphones
  - Seller: Tech Gadgets
  - Product Status: Published
  - Featured: Yes
  - Tags: Smartphone, Tech, Premium
  - Weight: 0.2
  - Dimensions: Length: 15, Width: 7, Height: 1, Unit: cm
  - Attributes: Color: Black, RAM: 8GB, Storage: 128GB

- **Organic Bananas**
  - Description: Fresh organic bananas from the farm
  - Short Description: Organic bananas
  - Price: 80
  - Sale Price: 70
  - SKU: ORG-BAN-001
  - Inventory Quantity: 150
  - Categories: Organic Food
  - Seller: Organic Farms
  - Product Status: Published
  - Featured: Yes
  - Tags: Organic, Fruit, Healthy
  - Weight: 0.5
  - Dimensions: Length: 20, Width: 5, Height: 5, Unit: cm
  - Attributes: Color: Yellow, Variety: Cavendish

- **Wireless Earbuds**
  - Description: High-quality wireless earbuds with noise cancellation
  - Short Description: Wireless earbuds
  - Price: 8000
  - Sale Price: 6500
  - SKU: TECH-EAR-001
  - Inventory Quantity: 75
  - Categories: Electronics
  - Seller: Tech Gadgets
  - Product Status: Published
  - Featured: Yes
  - Tags: Audio, Tech, Wireless
  - Weight: 0.05
  - Dimensions: Length: 5, Width: 5, Height: 3, Unit: cm
  - Attributes: Color: White, Battery Life: 8 hours, Noise Cancellation: Yes

- **Organic Vegetables Basket**
  - Description: Assorted organic vegetables freshly harvested
  - Short Description: Organic vegetables basket
  - Price: 350
  - Sale Price: 300
  - SKU: ORG-VEG-001
  - Inventory Quantity: 50
  - Categories: Organic Food
  - Seller: Organic Farms
  - Product Status: Published
  - Featured: Yes
  - Tags: Organic, Vegetables, Healthy
  - Weight: 3.0
  - Dimensions: Length: 30, Width: 20, Height: 15, Unit: cm
  - Attributes: Items: 10, Seasonal: Yes

### 4. Customer Entries

Created 3 customer entries:
- **John Doe**
  - Email: <EMAIL>
  - Phone: **********
  - Address: 123 Main St, Bangalore, Karnataka, 560001, India

- **Jane Smith**
  - Email: <EMAIL>
  - Phone: **********
  - Address: 456 Park Avenue, Mumbai, Maharashtra, 400001, India

- **Raj Kumar**
  - Email: <EMAIL>
  - Phone: **********
  - Address: 789 Lake View, Delhi, Delhi, 110001, India

### 5. Order Entries

Created 3 order entries:
- **ORD-001**
  - Customer: John Doe
  - Total Amount: 1200
  - Order Status: Processing
  - Payment Status: Paid
  - Order Date: Current date
  - Delivery Date: 3 days from now
  - Notes: Special delivery instructions

- **ORD-002**
  - Customer: Jane Smith
  - Total Amount: 45000
  - Order Status: Shipped
  - Payment Status: Paid
  - Order Date: Current date
  - Delivery Date: 2 days from now
  - Notes: Handle with care

- **ORD-003**
  - Customer: Raj Kumar
  - Total Amount: 6500
  - Order Status: Pending
  - Payment Status: Pending
  - Order Date: Current date
  - Delivery Date: 5 days from now
  - Notes: Call before delivery

### 6. Order Item Entries

Created 3 order item entries:
- **Order Item 1**
  - Product: Organic Apples
  - Quantity: 10
  - Price: 120
  - Total: 1200
  - Order: ORD-001

- **Order Item 2**
  - Product: Smartphone X
  - Quantity: 1
  - Price: 45000
  - Total: 45000
  - Order: ORD-002

- **Order Item 3**
  - Product: Wireless Earbuds
  - Quantity: 1
  - Price: 6500
  - Total: 6500
  - Order: ORD-003

### 7. Banner Entries

Created 3 banner entries:
- **Summer Sale**
  - Subtitle: Up to 50% off on all products
  - Link: /sale
  - Start Date: Current date
  - End Date: 30 days from now
  - Active: Yes
  - Position: 1

- **New Arrivals**
  - Subtitle: Check out our latest products
  - Link: /new-arrivals
  - Start Date: Current date
  - End Date: 45 days from now
  - Active: Yes
  - Position: 2

- **Organic Food Festival**
  - Subtitle: Fresh and healthy organic produce
  - Link: /organic-food
  - Start Date: Current date
  - End Date: 15 days from now
  - Active: Yes
  - Position: 3

### 8. Page Entries

Created 3 page entries:
- **About Us**
  - Slug: about-us
  - Content: Information about the company
  - SEO: Meta Title, Meta Description, Keywords
  - Status: Published

- **Terms of Service**
  - Slug: terms-of-service
  - Content: Terms of service information
  - SEO: Meta Title, Meta Description, Keywords
  - Status: Published

- **Privacy Policy**
  - Slug: privacy-policy
  - Content: Privacy policy information
  - SEO: Meta Title, Meta Description, Keywords
  - Status: Published

## API Access Issues

We encountered issues when trying to access the API endpoints:

1. **Forbidden Error (403)**:
   - When trying to access `/api/sellers` and other endpoints, we received a "Forbidden" error.
   - We attempted to configure permissions for the Public role to allow access to all endpoints, but the issue persisted.

2. **Authentication Issues**:
   - We created an API token with full access permissions, but still encountered authentication issues.
   - We also tried to create a user account and get a JWT token, but encountered validation errors.

## Possible Solutions

1. **Check Strapi Configuration**:
   - Verify that the Strapi server is properly configured to allow public access to API endpoints.
   - Check if there are any middleware or plugins that might be blocking access.

2. **Restart Strapi Server**:
   - Sometimes, permission changes require a server restart to take effect.

3. **Create a User with API Access**:
   - Create a user with the appropriate role and permissions.
   - Use the user's credentials to authenticate and access the API.

4. **Check API Token Configuration**:
   - Ensure that the API token is properly configured with the correct permissions.
   - Verify that the token is being sent correctly in the Authorization header.

## Conclusion

We successfully created all the sample entries in the Strapi CMS as specified in the sample-data-guide.md file. However, we encountered issues when trying to access the API endpoints. Further investigation is needed to resolve these issues.
