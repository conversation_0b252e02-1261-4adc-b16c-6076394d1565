# Creating Sample Data in Strapi CMS for ONDC Seller Platform

This guide provides step-by-step instructions for creating sample data in the Strapi CMS for the ONDC Seller Platform.

## Prerequisites

1. Ensure the Strapi server is running:
   ```bash
   cd packages/cms-strapi
   npm run develop
   ```

2. Access the Strapi admin panel at http://localhost:1339/admin

## 1. Configure API Permissions

Before creating content, we need to configure permissions to allow public access to the API:

1. Go to Settings > Roles
2. Click on "Public"
3. In the Permissions section, expand each content type and enable the following permissions:
   - find (GET)
   - findOne (GET)
4. Click "Save"

## 2. Creating Sample Entries

### 2.1 Seller Entries

1. Navigate to Content Manager > Seller
2. Click "Create new entry"
3. Fill in the following details for the first seller:
   - Name: Organic Farms
   - Description: Premium organic produce directly from farms
   - Logo: Upload a logo image
   - Banner: Upload a banner image
   - Email: <EMAIL>
   - Phone: 9876543210
   - Address: 123 Farm Road
   - City: Bangalore
   - State: Karnataka
   - Pincode: 560001
   - ONDC Seller ID: ONDC-ORG-001
   - Seller Status: Active
4. Click "Save" and then "Publish"

5. Create a second seller with the following details:
   - Name: Tech Gadgets
   - Description: Latest technology gadgets and accessories
   - Logo: Upload a logo image
   - Banner: Upload a banner image
   - Email: <EMAIL>
   - Phone: 8765432109
   - Address: 456 Tech Park
   - City: Mumbai
   - State: Maharashtra
   - Pincode: 400001
   - ONDC Seller ID: ONDC-TECH-002
   - Seller Status: Active
6. Click "Save" and then "Publish"

### 2.2 Product Category Entries

1. Navigate to Content Manager > Product Category
2. Click "Create new entry"
3. Fill in the following details for the first category:
   - Name: Electronics
   - Description: Electronic devices and gadgets
   - Image: Upload a category image
   - Slug: electronics
   - Featured: Yes
4. Click "Save" and then "Publish"

5. Create a second category with the following details:
   - Name: Smartphones
   - Description: Mobile phones and accessories
   - Image: Upload a category image
   - Slug: smartphones
   - Parent Category: Electronics
   - Featured: Yes
6. Click "Save" and then "Publish"

7. Create a third category with the following details:
   - Name: Organic Food
   - Description: Fresh organic produce
   - Image: Upload a category image
   - Slug: organic-food
   - Featured: Yes
8. Click "Save" and then "Publish"

### 2.3 Product Entries

1. Navigate to Content Manager > Product
2. Click "Create new entry"
3. Fill in the following details for the first product:
   - Name: Organic Apples
   - Description: Fresh organic apples from the farm
   - Short Description: Organic apples
   - Price: 150
   - Sale Price: 120
   - SKU: ORG-APL-001
   - Inventory Quantity: 100
   - Images: Upload product images
   - Categories: Organic Food
   - Seller: Organic Farms
   - Product Status: Published
   - Featured: Yes
   - Tags: Organic, Fruit, Healthy
   - Weight: 1.0
   - Dimensions: Length: 10, Width: 10, Height: 10, Unit: cm
   - Attributes: Add attributes like "Color: Red", "Variety: Fuji"
4. Click "Save" and then "Publish"

5. Create a second product:
   - Name: Smartphone X
   - Description: Latest smartphone with advanced features
   - Short Description: Premium smartphone
   - Price: 50000
   - Sale Price: 45000
   - SKU: TECH-SPX-001
   - Inventory Quantity: 50
   - Images: Upload product images
   - Categories: Smartphones
   - Seller: Tech Gadgets
   - Product Status: Published
   - Featured: Yes
   - Tags: Smartphone, Tech, Premium
   - Weight: 0.2
   - Dimensions: Length: 15, Width: 7, Height: 1, Unit: cm
   - Attributes: Add attributes like "Color: Black", "RAM: 8GB", "Storage: 128GB"
6. Click "Save" and then "Publish"

7. Create three more products with appropriate details, ensuring they're linked to the correct sellers and categories.

### 2.4 Customer Entries

1. Navigate to Content Manager > Customer
2. Click "Create new entry"
3. Fill in the following details for the first customer:
   - First Name: John
   - Last Name: Doe
   - Email: <EMAIL>
   - Phone: 9876543210
   - Addresses: Add address with Street: 123 Main St, City: Bangalore, State: Karnataka, Pincode: 560001, Country: India
4. Click "Save" and then "Publish"

5. Create two more customer entries:
   - Customer 2: Jane Smith (<EMAIL>)
   - Customer 3: Raj Kumar (<EMAIL>)

### 2.5 Order Entries

1. Navigate to Content Manager > Order
2. Click "Create new entry"
3. Fill in the following details for the first order:
   - Order Number: ORD-001
   - Customer: John Doe
   - Total Amount: 1200
   - Order Status: Processing
   - Payment Status: Paid
   - Shipping Address: Add address component
   - Billing Address: Add address component
   - Order Date: Select current date
   - Delivery Date: Select date 3 days from now
   - Notes: Special delivery instructions
4. Click "Save" and then "Publish"

5. Create two more order entries with different statuses and customers.

### 2.6 Order Item Entries

1. Navigate to Content Manager > Order Item
2. Click "Create new entry"
3. Fill in the following details for the first order item:
   - Product: Organic Apples
   - Quantity: 10
   - Price: 120
   - Total: 1200
   - Order: ORD-001
4. Click "Save" and then "Publish"

5. Create more order item entries for each order.

### 2.7 Banner Entries

1. Navigate to Content Manager > Banner
2. Click "Create new entry"
3. Fill in the following details for the first banner:
   - Title: Summer Sale
   - Subtitle: Up to 50% off on all products
   - Image: Upload a banner image
   - Link: /sale
   - Start Date: Current date
   - End Date: 30 days from now
   - Active: Yes
   - Position: 1
4. Click "Save" and then "Publish"

5. Create two more banner entries with different titles and images.

### 2.8 Page Entries

1. Navigate to Content Manager > Page
2. Click "Create new entry"
3. Fill in the following details for the first page:
   - Title: About Us
   - Slug: about-us
   - Content: Write about the company
   - SEO: Add SEO component with appropriate meta title, description, and keywords
   - Featured Image: Upload an image
   - Status: Published
4. Click "Save" and then "Publish"

5. Create two more pages:
   - Terms of Service (slug: terms-of-service)
   - Privacy Policy (slug: privacy-policy)

## 3. Testing API Endpoints

After creating all the sample data, you can test the API endpoints using curl or a tool like Postman.

### 3.1 Get all sellers

```bash
curl -X GET http://localhost:1339/api/sellers
```

### 3.2 Get a specific seller with related products

```bash
curl -X GET "http://localhost:1339/api/sellers/1?populate=products"
```

### 3.3 Get all products with related seller and categories

```bash
curl -X GET "http://localhost:1339/api/products?populate[0]=seller&populate[1]=categories"
```

### 3.4 Get products by seller

```bash
curl -X GET "http://localhost:1339/api/products?filters[seller][id][$eq]=1&populate=*"
```

### 3.5 Get orders by customer

```bash
curl -X GET "http://localhost:1339/api/orders?filters[customer][id][$eq]=1&populate[0]=customer&populate[1]=order_items.product"
```

## 4. Authentication for API Access

For protected endpoints, you need to authenticate using JWT tokens:

1. Create a user in the Strapi admin panel (Settings > Users & Permissions > Users)
2. Get a JWT token:

```bash
curl -X POST http://localhost:1339/api/auth/local \
  -H "Content-Type: application/json" \
  -d '{"identifier": "<EMAIL>", "password": "your-password"}'
```

3. Use the JWT token in subsequent requests:

```bash
curl -X GET http://localhost:1339/api/sellers \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 5. Conclusion

You have now created sample data for all content types in the Strapi CMS for the ONDC Seller Platform. This data can be used for testing the frontend integration and API functionality.

For more information about the available API endpoints, refer to the `api-endpoints.md` file.
