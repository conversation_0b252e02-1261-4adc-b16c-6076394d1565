# Strapi API Endpoints Documentation for ONDC Seller Platform

This document provides a comprehensive overview of all Strapi API endpoints available in the ONDC Seller Platform, indicating which ones are currently used and which are unused.

## Table of Contents

1. [Content-Type APIs](#1-content-type-apis)
2. [Users & Permissions APIs](#2-users--permissions-apis)
3. [Upload APIs](#3-upload-apis)
4. [GraphQL APIs](#4-graphql-apis)
5. [Admin Endpoints](#5-admin-endpoints)
6. [API Usage Status](#6-api-usage-status)

## 1. Content-Type APIs

For every Collection Type (e.g., `foo`), the following endpoints are available:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/foos` | List all items | Used |
| GET    | `/api/foos/:id` | Retrieve one item | Used |
| POST   | `/api/foos` | Create a new item | Unused |
| PUT    | `/api/foos/:id` | Replace an item | Unused |
| PATCH  | `/api/foos/:id` | Update an item | Unused |
| DELETE | `/api/foos/:id` | Delete an item | Unused |

For every Single Type (e.g., `bar`), the following endpoints are available:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/bar` | Retrieve the item | Used |
| PUT    | `/api/bar` | Replace the item | Unused |
| PATCH  | `/api/bar` | Update the item | Unused |

## 2. Users & Permissions APIs

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| POST   | `/api/auth/local` | Login | Used |
| POST   | `/api/auth/local/register` | Signup | Unused |
| POST   | `/api/auth/forgot-password` | Request password reset | Unused |
| POST   | `/api/auth/reset-password` | Perform password reset | Unused |
| GET    | `/api/users` | List users | Unused |
| GET    | `/api/users/:id` | Get user | Unused |
| PUT    | `/api/users/:id` | Update user | Unused |
| DELETE | `/api/users/:id` | Delete user | Unused |
| GET    | `/api/users/me` | Current user | Used |
| GET    | `/api/users-permissions/roles` | List roles | Unused |

## 3. Upload APIs

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/upload/files` | List files | Unused |
| GET    | `/api/upload/files/:id` | Get one file's metadata | Unused |
| POST   | `/api/upload` | Upload single file | Unused |
| POST   | `/api/upload/multiple` | Upload multiple files | Unused |
| DELETE | `/api/upload/files/:id` | Delete file | Unused |

## 4. GraphQL APIs

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| POST   | `/graphql` | Run queries & mutations | Unused |
| GET    | `/graphql` | GraphiQL GUI (dev mode) | Unused |

## 5. Admin Endpoints

These all require an admin JWT:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/admin` | Strapi dashboard | Unused |
| GET    | `/admin/content-type-builder/content-types` | List content types | Unused |
| GET    | `/admin/content-manager/content-types` | List content types | Unused |
| GET    | `/admin/content-manager/collection-types/:uid` | List collection types | Unused |
| GET    | `/admin/content-manager/single-types/:uid` | List single types | Unused |

## 6. API Usage Status

### Currently Used Endpoints

The ONDC Seller Platform currently uses the following Strapi API endpoints:

1. **Content Retrieval**
   - `GET /api/banners` - Used to fetch banner data for the homepage carousel
   - `GET /api/pages/:slug` - Used to fetch page content
   - `GET /api/sellers` - Used to fetch seller information
   - `GET /api/product-categories` - Used to fetch product categories for navigation

2. **Authentication**
   - `POST /api/auth/local` - Used for user authentication
   - `GET /api/users/me` - Used to fetch current user information

### Unused Endpoints

The following endpoints are available but not currently used in the application:

1. **Content Management**
   - All POST, PUT, PATCH, DELETE endpoints for content types
   - These would be used for content management from the frontend

2. **File Management**
   - All upload API endpoints
   - These would be used for file uploads from the frontend

3. **GraphQL**
   - All GraphQL endpoints
   - These would provide an alternative to REST for data fetching

4. **Admin**
   - All admin endpoints
   - These are used by the Strapi admin panel, not the frontend application

## 7. Implementation Details

### Authentication and API Token

The application uses a Strapi API token for authentication:

```javascript
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN || 'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

const strapiAPI = axios.create({
  baseURL: STRAPI_BASE_URL,
  headers: {
    Authorization: `Bearer ${STRAPI_API_TOKEN}`
  }
});
```

### Multi-Tenant Support

The application supports multi-tenancy by passing the tenant ID as a query parameter:

```javascript
const url = new URL(`${STRAPI_URL}/api${endpoint}`);
if (tenantId) {
  url.searchParams.append('tenant', tenantId);
}
```

## 8. Recommendations for Future Development

1. **Implement Content Management**
   - Use the POST, PUT, PATCH, DELETE endpoints to allow content management from the frontend
   - This would enable sellers to manage their own content

2. **Implement File Uploads**
   - Use the upload API endpoints to allow file uploads from the frontend
   - This would enable sellers to upload product images and other media

3. **Consider GraphQL**
   - Evaluate using GraphQL for more efficient data fetching
   - This would reduce over-fetching and under-fetching of data

4. **Enhance Security**
   - Implement proper user authentication and authorization
   - Use role-based access control to restrict access to certain endpoints
