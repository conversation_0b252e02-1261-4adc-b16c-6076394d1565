# Strapi API Usage Recommendations for ONDC Seller Platform

This document provides recommendations for improving the usage of Strapi API endpoints in the ONDC Seller Platform.

## Current Status

The ONDC Seller Platform currently uses Strapi primarily for content retrieval, focusing on:

1. **Banner Management**: Fetching banners for the homepage carousel
2. **Page Content**: Retrieving static page content
3. **Seller Information**: Getting seller profiles
4. **Product Categories**: Fetching categories for navigation

However, many powerful Strapi features remain unused, including:

1. **Content Management**: Creating, updating, and deleting content
2. **File Management**: Uploading and managing media files
3. **GraphQL**: Using GraphQL for more efficient data fetching
4. **User Management**: Managing users and permissions

## Recommendations

### 1. Implement Content Management Features

**Current Implementation**:
```javascript
// Only read operations are implemented
export async function getCollection(collection: string, params = {}) {
  const queryString = new URLSearchParams({
    populate: '*',
    ...params,
  }).toString();

  return fetchAPI(`/${collection}?${queryString}`);
}
```

**Recommended Implementation**:
```javascript
// Add create, update, and delete operations
export async function createItem(collection: string, data = {}) {
  return fetchAPI(`/${collection}`, {
    method: 'POST',
    body: JSON.stringify({ data }),
  });
}

export async function updateItem(collection: string, id: string | number, data = {}) {
  return fetchAPI(`/${collection}/${id}`, {
    method: 'PUT',
    body: JSON.stringify({ data }),
  });
}

export async function deleteItem(collection: string, id: string | number) {
  return fetchAPI(`/${collection}/${id}`, {
    method: 'DELETE',
  });
}
```

### 2. Implement Media Upload Functionality

**Current Implementation**:
No implementation for media uploads.

**Recommended Implementation**:
```javascript
export async function uploadMedia(file: File, info = {}) {
  const formData = new FormData();
  formData.append('files', file);
  
  if (info) {
    formData.append('fileInfo', JSON.stringify(info));
  }
  
  return fetch(`${STRAPI_URL}/api/upload`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${STRAPI_API_TOKEN}`,
    },
    body: formData,
  }).then(response => response.json());
}
```

### 3. Implement GraphQL for Efficient Data Fetching

**Current Implementation**:
Using REST API with multiple requests and potential over-fetching.

**Recommended Implementation**:
```javascript
export async function fetchGraphQL(query: string, variables = {}) {
  const response = await fetch(`${STRAPI_URL}/graphql`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${STRAPI_API_TOKEN}`,
    },
    body: JSON.stringify({
      query,
      variables,
    }),
  });

  return response.json();
}

// Example query for products with categories and seller
export async function getProductsWithRelations() {
  const query = `
    query {
      products {
        data {
          id
          attributes {
            name
            description
            price
            categories {
              data {
                id
                attributes {
                  name
                }
              }
            }
            seller {
              data {
                id
                attributes {
                  name
                }
              }
            }
          }
        }
      }
    }
  `;

  return fetchGraphQL(query);
}
```

### 4. Implement Proper Error Handling and Fallbacks

**Current Implementation**:
```javascript
try {
  const data = await getCollection('banners', {
    'filters[active][$eq]': true,
    populate: 'image',
    ...params
  });

  if (!data || !data.data) {
    return getFallbackBanners();
  }
  
  // Process banner images
  // ...
} catch (error) {
  console.error('Error fetching banners:', error);
  return getFallbackBanners();
}
```

**Recommended Implementation**:
```javascript
try {
  const data = await getCollection('banners', {
    'filters[active][$eq]': true,
    populate: 'image',
    ...params
  });

  if (!data || !data.data) {
    console.warn('No banner data received from Strapi, using fallback');
    return getFallbackBanners();
  }
  
  // Process banner images
  // ...
} catch (error) {
  // More detailed error handling
  if (error.response) {
    console.error(`Strapi API error (${error.response.status}):`, error.response.data);
    
    // Handle specific error codes
    if (error.response.status === 401) {
      // Handle authentication error
      refreshToken();
    } else if (error.response.status === 403) {
      // Handle permission error
      console.error('Permission denied to access banners');
    }
  } else if (error.request) {
    console.error('No response received from Strapi:', error.request);
    // Handle network errors
  } else {
    console.error('Error setting up request:', error.message);
  }
  
  return getFallbackBanners();
}
```

### 5. Implement Caching for Frequently Accessed Data

**Current Implementation**:
No caching implementation.

**Recommended Implementation**:
```javascript
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

export async function getCachedCollection(collection: string, params = {}) {
  const cacheKey = `${collection}:${JSON.stringify(params)}`;
  const cachedData = cache.get(cacheKey);
  
  if (cachedData && Date.now() - cachedData.timestamp < CACHE_TTL) {
    console.log(`Using cached data for ${cacheKey}`);
    return cachedData.data;
  }
  
  const data = await getCollection(collection, params);
  
  if (data) {
    cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });
  }
  
  return data;
}
```

### 6. Implement Pagination for Large Collections

**Current Implementation**:
No pagination implementation.

**Recommended Implementation**:
```javascript
export async function getPaginatedCollection(collection: string, page = 1, pageSize = 10, params = {}) {
  const queryString = new URLSearchParams({
    populate: '*',
    'pagination[page]': page.toString(),
    'pagination[pageSize]': pageSize.toString(),
    ...params,
  }).toString();

  return fetchAPI(`/${collection}?${queryString}`);
}
```

### 7. Implement Proper Authentication and Authorization

**Current Implementation**:
Using a static API token for all requests.

**Recommended Implementation**:
```javascript
// Store token in memory or secure storage
let authToken = null;
let tokenExpiry = null;

export async function login(identifier: string, password: string) {
  const response = await fetch(`${STRAPI_URL}/api/auth/local`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      identifier,
      password,
    }),
  });

  const data = await response.json();
  
  if (data.jwt) {
    authToken = data.jwt;
    // Set token expiry (e.g., 30 days from now)
    tokenExpiry = Date.now() + 30 * 24 * 60 * 60 * 1000;
    
    // Store in secure storage
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('auth_token', authToken);
      sessionStorage.setItem('token_expiry', tokenExpiry.toString());
    }
    
    return data.user;
  }
  
  throw new Error(data.error?.message || 'Authentication failed');
}

export function getAuthHeaders() {
  // Check if token is expired
  if (tokenExpiry && Date.now() > tokenExpiry) {
    // Token expired, clear it
    authToken = null;
    tokenExpiry = null;
    
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem('auth_token');
      sessionStorage.removeItem('token_expiry');
    }
  }
  
  // Use auth token if available, otherwise use API token
  if (authToken) {
    return {
      Authorization: `Bearer ${authToken}`,
    };
  }
  
  return {
    Authorization: `Bearer ${STRAPI_API_TOKEN}`,
  };
}
```

## Implementation Priority

1. **High Priority**
   - Implement proper error handling and fallbacks
   - Implement caching for frequently accessed data
   - Implement pagination for large collections

2. **Medium Priority**
   - Implement content management features
   - Implement proper authentication and authorization
   - Implement media upload functionality

3. **Low Priority**
   - Implement GraphQL for efficient data fetching

## Conclusion

By implementing these recommendations, the ONDC Seller Platform can fully leverage the capabilities of Strapi CMS, providing a more robust, efficient, and user-friendly experience for both end-users and content managers.
