# Strapi Content Types and API Endpoints for ONDC Seller Platform

This document provides detailed information about each content type in the ONDC Seller Platform's Strapi CMS and their corresponding API endpoints.

## Table of Contents

1. [Seller](#1-seller)
2. [Product Category](#2-product-category)
3. [Product](#3-product)
4. [Order](#4-order)
5. [Order Item](#5-order-item)
6. [Customer](#6-customer)
7. [Banner](#7-banner)
8. [Page](#8-page)
9. [Global](#9-global)
10. [About](#10-about)
11. [Article](#11-article)
12. [Category](#12-category)

## 1. Seller

**Collection Type**: `seller`

**Fields**:
- `name` (String)
- `description` (Blocks)
- `logo` (Media)
- `banner` (Media)
- `email` (Email)
- `phone` (String)
- `address` (Text)
- `city` (String)
- `state` (String)
- `pincode` (String)
- `ondc_seller_id` (String)
- `seller_status` (Enumeration: Active, Inactive, Pending)
- `products` (Relation to Product)

**API Endpoints**:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/sellers` | List all sellers | Used |
| GET    | `/api/sellers/:id` | Retrieve one seller | Used |
| GET    | `/api/sellers?populate=products` | List sellers with their products | Used |
| POST   | `/api/sellers` | Create a new seller | Unused |
| PUT    | `/api/sellers/:id` | Replace a seller | Unused |
| PATCH  | `/api/sellers/:id` | Update a seller | Unused |
| DELETE | `/api/sellers/:id` | Delete a seller | Unused |

## 2. Product Category

**Collection Type**: `product-category`

**Fields**:
- `name` (String)
- `description` (Rich Text)
- `image` (Media)
- `slug` (UID)
- `parent` (Relation to Product Category)
- `featured` (Boolean)
- `products` (Relation to Product)

**API Endpoints**:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/product-categories` | List all categories | Used |
| GET    | `/api/product-categories/:id` | Retrieve one category | Used |
| GET    | `/api/product-categories?populate[0]=parent&populate[1]=children` | List categories with relations | Used |
| POST   | `/api/product-categories` | Create a new category | Unused |
| PUT    | `/api/product-categories/:id` | Replace a category | Unused |
| PATCH  | `/api/product-categories/:id` | Update a category | Unused |
| DELETE | `/api/product-categories/:id` | Delete a category | Unused |

## 3. Product

**Collection Type**: `product`

**Fields**:
- `name` (String)
- `description` (Blocks)
- `short_description` (String)
- `price` (Decimal)
- `sale_price` (Decimal)
- `sku` (String)
- `inventory_quantity` (Integer)
- `images` (Media - Multiple)
- `categories` (Relation to Product Category - Multiple)
- `seller` (Relation to Seller)
- `product_status` (Enumeration: Draft, Published, Out of Stock)
- `featured` (Boolean)
- `tags` (Text - Multiple)
- `weight` (Number)
- `dimensions` (Component: Length, Width, Height)
- `attributes` (Component: Name, Value - Multiple)

**API Endpoints**:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/products` | List all products | Used |
| GET    | `/api/products/:id` | Retrieve one product | Used |
| GET    | `/api/products?populate=*` | List products with all relations | Used |
| GET    | `/api/products?filters[featured][$eq]=true` | List featured products | Used |
| GET    | `/api/products?populate[0]=seller&populate[1]=categories` | List products with specific relations | Used |
| POST   | `/api/products` | Create a new product | Unused |
| PUT    | `/api/products/:id` | Replace a product | Unused |
| PATCH  | `/api/products/:id` | Update a product | Unused |
| DELETE | `/api/products/:id` | Delete a product | Unused |

## 4. Order

**Collection Type**: `order`

**Fields**:
- `order_number` (String)
- `customer` (Relation to Customer)
- `order_items` (Relation to Order Item - Multiple)
- `total_amount` (Number)
- `status` (Enumeration: Pending, Processing, Shipped, Delivered, Cancelled)
- `payment_status` (Enumeration: Pending, Paid, Failed, Refunded)
- `shipping_address` (Component: Address)
- `billing_address` (Component: Address)
- `order_date` (DateTime)
- `delivery_date` (DateTime)
- `notes` (Text)

**API Endpoints**:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/orders` | List all orders | Unused |
| GET    | `/api/orders/:id` | Retrieve one order | Unused |
| GET    | `/api/orders?populate[0]=customer&populate[1]=order_items` | List orders with relations | Unused |
| POST   | `/api/orders` | Create a new order | Unused |
| PUT    | `/api/orders/:id` | Replace an order | Unused |
| PATCH  | `/api/orders/:id` | Update an order | Unused |
| DELETE | `/api/orders/:id` | Delete an order | Unused |

## 5. Order Item

**Collection Type**: `order-item`

**Fields**:
- `product` (Relation to Product)
- `quantity` (Number)
- `price` (Number)
- `total` (Number)
- `order` (Relation to Order)

**API Endpoints**:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/order-items` | List all order items | Unused |
| GET    | `/api/order-items/:id` | Retrieve one order item | Unused |
| GET    | `/api/order-items?populate[0]=product&populate[1]=order` | List order items with relations | Unused |
| POST   | `/api/order-items` | Create a new order item | Unused |
| PUT    | `/api/order-items/:id` | Replace an order item | Unused |
| PATCH  | `/api/order-items/:id` | Update an order item | Unused |
| DELETE | `/api/order-items/:id` | Delete an order item | Unused |

## 6. Customer

**Collection Type**: `customer`

**Fields**:
- `first_name` (String)
- `last_name` (String)
- `email` (Email)
- `phone` (String)
- `addresses` (Component: Address - Multiple)
- `orders` (Relation to Order - Multiple)

**API Endpoints**:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/customers` | List all customers | Unused |
| GET    | `/api/customers/:id` | Retrieve one customer | Unused |
| GET    | `/api/customers?populate=orders` | List customers with their orders | Unused |
| POST   | `/api/customers` | Create a new customer | Unused |
| PUT    | `/api/customers/:id` | Replace a customer | Unused |
| PATCH  | `/api/customers/:id` | Update a customer | Unused |
| DELETE | `/api/customers/:id` | Delete a customer | Unused |

## 7. Banner

**Collection Type**: `banner`

**Fields**:
- `title` (String)
- `subtitle` (String)
- `image` (Media)
- `link` (String)
- `start_date` (DateTime)
- `end_date` (DateTime)
- `active` (Boolean)
- `position` (Number)

**API Endpoints**:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/banners` | List all banners | Used |
| GET    | `/api/banners/:id` | Retrieve one banner | Used |
| GET    | `/api/banners?filters[active][$eq]=true&populate=image` | List active banners with images | Used |
| POST   | `/api/banners` | Create a new banner | Unused |
| PUT    | `/api/banners/:id` | Replace a banner | Unused |
| PATCH  | `/api/banners/:id` | Update a banner | Unused |
| DELETE | `/api/banners/:id` | Delete a banner | Unused |

## 8. Page

**Collection Type**: `page`

**Fields**:
- `title` (String)
- `slug` (UID)
- `content` (Rich Text)
- `featured_image` (Media)
- `seo` (Component: SEO)

**API Endpoints**:

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/api/pages` | List all pages | Used |
| GET    | `/api/pages/:id` | Retrieve one page | Used |
| GET    | `/api/pages?filters[slug][$eq]=about` | Get page by slug | Used |
| POST   | `/api/pages` | Create a new page | Unused |
| PUT    | `/api/pages/:id` | Replace a page | Unused |
| PATCH  | `/api/pages/:id` | Update a page | Unused |
| DELETE | `/api/pages/:id` | Delete a page | Unused |
