{"name": "@ondc-seller/cms-strapi", "version": "0.1.0", "private": true, "description": "Strapi CMS for ONDC Seller Platform", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "mcp:start": "node src/mcp-server.js", "dev:with-mcp": "concurrently \"npm run develop\" \"npm run mcp:start\"", "seed:example": "node ./scripts/seed.js", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "setup": "powershell -ExecutionPolicy Bypass -File ./scripts/setup.ps1", "start-db": "./scripts/start-db.sh"}, "dependencies": {"@strapi/plugin-cloud": "5.13.0", "@strapi/plugin-users-permissions": "5.13.0", "@strapi/strapi": "5.13.0", "axios": "^1.10.0", "better-sqlite3": "^11.10.0", "concurrently": "^8.2.2", "fs-extra": "^10.0.0", "mime-types": "^2.1.27", "pg": "^8.8.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "sharp": "^0.34.2", "styled-components": "^6.1.19", "ws": "^8.18.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "5ffa327f7059c1227f93e7e3f6b12f5264290b6b3c6e00e786c8ae77ed5d593b"}}