/**
 * Add specific categories and subcategories as requested
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('already exists')) {
      console.log(`⚠️ Item already exists, skipping...`);
      return null;
    }
    console.error(`Error with ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Main Categories to create
const mainCategories = [
  { name: 'Electronics', slug: 'electronics' },
  { name: 'Fashion', slug: 'fashion' },
  { name: 'Home & Garden', slug: 'home-garden' },
  { name: 'Sports & Fitness', slug: 'sports-fitness' },
  { name: 'Books & Media', slug: 'books-media' },
  { name: 'Health & Beauty', slug: 'health-beauty' },
  { name: 'Automotive', slug: 'automotive' },
  { name: 'Toys & Games', slug: 'toys-games' }
];

// Subcategories with their parent category
const subCategories = [
  { name: 'Smartphones', slug: 'smartphones', parentCategory: 'Electronics' },
  { name: 'Laptops', slug: 'laptops', parentCategory: 'Electronics' },
  { name: "Men's Clothing", slug: 'mens-clothing', parentCategory: 'Fashion' },
  { name: "Women's Clothing", slug: 'womens-clothing', parentCategory: 'Fashion' },
  { name: 'Furniture', slug: 'furniture', parentCategory: 'Home & Garden' },
  { name: 'Kitchen', slug: 'kitchen', parentCategory: 'Home & Garden' }
];

async function addSpecificCategories() {
  console.log('🎯 Adding specific categories and subcategories to Strapi CMS...');
  console.log('📋 This will only add the requested items without affecting existing data');
  
  try {
    // Step 1: Create main categories in Categories collection
    console.log('\n🏗️ Creating main categories in Categories collection...');
    const createdCategories = {};
    
    for (const category of mainCategories) {
      try {
        const categoryData = {
          name: category.name,
          slug: category.slug,
          description: `${category.name} category`,
          short_description: `Browse ${category.name.toLowerCase()} products`,
          featured: false,
          active: true,
          sort_order: 0
        };

        const result = await strapiRequest('/categories', 'POST', categoryData);
        if (result) {
          createdCategories[category.name] = result.data;
          console.log(`✅ Created category: ${category.name}`);
        } else {
          console.log(`ℹ️ Category already exists: ${category.name}`);
          // Try to find existing category
          const existing = await strapiRequest(`/categories?filters[slug][$eq]=${category.slug}`);
          if (existing.data && existing.data.length > 0) {
            createdCategories[category.name] = existing.data[0];
          }
        }
      } catch (error) {
        console.log(`⚠️ Could not create category ${category.name}, might already exist`);
      }
    }

    // Step 2: Create subcategories in Product Categories collection
    console.log('\n🔗 Creating subcategories in Product Categories collection...');
    
    for (const subCategory of subCategories) {
      try {
        const parentCategory = createdCategories[subCategory.parentCategory];
        
        const subCategoryData = {
          name: subCategory.name,
          slug: subCategory.slug,
          description: `${subCategory.name} subcategory under ${subCategory.parentCategory}`,
          short_description: `Browse ${subCategory.name.toLowerCase()}`,
          featured: false,
          active: true,
          sort_order: 0,
          category: parentCategory ? parentCategory.id : null
        };

        const result = await strapiRequest('/product-categories', 'POST', subCategoryData);
        if (result) {
          console.log(`✅ Created subcategory: ${subCategory.name} (under ${subCategory.parentCategory})`);
        } else {
          console.log(`ℹ️ Subcategory already exists: ${subCategory.name}`);
        }
      } catch (error) {
        console.log(`⚠️ Could not create subcategory ${subCategory.name}, might already exist`);
      }
    }

    console.log('\n🎉 Categories and subcategories addition completed!');
    console.log('\n📊 Summary:');
    console.log(`- Main Categories: ${mainCategories.length} items`);
    console.log(`- Subcategories: ${subCategories.length} items`);
    console.log('\n🌐 Verification:');
    console.log('- Categories: http://localhost:1337/api/categories');
    console.log('- Product Categories: http://localhost:1337/api/product-categories');
    console.log('- Admin Panel: http://localhost:1337/admin');
    
  } catch (error) {
    console.error('❌ Error during category addition:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  addSpecificCategories();
}

module.exports = { addSpecificCategories };
