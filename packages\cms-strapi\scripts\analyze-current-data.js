/**
 * Analyze Current Data Structure
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function analyzeCurrentData() {
  console.log('🔍 ANALYZING CURRENT DATA STRUCTURE');
  console.log('=' .repeat(60));
  
  try {
    // Get Categories
    console.log('\n📁 CATEGORIES COLLECTION:');
    const categoriesResponse = await axios.get(`${API_BASE}/categories`);
    const categories = categoriesResponse.data.data || [];
    console.log(`Found ${categories.length} categories`);
    
    categories.forEach((cat, index) => {
      console.log(`${index + 1}. ${cat.name} (ID: ${cat.id}, DocumentID: ${cat.documentId})`);
    });
    
    // Get Product Categories
    console.log('\n📦 PRODUCT CATEGORIES COLLECTION:');
    const productCategoriesResponse = await axios.get(`${API_BASE}/product-categories`);
    const productCategories = productCategoriesResponse.data.data || [];
    console.log(`Found ${productCategories.length} product categories`);
    
    // Analyze by category_type
    const mainCategories = productCategories.filter(pc => pc.category_type === 'main' || !pc.isSubcategory);
    const subCategories = productCategories.filter(pc => pc.category_type === 'sub' || pc.isSubcategory);
    
    console.log(`\n📊 BREAKDOWN:`);
    console.log(`• Main categories in Product Categories: ${mainCategories.length}`);
    console.log(`• Subcategories in Product Categories: ${subCategories.length}`);
    
    if (mainCategories.length > 0) {
      console.log('\n🏷️ MAIN CATEGORIES IN PRODUCT CATEGORIES (TO BE REMOVED):');
      mainCategories.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.name} (ID: ${cat.id}, isSubcategory: ${cat.isSubcategory})`);
      });
    }
    
    if (subCategories.length > 0) {
      console.log('\n🔗 SUBCATEGORIES (TO BE KEPT AND LINKED):');
      subCategories.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.name} (ID: ${cat.id}, isSubcategory: ${cat.isSubcategory})`);
      });
    }
    
    // Get Products
    console.log('\n🛍️ PRODUCTS COLLECTION:');
    const productsResponse = await axios.get(`${API_BASE}/products`);
    const products = productsResponse.data.data || [];
    console.log(`Found ${products.length} products`);
    
    console.log('\n✅ Analysis completed successfully!');
    
    return {
      categories,
      productCategories,
      mainCategories,
      subCategories,
      products
    };
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    return null;
  }
}

analyzeCurrentData();
