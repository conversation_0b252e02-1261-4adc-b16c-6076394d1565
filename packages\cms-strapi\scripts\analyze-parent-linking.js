/**
 * Analyze Current Parent Linking Status
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      details: error.response?.data
    };
  }
}

async function analyzeParentLinking() {
  console.log('🔍 ANALYZING PARENT LINKING STATUS');
  console.log('=' .repeat(60));
  
  try {
    // Get Categories collection
    console.log('\n📁 CATEGORIES COLLECTION:');
    const categoriesResult = await strapiRequest('/categories');
    
    if (categoriesResult.success) {
      const categories = categoriesResult.data.data || [];
      console.log(`Found ${categories.length} main categories:`);
      
      categories.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.name} (ID: ${cat.id}, DocumentID: ${cat.documentId})`);
      });
    } else {
      console.log('❌ Failed to fetch categories');
      return;
    }
    
    // Get Product Categories collection
    console.log('\n📦 PRODUCT CATEGORIES COLLECTION:');
    const productCategoriesResult = await strapiRequest('/product-categories');
    
    if (productCategoriesResult.success) {
      const productCategories = productCategoriesResult.data.data || [];
      console.log(`Found ${productCategories.length} product categories:`);
      
      const withParent = [];
      const withoutParent = [];
      const withCategoryLink = [];
      const withoutCategoryLink = [];
      
      productCategories.forEach((pc, index) => {
        console.log(`${index + 1}. ${pc.name} (isSubcategory: ${pc.isSubcategory})`);
        console.log(`   Parent: None (not populated)`);
        console.log(`   Category Link: None (not populated)`);

        // Since we're not populating, we'll check for parent/category existence differently
        withoutParent.push(pc);
        withoutCategoryLink.push(pc);
      });
      
      console.log('\n📊 SUMMARY:');
      console.log(`• Product categories with parent: ${withParent.length}`);
      console.log(`• Product categories without parent: ${withoutParent.length}`);
      console.log(`• Product categories with category link: ${withCategoryLink.length}`);
      console.log(`• Product categories without category link: ${withoutCategoryLink.length}`);
      
      if (withoutParent.length > 0) {
        console.log('\n⚠️ PRODUCT CATEGORIES WITHOUT PARENT:');
        withoutParent.forEach((pc, index) => {
          console.log(`${index + 1}. ${pc.name} (isSubcategory: ${pc.isSubcategory})`);
        });
      }
      
      if (withoutCategoryLink.length > 0) {
        console.log('\n⚠️ PRODUCT CATEGORIES WITHOUT CATEGORY LINK:');
        withoutCategoryLink.forEach((pc, index) => {
          console.log(`${index + 1}. ${pc.name} (isSubcategory: ${pc.isSubcategory})`);
        });
      }
      
    } else {
      console.log('❌ Failed to fetch product categories');
    }
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
  }
}

analyzeParentLinking();
