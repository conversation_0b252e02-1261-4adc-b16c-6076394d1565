/**
 * Clean Duplicate Banners from Strapi
 * This script identifies and removes duplicate banners based on title and content
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      details: error.response?.data
    };
  }
}

async function cleanDuplicateBanners() {
  console.log('🧹 CLEANING DUPLICATE BANNERS FROM STRAPI');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Get all banners
    console.log('\n📋 Fetching all banners...');
    const bannersResult = await strapiRequest('/banners');
    
    if (!bannersResult.success) {
      throw new Error('Failed to fetch banners');
    }
    
    const banners = bannersResult.data.data || [];
    console.log(`Found ${banners.length} total banners`);
    
    // Step 2: Group banners by title to identify duplicates
    console.log('\n🔍 Analyzing for duplicates...');
    const bannerGroups = {};
    
    banners.forEach(banner => {
      const title = banner.title;
      if (!bannerGroups[title]) {
        bannerGroups[title] = [];
      }
      bannerGroups[title].push(banner);
    });
    
    // Step 3: Identify duplicates
    const duplicateGroups = {};
    const uniqueBanners = [];
    let totalDuplicates = 0;
    
    Object.entries(bannerGroups).forEach(([title, group]) => {
      if (group.length > 1) {
        duplicateGroups[title] = group;
        totalDuplicates += group.length - 1; // Keep one, remove others
        
        // Keep the oldest banner (lowest ID)
        const sortedGroup = group.sort((a, b) => a.id - b.id);
        uniqueBanners.push(sortedGroup[0]);
        
        console.log(`📊 "${title}": ${group.length} duplicates found`);
        group.forEach((banner, index) => {
          console.log(`   ${index === 0 ? '✅ KEEP' : '❌ DELETE'} ID: ${banner.id} (Created: ${banner.createdAt})`);
        });
      } else {
        uniqueBanners.push(group[0]);
        console.log(`✅ "${title}": Unique banner`);
      }
    });
    
    console.log(`\n📊 Summary:`);
    console.log(`   • Total banners: ${banners.length}`);
    console.log(`   • Unique banners: ${uniqueBanners.length}`);
    console.log(`   • Duplicates to remove: ${totalDuplicates}`);
    
    if (totalDuplicates === 0) {
      console.log('\n🎉 No duplicates found! All banners are unique.');
      return;
    }
    
    // Step 4: Remove duplicates
    console.log('\n🗑️ Removing duplicate banners...');
    let removedCount = 0;
    let errorCount = 0;
    
    for (const [title, group] of Object.entries(duplicateGroups)) {
      // Skip the first banner (keep it), remove the rest
      const bannersToRemove = group.slice(1);
      
      for (const banner of bannersToRemove) {
        console.log(`Removing duplicate: "${title}" (ID: ${banner.id})`);
        
        const deleteResult = await strapiRequest(`/banners/${banner.documentId}`, 'DELETE');
        
        if (deleteResult.success) {
          console.log(`   ✅ Successfully removed banner ID: ${banner.id}`);
          removedCount++;
        } else {
          console.log(`   ❌ Failed to remove banner ID: ${banner.id}: ${deleteResult.error}`);
          errorCount++;
        }
        
        // Wait between deletions to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    // Step 5: Verify cleanup
    console.log('\n🔍 Verifying cleanup...');
    const verifyResult = await strapiRequest('/banners');
    
    if (verifyResult.success) {
      const remainingBanners = verifyResult.data.data || [];
      console.log(`✅ Verification complete: ${remainingBanners.length} banners remaining`);
      
      // List remaining banners
      console.log('\n📋 Remaining banners:');
      remainingBanners.forEach((banner, index) => {
        console.log(`${index + 1}. "${banner.title}" (ID: ${banner.id}, Position: ${banner.position})`);
      });
    }
    
    // Final summary
    console.log('\n✅ CLEANUP COMPLETED!');
    console.log('=' .repeat(60));
    console.log(`📊 Results:`);
    console.log(`   • Duplicates removed: ${removedCount}`);
    console.log(`   • Errors: ${errorCount}`);
    console.log(`   • Final banner count: ${uniqueBanners.length}`);
    
    if (removedCount > 0) {
      console.log('\n🎉 DUPLICATE BANNERS SUCCESSFULLY REMOVED!');
      console.log('\n📋 Next Steps:');
      console.log('   1. Refresh the frontend to see the updated banner count');
      console.log('   2. Verify banners display correctly');
      console.log('   3. Check admin panel for clean banner list');
    }
    
    console.log('\n🌐 Verification URLs:');
    console.log('   • Frontend: http://localhost:3000');
    console.log('   • API: http://localhost:3000/api/banners');
    console.log('   • Admin Panel: http://localhost:1337/admin');
    
  } catch (error) {
    console.error('❌ CLEANUP FAILED:', error.message);
  }
}

// Run the cleanup
if (require.main === module) {
  cleanDuplicateBanners();
}

module.exports = { cleanDuplicateBanners };
