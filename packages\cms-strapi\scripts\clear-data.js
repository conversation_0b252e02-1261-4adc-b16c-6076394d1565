/**
 * Clear existing data from Strapi CMS
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error with ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

async function clearData() {
  console.log('🧹 Clearing existing data from Strapi CMS...');
  
  try {
    // Clear products first (they have relationships)
    console.log('\n🗑️ Clearing products...');
    const products = await strapiRequest('/products');
    if (products.data && products.data.length > 0) {
      for (const product of products.data) {
        try {
          await strapiRequest(`/products/${product.id}`, 'DELETE');
          console.log(`✅ Deleted product: ${product.attributes?.name || product.name || product.id}`);
        } catch (error) {
          console.log(`⚠️ Could not delete product ${product.id}`);
        }
      }
    }

    // Clear categories
    console.log('\n🗑️ Clearing categories...');
    const categories = await strapiRequest('/product-categories');
    if (categories.data && categories.data.length > 0) {
      for (const category of categories.data) {
        try {
          await strapiRequest(`/product-categories/${category.id}`, 'DELETE');
          console.log(`✅ Deleted category: ${category.attributes?.name || category.name || category.id}`);
        } catch (error) {
          console.log(`⚠️ Could not delete category ${category.id}`);
        }
      }
    }

    // Clear banners
    console.log('\n🗑️ Clearing banners...');
    const banners = await strapiRequest('/banners');
    if (banners.data && banners.data.length > 0) {
      for (const banner of banners.data) {
        try {
          await strapiRequest(`/banners/${banner.id}`, 'DELETE');
          console.log(`✅ Deleted banner: ${banner.attributes?.title || banner.title || banner.id}`);
        } catch (error) {
          console.log(`⚠️ Could not delete banner ${banner.id}`);
        }
      }
    }

    // Clear pages
    console.log('\n🗑️ Clearing pages...');
    const pages = await strapiRequest('/pages');
    if (pages.data && pages.data.length > 0) {
      for (const page of pages.data) {
        try {
          await strapiRequest(`/pages/${page.id}`, 'DELETE');
          console.log(`✅ Deleted page: ${page.attributes?.title || page.title || page.id}`);
        } catch (error) {
          console.log(`⚠️ Could not delete page ${page.id}`);
        }
      }
    }

    console.log('\n🎉 Data clearing completed!');
    
  } catch (error) {
    console.error('❌ Error during data clearing:', error);
    process.exit(1);
  }
}

// Run the clearing script
if (require.main === module) {
  clearData();
}

module.exports = { clearData };
