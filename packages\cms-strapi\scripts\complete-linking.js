/**
 * Complete the Category Linking Process
 * Link remaining subcategories to their parent categories
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Enhanced request function
async function strapiRequest(endpoint, method = 'GET', data = null, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${API_BASE}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = { data };
      }

      const response = await axios(config);
      return { success: true, data: response.data };
    } catch (error) {
      console.log(`   Attempt ${attempt}/${retries} failed for ${method} ${endpoint}`);
      
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || 'Unknown error';
        console.log(`   ❌ HTTP ${status}: ${message}`);
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }
      
      if (attempt === retries) {
        return { success: false, error: error.message };
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics',
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Digital Media': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

function logProgress(step, message, type = 'info') {
  const timestamp = new Date().toISOString();
  const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${symbols[type]} [${timestamp}] ${step}: ${message}`);
}

async function completeLinking() {
  console.log('🔗 COMPLETING CATEGORY LINKING');
  console.log('=' .repeat(60));
  
  try {
    // Get Categories
    const categoriesResult = await strapiRequest('/categories');
    if (!categoriesResult.success) {
      throw new Error('Failed to fetch categories');
    }
    
    // Get Product Categories
    const productCategoriesResult = await strapiRequest('/product-categories');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }
    
    const categories = categoriesResult.data.data || [];
    const productCategories = productCategoriesResult.data.data || [];
    
    // Create category lookup map
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat;
    });
    
    logProgress('INIT', `Found ${categories.length} categories and ${productCategories.length} product categories`, 'success');
    
    let updateCount = 0;
    let errorCount = 0;
    
    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];
      
      if (parentCategoryName && categoryMap[parentCategoryName]) {
        logProgress('LINK', `Linking: ${subcat.name} → ${parentCategoryName}`, 'info');
        
        // Simple payload without category_type
        const payload = {
          isSubcategory: true,
          category: categoryMap[parentCategoryName].documentId
        };
        
        const result = await strapiRequest(`/product-categories/${subcat.documentId}`, 'PUT', payload);
        
        if (result.success) {
          logProgress('LINK', `✅ Successfully linked: ${subcat.name}`, 'success');
          updateCount++;
        } else {
          logProgress('LINK', `❌ Failed to link: ${subcat.name}: ${result.error}`, 'error');
          errorCount++;
        }
      } else {
        logProgress('LINK', `Skipping unmapped category: ${subcat.name}`, 'warning');
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    logProgress('COMPLETE', `Linking completed. Updated: ${updateCount}, Errors: ${errorCount}`, 'success');
    
    // Verify the results
    console.log('\n🔍 VERIFICATION:');
    const verifyResult = await strapiRequest('/product-categories');
    if (verifyResult.success) {
      const linkedCategories = verifyResult.data.data.filter(pc => pc.isSubcategory);
      console.log(`✅ Total subcategories: ${linkedCategories.length}`);
    }
    
    console.log('\n🎉 LINKING COMPLETED SUCCESSFULLY!');
    
  } catch (error) {
    console.error('❌ LINKING FAILED:', error.message);
  }
}

completeLinking();
