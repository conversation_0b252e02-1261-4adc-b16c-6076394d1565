/**
 * DEPRECATED - Complete migration script for Strapi v5 (uses documentId instead of id)
 * This script has been replaced by diagnose-and-fix.js due to API compatibility issues
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Enhanced request function for Strapi v5
async function strapiRequest(endpoint, method = 'GET', data = null, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${API_BASE}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = { data };
      }

      const response = await axios(config);
      return { success: true, data: response.data };
    } catch (error) {
      console.log(`   Attempt ${attempt}/${retries} failed for ${method} ${endpoint}`);
      
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || 'Unknown error';
        console.log(`   ❌ HTTP ${status}: ${message}`);
        
        if (status === 400 && message.includes('already exists')) {
          return { success: true, data: null, exists: true };
        }
        
        if (status === 403) {
          console.log(`   ❌ Permission denied. Please check API permissions.`);
          return { success: false, error: 'Permission denied' };
        }
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }
      
      if (attempt === retries) {
        return { success: false, error: error.message };
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics',
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

function logProgress(step, message, type = 'info') {
  const timestamp = new Date().toISOString();
  const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${symbols[type]} [${timestamp}] ${step}: ${message}`);
}

async function createBackup(step, data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, 'migration-backups');
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const backupFile = path.join(backupDir, `${step}_${timestamp}.json`);
  fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
  console.log(`   📁 Backup created: ${backupFile}`);
  return backupFile;
}

// STEP 2: Establish Parent-Child Relationships (Strapi v5)
async function step2_establishRelationships() {
  logProgress('STEP 2', 'Starting parent-child relationships establishment', 'info');
  
  try {
    // Get all categories
    logProgress('STEP 2', 'Fetching main categories...', 'info');
    const categoriesResult = await strapiRequest('/categories?pagination[pageSize]=100');
    if (!categoriesResult.success) {
      throw new Error('Failed to fetch categories');
    }
    
    const categories = categoriesResult.data.data || [];
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat;
    });
    
    logProgress('STEP 2', `Found ${categories.length} main categories`, 'success');
    
    // Get all product categories
    logProgress('STEP 2', 'Fetching product categories...', 'info');
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }
    
    const productCategories = productCategoriesResult.data.data || [];
    await createBackup('step2_before', { categories, productCategories });
    
    logProgress('STEP 2', `Found ${productCategories.length} product categories`, 'success');
    
    // Group subcategories by parent category
    const relationshipUpdates = {};
    let mappedCount = 0;
    
    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];
      if (parentCategoryName && categoryMap[parentCategoryName]) {
        if (!relationshipUpdates[parentCategoryName]) {
          relationshipUpdates[parentCategoryName] = {
            category: categoryMap[parentCategoryName],
            children: []
          };
        }
        relationshipUpdates[parentCategoryName].children.push(subcat.documentId);
        mappedCount++;
        logProgress('STEP 2', `Mapped ${subcat.name} → ${parentCategoryName}`, 'info');
      } else {
        logProgress('STEP 2', `No mapping found for: ${subcat.name}`, 'warning');
      }
    }
    
    logProgress('STEP 2', `Mapped ${mappedCount} subcategories to parent categories`, 'success');
    
    // Update each main category with its children (using documentId)
    let updateCount = 0;
    for (const [categoryName, updateData] of Object.entries(relationshipUpdates)) {
      logProgress('STEP 2', `Updating relationships for: ${categoryName} (${updateData.children.length} children)`, 'info');
      
      const updatePayload = {
        children: updateData.children
      };
      
      // Use documentId for Strapi v5
      const result = await strapiRequest(`/categories/${updateData.category.documentId}`, 'PUT', updatePayload);
      
      if (result.success) {
        logProgress('STEP 2', `Successfully linked ${updateData.children.length} children to ${categoryName}`, 'success');
        updateCount++;
      } else {
        logProgress('STEP 2', `Failed to update relationships for ${categoryName}: ${result.error}`, 'error');
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    await createBackup('step2_after', relationshipUpdates);
    logProgress('STEP 2', `Relationships establishment completed. Updated: ${updateCount}/${Object.keys(relationshipUpdates).length} categories`, 'success');
    return relationshipUpdates;

  } catch (error) {
    logProgress('STEP 2', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// STEP 3: Clean Up Product Categories Collection (Strapi v5)
async function step3_cleanupProductCategories() {
  logProgress('STEP 3', 'Starting product categories cleanup', 'info');

  try {
    // Get all categories
    const categoriesResult = await strapiRequest('/categories?pagination[pageSize]=100');
    if (!categoriesResult.success) {
      throw new Error('Failed to fetch categories');
    }

    const categories = categoriesResult.data.data || [];
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat;
    });

    // Get all product categories
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }

    const productCategories = productCategoriesResult.data.data || [];
    await createBackup('step3_before', productCategories);

    let updateCount = 0;
    let subcategoryCount = 0;

    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];

      if (parentCategoryName && categoryMap[parentCategoryName]) {
        logProgress('STEP 3', `Updating subcategory: ${subcat.name}`, 'info');

        const updatePayload = {
          isSubcategory: true,
          parent: categoryMap[parentCategoryName].documentId
        };

        // Use documentId for Strapi v5
        const result = await strapiRequest(`/product-categories/${subcat.documentId}`, 'PUT', updatePayload);

        if (result.success) {
          logProgress('STEP 3', `Successfully updated subcategory: ${subcat.name}`, 'success');
          updateCount++;
          subcategoryCount++;
        } else {
          logProgress('STEP 3', `Failed to update subcategory: ${subcat.name}: ${result.error}`, 'error');
        }
      } else {
        logProgress('STEP 3', `Skipping unmapped category: ${subcat.name}`, 'warning');
      }

      await new Promise(resolve => setTimeout(resolve, 300));
    }

    await createBackup('step3_after', { updateCount, subcategoryCount });
    logProgress('STEP 3', `Product categories cleanup completed. Updated: ${updateCount} subcategories`, 'success');
    return { updateCount, subcategoryCount };

  } catch (error) {
    logProgress('STEP 3', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// STEP 4: Verify Product Collection Relationships (Strapi v5)
async function step4_updateProductRelationships() {
  logProgress('STEP 4', 'Starting product relationships verification', 'info');

  try {
    // Get all products
    const productsResult = await strapiRequest('/products?pagination[pageSize]=100&populate=category');
    if (!productsResult.success) {
      logProgress('STEP 4', 'No products found or products collection not accessible', 'warning');
      return { updateCount: 0, totalProducts: 0 };
    }

    const products = productsResult.data.data || [];
    await createBackup('step4_before', products);

    let verifiedCount = 0;
    let totalProducts = products.length;

    for (const product of products) {
      if (product.category) {
        logProgress('STEP 4', `Verifying product: ${product.name || product.id}`, 'info');

        // Get the current category details with parent relationship
        const categoryResult = await strapiRequest(`/product-categories/${product.category.documentId}?populate=parent`);

        if (categoryResult.success && categoryResult.data.data) {
          const category = categoryResult.data.data;

          if (category.parent) {
            logProgress('STEP 4', `Product "${product.name || product.id}" properly linked: ${category.name} → ${category.parent.name}`, 'success');
            verifiedCount++;
          } else {
            logProgress('STEP 4', `Product "${product.name || product.id}" category "${category.name}" has no parent`, 'warning');
          }
        }
      } else {
        logProgress('STEP 4', `Product "${product.name || product.id}" has no category`, 'warning');
      }

      await new Promise(resolve => setTimeout(resolve, 200));
    }

    await createBackup('step4_after', { verifiedCount, totalProducts });
    logProgress('STEP 4', `Product relationships verification completed. Verified: ${verifiedCount}/${totalProducts} products`, 'success');
    return { updateCount: verifiedCount, totalProducts };

  } catch (error) {
    logProgress('STEP 4', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// Main function to complete remaining migration steps
async function completeRemainingMigration() {
  console.log('🔄 COMPLETING REMAINING MIGRATION STEPS (2-4) - STRAPI V5');
  console.log('=' .repeat(80));
  console.log('📋 Using documentId for Strapi v5 compatibility...');
  console.log('🔄 Steps: Relationships → Cleanup → Products');
  console.log('=' .repeat(80));

  const startTime = Date.now();

  try {
    // Test connectivity first
    logProgress('INIT', 'Testing API connectivity...', 'info');
    const healthCheck = await strapiRequest('/categories');

    if (!healthCheck.success) {
      throw new Error('Cannot connect to Strapi API. Please ensure Strapi is running and permissions are set.');
    }

    logProgress('INIT', 'API connectivity confirmed', 'success');

    // Step 2: Establish Parent-Child Relationships
    console.log('\n🔗 STEP 2: ESTABLISH PARENT-CHILD RELATIONSHIPS');
    console.log('-' .repeat(50));
    const relationships = await step2_establishRelationships();

    // Step 3: Clean Up Product Categories Collection
    console.log('\n🧹 STEP 3: CLEAN UP PRODUCT CATEGORIES COLLECTION');
    console.log('-' .repeat(50));
    const cleanup = await step3_cleanupProductCategories();

    // Step 4: Update Product Collection Relationships
    console.log('\n🔄 STEP 4: VERIFY PRODUCT COLLECTION RELATIONSHIPS');
    console.log('-' .repeat(50));
    const products = await step4_updateProductRelationships();

    // Final summary
    console.log('\n✅ REMAINING MIGRATION STEPS COMPLETED SUCCESSFULLY!');
    console.log('=' .repeat(80));

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log(`📊 Summary:`);
    console.log(`   • Parent-Child Relationships: ${Object.keys(relationships).length} categories updated`);
    console.log(`   • Subcategories Cleaned: ${cleanup.subcategoryCount}`);
    console.log(`   • Products Verified: ${products.updateCount}/${products.totalProducts}`);

    console.log('\n🌐 Verification URLs:');
    console.log('   • Categories: http://localhost:1337/api/categories');
    console.log('   • Product Categories: http://localhost:1337/api/product-categories');
    console.log('   • Products: http://localhost:1337/api/products');
    console.log('   • Admin Panel: http://localhost:1337/admin');

    console.log('\n🎉 Complete category system migration finished successfully!');
    console.log('\n📋 Final Structure:');
    console.log('   Categories Collection: 8 main categories with children relationships');
    console.log('   Product Categories Collection: Subcategories linked to parent categories');
    console.log('   Products Collection: Properly linked to subcategories');

    // Final verification
    console.log('\n🔍 FINAL VERIFICATION:');
    console.log('-' .repeat(50));

    const finalCategoriesCheck = await strapiRequest('/categories?populate=children');
    if (finalCategoriesCheck.success) {
      const finalCategories = finalCategoriesCheck.data.data || [];
      finalCategories.forEach(cat => {
        const childrenCount = cat.children?.length || 0;
        console.log(`   📁 ${cat.name}: ${childrenCount} children`);
      });
    }

  } catch (error) {
    console.error('\n❌ MIGRATION COMPLETION FAILED');
    console.error('=' .repeat(80));
    console.error(`Error: ${error.message}`);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Check Strapi server status');
    console.error('2. Verify API permissions');
    console.error('3. Check database connectivity');
    console.error('4. Review migration backups in ./migration-backups/');

    process.exit(1);
  }
}

// Run the completion script
if (require.main === module) {
  completeRemainingMigration();
}

module.exports = {
  completeRemainingMigration,
  step2_establishRelationships,
  step3_cleanupProductCategories,
  step4_updateProductRelationships
};
