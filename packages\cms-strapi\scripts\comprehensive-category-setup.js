/**
 * Comprehensive Category Hierarchy Setup Script
 *
 * This script implements the complete category-subcategory system:
 * 1. Creates missing subcategories
 * 2. Establishes proper parent-child relationships
 * 3. Updates isSubcategory flags
 * 4. Assigns products to appropriate subcategories
 */

const axios = require('axios');

// Strapi API configuration
const STRAPI_URL = 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN || '';

// Create axios instance for Strapi API
const strapiAPI = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
    ...(API_TOKEN && { Authorization: `Bearer ${API_TOKEN}` }),
  },
});

// Complete category hierarchy structure from organize-category-hierarchy.js
const CATEGORY_HIERARCHY = {
  // Electronics and subcategories
  Electronics: {
    subcategories: [
      'Smartphones',
      'Laptops & Computers',
      'Audio & Headphones',
      'Gaming',
      'Cameras & Photography',
    ],
  },

  // Fashion and subcategories
  'Fashion & Apparel': {
    subcategories: [
      "Men's Clothing",
      "Women's Clothing",
      'Shoes & Footwear',
      'Accessories',
      'Kids & Baby',
    ],
  },

  // Home & Garden and subcategories
  'Home & Garden': {
    subcategories: [
      'Furniture',
      'Home Decor',
      'Kitchen & Dining',
      'Garden & Outdoor',
      'Storage & Organization',
    ],
  },

  // Health & Beauty and subcategories
  'Health & Beauty': {
    subcategories: [
      'Skincare & Personal Care',
      'Makeup & Cosmetics',
      'Hair Care',
      'Health Supplements',
      'Wellness & Fitness',
    ],
  },

  // Sports & Outdoors and subcategories
  'Sports & Outdoors': {
    subcategories: [
      'Fitness Equipment',
      'Team Sports',
      'Outdoor Recreation',
      'Winter Sports',
      'Water Sports',
    ],
  },

  // Books & Media and subcategories
  'Books & Media': {
    subcategories: ['Books', 'Movies & TV', 'Music', 'Games & Toys', 'Educational'],
  },

  // Automotive and subcategories
  Automotive: {
    subcategories: ['Car Accessories', 'Car Parts', 'Motorcycle', 'Tools & Equipment'],
  },

  // Food & Beverages and subcategories
  'Food & Beverages': {
    subcategories: [
      'Fresh Produce',
      'Pantry Staples',
      'Beverages',
      'Snacks & Confectionery',
      'Gourmet & Specialty',
    ],
  },
};

/**
 * Get all existing categories from Strapi
 */
async function getAllCategories() {
  try {
    console.log('📂 Fetching all categories from Strapi...');
    const response = await strapiAPI.get('/product-categories?pagination[pageSize]=100');
    const categories = response.data.data || [];
    console.log(`✅ Found ${categories.length} categories`);
    return categories;
  } catch (error) {
    console.error('❌ Error fetching categories:', error.message);
    throw error;
  }
}

/**
 * Get all existing products from Strapi
 */
async function getAllProducts() {
  try {
    console.log('📦 Fetching all products from Strapi...');
    const response = await strapiAPI.get('/products?pagination[pageSize]=100&populate=categories');
    const products = response.data.data || [];
    console.log(`✅ Found ${products.length} products`);
    return products;
  } catch (error) {
    console.error('❌ Error fetching products:', error.message);
    throw error;
  }
}

/**
 * Find category by name (case-insensitive, flexible matching)
 */
function findCategoryByName(categories, targetName) {
  return categories.find(cat => {
    const catName = cat.name.toLowerCase().trim();
    const target = targetName.toLowerCase().trim();

    // Exact match
    if (catName === target) return true;

    // Partial match for similar names
    if (catName.includes(target) || target.includes(catName)) return true;

    // Handle common variations
    const variations = {
      'fashion & apparel': ['fashion', 'fashion & apparel'],
      'home & garden': ['home & garden', 'home garden'],
      'health & beauty': ['health & beauty', 'beauty & health', 'beauty health'],
      'sports & outdoors': ['sports & outdoors', 'sports fitness', 'sports & fitness'],
      'books & media': ['books & media', 'books media'],
      'food & beverages': ['food & beverages', 'food beverages'],
    };

    for (const [key, values] of Object.entries(variations)) {
      if (values.includes(target) && values.includes(catName)) return true;
    }

    return false;
  });
}

/**
 * Create a new category
 */
async function createCategory(categoryData) {
  try {
    const response = await strapiAPI.post('/product-categories', {
      data: categoryData,
    });
    return response.data.data;
  } catch (error) {
    console.error(`❌ Error creating category:`, error.message);
    throw error;
  }
}

/**
 * Update category with parent relationship and isSubcategory flag
 */
async function updateCategory(category, updateData) {
  try {
    const categoryId = category.documentId || category.id;
    const response = await strapiAPI.put(`/product-categories/${categoryId}`, {
      data: updateData,
    });
    return response.data;
  } catch (error) {
    console.error(`❌ Error updating category ${categoryId}:`, error.message);
    throw error;
  }
}

/**
 * Generate slug from name
 */
function generateSlug(name) {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
}

/**
 * Create missing subcategories
 */
async function createMissingSubcategories(categories, parentCategory, subcategoryNames) {
  const createdSubcategories = [];

  for (const subcategoryName of subcategoryNames) {
    const existingSubcategory = findCategoryByName(categories, subcategoryName);

    if (!existingSubcategory) {
      console.log(`  📝 Creating missing subcategory: ${subcategoryName}`);

      try {
        const newSubcategory = await createCategory({
          name: subcategoryName,
          slug: generateSlug(subcategoryName),
          description: `${subcategoryName} products under ${parentCategory.name}`,
          isSubcategory: true,
          parent: parentCategory.documentId || parentCategory.id,
        });

        createdSubcategories.push(newSubcategory);
        categories.push(newSubcategory); // Add to local array for future lookups
        console.log(`  ✅ Created subcategory: ${subcategoryName}`);
      } catch (error) {
        console.error(`  ❌ Failed to create subcategory ${subcategoryName}:`, error.message);
      }
    }
  }

  return createdSubcategories;
}

/**
 * Assign products to appropriate subcategories based on product names and descriptions
 */
async function assignProductsToSubcategories(products, categories) {
  console.log('\n📦 Starting product category assignment...');

  const productCategoryMapping = {
    // Electronics subcategories
    smartphones: ['phone', 'smartphone', 'mobile', 'iphone', 'android', 'samsung', 'xiaomi'],
    'laptops & computers': [
      'laptop',
      'computer',
      'pc',
      'desktop',
      'macbook',
      'notebook',
      'workstation',
    ],
    'audio & headphones': ['headphone', 'earphone', 'speaker', 'audio', 'sound', 'music', 'beats'],
    gaming: ['game', 'gaming', 'console', 'playstation', 'xbox', 'nintendo', 'controller'],
    'cameras & photography': ['camera', 'photo', 'photography', 'lens', 'dslr', 'canon', 'nikon'],

    // Fashion subcategories
    "men's clothing": ['men', 'shirt', 'pants', 'jeans', 'jacket', 'suit', 'formal'],
    "women's clothing": ['women', 'dress', 'blouse', 'skirt', 'top', 'leggings', 'saree'],
    'shoes & footwear': ['shoe', 'boot', 'sneaker', 'sandal', 'heel', 'footwear', 'nike', 'adidas'],
    accessories: ['bag', 'wallet', 'watch', 'jewelry', 'belt', 'sunglasses', 'hat'],
    'kids & baby': ['kids', 'baby', 'children', 'infant', 'toddler', 'toy'],

    // Home & Garden subcategories
    furniture: ['chair', 'table', 'sofa', 'bed', 'desk', 'furniture', 'cabinet'],
    'home decor': ['decor', 'decoration', 'art', 'frame', 'vase', 'candle', 'pillow'],
    'kitchen & dining': ['kitchen', 'cooking', 'utensil', 'plate', 'cup', 'cookware', 'dining'],
    'garden & outdoor': ['garden', 'plant', 'outdoor', 'patio', 'lawn', 'flower', 'seed'],
    'storage & organization': ['storage', 'organizer', 'box', 'container', 'shelf', 'basket'],

    // Health & Beauty subcategories
    'skincare & personal care': ['skincare', 'cream', 'lotion', 'soap', 'shampoo', 'personal care'],
    'makeup & cosmetics': ['makeup', 'cosmetic', 'lipstick', 'foundation', 'mascara', 'beauty'],
    'hair care': ['hair', 'shampoo', 'conditioner', 'styling', 'haircare'],
    'health supplements': ['vitamin', 'supplement', 'protein', 'health', 'nutrition', 'medicine'],
    'wellness & fitness': ['fitness', 'wellness', 'yoga', 'exercise', 'gym', 'workout'],

    // Sports & Outdoors subcategories
    'fitness equipment': ['fitness', 'gym', 'exercise', 'weight', 'treadmill', 'equipment'],
    'team sports': ['football', 'basketball', 'soccer', 'cricket', 'tennis', 'sports'],
    'outdoor recreation': ['camping', 'hiking', 'outdoor', 'adventure', 'backpack', 'tent'],
    'winter sports': ['ski', 'snow', 'winter', 'ice', 'snowboard'],
    'water sports': ['swim', 'water', 'surf', 'diving', 'boat', 'fishing'],

    // Books & Media subcategories
    books: ['book', 'novel', 'story', 'literature', 'reading'],
    'movies & tv': ['movie', 'film', 'tv', 'dvd', 'blu-ray', 'series'],
    music: ['music', 'cd', 'album', 'song', 'audio'],
    'games & toys': ['game', 'toy', 'puzzle', 'board game', 'card game'],
    educational: ['education', 'learning', 'textbook', 'course', 'study'],

    // Automotive subcategories
    'car accessories': ['car', 'auto', 'vehicle', 'accessory', 'interior', 'exterior'],
    'car parts': ['engine', 'brake', 'tire', 'battery', 'part', 'component'],
    motorcycle: ['motorcycle', 'bike', 'motorbike', 'scooter', 'helmet'],
    'tools & equipment': ['tool', 'equipment', 'wrench', 'screwdriver', 'repair'],

    // Food & Beverages subcategories
    'fresh produce': ['fruit', 'vegetable', 'fresh', 'organic', 'produce'],
    'pantry staples': ['rice', 'flour', 'oil', 'spice', 'grain', 'staple'],
    beverages: ['drink', 'juice', 'coffee', 'tea', 'water', 'soda'],
    'snacks & confectionery': ['snack', 'candy', 'chocolate', 'biscuit', 'chips'],
    'gourmet & specialty': ['gourmet', 'specialty', 'premium', 'artisan', 'delicacy'],
  };

  let assignmentCount = 0;
  let errorCount = 0;

  for (const product of products) {
    try {
      const productName = product.name.toLowerCase();
      // Handle different description formats (string or object)
      let productDescription = '';
      if (typeof product.description === 'string') {
        productDescription = product.description.toLowerCase();
      } else if (product.description && typeof product.description === 'object') {
        // Handle rich text or object descriptions
        productDescription = JSON.stringify(product.description).toLowerCase();
      }
      const searchText = `${productName} ${productDescription}`;

      let bestMatch = null;
      let bestScore = 0;

      // Find the best matching subcategory
      for (const [subcategoryName, keywords] of Object.entries(productCategoryMapping)) {
        let score = 0;

        for (const keyword of keywords) {
          if (searchText.includes(keyword)) {
            score += keyword.length; // Longer keywords get higher scores
          }
        }

        if (score > bestScore) {
          bestScore = score;
          bestMatch = subcategoryName;
        }
      }

      if (bestMatch && bestScore > 0) {
        // Find the subcategory in our categories list
        const subcategory = findCategoryByName(categories, bestMatch);

        if (subcategory) {
          // Update product with the subcategory
          const productId = product.documentId || product.id;
          await strapiAPI.put(`/products/${productId}`, {
            data: {
              categories: [subcategory.documentId || subcategory.id],
            },
          });

          assignmentCount++;
          console.log(`  ✅ Assigned "${product.name}" → ${subcategory.name}`);
        }
      } else {
        console.log(`  ⚠️ No suitable subcategory found for: ${product.name}`);
      }
    } catch (error) {
      errorCount++;
      console.error(`  ❌ Failed to assign product ${product.name}:`, error.message);
    }
  }

  console.log(`\n📊 Product Assignment Summary:`);
  console.log(`✅ Products assigned: ${assignmentCount}`);
  console.log(`❌ Assignment errors: ${errorCount}`);

  return { assignmentCount, errorCount };
}

/**
 * Main function to implement comprehensive category hierarchy
 */
async function implementComprehensiveCategorySystem() {
  console.log('🚀 Starting comprehensive category-subcategory system implementation...');

  try {
    // Create restore point
    console.log('💾 Creating restore point...');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    // Get all existing data
    const categories = await getAllCategories();
    const products = await getAllProducts();

    // Track all updates
    const updates = {
      parentCategories: [],
      subcategoriesUpdated: [],
      subcategoriesCreated: [],
      productsAssigned: 0,
      notFound: [],
    };

    // Phase 1: Process each parent category and its subcategories
    console.log('\n📁 Phase 1: Setting up category hierarchy...');

    for (const [parentName, config] of Object.entries(CATEGORY_HIERARCHY)) {
      console.log(`\n📁 Processing parent category: ${parentName}`);

      // Find or create parent category
      let parentCategory = findCategoryByName(categories, parentName);

      if (!parentCategory) {
        console.log(`📝 Creating missing parent category: ${parentName}`);
        try {
          parentCategory = await createCategory({
            name: parentName,
            slug: generateSlug(parentName),
            description: `${parentName} products and subcategories`,
            isSubcategory: false,
            parent: null,
          });
          categories.push(parentCategory);
          console.log(`✅ Created parent category: ${parentName}`);
        } catch (error) {
          console.error(`❌ Failed to create parent category ${parentName}:`, error.message);
          continue;
        }
      }

      // Update parent category
      console.log(`✅ Found parent: ${parentCategory.name} (ID: ${parentCategory.id})`);

      try {
        await updateCategory(parentCategory, {
          isSubcategory: false,
          parent: null,
        });
        updates.parentCategories.push(parentCategory.name);
        console.log(`✅ Updated parent category: ${parentCategory.name}`);
      } catch (error) {
        console.error(`❌ Failed to update parent category ${parentCategory.name}:`, error.message);
      }

      // Create missing subcategories
      const createdSubcategories = await createMissingSubcategories(
        categories,
        parentCategory,
        config.subcategories
      );
      updates.subcategoriesCreated.push(...createdSubcategories.map(sub => sub.name));

      // Process existing subcategories
      for (const subcategoryName of config.subcategories) {
        const subcategory = findCategoryByName(categories, subcategoryName);

        if (subcategory) {
          console.log(`  ├── Found subcategory: ${subcategory.name} (ID: ${subcategory.id})`);

          try {
            await updateCategory(subcategory, {
              isSubcategory: true,
              parent: parentCategory.documentId || parentCategory.id,
            });
            updates.subcategoriesUpdated.push(subcategory.name);
            console.log(`  ✅ Updated subcategory: ${subcategory.name} → ${parentCategory.name}`);
          } catch (error) {
            console.error(`  ❌ Failed to update subcategory ${subcategory.name}:`, error.message);
          }
        } else {
          updates.notFound.push(subcategoryName);
        }
      }
    }

    // Phase 2: Assign products to subcategories
    console.log('\n📦 Phase 2: Assigning products to subcategories...');
    const productAssignment = await assignProductsToSubcategories(products, categories);
    updates.productsAssigned = productAssignment.assignmentCount;

    // Final summary report
    console.log('\n📊 COMPREHENSIVE CATEGORY SYSTEM IMPLEMENTATION SUMMARY');
    console.log('=========================================================');
    console.log(`✅ Parent categories processed: ${updates.parentCategories.length}`);
    console.log(`✅ Subcategories updated: ${updates.subcategoriesUpdated.length}`);
    console.log(`✅ Subcategories created: ${updates.subcategoriesCreated.length}`);
    console.log(`✅ Products assigned to subcategories: ${updates.productsAssigned}`);
    console.log(`⚠️ Categories not found: ${updates.notFound.length}`);

    if (updates.notFound.length > 0) {
      console.log('\n⚠️ Categories not found (will be created if needed):');
      updates.notFound.forEach(name => console.log(`  - ${name}`));
    }

    if (updates.subcategoriesCreated.length > 0) {
      console.log('\n✅ New subcategories created:');
      updates.subcategoriesCreated.forEach(name => console.log(`  + ${name}`));
    }

    console.log('\n🎉 Comprehensive category-subcategory system implementation completed!');

    return updates;
  } catch (error) {
    console.error('❌ Error implementing comprehensive category system:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  implementComprehensiveCategorySystem()
    .then(updates => {
      console.log('✅ Script completed successfully');
      console.log('📊 Final Summary:', updates);
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = {
  implementComprehensiveCategorySystem,
  CATEGORY_HIERARCHY,
  assignProductsToSubcategories,
};
