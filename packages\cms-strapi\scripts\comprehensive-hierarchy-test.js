/**
 * Comprehensive Hierarchical Category System Testing
 * Tests all aspects of the category-subcategory relationship system
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Test configuration
const TEST_CONFIG = {
  testCategory: {
    name: 'Test Electronics Category',
    description: 'Test main category for electronics',
    short_description: 'Test electronics category',
    slug: 'test-electronics-category',
    featured: true,
    active: true,
    sort_order: 999
  },
  testSubcategory: {
    name: 'Test Smartphones Subcategory',
    description: 'Test subcategory for smartphones',
    short_description: 'Test smartphones subcategory',
    slug: 'test-smartphones-subcategory',
    isSubcategory: true,
    active: true,
    sort_order: 1
  }
};

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      details: error.response?.data
    };
  }
}

function logTest(testName, result, details = '') {
  const status = result ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${testName}`);
  if (details) {
    console.log(`   ${details}`);
  }
}

function logSection(title) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🔍 ${title}`);
  console.log(`${'='.repeat(60)}`);
}

// Test 1: API Connectivity
async function testAPIConnectivity() {
  logSection('API CONNECTIVITY TESTS');
  
  // Test Categories endpoint
  const categoriesResult = await strapiRequest('/categories');
  logTest('Categories API Endpoint', categoriesResult.success, 
    categoriesResult.success ? `Found ${categoriesResult.data.data?.length || 0} categories` : categoriesResult.error);
  
  // Test Product Categories endpoint
  const productCategoriesResult = await strapiRequest('/product-categories');
  logTest('Product Categories API Endpoint', productCategoriesResult.success,
    productCategoriesResult.success ? `Found ${productCategoriesResult.data.data?.length || 0} product categories` : productCategoriesResult.error);
  
  // Test populated relationships
  const populatedResult = await strapiRequest('/product-categories?populate=parent');
  logTest('Populated Relationships', populatedResult.success,
    populatedResult.success ? 'Parent relationships can be populated' : populatedResult.error);
  
  return {
    categoriesWorking: categoriesResult.success,
    productCategoriesWorking: productCategoriesResult.success,
    populationWorking: populatedResult.success
  };
}

// Test 2: Schema Analysis
async function testSchemaStructure() {
  logSection('SCHEMA STRUCTURE ANALYSIS');
  
  // Get sample data to analyze structure
  const categoriesResult = await strapiRequest('/categories?pagination[pageSize]=1');
  const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=1&populate=parent');
  
  let categoryFields = [];
  let productCategoryFields = [];
  
  if (categoriesResult.success && categoriesResult.data.data.length > 0) {
    categoryFields = Object.keys(categoriesResult.data.data[0]);
    logTest('Categories Collection Fields', true, `Fields: ${categoryFields.join(', ')}`);
  } else {
    logTest('Categories Collection Fields', false, 'No sample data available');
  }
  
  if (productCategoriesResult.success && productCategoriesResult.data.data.length > 0) {
    productCategoryFields = Object.keys(productCategoriesResult.data.data[0]);
    logTest('Product Categories Collection Fields', true, `Fields: ${productCategoryFields.join(', ')}`);
  } else {
    logTest('Product Categories Collection Fields', false, 'No sample data available');
  }
  
  // Check for required relationship fields
  const hasParentField = productCategoryFields.includes('parent');
  const hasIsSubcategoryField = productCategoryFields.includes('isSubcategory');
  
  logTest('Parent Relationship Field', hasParentField, hasParentField ? 'parent field exists' : 'parent field missing');
  logTest('isSubcategory Field', hasIsSubcategoryField, hasIsSubcategoryField ? 'isSubcategory field exists' : 'isSubcategory field missing');
  
  return {
    categoryFields,
    productCategoryFields,
    hasParentField,
    hasIsSubcategoryField
  };
}

// Test 3: Create Test Category
async function testCategoryCreation() {
  logSection('CATEGORY CREATION TESTS');
  
  // Create test main category
  const createCategoryResult = await strapiRequest('/categories', 'POST', TEST_CONFIG.testCategory);
  logTest('Create Main Category', createCategoryResult.success,
    createCategoryResult.success ? `Created category: ${createCategoryResult.data.data.name}` : createCategoryResult.error);
  
  if (!createCategoryResult.success) {
    return { testCategoryId: null, testCategoryDocumentId: null };
  }
  
  const testCategoryId = createCategoryResult.data.data.id;
  const testCategoryDocumentId = createCategoryResult.data.data.documentId;
  
  // Verify category was created
  const verifyCategoryResult = await strapiRequest(`/categories/${testCategoryDocumentId}`);
  logTest('Verify Category Creation', verifyCategoryResult.success,
    verifyCategoryResult.success ? `Category verified: ${verifyCategoryResult.data.data.name}` : verifyCategoryResult.error);
  
  return { testCategoryId, testCategoryDocumentId };
}

// Test 4: Create Test Subcategory with Parent Relationship
async function testSubcategoryCreation(parentCategoryDocumentId) {
  logSection('SUBCATEGORY CREATION AND LINKING TESTS');
  
  if (!parentCategoryDocumentId) {
    logTest('Create Subcategory', false, 'No parent category available');
    return { testSubcategoryId: null, testSubcategoryDocumentId: null };
  }
  
  // Create subcategory with parent relationship
  const subcategoryData = {
    ...TEST_CONFIG.testSubcategory,
    parent: parentCategoryDocumentId
  };
  
  const createSubcategoryResult = await strapiRequest('/product-categories', 'POST', subcategoryData);
  logTest('Create Subcategory with Parent Link', createSubcategoryResult.success,
    createSubcategoryResult.success ? `Created subcategory: ${createSubcategoryResult.data.data.name}` : createSubcategoryResult.error);
  
  if (!createSubcategoryResult.success) {
    return { testSubcategoryId: null, testSubcategoryDocumentId: null };
  }
  
  const testSubcategoryId = createSubcategoryResult.data.data.id;
  const testSubcategoryDocumentId = createSubcategoryResult.data.data.documentId;
  
  // Verify subcategory with populated parent
  const verifySubcategoryResult = await strapiRequest(`/product-categories/${testSubcategoryDocumentId}?populate=parent`);
  logTest('Verify Subcategory Parent Relationship', verifySubcategoryResult.success,
    verifySubcategoryResult.success && verifySubcategoryResult.data.data.parent ? 
    `Parent linked: ${verifySubcategoryResult.data.data.parent.name}` : 'Parent relationship not found');
  
  return { testSubcategoryId, testSubcategoryDocumentId };
}

// Test 5: Bidirectional Relationship Verification
async function testBidirectionalRelationships(categoryDocumentId, subcategoryDocumentId) {
  logSection('BIDIRECTIONAL RELATIONSHIP TESTS');
  
  if (!categoryDocumentId || !subcategoryDocumentId) {
    logTest('Bidirectional Relationships', false, 'Missing test data');
    return false;
  }
  
  // Test parent -> children relationship
  const categoryWithChildrenResult = await strapiRequest(`/categories/${categoryDocumentId}?populate=product_categories`);
  logTest('Category -> Product Categories Relationship', categoryWithChildrenResult.success,
    categoryWithChildrenResult.success ? 
    `Found ${categoryWithChildrenResult.data.data.product_categories?.length || 0} linked product categories` : 
    categoryWithChildrenResult.error);
  
  // Test child -> parent relationship
  const subcategoryWithParentResult = await strapiRequest(`/product-categories/${subcategoryDocumentId}?populate=parent`);
  logTest('Subcategory -> Parent Relationship', subcategoryWithParentResult.success,
    subcategoryWithParentResult.success && subcategoryWithParentResult.data.data.parent ? 
    `Parent: ${subcategoryWithParentResult.data.data.parent.name}` : 'No parent found');
  
  return categoryWithChildrenResult.success && subcategoryWithParentResult.success;
}

// Test 6: Filtering and Querying
async function testFilteringCapabilities() {
  logSection('FILTERING AND QUERYING TESTS');
  
  // Test filtering subcategories
  const subcategoriesResult = await strapiRequest('/product-categories?filters[isSubcategory][$eq]=true&populate=parent');
  logTest('Filter Subcategories', subcategoriesResult.success,
    subcategoriesResult.success ? 
    `Found ${subcategoriesResult.data.data?.length || 0} subcategories` : subcategoriesResult.error);
  
  // Test filtering main categories (non-subcategories)
  const mainCategoriesResult = await strapiRequest('/product-categories?filters[isSubcategory][$eq]=false');
  logTest('Filter Main Categories in Product Categories', mainCategoriesResult.success,
    mainCategoriesResult.success ? 
    `Found ${mainCategoriesResult.data.data?.length || 0} main categories` : mainCategoriesResult.error);
  
  // Test categories with populated product categories
  const categoriesWithProductCategoriesResult = await strapiRequest('/categories?populate=product_categories');
  logTest('Categories with Product Categories', categoriesWithProductCategoriesResult.success,
    categoriesWithProductCategoriesResult.success ? 
    `Found ${categoriesWithProductCategoriesResult.data.data?.length || 0} categories` : categoriesWithProductCategoriesResult.error);
  
  return {
    subcategoriesFilter: subcategoriesResult.success,
    mainCategoriesFilter: mainCategoriesResult.success,
    populatedCategories: categoriesWithProductCategoriesResult.success
  };
}

// Test 7: Cleanup Test Data
async function cleanupTestData(categoryDocumentId, subcategoryDocumentId) {
  logSection('CLEANUP TEST DATA');
  
  let cleanupResults = { subcategory: false, category: false };
  
  // Delete test subcategory
  if (subcategoryDocumentId) {
    const deleteSubcategoryResult = await strapiRequest(`/product-categories/${subcategoryDocumentId}`, 'DELETE');
    cleanupResults.subcategory = deleteSubcategoryResult.success;
    logTest('Delete Test Subcategory', deleteSubcategoryResult.success,
      deleteSubcategoryResult.success ? 'Test subcategory deleted' : deleteSubcategoryResult.error);
  }
  
  // Delete test category
  if (categoryDocumentId) {
    const deleteCategoryResult = await strapiRequest(`/categories/${categoryDocumentId}`, 'DELETE');
    cleanupResults.category = deleteCategoryResult.success;
    logTest('Delete Test Category', deleteCategoryResult.success,
      deleteCategoryResult.success ? 'Test category deleted' : deleteCategoryResult.error);
  }
  
  return cleanupResults;
}

// Main test execution
async function runComprehensiveTests() {
  console.log('🧪 COMPREHENSIVE HIERARCHICAL CATEGORY SYSTEM TESTING');
  console.log('=' .repeat(80));
  console.log('Testing all aspects of the category-subcategory relationship system');
  console.log('=' .repeat(80));
  
  const startTime = Date.now();
  let testResults = {};
  
  try {
    // Test 1: API Connectivity
    testResults.connectivity = await testAPIConnectivity();
    
    // Test 2: Schema Structure
    testResults.schema = await testSchemaStructure();
    
    // Test 3: Category Creation
    const { testCategoryId, testCategoryDocumentId } = await testCategoryCreation();
    
    // Test 4: Subcategory Creation
    const { testSubcategoryId, testSubcategoryDocumentId } = await testSubcategoryCreation(testCategoryDocumentId);
    
    // Test 5: Bidirectional Relationships
    testResults.bidirectional = await testBidirectionalRelationships(testCategoryDocumentId, testSubcategoryDocumentId);
    
    // Test 6: Filtering Capabilities
    testResults.filtering = await testFilteringCapabilities();
    
    // Test 7: Cleanup
    testResults.cleanup = await cleanupTestData(testCategoryDocumentId, testSubcategoryDocumentId);
    
    // Final Summary
    logSection('TEST SUMMARY');
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`⏱️  Test Duration: ${duration} seconds`);
    console.log(`\n📊 Test Results:`);
    console.log(`   • API Connectivity: ${testResults.connectivity?.categoriesWorking && testResults.connectivity?.productCategoriesWorking ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   • Schema Structure: ${testResults.schema?.hasParentField && testResults.schema?.hasIsSubcategoryField ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   • Bidirectional Relationships: ${testResults.bidirectional ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   • Filtering Capabilities: ${testResults.filtering?.subcategoriesFilter && testResults.filtering?.populatedCategories ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   • Cleanup: ${testResults.cleanup?.category && testResults.cleanup?.subcategory ? '✅ PASS' : '❌ FAIL'}`);
    
    const overallSuccess = testResults.connectivity?.categoriesWorking && 
                          testResults.connectivity?.productCategoriesWorking &&
                          testResults.schema?.hasParentField &&
                          testResults.schema?.hasIsSubcategoryField &&
                          testResults.bidirectional &&
                          testResults.filtering?.subcategoriesFilter;
    
    console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ HIERARCHICAL SYSTEM WORKING' : '❌ ISSUES DETECTED'}`);
    
    if (overallSuccess) {
      console.log('\n🎉 The hierarchical category system is fully functional and ready for production use!');
    } else {
      console.log('\n⚠️ Some issues were detected. Please review the test results above.');
    }
    
  } catch (error) {
    console.error('\n❌ TEST EXECUTION FAILED');
    console.error(`Error: ${error.message}`);
  }
}

// Run the tests
if (require.main === module) {
  runComprehensiveTests();
}

module.exports = { runComprehensiveTests };
