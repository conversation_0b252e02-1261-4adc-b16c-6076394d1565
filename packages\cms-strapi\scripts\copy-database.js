/**
 * Copy Database Script
 * This script helps copy the entire Strapi database to another machine
 */

const fs = require('fs');
const path = require('path');

function copyDatabase() {
  console.log('💾 STRAPI DATABASE COPY UTILITY');
  console.log('=' .repeat(50));
  
  const projectRoot = path.join(__dirname, '..');
  const dbPath = path.join(projectRoot, '.tmp');
  const uploadsPath = path.join(projectRoot, 'public', 'uploads');
  const backupDir = path.join(projectRoot, 'database-backup');
  
  try {
    // Create backup directory
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
      console.log(`✅ Created backup directory: ${backupDir}`);
    }
    
    console.log('\n📋 Database Copy Information:');
    console.log(`   Source Database: ${dbPath}`);
    console.log(`   Source Uploads: ${uploadsPath}`);
    console.log(`   Backup Location: ${backupDir}`);
    
    // Check if database exists
    if (fs.existsSync(dbPath)) {
      console.log('\n✅ Database found - ready for copy');
      
      // Copy database files
      const dbBackupPath = path.join(backupDir, '.tmp');
      if (fs.existsSync(dbBackupPath)) {
        fs.rmSync(dbBackupPath, { recursive: true, force: true });
      }
      
      copyDirectory(dbPath, dbBackupPath);
      console.log('✅ Database files copied');
      
    } else {
      console.log('\n❌ Database not found. Make sure Strapi has been started at least once.');
    }
    
    // Check if uploads exist
    if (fs.existsSync(uploadsPath)) {
      console.log('✅ Upload files found - copying...');
      
      const uploadsBackupPath = path.join(backupDir, 'uploads');
      if (fs.existsSync(uploadsBackupPath)) {
        fs.rmSync(uploadsBackupPath, { recursive: true, force: true });
      }
      
      copyDirectory(uploadsPath, uploadsBackupPath);
      console.log('✅ Upload files copied');
      
    } else {
      console.log('⚠️  No upload files found');
    }
    
    // Create instructions file
    const instructionsPath = path.join(backupDir, 'RESTORE-INSTRUCTIONS.md');
    const instructions = `# Database Restore Instructions

## What's Included
- \`.tmp/\` - Complete SQLite database with all data
- \`uploads/\` - All uploaded media files (if any)

## How to Restore on Another Machine

### Step 1: Setup New Strapi Instance
1. Copy the entire ONDC seller app project to the new machine
2. Install dependencies: \`npm install\`
3. **DO NOT start Strapi yet**

### Step 2: Copy Database Files
1. Copy the \`.tmp\` folder from this backup to: \`packages/cms-strapi/.tmp\`
2. Copy the \`uploads\` folder to: \`packages/cms-strapi/public/uploads\`

### Step 3: Update Configuration (if needed)
1. Check \`packages/cms-strapi/.env\` file
2. Update any environment-specific settings
3. Ensure database configuration matches

### Step 4: Start Strapi
1. Navigate to: \`packages/cms-strapi\`
2. Run: \`npm run develop\`
3. Access admin panel: \`http://localhost:1337/admin\`

## Alternative: Fresh Install + Data Import
If database copy doesn't work:
1. Start fresh Strapi instance
2. Use the export/import scripts instead
3. Run: \`node scripts/import-data.js\`

## Verification
After restore, verify:
- [ ] Admin panel loads: http://localhost:1337/admin
- [ ] API responds: http://localhost:1337/api
- [ ] Data is present: http://localhost:1337/api/banners
- [ ] Media files work (if any)

## Backup Created
- **Date**: ${new Date().toISOString()}
- **Source**: ${projectRoot}
- **Backup**: ${backupDir}
`;
    
    fs.writeFileSync(instructionsPath, instructions);
    
    console.log('\n✅ DATABASE BACKUP COMPLETED!');
    console.log('=' .repeat(50));
    console.log(`📁 Backup location: ${backupDir}`);
    console.log('📖 Read RESTORE-INSTRUCTIONS.md for restore steps');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Copy the entire "database-backup" folder to your new machine');
    console.log('2. Follow the restore instructions');
    console.log('3. Start Strapi and verify data');
    
  } catch (error) {
    console.error('❌ Database copy failed:', error.message);
    throw error;
  }
}

function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const items = fs.readdirSync(src);
  
  for (const item of items) {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);
    
    const stat = fs.statSync(srcPath);
    
    if (stat.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// Run if called directly
if (require.main === module) {
  copyDatabase();
}

module.exports = { copyDatabase };
