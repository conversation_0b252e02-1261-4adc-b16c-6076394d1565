/**
 * Simple script to create the required categories
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function createCategory(categoryData) {
  try {
    const response = await axios.post(`${API_BASE}/categories`, {
      data: categoryData
    }, {
      headers: {
        'Content-Type': 'application/json',
      }
    });
    return response.data;
  } catch (error) {
    console.error(`Error creating category ${categoryData.name}:`, error.response?.data || error.message);
    return null;
  }
}

async function createSimpleCategories() {
  console.log('🎯 Creating categories in the categories collection...');
  
  const categories = [
    {
      name: 'Electronics',
      slug: 'electronics',
      description: 'Electronics and technology products',
      short_description: 'Browse electronics products',
      featured: false,
      active: true,
      sort_order: 1
    },
    {
      name: 'Fashion',
      slug: 'fashion',
      description: 'Fashion and clothing products',
      short_description: 'Browse fashion products',
      featured: false,
      active: true,
      sort_order: 2
    },
    {
      name: 'Home & Garden',
      slug: 'home-garden',
      description: 'Home and garden products',
      short_description: 'Browse home & garden products',
      featured: false,
      active: true,
      sort_order: 3
    },
    {
      name: 'Sports & Fitness',
      slug: 'sports-fitness',
      description: 'Sports and fitness products',
      short_description: 'Browse sports & fitness products',
      featured: false,
      active: true,
      sort_order: 4
    },
    {
      name: 'Books & Media',
      slug: 'books-media',
      description: 'Books and media products',
      short_description: 'Browse books & media products',
      featured: false,
      active: true,
      sort_order: 5
    },
    {
      name: 'Health & Beauty',
      slug: 'health-beauty',
      description: 'Health and beauty products',
      short_description: 'Browse health & beauty products',
      featured: false,
      active: true,
      sort_order: 6
    },
    {
      name: 'Automotive',
      slug: 'automotive',
      description: 'Automotive products',
      short_description: 'Browse automotive products',
      featured: false,
      active: true,
      sort_order: 7
    },
    {
      name: 'Toys & Games',
      slug: 'toys-games',
      description: 'Toys and games products',
      short_description: 'Browse toys & games products',
      featured: false,
      active: true,
      sort_order: 8
    }
  ];

  let successCount = 0;
  
  for (const category of categories) {
    console.log(`\n📝 Creating category: ${category.name}`);
    const result = await createCategory(category);
    
    if (result) {
      console.log(`✅ Successfully created: ${category.name}`);
      successCount++;
    } else {
      console.log(`⚠️ Failed to create: ${category.name}`);
    }
    
    // Small delay to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log(`\n🎉 Category creation completed!`);
  console.log(`📊 Successfully created: ${successCount}/${categories.length} categories`);
  console.log('\n🌐 Verification:');
  console.log('- Categories API: http://localhost:1337/api/categories');
  console.log('- Admin Panel: http://localhost:1337/admin/content-manager/collection-types/api::category.category');
}

// Run the script
if (require.main === module) {
  createSimpleCategories();
}

module.exports = { createSimpleCategories };
