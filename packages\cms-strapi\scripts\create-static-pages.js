#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create static pages in Strapi CMS
 * This script creates the required static pages for the multi-tenant e-commerce platform
 */

const fs = require('fs');
const path = require('path');

// Static pages data
const staticPages = [
  {
    title: 'Terms of Service',
    slug: 'terms-of-service',
    content: `
      <h1>Terms of Service</h1>
      
      <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>
      
      <h2>1. Acceptance of Terms</h2>
      <p>By accessing and using the ONDC Seller Platform, you accept and agree to be bound by the terms and provision of this agreement.</p>
      
      <h2>2. Use License</h2>
      <p>Permission is granted to temporarily download one copy of the materials on ONDC Seller Platform for personal, non-commercial transitory viewing only.</p>
      
      <h2>3. Disclaimer</h2>
      <p>The materials on ONDC Seller Platform are provided on an 'as is' basis. ONDC Seller Platform makes no warranties, expressed or implied.</p>
      
      <h2>4. Limitations</h2>
      <p>In no event shall ONDC Seller Platform or its suppliers be liable for any damages arising out of the use or inability to use the materials.</p>
      
      <h2>5. Accuracy of Materials</h2>
      <p>The materials appearing on ONDC Seller Platform could include technical, typographical, or photographic errors.</p>
      
      <h2>6. Links</h2>
      <p>ONDC Seller Platform has not reviewed all of the sites linked to our platform and is not responsible for the contents of any such linked site.</p>
      
      <h2>7. Modifications</h2>
      <p>ONDC Seller Platform may revise these terms of service at any time without notice.</p>
      
      <h2>8. Governing Law</h2>
      <p>These terms and conditions are governed by and construed in accordance with the laws of India.</p>
      
      <h2>Contact Information</h2>
      <p>If you have any questions about these Terms of Service, please contact <NAME_EMAIL> or +91 **********.</p>
    `,
    excerpt: 'Terms of Service for ONDC Seller Platform users and sellers.',
    metaTitle: 'Terms of Service - ONDC Seller Platform',
    metaDescription: 'Read the Terms of Service for ONDC Seller Platform. Understand your rights and obligations when using our e-commerce platform.',
    metaKeywords: 'terms of service, legal, ONDC, seller platform, conditions',
    status: 'published',
    template: 'terms',
    pageType: 'legal',
    isGlobal: true
  },
  {
    title: 'Contact Us',
    slug: 'contact',
    content: `
      <h1>Contact Us</h1>
      
      <p>We're here to help! Get in touch with our team for any questions, support, or feedback.</p>
      
      <h2>Get in Touch</h2>
      
      <div class="contact-info">
        <h3>📧 Email Support</h3>
        <p><strong>General Inquiries:</strong> <EMAIL></p>
        <p><strong>Technical Support:</strong> <EMAIL></p>
        <p><strong>Business Partnerships:</strong> <EMAIL></p>
        
        <h3>📞 Phone Support</h3>
        <p><strong>Customer Support:</strong> +91 **********</p>
        <p><strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM IST</p>
        
        <h3>📍 Office Address</h3>
        <p>ONDC Seller Platform<br>
        123 Commerce Street<br>
        Business District<br>
        Mumbai, Maharashtra 400001<br>
        India</p>
      </div>
      
      <h2>Support Categories</h2>
      
      <h3>🛍️ For Sellers</h3>
      <ul>
        <li>Account setup and verification</li>
        <li>Product listing assistance</li>
        <li>Order management support</li>
        <li>Payment and settlement queries</li>
      </ul>
      
      <h3>🛒 For Buyers</h3>
      <ul>
        <li>Order tracking and delivery</li>
        <li>Returns and refunds</li>
        <li>Product information</li>
        <li>Account management</li>
      </ul>
      
      <h2>Frequently Asked Questions</h2>
      <p>Before contacting us, you might find answers to common questions in our <a href="/faq">FAQ section</a>.</p>
      
      <h2>Business Hours</h2>
      <p><strong>Customer Support:</strong> Monday - Sunday, 9:00 AM - 9:00 PM IST<br>
      <strong>Technical Support:</strong> Monday - Friday, 9:00 AM - 6:00 PM IST<br>
      <strong>Business Inquiries:</strong> Monday - Friday, 10:00 AM - 5:00 PM IST</p>
    `,
    excerpt: 'Get in touch with ONDC Seller Platform support team for assistance.',
    metaTitle: 'Contact Us - ONDC Seller Platform',
    metaDescription: 'Contact ONDC Seller Platform for support, inquiries, and assistance. Multiple ways to reach our team.',
    metaKeywords: 'contact, support, help, ONDC, customer service, phone, email',
    status: 'published',
    template: 'contact',
    pageType: 'support',
    isGlobal: true
  },
  {
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content: `
      <h1>Privacy Policy</h1>
      
      <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>
      
      <h2>1. Information We Collect</h2>
      
      <h3>Personal Information</h3>
      <p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>
      
      <h3>Usage Information</h3>
      <p>We collect information about how you use our platform, including pages visited, features used, and actions taken.</p>
      
      <h3>Device Information</h3>
      <p>We collect information about the device you use to access our platform, including IP address, browser type, and operating system.</p>
      
      <h2>2. How We Use Your Information</h2>
      <ul>
        <li>To provide and maintain our services</li>
        <li>To process transactions and send related information</li>
        <li>To send you technical notices and support messages</li>
        <li>To communicate with you about products, services, and events</li>
        <li>To monitor and analyze trends and usage</li>
        <li>To detect, investigate, and prevent fraudulent transactions</li>
      </ul>
      
      <h2>3. Information Sharing</h2>
      <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
      
      <h2>4. Data Security</h2>
      <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
      
      <h2>5. Your Rights</h2>
      <p>You have the right to access, update, or delete your personal information. You may also opt out of certain communications from us.</p>
      
      <h2>6. Cookies</h2>
      <p>We use cookies and similar technologies to enhance your experience on our platform and analyze usage patterns.</p>
      
      <h2>7. Changes to This Policy</h2>
      <p>We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page.</p>
      
      <h2>Contact Us</h2>
      <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>
    `,
    excerpt: 'Learn how ONDC Seller Platform collects, uses, and protects your personal information.',
    metaTitle: 'Privacy Policy - ONDC Seller Platform',
    metaDescription: 'Read our Privacy Policy to understand how ONDC Seller Platform handles your personal data and protects your privacy.',
    metaKeywords: 'privacy policy, data protection, personal information, ONDC, privacy rights',
    status: 'published',
    template: 'privacy',
    pageType: 'legal',
    isGlobal: true
  }
];

// Additional pages (FAQ and Returns) will be added in the next part
const additionalPages = [
  {
    title: 'Frequently Asked Questions',
    slug: 'faq',
    content: `
      <h1>Frequently Asked Questions</h1>
      
      <h2>General Questions</h2>
      
      <h3>What is ONDC Seller Platform?</h3>
      <p>ONDC Seller Platform is a comprehensive e-commerce solution that enables sellers to list and sell their products on the Open Network for Digital Commerce (ONDC).</p>
      
      <h3>How do I get started as a seller?</h3>
      <p>To get started, simply sign up for an account, complete the verification process, and start listing your products. Our team will guide you through the onboarding process.</p>
      
      <h2>Account & Registration</h2>
      
      <h3>What documents do I need to register?</h3>
      <ul>
        <li>Business registration certificate</li>
        <li>GST registration number</li>
        <li>Bank account details</li>
        <li>Identity proof (Aadhaar/PAN)</li>
        <li>Address proof</li>
      </ul>
      
      <h3>How long does verification take?</h3>
      <p>Account verification typically takes 2-3 business days. You'll receive email updates on your verification status.</p>
      
      <h2>Product Listing</h2>
      
      <h3>How many products can I list?</h3>
      <p>There's no limit to the number of products you can list on our platform.</p>
      
      <h3>What product categories are supported?</h3>
      <p>We support a wide range of categories including electronics, fashion, home & garden, books, and more.</p>
      
      <h2>Orders & Payments</h2>
      
      <h3>How do I receive payments?</h3>
      <p>Payments are automatically transferred to your registered bank account within 2-3 business days after order delivery.</p>
      
      <h3>What are the commission charges?</h3>
      <p>Commission charges vary by category. Please refer to our seller agreement for detailed information.</p>
      
      <h2>Shipping & Delivery</h2>
      
      <h3>Do you provide shipping services?</h3>
      <p>Yes, we have partnerships with multiple logistics providers to ensure reliable delivery across India.</p>
      
      <h3>Can I use my own shipping partner?</h3>
      <p>Yes, you can use your preferred shipping partner or our recommended logistics partners.</p>
      
      <h2>Support</h2>
      
      <h3>How can I contact support?</h3>
      <p>You can reach our support team via <NAME_EMAIL> or call +91 **********.</p>
      
      <h3>What are your support hours?</h3>
      <p>Our support team is available Monday to Sunday, 9:00 AM to 9:00 PM IST.</p>
    `,
    excerpt: 'Find answers to commonly asked questions about ONDC Seller Platform.',
    metaTitle: 'FAQ - ONDC Seller Platform',
    metaDescription: 'Get answers to frequently asked questions about selling on ONDC Seller Platform.',
    metaKeywords: 'FAQ, questions, help, support, ONDC, seller guide',
    status: 'published',
    template: 'faq',
    pageType: 'support',
    isGlobal: true
  },
  {
    title: 'Returns & Refunds',
    slug: 'returns',
    content: `
      <h1>Returns & Refunds Policy</h1>
      
      <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>
      
      <h2>Return Policy Overview</h2>
      <p>We want you to be completely satisfied with your purchase. If you're not happy with your order, we offer a hassle-free return policy.</p>
      
      <h2>Return Eligibility</h2>
      
      <h3>Items Eligible for Return</h3>
      <ul>
        <li>Items in original condition with tags attached</li>
        <li>Items returned within the specified return window</li>
        <li>Items that are not damaged or used</li>
        <li>Items with original packaging and accessories</li>
      </ul>
      
      <h3>Items Not Eligible for Return</h3>
      <ul>
        <li>Perishable goods (food, flowers, etc.)</li>
        <li>Personal care items</li>
        <li>Customized or personalized items</li>
        <li>Digital downloads</li>
        <li>Gift cards</li>
      </ul>
      
      <h2>Return Timeframes</h2>
      <ul>
        <li><strong>Electronics:</strong> 7 days from delivery</li>
        <li><strong>Fashion & Apparel:</strong> 15 days from delivery</li>
        <li><strong>Home & Garden:</strong> 10 days from delivery</li>
        <li><strong>Books:</strong> 30 days from delivery</li>
      </ul>
      
      <h2>How to Return an Item</h2>
      
      <h3>Step 1: Initiate Return</h3>
      <p>Log into your account and go to "My Orders" to initiate a return request.</p>
      
      <h3>Step 2: Select Return Reason</h3>
      <p>Choose the appropriate reason for your return from the dropdown menu.</p>
      
      <h3>Step 3: Schedule Pickup</h3>
      <p>Schedule a convenient pickup time or drop off at a designated location.</p>
      
      <h3>Step 4: Pack the Item</h3>
      <p>Pack the item securely in its original packaging with all accessories.</p>
      
      <h2>Refund Process</h2>
      
      <h3>Refund Timeline</h3>
      <ul>
        <li><strong>Credit/Debit Cards:</strong> 5-7 business days</li>
        <li><strong>Net Banking:</strong> 5-7 business days</li>
        <li><strong>UPI:</strong> 1-3 business days</li>
        <li><strong>Wallet:</strong> Instant</li>
      </ul>
      
      <h3>Refund Amount</h3>
      <p>You will receive a full refund of the item price. Shipping charges are non-refundable unless the return is due to our error.</p>
      
      <h2>Exchange Policy</h2>
      <p>We offer exchanges for size, color, or model variations subject to availability. Exchange requests must be made within the return window.</p>
      
      <h2>Damaged or Defective Items</h2>
      <p>If you receive a damaged or defective item, please contact us <NAME_EMAIL> with photos of the item and packaging.</p>
      
      <h2>Contact Us</h2>
      <p>For any questions about returns or refunds, please contact our customer support team:</p>
      <ul>
        <li><strong>Email:</strong> <EMAIL></li>
        <li><strong>Phone:</strong> +91 **********</li>
        <li><strong>Hours:</strong> Monday - Sunday, 9:00 AM - 9:00 PM IST</li>
      </ul>
    `,
    excerpt: 'Learn about our returns and refunds policy for a hassle-free shopping experience.',
    metaTitle: 'Returns & Refunds - ONDC Seller Platform',
    metaDescription: 'Understand our returns and refunds policy. Easy returns, quick refunds, and excellent customer service.',
    metaKeywords: 'returns, refunds, exchange, policy, customer service, ONDC',
    status: 'published',
    template: 'returns',
    pageType: 'support',
    isGlobal: true
  }
];

// Combine all pages
const allPages = [...staticPages, ...additionalPages];

console.log('📄 Static Pages Data Generated');
console.log(`📊 Total pages: ${allPages.length}`);
console.log('📝 Pages created:');
allPages.forEach(page => {
  console.log(`   - ${page.title} (/${page.slug})`);
});

console.log('\n🚀 To create these pages in Strapi CMS:');
console.log('1. Start your Strapi CMS server');
console.log('2. Go to Content Manager > Pages');
console.log('3. Create new entries using the data above');
console.log('4. Make sure to set status to "published"');

// Export for use in other scripts
module.exports = { allPages, staticPages, additionalPages };
