/**
 * Strapi Data Transfer Manager
 * Master script for all data transfer operations
 */

const { exportData } = require('./export-data');
const { importData } = require('./import-data');
const { copyDatabase } = require('./copy-database');
const { setupNetworkAccess } = require('./setup-network-access');

function showMenu() {
  console.log('\n🚀 STRAPI DATA TRANSFER MANAGER');
  console.log('=' .repeat(50));
  console.log('Choose an option:');
  console.log('');
  console.log('📤 EXPORT OPTIONS:');
  console.log('  1. Export data to JSON files');
  console.log('  2. Copy entire database');
  console.log('');
  console.log('📥 IMPORT OPTIONS:');
  console.log('  3. Import data from JSON files');
  console.log('');
  console.log('🌐 NETWORK ACCESS:');
  console.log('  4. Setup network access (access from other devices)');
  console.log('');
  console.log('📋 INFORMATION:');
  console.log('  5. Show all available methods');
  console.log('  6. Exit');
  console.log('');
}

function showAllMethods() {
  console.log('\n📚 ALL DATA TRANSFER METHODS');
  console.log('=' .repeat(50));
  
  console.log('\n🔄 METHOD 1: JSON Export/Import');
  console.log('   Best for: Different Strapi versions, selective data transfer');
  console.log('   Steps:');
  console.log('   1. Run: node scripts/data-transfer-manager.js → Option 1');
  console.log('   2. Copy exports folder to new machine');
  console.log('   3. Run: node scripts/data-transfer-manager.js → Option 3');
  
  console.log('\n💾 METHOD 2: Database Copy');
  console.log('   Best for: Identical setups, complete transfer');
  console.log('   Steps:');
  console.log('   1. Run: node scripts/data-transfer-manager.js → Option 2');
  console.log('   2. Copy database-backup folder to new machine');
  console.log('   3. Follow restore instructions');
  
  console.log('\n🌐 METHOD 3: Network Access');
  console.log('   Best for: Real-time access, development, testing');
  console.log('   Steps:');
  console.log('   1. Run: node scripts/data-transfer-manager.js → Option 4');
  console.log('   2. Access from any device on same network');
  console.log('   3. Use provided IP address and URLs');
  
  console.log('\n📱 METHOD 4: Cloud Deployment');
  console.log('   Best for: Production, remote access');
  console.log('   Options:');
  console.log('   - Deploy to Heroku, Railway, or DigitalOcean');
  console.log('   - Use cloud databases (PostgreSQL, MySQL)');
  console.log('   - Configure environment variables');
  
  console.log('\n🔧 METHOD 5: Manual API Transfer');
  console.log('   Best for: Custom requirements, specific data');
  console.log('   Steps:');
  console.log('   1. Export specific collections via API');
  console.log('   2. Transform data as needed');
  console.log('   3. Import via POST requests to new instance');
}

async function handleChoice(choice) {
  try {
    switch (choice) {
      case '1':
        console.log('\n📤 Starting data export...');
        await exportData();
        break;
        
      case '2':
        console.log('\n💾 Starting database copy...');
        copyDatabase();
        break;
        
      case '3':
        console.log('\n📥 Starting data import...');
        await importData();
        break;
        
      case '4':
        console.log('\n🌐 Setting up network access...');
        setupNetworkAccess();
        break;
        
      case '5':
        showAllMethods();
        break;
        
      case '6':
        console.log('\n👋 Goodbye!');
        process.exit(0);
        break;
        
      default:
        console.log('\n❌ Invalid choice. Please try again.');
    }
  } catch (error) {
    console.error('\n❌ Operation failed:', error.message);
  }
}

async function main() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const askQuestion = (question) => {
    return new Promise((resolve) => {
      rl.question(question, resolve);
    });
  };
  
  while (true) {
    showMenu();
    const choice = await askQuestion('Enter your choice (1-6): ');
    await handleChoice(choice.trim());
    
    if (choice.trim() === '6') break;
    
    console.log('\n' + '='.repeat(50));
    await askQuestion('Press Enter to continue...');
  }
  
  rl.close();
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  exportData,
  importData,
  copyDatabase,
  setupNetworkAccess
};
