/**
 * Diagnose and Fix Migration Script for Strapi v5
 * Investigates relationship structure and completes the migration
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Enhanced request function with better error handling
async function strapiRequest(endpoint, method = 'GET', data = null, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${API_BASE}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 20000, // Increased timeout
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = { data };
      }

      const response = await axios(config);
      return { success: true, data: response.data };
    } catch (error) {
      console.log(`   Attempt ${attempt}/${retries} failed for ${method} ${endpoint}`);
      
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || 'Unknown error';
        const details = error.response.data?.error?.details || {};
        
        console.log(`   ❌ HTTP ${status}: ${message}`);
        if (Object.keys(details).length > 0) {
          console.log(`   📋 Details:`, JSON.stringify(details, null, 2));
        }
        
        if (status === 400 && message.includes('already exists')) {
          return { success: true, data: null, exists: true };
        }
        
        if (status === 403) {
          console.log(`   ❌ Permission denied. Check API permissions.`);
          return { success: false, error: 'Permission denied' };
        }
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }
      
      if (attempt === retries) {
        return { 
          success: false, 
          error: error.message,
          status: error.response?.status,
          details: error.response?.data
        };
      }
      
      // Progressive backoff
      const delay = Math.min(2000 * attempt, 8000);
      console.log(`   ⏳ Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics',
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

function logProgress(step, message, type = 'info') {
  const timestamp = new Date().toISOString();
  const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${symbols[type]} [${timestamp}] ${step}: ${message}`);
}

async function createBackup(step, data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, 'migration-backups');
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const backupFile = path.join(backupDir, `${step}_${timestamp}.json`);
  fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
  console.log(`   📁 Backup created: ${backupFile}`);
  return backupFile;
}

// PHASE 1: Comprehensive Diagnosis
async function diagnoseRelationshipStructure() {
  logProgress('DIAGNOSIS', 'Starting comprehensive relationship structure analysis', 'info');
  
  try {
    // 1. Get sample category with all fields
    logProgress('DIAGNOSIS', 'Analyzing Categories collection structure...', 'info');
    const categoriesResult = await strapiRequest('/categories?pagination[pageSize]=1');
    if (!categoriesResult.success) {
      throw new Error('Failed to fetch categories');
    }
    
    const sampleCategory = categoriesResult.data.data[0];
    console.log('\n📋 Sample Category Structure:');
    console.log(JSON.stringify(sampleCategory, null, 2));
    
    // 2. Get sample product category with all fields
    logProgress('DIAGNOSIS', 'Analyzing Product Categories collection structure...', 'info');
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=1');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }
    
    const sampleProductCategory = productCategoriesResult.data.data[0];
    console.log('\n📋 Sample Product Category Structure:');
    console.log(JSON.stringify(sampleProductCategory, null, 2));
    
    // 3. Test individual access with documentId
    logProgress('DIAGNOSIS', 'Testing individual item access...', 'info');
    const individualCategoryResult = await strapiRequest(`/categories/${sampleCategory.documentId}`);
    if (individualCategoryResult.success) {
      console.log('\n✅ Individual category access works with documentId');
    } else {
      console.log('\n❌ Individual category access failed:', individualCategoryResult.error);
    }
    
    const individualProductCategoryResult = await strapiRequest(`/product-categories/${sampleProductCategory.documentId}`);
    if (individualProductCategoryResult.success) {
      console.log('✅ Individual product category access works with documentId');
    } else {
      console.log('❌ Individual product category access failed:', individualProductCategoryResult.error);
    }
    
    // 4. Test simple PUT operation
    logProgress('DIAGNOSIS', 'Testing simple PUT operation...', 'info');
    const testPutResult = await strapiRequest(`/product-categories/${sampleProductCategory.documentId}`, 'PUT', {
      name: sampleProductCategory.name, // Update with same name
      category_type: sampleProductCategory.category_type || 'subcategory' // Fix null category_type
    });
    
    if (testPutResult.success) {
      console.log('✅ Simple PUT operation works');
    } else {
      console.log('❌ Simple PUT operation failed:', testPutResult.error);
      console.log('   Status:', testPutResult.status);
      console.log('   Details:', testPutResult.details);
    }
    
    return {
      sampleCategory,
      sampleProductCategory,
      individualAccess: individualCategoryResult.success && individualProductCategoryResult.success,
      putOperation: testPutResult.success
    };
    
  } catch (error) {
    logProgress('DIAGNOSIS', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// PHASE 2: Test Relationship Update
async function testRelationshipUpdate() {
  logProgress('TEST', 'Testing relationship update with single item...', 'info');
  
  try {
    // Get all categories and product categories
    const categoriesResult = await strapiRequest('/categories?pagination[pageSize]=100');
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100');
    
    if (!categoriesResult.success || !productCategoriesResult.success) {
      throw new Error('Failed to fetch collections');
    }
    
    const categories = categoriesResult.data.data || [];
    const productCategories = productCategoriesResult.data.data || [];
    
    // Find Electronics category and Smartphones subcategory for testing
    const electronicsCategory = categories.find(cat => cat.name === 'Electronics');
    const smartphonesSubcategory = productCategories.find(cat => cat.name === 'Smartphones');
    
    if (!electronicsCategory || !smartphonesSubcategory) {
      throw new Error('Test categories not found');
    }
    
    console.log(`\n🧪 Test Setup:`);
    console.log(`   Main Category: ${electronicsCategory.name} (${electronicsCategory.documentId})`);
    console.log(`   Subcategory: ${smartphonesSubcategory.name} (${smartphonesSubcategory.documentId})`);
    
    // Test different payload formats
    const testPayloads = [
      {
        name: 'Format 1: Direct documentId',
        payload: {
          isSubcategory: true,
          parent: electronicsCategory.documentId,
          category_type: 'subcategory'
        }
      },
      {
        name: 'Format 2: Object with documentId',
        payload: {
          isSubcategory: true,
          parent: {
            documentId: electronicsCategory.documentId
          },
          category_type: 'subcategory'
        }
      },
      {
        name: 'Format 3: Object with id',
        payload: {
          isSubcategory: true,
          parent: {
            id: electronicsCategory.id
          },
          category_type: 'subcategory'
        }
      },
      {
        name: 'Format 4: Array format',
        payload: {
          isSubcategory: true,
          parent: [electronicsCategory.documentId],
          category_type: 'subcategory'
        }
      }
    ];
    
    for (const test of testPayloads) {
      console.log(`\n🧪 Testing ${test.name}...`);
      
      const result = await strapiRequest(
        `/product-categories/${smartphonesSubcategory.documentId}`, 
        'PUT', 
        test.payload
      );
      
      if (result.success) {
        console.log(`   ✅ ${test.name}: SUCCESS`);
        
        // Verify the update worked
        const verifyResult = await strapiRequest(`/product-categories/${smartphonesSubcategory.documentId}?populate=parent`);
        if (verifyResult.success && verifyResult.data.data.parent) {
          console.log(`   ✅ Verification: Parent relationship established`);
          console.log(`   📋 Parent: ${verifyResult.data.data.parent.name}`);
          return { success: true, workingFormat: test.name, payload: test.payload };
        } else {
          console.log(`   ⚠️ Update succeeded but parent relationship not found`);
        }
      } else {
        console.log(`   ❌ ${test.name}: FAILED - ${result.error}`);
        if (result.details) {
          console.log(`   📋 Details:`, JSON.stringify(result.details, null, 2));
        }
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return { success: false, error: 'No working format found' };

  } catch (error) {
    logProgress('TEST', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// PHASE 3: Complete Migration with Working Format
async function completeMigrationWithWorkingFormat(workingFormat) {
  logProgress('MIGRATION', 'Starting complete migration with verified format', 'info');

  try {
    // Get all categories and product categories
    const categoriesResult = await strapiRequest('/categories?pagination[pageSize]=100');
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100');

    if (!categoriesResult.success || !productCategoriesResult.success) {
      throw new Error('Failed to fetch collections');
    }

    const categories = categoriesResult.data.data || [];
    const productCategories = productCategoriesResult.data.data || [];

    // Create category lookup map
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat;
    });

    await createBackup('migration_before_final', { categories, productCategories });

    logProgress('MIGRATION', `Found ${categories.length} main categories and ${productCategories.length} product categories`, 'success');

    let updateCount = 0;
    let errorCount = 0;
    const results = [];

    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];

      if (parentCategoryName && categoryMap[parentCategoryName]) {
        logProgress('MIGRATION', `Updating: ${subcat.name} → ${parentCategoryName}`, 'info');

        // Use the working format discovered in testing
        let payload;
        if (workingFormat.name.includes('Format 1')) {
          payload = {
            isSubcategory: true,
            parent: categoryMap[parentCategoryName].documentId,
            category_type: 'subcategory'
          };
        } else if (workingFormat.name.includes('Format 2')) {
          payload = {
            isSubcategory: true,
            parent: {
              documentId: categoryMap[parentCategoryName].documentId
            },
            category_type: 'subcategory'
          };
        } else if (workingFormat.name.includes('Format 3')) {
          payload = {
            isSubcategory: true,
            parent: {
              id: categoryMap[parentCategoryName].id
            },
            category_type: 'subcategory'
          };
        } else {
          payload = workingFormat.payload;
        }

        // Ensure category_type is set (required field)
        if (!payload.category_type) {
          payload.category_type = 'subcategory';
        }

        const result = await strapiRequest(`/product-categories/${subcat.documentId}`, 'PUT', payload);

        if (result.success) {
          logProgress('MIGRATION', `✅ Successfully updated: ${subcat.name}`, 'success');
          updateCount++;
          results.push({ name: subcat.name, parent: parentCategoryName, status: 'success' });
        } else {
          logProgress('MIGRATION', `❌ Failed to update: ${subcat.name}: ${result.error}`, 'error');
          errorCount++;
          results.push({ name: subcat.name, parent: parentCategoryName, status: 'failed', error: result.error });
        }
      } else {
        logProgress('MIGRATION', `Skipping unmapped category: ${subcat.name}`, 'warning');
        results.push({ name: subcat.name, parent: 'none', status: 'skipped' });
      }

      // Delay between updates to prevent rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    await createBackup('migration_after_final', { updateCount, errorCount, results });

    logProgress('MIGRATION', `Migration completed. Updated: ${updateCount}, Errors: ${errorCount}`, 'success');
    return { updateCount, errorCount, results };

  } catch (error) {
    logProgress('MIGRATION', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// PHASE 4: Final Verification
async function finalVerification() {
  logProgress('VERIFY', 'Starting final verification of relationships', 'info');

  try {
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100&populate=parent');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories for verification');
    }

    const productCategories = productCategoriesResult.data.data || [];
    let verifiedCount = 0;
    let totalMapped = 0;

    console.log('\n📋 FINAL RELATIONSHIP VERIFICATION:');
    console.log('=' .repeat(60));

    // Group by parent category
    const groupedResults = {};

    for (const subcat of productCategories) {
      const expectedParent = subcategoryMapping[subcat.name];

      if (expectedParent) {
        totalMapped++;

        if (!groupedResults[expectedParent]) {
          groupedResults[expectedParent] = { success: [], failed: [] };
        }

        if (subcat.parent && subcat.parent.name === expectedParent) {
          console.log(`   ✅ ${subcat.name} → ${subcat.parent.name}`);
          groupedResults[expectedParent].success.push(subcat.name);
          verifiedCount++;
        } else {
          console.log(`   ❌ ${subcat.name} → ${subcat.parent?.name || 'NO PARENT'} (expected: ${expectedParent})`);
          groupedResults[expectedParent].failed.push(subcat.name);
        }
      }
    }

    console.log('\n📊 SUMMARY BY PARENT CATEGORY:');
    console.log('-' .repeat(40));

    for (const [parentName, results] of Object.entries(groupedResults)) {
      const total = results.success.length + results.failed.length;
      console.log(`\n📁 ${parentName}: ${results.success.length}/${total} successful`);

      if (results.success.length > 0) {
        console.log(`   ✅ Success: ${results.success.join(', ')}`);
      }

      if (results.failed.length > 0) {
        console.log(`   ❌ Failed: ${results.failed.join(', ')}`);
      }
    }

    logProgress('VERIFY', `Verification completed. ${verifiedCount}/${totalMapped} relationships verified`, 'success');
    return { verifiedCount, totalMapped, groupedResults };

  } catch (error) {
    logProgress('VERIFY', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// Main execution function
async function runDiagnosisAndFix() {
  console.log('🔍 STRAPI V5 MIGRATION DIAGNOSIS AND FIX');
  console.log('=' .repeat(80));
  console.log('📋 Phase 1: Diagnose relationship structure');
  console.log('📋 Phase 2: Test relationship update formats');
  console.log('📋 Phase 3: Complete migration with working format');
  console.log('📋 Phase 4: Final verification');
  console.log('=' .repeat(80));

  const startTime = Date.now();

  try {
    // Test connectivity
    logProgress('INIT', 'Testing API connectivity...', 'info');
    const healthCheck = await strapiRequest('/categories');
    if (!healthCheck.success) {
      throw new Error('Cannot connect to Strapi API. Please ensure Strapi is running.');
    }
    logProgress('INIT', 'API connectivity confirmed', 'success');

    // Phase 1: Diagnosis
    console.log('\n🔍 PHASE 1: COMPREHENSIVE DIAGNOSIS');
    console.log('-' .repeat(50));
    const diagnosisResult = await diagnoseRelationshipStructure();

    if (!diagnosisResult.individualAccess || !diagnosisResult.putOperation) {
      throw new Error('Basic API operations failed. Cannot proceed with migration.');
    }

    // Phase 2: Test relationship formats
    console.log('\n🧪 PHASE 2: TEST RELATIONSHIP FORMATS');
    console.log('-' .repeat(50));
    const testResult = await testRelationshipUpdate();

    if (!testResult.success) {
      throw new Error('No working relationship format found. Manual investigation required.');
    }

    console.log(`\n✅ Working format identified: ${testResult.workingFormat}`);

    // Phase 3: Complete migration
    console.log('\n🔄 PHASE 3: COMPLETE MIGRATION');
    console.log('-' .repeat(50));
    const migrationResult = await completeMigrationWithWorkingFormat(testResult);

    // Phase 4: Final verification
    console.log('\n🔍 PHASE 4: FINAL VERIFICATION');
    console.log('-' .repeat(50));
    const verificationResult = await finalVerification();

    // Final summary
    console.log('\n✅ DIAGNOSIS AND FIX COMPLETED!');
    console.log('=' .repeat(80));

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log(`📊 Final Results:`);
    console.log(`   • Working Format: ${testResult.workingFormat}`);
    console.log(`   • Subcategories Updated: ${migrationResult.updateCount}`);
    console.log(`   • Update Errors: ${migrationResult.errorCount}`);
    console.log(`   • Relationships Verified: ${verificationResult.verifiedCount}/${verificationResult.totalMapped}`);

    console.log('\n🌐 Verification URLs:');
    console.log('   • Categories: http://localhost:1337/api/categories');
    console.log('   • Product Categories: http://localhost:1337/api/product-categories?populate=parent');
    console.log('   • Admin Panel: http://localhost:1337/admin');

    if (verificationResult.verifiedCount === verificationResult.totalMapped) {
      console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY! All relationships verified.');
    } else {
      console.log('\n⚠️ MIGRATION PARTIALLY COMPLETED. Some relationships need manual review.');
    }

  } catch (error) {
    console.error('\n❌ DIAGNOSIS AND FIX FAILED');
    console.error('=' .repeat(80));
    console.error(`Error: ${error.message}`);
    console.error('\n🔧 Next Steps:');
    console.error('1. Check Strapi server status and API permissions');
    console.error('2. Review migration backups in ./migration-backups/');
    console.error('3. Consider manual completion via admin panel');

    process.exit(1);
  }
}

// Run the diagnosis and fix
if (require.main === module) {
  runDiagnosisAndFix();
}

module.exports = {
  runDiagnosisAndFix,
  diagnoseRelationshipStructure,
  testRelationshipUpdate,
  completeMigrationWithWorkingFormat,
  finalVerification
};
