/**
 * Diagnostic script to understand the API structure and identify correct endpoints
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data
    };
  }
}

async function diagnoseAPI() {
  console.log('🔍 DIAGNOSING STRAPI API STRUCTURE');
  console.log('=' .repeat(60));
  
  // Test basic connectivity
  console.log('\n1. Testing basic connectivity...');
  const healthCheck = await strapiRequest('/categories');
  if (healthCheck.success) {
    console.log('✅ Basic API connectivity: OK');
    console.log(`   Found ${healthCheck.data.data?.length || 0} categories`);
  } else {
    console.log('❌ Basic API connectivity: FAILED');
    console.log(`   Error: ${healthCheck.error}`);
    return;
  }
  
  // Get detailed category information
  console.log('\n2. Analyzing Categories collection...');
  const categories = healthCheck.data.data || [];
  
  console.log('\n📋 Categories found:');
  categories.forEach((cat, index) => {
    console.log(`   ${index + 1}. ID: ${cat.id}, Name: "${cat.name}", Attributes: ${Object.keys(cat).join(', ')}`);
  });
  
  // Test individual category access
  if (categories.length > 0) {
    const firstCategory = categories[0];
    console.log(`\n3. Testing individual category access (ID: ${firstCategory.id})...`);
    
    const singleCat = await strapiRequest(`/categories/${firstCategory.id}`);
    if (singleCat.success) {
      console.log('✅ Individual category access: OK');
      console.log(`   Category structure:`, JSON.stringify(singleCat.data.data, null, 2));
    } else {
      console.log('❌ Individual category access: FAILED');
      console.log(`   Status: ${singleCat.status}, Error: ${singleCat.error}`);
    }
    
    // Test PUT operation
    console.log(`\n4. Testing PUT operation on category ${firstCategory.id}...`);
    const testUpdate = await strapiRequest(`/categories/${firstCategory.id}`, 'PUT', {
      name: firstCategory.name // Just update with same name
    });
    
    if (testUpdate.success) {
      console.log('✅ PUT operation: OK');
    } else {
      console.log('❌ PUT operation: FAILED');
      console.log(`   Status: ${testUpdate.status}, Error: ${testUpdate.error}`);
      console.log(`   Response:`, testUpdate.responseData);
    }
  }
  
  // Test Product Categories
  console.log('\n5. Analyzing Product Categories collection...');
  const productCats = await strapiRequest('/product-categories');
  if (productCats.success) {
    console.log('✅ Product Categories access: OK');
    console.log(`   Found ${productCats.data.data?.length || 0} product categories`);
    
    const productCategories = productCats.data.data || [];
    console.log('\n📋 Product Categories found:');
    productCategories.slice(0, 5).forEach((cat, index) => {
      console.log(`   ${index + 1}. ID: ${cat.id}, Name: "${cat.name}", Attributes: ${Object.keys(cat).join(', ')}`);
    });
    
    if (productCategories.length > 5) {
      console.log(`   ... and ${productCategories.length - 5} more`);
    }
    
    // Test individual product category access
    if (productCategories.length > 0) {
      const firstProdCat = productCategories[0];
      console.log(`\n6. Testing individual product category access (ID: ${firstProdCat.id})...`);
      
      const singleProdCat = await strapiRequest(`/product-categories/${firstProdCat.id}`);
      if (singleProdCat.success) {
        console.log('✅ Individual product category access: OK');
        console.log(`   Product category structure:`, JSON.stringify(singleProdCat.data.data, null, 2));
      } else {
        console.log('❌ Individual product category access: FAILED');
        console.log(`   Status: ${singleProdCat.status}, Error: ${singleProdCat.error}`);
      }
      
      // Test PUT operation on product category
      console.log(`\n7. Testing PUT operation on product category ${firstProdCat.id}...`);
      const testProdUpdate = await strapiRequest(`/product-categories/${firstProdCat.id}`, 'PUT', {
        name: firstProdCat.name // Just update with same name
      });
      
      if (testProdUpdate.success) {
        console.log('✅ Product Category PUT operation: OK');
      } else {
        console.log('❌ Product Category PUT operation: FAILED');
        console.log(`   Status: ${testProdUpdate.status}, Error: ${testProdUpdate.error}`);
        console.log(`   Response:`, testProdUpdate.responseData);
      }
    }
  } else {
    console.log('❌ Product Categories access: FAILED');
    console.log(`   Error: ${productCats.error}`);
  }
  
  // Test API permissions endpoints
  console.log('\n8. Testing API permissions...');
  const permissionsTest = await strapiRequest('/users-permissions/roles');
  if (permissionsTest.success) {
    console.log('✅ Permissions endpoint accessible');
  } else {
    console.log('❌ Permissions endpoint not accessible');
    console.log(`   Status: ${permissionsTest.status}, Error: ${permissionsTest.error}`);
  }
  
  console.log('\n🎯 DIAGNOSIS COMPLETE');
  console.log('=' .repeat(60));
}

// Run the diagnostic
if (require.main === module) {
  diagnoseAPI().catch(console.error);
}

module.exports = { diagnoseAPI };
