/**
 * Diagnose Relationship Structure Issues
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      details: error.response?.data
    };
  }
}

async function diagnoseRelationshipIssue() {
  console.log('🔍 DIAGNOSING RELATIONSHIP STRUCTURE ISSUES');
  console.log('=' .repeat(60));
  
  // 1. Check existing data structure
  console.log('\n📋 EXISTING DATA ANALYSIS:');
  
  const categoriesResult = await strapiRequest('/categories');
  if (categoriesResult.success) {
    console.log(`✅ Categories: ${categoriesResult.data.data.length} found`);
    console.log('Sample category structure:');
    console.log(JSON.stringify(categoriesResult.data.data[0], null, 2));
  }
  
  const productCategoriesResult = await strapiRequest('/product-categories?populate=parent');
  if (productCategoriesResult.success) {
    console.log(`\n✅ Product Categories: ${productCategoriesResult.data.data.length} found`);
    
    // Analyze existing relationships
    const withParent = productCategoriesResult.data.data.filter(pc => pc.parent);
    const withoutParent = productCategoriesResult.data.data.filter(pc => !pc.parent);
    
    console.log(`   • With parent relationship: ${withParent.length}`);
    console.log(`   • Without parent relationship: ${withoutParent.length}`);
    
    if (withParent.length > 0) {
      console.log('\nSample product category with parent:');
      console.log(JSON.stringify(withParent[0], null, 2));
    }
  }
  
  // 2. Test different relationship formats
  console.log('\n🧪 TESTING RELATIONSHIP FORMATS:');
  
  // Get a test category
  const testCategory = categoriesResult.data.data[0];
  if (!testCategory) {
    console.log('❌ No categories available for testing');
    return;
  }
  
  console.log(`Using test category: ${testCategory.name} (${testCategory.documentId})`);
  
  // Test Format 1: Direct documentId
  console.log('\n🔬 Testing Format 1: Direct documentId');
  const format1Data = {
    name: 'Test Subcategory Format 1',
    slug: 'test-subcategory-format-1',
    isSubcategory: true,
    parent: testCategory.documentId
  };
  
  const format1Result = await strapiRequest('/product-categories', 'POST', format1Data);
  console.log(`Result: ${format1Result.success ? '✅ SUCCESS' : '❌ FAIL'}`);
  if (!format1Result.success) {
    console.log(`Error: ${format1Result.error}`);
    console.log(`Status: ${format1Result.status}`);
    console.log(`Details:`, JSON.stringify(format1Result.details, null, 2));
  } else {
    // Clean up
    await strapiRequest(`/product-categories/${format1Result.data.data.documentId}`, 'DELETE');
  }
  
  // Test Format 2: Object with documentId
  console.log('\n🔬 Testing Format 2: Object with documentId');
  const format2Data = {
    name: 'Test Subcategory Format 2',
    slug: 'test-subcategory-format-2',
    isSubcategory: true,
    parent: {
      documentId: testCategory.documentId
    }
  };
  
  const format2Result = await strapiRequest('/product-categories', 'POST', format2Data);
  console.log(`Result: ${format2Result.success ? '✅ SUCCESS' : '❌ FAIL'}`);
  if (!format2Result.success) {
    console.log(`Error: ${format2Result.error}`);
    console.log(`Status: ${format2Result.status}`);
    console.log(`Details:`, JSON.stringify(format2Result.details, null, 2));
  } else {
    // Clean up
    await strapiRequest(`/product-categories/${format2Result.data.data.documentId}`, 'DELETE');
  }
  
  // Test Format 3: Object with id
  console.log('\n🔬 Testing Format 3: Object with id');
  const format3Data = {
    name: 'Test Subcategory Format 3',
    slug: 'test-subcategory-format-3',
    isSubcategory: true,
    parent: {
      id: testCategory.id
    }
  };
  
  const format3Result = await strapiRequest('/product-categories', 'POST', format3Data);
  console.log(`Result: ${format3Result.success ? '✅ SUCCESS' : '❌ FAIL'}`);
  if (!format3Result.success) {
    console.log(`Error: ${format3Result.error}`);
    console.log(`Status: ${format3Result.status}`);
    console.log(`Details:`, JSON.stringify(format3Result.details, null, 2));
  } else {
    // Clean up
    await strapiRequest(`/product-categories/${format3Result.data.data.documentId}`, 'DELETE');
  }
  
  // Test Format 4: Linking to Categories collection instead
  console.log('\n🔬 Testing Format 4: Link to Categories collection (category field)');
  const format4Data = {
    name: 'Test Subcategory Format 4',
    slug: 'test-subcategory-format-4',
    isSubcategory: true,
    category: testCategory.documentId
  };
  
  const format4Result = await strapiRequest('/product-categories', 'POST', format4Data);
  console.log(`Result: ${format4Result.success ? '✅ SUCCESS' : '❌ FAIL'}`);
  if (!format4Result.success) {
    console.log(`Error: ${format4Result.error}`);
    console.log(`Status: ${format4Result.status}`);
    console.log(`Details:`, JSON.stringify(format4Result.details, null, 2));
  } else {
    // Verify the relationship
    const verifyResult = await strapiRequest(`/product-categories/${format4Result.data.data.documentId}?populate=category`);
    if (verifyResult.success) {
      console.log('✅ Relationship verified with Categories collection');
      console.log(`Linked to: ${verifyResult.data.data.category?.name}`);
    }
    // Clean up
    await strapiRequest(`/product-categories/${format4Result.data.data.documentId}`, 'DELETE');
  }
  
  // 3. Analyze the schema issue
  console.log('\n📋 SCHEMA ANALYSIS:');
  console.log('Based on the schema analysis, the Product Categories collection has:');
  console.log('• parent: relation to api::product-category.product-category (self-referential)');
  console.log('• category: relation to api::category.category (cross-collection)');
  console.log('');
  console.log('This suggests a dual relationship system where:');
  console.log('1. Product Categories can have parent-child relationships within themselves');
  console.log('2. Product Categories can also link to main Categories collection');
  console.log('');
  console.log('The migration appears to be using the "parent" field to link to Categories,');
  console.log('but the schema expects "parent" to link to other Product Categories.');
  console.log('The correct field for linking to Categories collection is "category".');
}

diagnoseRelationshipIssue();
