/**
 * Export Strapi Data Script
 * This script exports all Strapi data to JSON files for transfer to another machine
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const EXPORT_DIR = path.join(__dirname, '..', 'exports');

// Collections to export
const COLLECTIONS = [
  'banners',
  'categories', 
  'product-categories',
  'products',
  'pages'
];

async function exportData() {
  console.log('📦 EXPORTING STRAPI DATA');
  console.log('=' .repeat(50));
  
  try {
    // Create export directory
    if (!fs.existsSync(EXPORT_DIR)) {
      fs.mkdirSync(EXPORT_DIR, { recursive: true });
      console.log(`✅ Created export directory: ${EXPORT_DIR}`);
    }
    
    const exportData = {
      timestamp: new Date().toISOString(),
      collections: {},
      metadata: {
        strapiVersion: '5.0.0',
        exportedBy: 'export-data.js',
        totalRecords: 0
      }
    };
    
    console.log('\n🔄 Exporting collections...');
    
    for (const collection of COLLECTIONS) {
      try {
        console.log(`\n📋 Exporting ${collection}...`);
        
        const response = await axios.get(`${STRAPI_URL}/api/${collection}?pagination[pageSize]=100`);
        
        if (response.data && response.data.data) {
          const data = response.data.data;
          exportData.collections[collection] = {
            data: data,
            count: data.length,
            meta: response.data.meta || {}
          };
          
          console.log(`   ✅ Exported ${data.length} ${collection} records`);
          exportData.metadata.totalRecords += data.length;
          
          // Save individual collection file
          const collectionFile = path.join(EXPORT_DIR, `${collection}.json`);
          fs.writeFileSync(collectionFile, JSON.stringify({
            collection: collection,
            data: data,
            meta: response.data.meta,
            exportedAt: new Date().toISOString()
          }, null, 2));
          
        } else {
          console.log(`   ⚠️  No data found for ${collection}`);
          exportData.collections[collection] = { data: [], count: 0, meta: {} };
        }
        
      } catch (error) {
        console.log(`   ❌ Error exporting ${collection}: ${error.message}`);
        exportData.collections[collection] = { 
          data: [], 
          count: 0, 
          error: error.message 
        };
      }
    }
    
    // Save complete export file
    const exportFile = path.join(EXPORT_DIR, 'complete-export.json');
    fs.writeFileSync(exportFile, JSON.stringify(exportData, null, 2));
    
    // Create import instructions
    const instructionsFile = path.join(EXPORT_DIR, 'IMPORT-INSTRUCTIONS.md');
    const instructions = `# Strapi Data Import Instructions

## Files Exported
- \`complete-export.json\` - All data in one file
- \`banners.json\` - Banner data only
- \`categories.json\` - Category data only
- \`product-categories.json\` - Product category data only
- \`products.json\` - Product data only
- \`pages.json\` - Page data only

## How to Import on Another Machine

### Method 1: Using Import Script
1. Copy all files from this exports folder to the new machine
2. Place them in the same location: \`packages/cms-strapi/exports/\`
3. Run: \`node scripts/import-data.js\`

### Method 2: Manual API Import
1. Start Strapi on the new machine
2. Use the individual JSON files to POST data via API
3. Example: \`POST http://localhost:1337/api/banners\` with banner data

### Method 3: Database Copy
1. Copy the entire \`.tmp\` folder (SQLite database)
2. Place it in the new Strapi installation
3. Start Strapi normally

## Export Summary
- **Total Records**: ${exportData.metadata.totalRecords}
- **Collections**: ${Object.keys(exportData.collections).length}
- **Exported At**: ${exportData.timestamp}

## Next Steps
1. Copy this entire \`exports\` folder to your new machine
2. Follow one of the import methods above
3. Verify data in Strapi admin panel
`;
    
    fs.writeFileSync(instructionsFile, instructions);
    
    console.log('\n✅ EXPORT COMPLETED!');
    console.log('=' .repeat(50));
    console.log(`📁 Export location: ${EXPORT_DIR}`);
    console.log(`📊 Total records exported: ${exportData.metadata.totalRecords}`);
    console.log(`📋 Collections exported: ${Object.keys(exportData.collections).join(', ')}`);
    console.log('\n📖 Next steps:');
    console.log('1. Copy the entire "exports" folder to your new machine');
    console.log('2. Read IMPORT-INSTRUCTIONS.md for import options');
    console.log('3. Run the import script on the new machine');
    
    return exportData;
    
  } catch (error) {
    console.error('❌ Export failed:', error.message);
    throw error;
  }
}

// Run export if called directly
if (require.main === module) {
  exportData().catch(console.error);
}

module.exports = { exportData };
