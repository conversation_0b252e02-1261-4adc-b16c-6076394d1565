/**
 * Final Migration Script - Complete Parent-Child Relationships
 * Fixed to exclude category_type field and use proper Strapi v5 format
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Enhanced request function with better error handling
async function strapiRequest(endpoint, method = 'GET', data = null, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${API_BASE}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = { data };
      }

      const response = await axios(config);
      return { success: true, data: response.data };
    } catch (error) {
      console.log(`   Attempt ${attempt}/${retries} failed for ${method} ${endpoint}`);
      
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || 'Unknown error';
        console.log(`   ❌ HTTP ${status}: ${message}`);
        
        if (status === 400 && message.includes('already exists')) {
          return { success: true, data: null, exists: true };
        }
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }
      
      if (attempt === retries) {
        return { success: false, error: error.message };
      }
      
      // Progressive backoff
      const delay = Math.min(1000 * attempt, 3000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics',
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

function logProgress(step, message, type = 'info') {
  const timestamp = new Date().toISOString();
  const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${symbols[type]} [${timestamp}] ${step}: ${message}`);
}

async function createBackup(step, data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, 'migration-backups');
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const backupFile = path.join(backupDir, `${step}_${timestamp}.json`);
  fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
  console.log(`   📁 Backup created: ${backupFile}`);
  return backupFile;
}

// Main migration function
async function completeMigration() {
  logProgress('MIGRATION', 'Starting final migration to establish parent-child relationships', 'info');
  
  try {
    // Get all categories and product categories
    logProgress('MIGRATION', 'Fetching categories and product categories...', 'info');
    
    const categoriesResult = await strapiRequest('/categories?pagination[pageSize]=100');
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100');
    
    if (!categoriesResult.success || !productCategoriesResult.success) {
      throw new Error('Failed to fetch collections');
    }
    
    const categories = categoriesResult.data.data || [];
    const productCategories = productCategoriesResult.data.data || [];
    
    // Create category lookup map
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat;
    });
    
    await createBackup('final_migration_before', { categories, productCategories });
    
    logProgress('MIGRATION', `Found ${categories.length} main categories and ${productCategories.length} product categories`, 'success');
    
    let updateCount = 0;
    let errorCount = 0;
    const results = [];
    
    // Process each product category
    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];
      
      if (parentCategoryName && categoryMap[parentCategoryName]) {
        logProgress('MIGRATION', `Updating: ${subcat.name} → ${parentCategoryName}`, 'info');
        
        // Create payload WITHOUT category_type field
        const payload = {
          isSubcategory: true,
          parent: categoryMap[parentCategoryName].documentId
        };
        
        const result = await strapiRequest(`/product-categories/${subcat.documentId}`, 'PUT', payload);
        
        if (result.success) {
          logProgress('MIGRATION', `✅ Successfully updated: ${subcat.name}`, 'success');
          updateCount++;
          results.push({ name: subcat.name, parent: parentCategoryName, status: 'success' });
        } else {
          logProgress('MIGRATION', `❌ Failed to update: ${subcat.name}: ${result.error}`, 'error');
          errorCount++;
          results.push({ name: subcat.name, parent: parentCategoryName, status: 'failed', error: result.error });
        }
      } else {
        logProgress('MIGRATION', `Skipping unmapped category: ${subcat.name}`, 'warning');
        results.push({ name: subcat.name, parent: 'none', status: 'skipped' });
      }
      
      // Small delay between updates to prevent rate limiting
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    await createBackup('final_migration_after', { updateCount, errorCount, results });
    
    logProgress('MIGRATION', `Migration completed. Updated: ${updateCount}, Errors: ${errorCount}`, 'success');
    return { updateCount, errorCount, results };
    
  } catch (error) {
    logProgress('MIGRATION', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// Verification function
async function verifyRelationships() {
  logProgress('VERIFY', 'Starting final verification of relationships', 'info');
  
  try {
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100&populate=parent');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories for verification');
    }
    
    const productCategories = productCategoriesResult.data.data || [];
    let verifiedCount = 0;
    let totalMapped = 0;
    
    console.log('\n📋 FINAL RELATIONSHIP VERIFICATION:');
    console.log('=' .repeat(60));
    
    // Group by parent category
    const groupedResults = {};
    
    for (const subcat of productCategories) {
      const expectedParent = subcategoryMapping[subcat.name];
      
      if (expectedParent) {
        totalMapped++;
        
        if (!groupedResults[expectedParent]) {
          groupedResults[expectedParent] = { success: [], failed: [] };
        }
        
        if (subcat.parent && subcat.parent.name === expectedParent) {
          console.log(`   ✅ ${subcat.name} → ${subcat.parent.name}`);
          groupedResults[expectedParent].success.push(subcat.name);
          verifiedCount++;
        } else {
          console.log(`   ❌ ${subcat.name} → ${subcat.parent?.name || 'NO PARENT'} (expected: ${expectedParent})`);
          groupedResults[expectedParent].failed.push(subcat.name);
        }
      }
    }
    
    console.log('\n📊 SUMMARY BY PARENT CATEGORY:');
    console.log('-' .repeat(40));
    
    for (const [parentName, results] of Object.entries(groupedResults)) {
      const total = results.success.length + results.failed.length;
      console.log(`\n📁 ${parentName}: ${results.success.length}/${total} successful`);
      
      if (results.success.length > 0) {
        console.log(`   ✅ Success: ${results.success.join(', ')}`);
      }
      
      if (results.failed.length > 0) {
        console.log(`   ❌ Failed: ${results.failed.join(', ')}`);
      }
    }
    
    logProgress('VERIFY', `Verification completed. ${verifiedCount}/${totalMapped} relationships verified`, 'success');
    return { verifiedCount, totalMapped, groupedResults };
    
  } catch (error) {
    logProgress('VERIFY', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// Main execution function
async function runFinalMigration() {
  console.log('🔄 FINAL MIGRATION: COMPLETE PARENT-CHILD RELATIONSHIPS');
  console.log('=' .repeat(80));
  console.log('📋 Fixed to exclude category_type field validation');
  console.log('📋 Using Strapi v5 documentId format for relationships');
  console.log('📋 Establishing 17 subcategory → 5 main category relationships');
  console.log('=' .repeat(80));
  
  const startTime = Date.now();
  
  try {
    // Test connectivity
    logProgress('INIT', 'Testing API connectivity...', 'info');
    const healthCheck = await strapiRequest('/categories');
    if (!healthCheck.success) {
      throw new Error('Cannot connect to Strapi API. Please ensure Strapi is running.');
    }
    logProgress('INIT', 'API connectivity confirmed', 'success');
    
    // Execute migration
    console.log('\n🔄 STEP 1: COMPLETE MIGRATION');
    console.log('-' .repeat(50));
    const migrationResult = await completeMigration();
    
    // Verify results
    console.log('\n🔍 STEP 2: VERIFY RELATIONSHIPS');
    console.log('-' .repeat(50));
    const verificationResult = await verifyRelationships();
    
    // Final summary
    console.log('\n✅ FINAL MIGRATION COMPLETED!');
    console.log('=' .repeat(80));
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log(`📊 Final Results:`);
    console.log(`   • Subcategories Updated: ${migrationResult.updateCount}`);
    console.log(`   • Update Errors: ${migrationResult.errorCount}`);
    console.log(`   • Relationships Verified: ${verificationResult.verifiedCount}/${verificationResult.totalMapped}`);
    
    console.log('\n🌐 Verification URLs:');
    console.log('   • Categories: http://localhost:1337/api/categories');
    console.log('   • Product Categories: http://localhost:1337/api/product-categories?populate=parent');
    console.log('   • Admin Panel: http://localhost:1337/admin');
    
    if (verificationResult.verifiedCount === verificationResult.totalMapped) {
      console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY! All relationships verified.');
    } else {
      console.log('\n⚠️ MIGRATION PARTIALLY COMPLETED. Some relationships need manual review.');
    }
    
  } catch (error) {
    console.error('\n❌ FINAL MIGRATION FAILED');
    console.error('=' .repeat(80));
    console.error(`Error: ${error.message}`);
    console.error('\n🔧 Next Steps:');
    console.error('1. Check Strapi server status and API permissions');
    console.error('2. Review migration backups in ./migration-backups/');
    console.error('3. Consider manual completion via admin panel');
    
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runFinalMigration();
}

module.exports = { runFinalMigration, completeMigration, verifyRelationships };
