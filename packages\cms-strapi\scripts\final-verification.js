/**
 * Final verification script for completed Strapi migration
 * This script performs comprehensive testing of the migration
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1339';
const FRONTEND_URL = 'http://localhost:3001';

const expectedPages = [
  { slug: 'about-us', title: 'About Us', template: 'about', featured: false },
  { slug: 'contact', title: 'Contact Us', template: 'contact', featured: false },
  { slug: 'privacy-policy', title: 'Privacy Policy', template: 'default', featured: false },
  { slug: 'terms', title: 'Terms and Conditions', template: 'default', featured: false },
  { slug: 'faq', title: 'Frequently Asked Questions', template: 'default', featured: true },
  { slug: 'help', title: 'Help & Support', template: 'default', featured: false },
];

async function testStrapiAPI() {
  console.log('🔍 TESTING STRAPI CMS API');
  console.log('='.repeat(50));
  
  let allPagesExist = true;
  let allPagesComplete = true;
  
  for (const expectedPage of expectedPages) {
    try {
      const response = await axios.get(
        `${STRAPI_URL}/api/pages?filters[slug][$eq]=${expectedPage.slug}&populate=*`
      );
      
      if (response.data.data.length > 0) {
        const page = response.data.data[0];
        console.log(`✅ ${expectedPage.title}`);
        
        // Check content
        const hasRichContent = page.content && page.content.length > 100;
        console.log(`   📄 Content: ${hasRichContent ? '✅ Rich' : '❌ Basic'} (${page.content?.length || 0} chars)`);
        
        // Check metadata
        const hasMetadata = page.excerpt && page.metaTitle && page.metaDescription;
        console.log(`   🔍 SEO: ${hasMetadata ? '✅ Complete' : '❌ Missing'}`);
        
        // Check status
        console.log(`   📊 Status: ${page.status === 'published' ? '✅ Published' : '❌ ' + (page.status || 'Draft')}`);
        
        // Check template
        console.log(`   🎨 Template: ${page.template === expectedPage.template ? '✅' : '❌'} ${page.template || 'not set'}`);
        
        // Check featured
        if (expectedPage.featured) {
          console.log(`   ⭐ Featured: ${page.featured ? '✅ Yes' : '❌ No'}`);
        }
        
        if (!hasRichContent || !hasMetadata || page.status !== 'published') {
          allPagesComplete = false;
        }
        
      } else {
        console.log(`❌ ${expectedPage.title} - NOT FOUND`);
        allPagesExist = false;
        allPagesComplete = false;
      }
      
      console.log('');
    } catch (error) {
      console.log(`❌ ${expectedPage.title} - ERROR: ${error.message}`);
      allPagesExist = false;
      allPagesComplete = false;
    }
  }
  
  return { allPagesExist, allPagesComplete };
}

async function testFrontendIntegration() {
  console.log('🌐 TESTING FRONTEND INTEGRATION');
  console.log('='.repeat(50));
  
  let allPagesLoad = true;
  let allPagesFromStrapi = true;
  
  for (const expectedPage of expectedPages) {
    try {
      // Test page loading
      const response = await axios.get(`${FRONTEND_URL}/${expectedPage.slug}`, {
        timeout: 10000,
      });
      
      if (response.status === 200) {
        console.log(`✅ ${expectedPage.slug} - Loads successfully`);
        
        // Check if content is from Strapi (look for rich content indicators)
        const hasRichContent = response.data.includes('<h2>') || response.data.includes('<h3>');
        console.log(`   📄 Content Source: ${hasRichContent ? '✅ Strapi CMS' : '⚠️ Fallback'}`);
        
        if (!hasRichContent) {
          allPagesFromStrapi = false;
        }
        
      } else {
        console.log(`❌ ${expectedPage.slug} - HTTP ${response.status}`);
        allPagesLoad = false;
      }
      
    } catch (error) {
      console.log(`❌ ${expectedPage.slug} - ERROR: ${error.message}`);
      allPagesLoad = false;
      allPagesFromStrapi = false;
    }
  }
  
  return { allPagesLoad, allPagesFromStrapi };
}

async function testCacheSystem() {
  console.log('⚡ TESTING CACHE SYSTEM');
  console.log('='.repeat(50));
  
  try {
    // Test cache by making multiple requests
    const start1 = Date.now();
    await axios.get(`${STRAPI_URL}/api/pages?filters[slug][$eq]=about-us`);
    const time1 = Date.now() - start1;
    
    const start2 = Date.now();
    await axios.get(`${STRAPI_URL}/api/pages?filters[slug][$eq]=about-us`);
    const time2 = Date.now() - start2;
    
    console.log(`📊 API Response Times:`);
    console.log(`   First request: ${time1}ms`);
    console.log(`   Second request: ${time2}ms`);
    console.log(`   Cache working: ${time2 < time1 ? '✅ Yes' : '⚠️ Unclear'}`);
    
    return true;
  } catch (error) {
    console.log(`❌ Cache test failed: ${error.message}`);
    return false;
  }
}

async function generateFinalReport() {
  console.log('\n🎉 FINAL MIGRATION REPORT');
  console.log('='.repeat(60));
  
  const strapiResults = await testStrapiAPI();
  const frontendResults = await testFrontendIntegration();
  const cacheWorking = await testCacheSystem();
  
  console.log('\n📊 SUMMARY');
  console.log('='.repeat(30));
  console.log(`Strapi Pages: ${strapiResults.allPagesExist ? '✅' : '❌'} All pages exist`);
  console.log(`Content Complete: ${strapiResults.allPagesComplete ? '✅' : '❌'} Rich content & metadata`);
  console.log(`Frontend Loading: ${frontendResults.allPagesLoad ? '✅' : '❌'} All pages accessible`);
  console.log(`Strapi Integration: ${frontendResults.allPagesFromStrapi ? '✅' : '❌'} Using CMS content`);
  console.log(`Cache System: ${cacheWorking ? '✅' : '❌'} Performance optimization`);
  
  const migrationComplete = strapiResults.allPagesExist && 
                           strapiResults.allPagesComplete && 
                           frontendResults.allPagesLoad && 
                           frontendResults.allPagesFromStrapi;
  
  if (migrationComplete) {
    console.log('\n🎉 MIGRATION SUCCESSFUL!');
    console.log('✅ All 6 pages are in Strapi with rich content');
    console.log('✅ Frontend is loading content from Strapi CMS');
    console.log('✅ Caching system is active');
    console.log('✅ SEO metadata is complete');
    console.log('\n🚀 The 5-phase Strapi CMS integration is COMPLETE!');
    
    console.log('\n🔗 Test Your Pages:');
    expectedPages.forEach(page => {
      console.log(`   http://localhost:3001/${page.slug}`);
    });
    
  } else {
    console.log('\n⚠️ MIGRATION INCOMPLETE');
    console.log('Some issues need to be resolved before the migration is complete.');
    
    if (!strapiResults.allPagesExist) {
      console.log('❌ Some pages are missing from Strapi');
    }
    if (!strapiResults.allPagesComplete) {
      console.log('❌ Some pages need content or metadata updates');
    }
    if (!frontendResults.allPagesFromStrapi) {
      console.log('❌ Frontend is still using fallback content');
    }
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Clear cache: Clear browser cache and restart frontend');
  console.log('2. Test admin: http://localhost:3001/admin/pages');
  console.log('3. Monitor logs: Check browser console for any errors');
}

// Run the final verification
generateFinalReport().catch(console.error);
