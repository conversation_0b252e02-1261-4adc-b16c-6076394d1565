/**
 * Get Network Information for Strapi Access
 * This script helps you find the IP addresses to access Strapi from other machines
 */

const os = require('os');

function getNetworkInfo() {
  console.log('🌐 STRAPI NETWORK ACCESS INFORMATION');
  console.log('=' .repeat(60));
  
  const interfaces = os.networkInterfaces();
  const networkInfo = [];
  
  console.log('\n📍 Available Network Interfaces:');
  
  Object.keys(interfaces).forEach(interfaceName => {
    const interfaceInfo = interfaces[interfaceName];
    
    interfaceInfo.forEach(details => {
      if (details.family === 'IPv4' && !details.internal) {
        networkInfo.push({
          interface: interfaceName,
          address: details.address,
          netmask: details.netmask,
          mac: details.mac
        });
        
        console.log(`\n🔗 ${interfaceName}:`);
        console.log(`   IP Address: ${details.address}`);
        console.log(`   Netmask: ${details.netmask}`);
        console.log(`   MAC: ${details.mac}`);
        console.log(`   Strapi URL: http://${details.address}:1337`);
        console.log(`   Admin Panel: http://${details.address}:1337/admin`);
        console.log(`   API Base: http://${details.address}:1337/api`);
      }
    });
  });
  
  console.log('\n🔥 QUICK ACCESS URLS:');
  console.log('=' .repeat(40));
  
  if (networkInfo.length > 0) {
    const primaryIP = networkInfo[0].address;
    console.log(`📱 Mobile/Other Device Access:`);
    console.log(`   Strapi Admin: http://${primaryIP}:1337/admin`);
    console.log(`   API Endpoint: http://${primaryIP}:1337/api`);
    console.log(`   Banners API: http://${primaryIP}:1337/api/banners`);
    console.log(`   Products API: http://${primaryIP}:1337/api/products`);
    console.log(`   Categories API: http://${primaryIP}:1337/api/product-categories`);
    
    console.log(`\n💻 Local Access:`);
    console.log(`   Strapi Admin: http://localhost:1337/admin`);
    console.log(`   API Endpoint: http://localhost:1337/api`);
  } else {
    console.log('❌ No external network interfaces found.');
    console.log('💡 Make sure you are connected to a network (WiFi/Ethernet)');
  }
  
  console.log('\n🛡️ SECURITY NOTES:');
  console.log('=' .repeat(40));
  console.log('⚠️  This configuration is for DEVELOPMENT only');
  console.log('⚠️  Do not use in production without proper security');
  console.log('⚠️  Consider using VPN or SSH tunneling for remote access');
  
  console.log('\n🔧 FIREWALL CONFIGURATION:');
  console.log('=' .repeat(40));
  console.log('Windows: Allow port 1337 in Windows Firewall');
  console.log('macOS: System Preferences > Security & Privacy > Firewall');
  console.log('Linux: sudo ufw allow 1337');
  
  return networkInfo;
}

// Run the script
if (require.main === module) {
  getNetworkInfo();
}

module.exports = { getNetworkInfo };
