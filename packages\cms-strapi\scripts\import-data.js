/**
 * Import Strapi Data Script
 * This script imports data from exported JSON files into a new Strapi instance
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const IMPORT_DIR = path.join(__dirname, '..', 'exports');

// Import order (important for relationships)
const IMPORT_ORDER = [
  'categories',
  'product-categories', 
  'products',
  'banners',
  'pages'
];

async function importData() {
  console.log('📥 IMPORTING STRAPI DATA');
  console.log('=' .repeat(50));
  
  try {
    // Check if export directory exists
    if (!fs.existsSync(IMPORT_DIR)) {
      throw new Error(`Import directory not found: ${IMPORT_DIR}`);
    }
    
    // Check if Strapi is running
    try {
      await axios.get(`${STRAPI_URL}/api`);
      console.log('✅ Strapi is running and accessible');
    } catch (error) {
      throw new Error('❌ Strapi is not running. Please start Strapi first.');
    }
    
    const importResults = {
      timestamp: new Date().toISOString(),
      imported: {},
      errors: {},
      totalImported: 0
    };
    
    console.log('\n🔄 Importing collections...');
    
    for (const collection of IMPORT_ORDER) {
      const collectionFile = path.join(IMPORT_DIR, `${collection}.json`);
      
      if (!fs.existsSync(collectionFile)) {
        console.log(`⚠️  Skipping ${collection} - file not found`);
        continue;
      }
      
      try {
        console.log(`\n📋 Importing ${collection}...`);
        
        const fileContent = fs.readFileSync(collectionFile, 'utf8');
        const collectionData = JSON.parse(fileContent);
        
        if (!collectionData.data || collectionData.data.length === 0) {
          console.log(`   ⚠️  No data to import for ${collection}`);
          continue;
        }
        
        let imported = 0;
        let errors = 0;
        
        for (const item of collectionData.data) {
          try {
            // Prepare data for import (remove id, documentId, timestamps)
            const importItem = {
              ...item.attributes || item,
            };
            
            // Remove system fields
            delete importItem.id;
            delete importItem.documentId;
            delete importItem.createdAt;
            delete importItem.updatedAt;
            delete importItem.publishedAt;
            
            const response = await axios.post(`${STRAPI_URL}/api/${collection}`, {
              data: importItem
            });
            
            if (response.status === 200 || response.status === 201) {
              imported++;
            }
            
          } catch (itemError) {
            errors++;
            console.log(`     ❌ Error importing item: ${itemError.message}`);
          }
          
          // Add delay to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        importResults.imported[collection] = imported;
        importResults.errors[collection] = errors;
        importResults.totalImported += imported;
        
        console.log(`   ✅ Imported ${imported} ${collection} records`);
        if (errors > 0) {
          console.log(`   ⚠️  ${errors} errors occurred`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error importing ${collection}: ${error.message}`);
        importResults.errors[collection] = error.message;
      }
    }
    
    // Save import results
    const resultsFile = path.join(IMPORT_DIR, 'import-results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(importResults, null, 2));
    
    console.log('\n✅ IMPORT COMPLETED!');
    console.log('=' .repeat(50));
    console.log(`📊 Total records imported: ${importResults.totalImported}`);
    console.log(`📋 Collections processed: ${Object.keys(importResults.imported).length}`);
    
    console.log('\n📊 Import Summary:');
    Object.entries(importResults.imported).forEach(([collection, count]) => {
      const errorCount = importResults.errors[collection] || 0;
      console.log(`   ${collection}: ${count} imported${errorCount > 0 ? `, ${errorCount} errors` : ''}`);
    });
    
    console.log('\n🌐 Verification URLs:');
    console.log(`   Admin Panel: ${STRAPI_URL}/admin`);
    console.log(`   API: ${STRAPI_URL}/api`);
    
    return importResults;
    
  } catch (error) {
    console.error('❌ Import failed:', error.message);
    throw error;
  }
}

// Run import if called directly
if (require.main === module) {
  importData().catch(console.error);
}

module.exports = { importData };
