/**
 * Link Subcategories to Parent Categories
 * This script will link product categories (subcategories) to their parent categories from the Categories collection
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Enhanced request function
async function strapiRequest(endpoint, method = 'GET', data = null, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${API_BASE}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = { data };
      }

      const response = await axios(config);
      return { success: true, data: response.data };
    } catch (error) {
      console.log(`   Attempt ${attempt}/${retries} failed for ${method} ${endpoint}`);
      
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || 'Unknown error';
        console.log(`   ❌ HTTP ${status}: ${message}`);
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }
      
      if (attempt === retries) {
        return { success: false, error: error.message };
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics',
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Digital Media': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

function logProgress(step, message, type = 'info') {
  const timestamp = new Date().toISOString();
  const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${symbols[type]} [${timestamp}] ${step}: ${message}`);
}

async function createBackup(step, data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, 'parent-linking-backups');
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const backupFile = path.join(backupDir, `${step}_${timestamp}.json`);
  fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
  console.log(`   📁 Backup created: ${backupFile}`);
  return backupFile;
}

async function linkSubcategoriesToParents() {
  console.log('🔗 LINKING SUBCATEGORIES TO PARENT CATEGORIES');
  console.log('=' .repeat(80));
  console.log('This script will link product categories to their parent categories from the Categories collection');
  console.log('=' .repeat(80));
  
  const startTime = Date.now();
  
  try {
    // Test connectivity first
    logProgress('TEST', 'Testing API connectivity...', 'info');
    const healthCheck = await strapiRequest('/categories?pagination[pageSize]=1');
    if (!healthCheck.success) {
      throw new Error('Cannot connect to Strapi API. Please ensure Strapi is running.');
    }
    logProgress('TEST', 'API connectivity confirmed', 'success');

    // Step 1: Get Categories collection
    logProgress('FETCH', 'Getting Categories collection...', 'info');
    const categoriesResult = await strapiRequest('/categories');
    
    if (!categoriesResult.success) {
      throw new Error('Failed to fetch categories');
    }
    
    const categories = categoriesResult.data.data || [];
    logProgress('FETCH', `Found ${categories.length} main categories`, 'success');
    
    // Create category lookup map
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat;
      console.log(`   📁 ${cat.name} (${cat.documentId})`);
    });
    
    // Step 2: Get Product Categories collection
    logProgress('FETCH', 'Getting Product Categories collection...', 'info');
    const productCategoriesResult = await strapiRequest('/product-categories');
    
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }
    
    const productCategories = productCategoriesResult.data.data || [];
    logProgress('FETCH', `Found ${productCategories.length} product categories`, 'success');
    
    // Create backup
    await createBackup('before-parent-linking', { categories, productCategories });
    
    // Step 3: Link subcategories to parent categories
    logProgress('LINK', 'Starting parent linking process...', 'info');
    
    let linkedCount = 0;
    let errorCount = 0;
    let skippedCount = 0;
    const results = [];
    
    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];
      
      if (parentCategoryName && categoryMap[parentCategoryName]) {
        logProgress('LINK', `Linking: ${subcat.name} → ${parentCategoryName}`, 'info');
        
        // Create a main category in Product Categories collection to serve as parent
        // First check if this parent already exists in Product Categories
        const existingParentResult = await strapiRequest(`/product-categories?filters[name][$eq]=${encodeURIComponent(parentCategoryName)}&filters[isSubcategory][$eq]=false`);
        
        let parentProductCategoryId = null;
        
        if (existingParentResult.success && existingParentResult.data.data.length > 0) {
          // Parent already exists
          parentProductCategoryId = existingParentResult.data.data[0].documentId;
          logProgress('LINK', `Using existing parent: ${parentCategoryName}`, 'info');
        } else {
          // Create parent in Product Categories collection
          const parentData = {
            name: parentCategoryName,
            slug: parentCategoryName.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
            description: `Main ${parentCategoryName} category`,
            isSubcategory: false,
            active: true,
            sort_order: 1,
            category: categoryMap[parentCategoryName].documentId
          };
          
          const createParentResult = await strapiRequest('/product-categories', 'POST', parentData);
          
          if (createParentResult.success) {
            parentProductCategoryId = createParentResult.data.data.documentId;
            logProgress('LINK', `Created parent category: ${parentCategoryName}`, 'success');
          } else {
            logProgress('LINK', `Failed to create parent: ${parentCategoryName}: ${createParentResult.error}`, 'error');
            errorCount++;
            results.push({ name: subcat.name, parent: parentCategoryName, status: 'failed', error: 'Parent creation failed' });
            continue;
          }
        }
        
        // Now link the subcategory to the parent
        const updatePayload = {
          isSubcategory: true,
          parent: parentProductCategoryId,
          category: categoryMap[parentCategoryName].documentId
        };
        
        const updateResult = await strapiRequest(`/product-categories/${subcat.documentId}`, 'PUT', updatePayload);
        
        if (updateResult.success) {
          logProgress('LINK', `✅ Successfully linked: ${subcat.name} → ${parentCategoryName}`, 'success');
          linkedCount++;
          results.push({ name: subcat.name, parent: parentCategoryName, status: 'success' });
        } else {
          logProgress('LINK', `❌ Failed to link: ${subcat.name}: ${updateResult.error}`, 'error');
          errorCount++;
          results.push({ name: subcat.name, parent: parentCategoryName, status: 'failed', error: updateResult.error });
        }
      } else {
        logProgress('LINK', `Skipping unmapped category: ${subcat.name}`, 'warning');
        skippedCount++;
        results.push({ name: subcat.name, parent: 'none', status: 'skipped' });
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Step 4: Verification
    logProgress('VERIFY', 'Verifying parent links...', 'info');
    
    const verificationResult = await strapiRequest('/product-categories');
    if (verificationResult.success) {
      const updatedProductCategories = verificationResult.data.data || [];
      const mainCategories = updatedProductCategories.filter(pc => !pc.isSubcategory);
      const subcategories = updatedProductCategories.filter(pc => pc.isSubcategory);
      
      console.log('\n📊 VERIFICATION RESULTS:');
      console.log('-' .repeat(50));
      console.log(`📁 Main categories in Product Categories: ${mainCategories.length}`);
      console.log(`📦 Subcategories in Product Categories: ${subcategories.length}`);
      console.log(`🔗 Total Product Categories: ${updatedProductCategories.length}`);
    }
    
    // Final summary
    console.log('\n✅ PARENT LINKING COMPLETED!');
    console.log('=' .repeat(80));
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log(`📊 Results:`);
    console.log(`   • Successfully linked: ${linkedCount}`);
    console.log(`   • Errors: ${errorCount}`);
    console.log(`   • Skipped: ${skippedCount}`);
    console.log(`   • Total processed: ${productCategories.length}`);
    
    // Save results
    await createBackup('linking-results', { results, summary: { linkedCount, errorCount, skippedCount } });
    
    console.log('\n🌐 Verification URLs:');
    console.log('   • Categories: http://localhost:1337/api/categories');
    console.log('   • Product Categories: http://localhost:1337/api/product-categories');
    console.log('   • Admin Panel: http://localhost:1337/admin');
    
    if (linkedCount > 0) {
      console.log('\n🎉 SUBCATEGORIES SUCCESSFULLY LINKED TO PARENT CATEGORIES!');
      console.log('\n📋 Next Steps:');
      console.log('   1. Verify the hierarchy in the admin panel');
      console.log('   2. Test API queries with parent population');
      console.log('   3. Link products to appropriate subcategories');
    } else {
      console.log('\n⚠️ No subcategories were linked. Please review the errors above.');
    }
    
  } catch (error) {
    console.error('\n❌ PARENT LINKING FAILED');
    console.error('=' .repeat(80));
    console.error(`Error: ${error.message}`);
    
    process.exit(1);
  }
}

// Run the linking
if (require.main === module) {
  linkSubcategoriesToParents();
}

module.exports = { linkSubcategoriesToParents };
