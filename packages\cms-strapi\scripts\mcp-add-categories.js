/**
 * Complete Category System Migration Script for Strapi CMS
 * Automates the full restructuring of categories and subcategories with proper relationships
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Migration state tracking
const migrationState = {
  step1_categories: { completed: false, data: {} },
  step2_relationships: { completed: false, data: {} },
  step3_cleanup: { completed: false, data: {} },
  step4_products: { completed: false, data: {} }
};

// Enhanced request function with better error handling
async function strapiRequest(endpoint, method = 'GET', data = null, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${API_BASE}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 15000, // 15 second timeout
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = { data };
      }

      const response = await axios(config);
      return { success: true, data: response.data };
    } catch (error) {
      console.log(`   Attempt ${attempt}/${retries} failed for ${method} ${endpoint}`);

      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || 'Unknown error';

        if (status === 400 && message.includes('already exists')) {
          console.log(`   ✅ Item already exists, continuing...`);
          return { success: true, data: null, exists: true };
        }

        if (status === 403) {
          console.log(`   ❌ Permission denied. Please check API permissions.`);
          return { success: false, error: 'Permission denied' };
        }

        console.log(`   ❌ HTTP ${status}: ${message}`);
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }

      if (attempt === retries) {
        return { success: false, error: error.message };
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}

// Backup and logging functions
async function createBackup(step, data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, 'migration-backups');

  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  const backupFile = path.join(backupDir, `${step}_${timestamp}.json`);
  fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
  console.log(`   📁 Backup created: ${backupFile}`);
  return backupFile;
}

function logProgress(step, message, type = 'info') {
  const timestamp = new Date().toISOString();
  const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${symbols[type]} [${timestamp}] ${step}: ${message}`);
}

// Main categories configuration
const mainCategories = [
  {
    name: 'Electronics',
    slug: 'electronics',
    description: 'Electronic devices, gadgets, and technology products',
    short_description: 'Browse the latest electronics and tech gadgets',
    featured: true,
    active: true,
    sort_order: 1,
    meta_title: 'Electronics - ONDC Seller',
    meta_description: 'Shop electronics, smartphones, laptops, and tech accessories'
  },
  {
    name: 'Fashion',
    slug: 'fashion',
    description: 'Clothing, footwear, and fashion accessories',
    short_description: 'Discover trendy fashion and clothing',
    featured: true,
    active: true,
    sort_order: 2,
    meta_title: 'Fashion - ONDC Seller',
    meta_description: 'Shop fashion, clothing, footwear, and accessories'
  },
  {
    name: 'Home & Garden',
    slug: 'home-garden',
    description: 'Home improvement, furniture, and garden products',
    short_description: 'Transform your home and garden',
    featured: true,
    active: true,
    sort_order: 3,
    meta_title: 'Home & Garden - ONDC Seller',
    meta_description: 'Shop furniture, home decor, kitchen, and garden products'
  },
  {
    name: 'Sports & Fitness',
    slug: 'sports-fitness',
    description: 'Sports equipment, fitness gear, and activewear',
    short_description: 'Stay active with sports and fitness products',
    featured: false,
    active: true,
    sort_order: 4,
    meta_title: 'Sports & Fitness - ONDC Seller',
    meta_description: 'Shop sports equipment, fitness gear, and activewear'
  },
  {
    name: 'Books & Media',
    slug: 'books-media',
    description: 'Books, magazines, and digital media',
    short_description: 'Explore books and media content',
    featured: false,
    active: true,
    sort_order: 5,
    meta_title: 'Books & Media - ONDC Seller',
    meta_description: 'Shop books, magazines, and digital media'
  },
  {
    name: 'Health & Beauty',
    slug: 'health-beauty',
    description: 'Health products, cosmetics, and personal care',
    short_description: 'Health and beauty essentials',
    featured: false,
    active: true,
    sort_order: 6,
    meta_title: 'Health & Beauty - ONDC Seller',
    meta_description: 'Shop health products, cosmetics, and personal care items'
  },
  {
    name: 'Automotive',
    slug: 'automotive',
    description: 'Car accessories, parts, and automotive products',
    short_description: 'Automotive parts and accessories',
    featured: false,
    active: true,
    sort_order: 7,
    meta_title: 'Automotive - ONDC Seller',
    meta_description: 'Shop car accessories, parts, and automotive products'
  },
  {
    name: 'Toys & Games',
    slug: 'toys-games',
    description: 'Toys, games, and entertainment products',
    short_description: 'Fun toys and games for all ages',
    featured: false,
    active: true,
    sort_order: 8,
    meta_title: 'Toys & Games - ONDC Seller',
    meta_description: 'Shop toys, games, and entertainment products'
  }
];

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics', // Electronics accessories
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

// STEP 1: Populate Categories Collection
async function step1_populateCategories() {
  logProgress('STEP 1', 'Starting main categories population', 'info');

  try {
    // Test API connectivity
    const healthCheck = await strapiRequest('/categories');
    if (!healthCheck.success) {
      throw new Error('Cannot connect to Strapi API. Check permissions and connectivity.');
    }

    logProgress('STEP 1', 'API connectivity confirmed', 'success');

    // Create backup of current state
    const currentCategories = await strapiRequest('/categories?pagination[pageSize]=100');
    await createBackup('step1_before', currentCategories.data);

    const createdCategories = {};
    let successCount = 0;

    for (const [index, category] of mainCategories.entries()) {
      logProgress('STEP 1', `Creating category ${index + 1}/${mainCategories.length}: ${category.name}`, 'info');

      const result = await strapiRequest('/categories', 'POST', category);

      if (result.success) {
        if (result.exists) {
          // Try to fetch existing category
          const existing = await strapiRequest(`/categories?filters[slug][$eq]=${category.slug}`);
          if (existing.success && existing.data?.data?.length > 0) {
            createdCategories[category.name] = existing.data.data[0];
            logProgress('STEP 1', `Category already exists: ${category.name}`, 'warning');
          }
        } else {
          createdCategories[category.name] = result.data.data;
          logProgress('STEP 1', `Successfully created: ${category.name}`, 'success');
          successCount++;
        }
      } else {
        logProgress('STEP 1', `Failed to create: ${category.name} - ${result.error}`, 'error');
        throw new Error(`Failed to create category: ${category.name}`);
      }

      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Verify all categories exist
    const verification = await strapiRequest('/categories?pagination[pageSize]=100');
    if (verification.success) {
      const totalCategories = verification.data?.meta?.pagination?.total || 0;
      logProgress('STEP 1', `Verification complete: ${totalCategories} categories in database`, 'success');
    }

    // Save state
    migrationState.step1_categories.completed = true;
    migrationState.step1_categories.data = createdCategories;
    await createBackup('step1_after', { categories: createdCategories, verification: verification.data });

    logProgress('STEP 1', `Categories population completed. Created: ${successCount}, Total: ${Object.keys(createdCategories).length}`, 'success');
    return createdCategories;

  } catch (error) {
    logProgress('STEP 1', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// STEP 2: Establish Parent-Child Relationships
async function step2_establishRelationships(createdCategories) {
  logProgress('STEP 2', 'Starting parent-child relationships establishment', 'info');

  try {
    // Get all product categories (subcategories)
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }

    const productCategories = productCategoriesResult.data.data || [];
    await createBackup('step2_before', { productCategories, createdCategories });

    const relationshipUpdates = {};

    // Group subcategories by parent category
    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];
      if (parentCategoryName && createdCategories[parentCategoryName]) {
        if (!relationshipUpdates[parentCategoryName]) {
          relationshipUpdates[parentCategoryName] = {
            category: createdCategories[parentCategoryName],
            children: []
          };
        }
        relationshipUpdates[parentCategoryName].children.push(subcat.id);
      }
    }

    // Update each main category with its children
    let updateCount = 0;
    for (const [categoryName, updateData] of Object.entries(relationshipUpdates)) {
      logProgress('STEP 2', `Updating relationships for: ${categoryName}`, 'info');

      const updatePayload = {
        children: updateData.children
      };

      const result = await strapiRequest(`/categories/${updateData.category.id}`, 'PUT', updatePayload);

      if (result.success) {
        logProgress('STEP 2', `Successfully linked ${updateData.children.length} children to ${categoryName}`, 'success');
        updateCount++;
      } else {
        logProgress('STEP 2', `Failed to update relationships for ${categoryName}`, 'error');
      }

      await new Promise(resolve => setTimeout(resolve, 500));
    }

    migrationState.step2_relationships.completed = true;
    migrationState.step2_relationships.data = relationshipUpdates;
    await createBackup('step2_after', relationshipUpdates);

    logProgress('STEP 2', `Relationships establishment completed. Updated: ${updateCount} categories`, 'success');
    return relationshipUpdates;

  } catch (error) {
    logProgress('STEP 2', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// STEP 3: Clean Up Product Categories Collection
async function step3_cleanupProductCategories(createdCategories) {
  logProgress('STEP 3', 'Starting product categories cleanup', 'info');

  try {
    // Get all product categories
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }

    const productCategories = productCategoriesResult.data.data || [];
    await createBackup('step3_before', productCategories);

    let updateCount = 0;
    let subcategoryCount = 0;

    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];

      if (parentCategoryName && createdCategories[parentCategoryName]) {
        logProgress('STEP 3', `Updating subcategory: ${subcat.name}`, 'info');

        const updatePayload = {
          isSubcategory: true,
          parent: createdCategories[parentCategoryName].id,
          // Remove old category reference if it exists
          category: null
        };

        const result = await strapiRequest(`/product-categories/${subcat.id}`, 'PUT', updatePayload);

        if (result.success) {
          logProgress('STEP 3', `Successfully updated subcategory: ${subcat.name}`, 'success');
          updateCount++;
          subcategoryCount++;
        } else {
          logProgress('STEP 3', `Failed to update subcategory: ${subcat.name}`, 'error');
        }
      } else {
        // This is a main category that should be removed from product-categories
        logProgress('STEP 3', `Marking main category for cleanup: ${subcat.name}`, 'warning');
      }

      await new Promise(resolve => setTimeout(resolve, 300));
    }

    migrationState.step3_cleanup.completed = true;
    migrationState.step3_cleanup.data = { updateCount, subcategoryCount };
    await createBackup('step3_after', { updateCount, subcategoryCount });

    logProgress('STEP 3', `Product categories cleanup completed. Updated: ${updateCount} subcategories`, 'success');
    return { updateCount, subcategoryCount };

  } catch (error) {
    logProgress('STEP 3', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// STEP 4: Update Product Collection Relationships
async function step4_updateProductRelationships() {
  logProgress('STEP 4', 'Starting product relationships update', 'info');

  try {
    // Get all products
    const productsResult = await strapiRequest('/products?pagination[pageSize]=100');
    if (!productsResult.success) {
      throw new Error('Failed to fetch products');
    }

    const products = productsResult.data.data || [];
    await createBackup('step4_before', products);

    let updateCount = 0;

    for (const product of products) {
      // Check if product has category relationships that need updating
      if (product.category) {
        logProgress('STEP 4', `Checking product: ${product.name || product.id}`, 'info');

        // Get the current category details
        const categoryResult = await strapiRequest(`/product-categories/${product.category.id}?populate=parent`);

        if (categoryResult.success && categoryResult.data.data) {
          const category = categoryResult.data.data;

          // If this category now has a parent, ensure proper linking
          if (category.parent) {
            logProgress('STEP 4', `Product ${product.name || product.id} properly linked to subcategory ${category.name}`, 'success');
            updateCount++;
          }
        }
      }

      await new Promise(resolve => setTimeout(resolve, 200));
    }

    migrationState.step4_products.completed = true;
    migrationState.step4_products.data = { updateCount, totalProducts: products.length };
    await createBackup('step4_after', { updateCount, totalProducts: products.length });

    logProgress('STEP 4', `Product relationships update completed. Verified: ${updateCount} products`, 'success');
    return { updateCount, totalProducts: products.length };

  } catch (error) {
    logProgress('STEP 4', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// Main migration orchestrator
async function runCompleteMigration() {
  console.log('🚀 COMPLETE CATEGORY SYSTEM MIGRATION');
  console.log('=' .repeat(80));
  console.log('📋 This will perform a complete restructuring of the category system');
  console.log('🔄 Steps: Populate → Relationships → Cleanup → Products');
  console.log('=' .repeat(80));

  const startTime = Date.now();

  try {
    // Step 1: Populate Categories Collection
    console.log('\n📦 STEP 1: POPULATE CATEGORIES COLLECTION');
    console.log('-' .repeat(50));
    const createdCategories = await step1_populateCategories();

    // Step 2: Establish Parent-Child Relationships
    console.log('\n🔗 STEP 2: ESTABLISH PARENT-CHILD RELATIONSHIPS');
    console.log('-' .repeat(50));
    const relationships = await step2_establishRelationships(createdCategories);

    // Step 3: Clean Up Product Categories Collection
    console.log('\n🧹 STEP 3: CLEAN UP PRODUCT CATEGORIES COLLECTION');
    console.log('-' .repeat(50));
    const cleanup = await step3_cleanupProductCategories(createdCategories);

    // Step 4: Update Product Collection Relationships
    console.log('\n🔄 STEP 4: UPDATE PRODUCT COLLECTION RELATIONSHIPS');
    console.log('-' .repeat(50));
    const products = await step4_updateProductRelationships();

    // Final verification and summary
    console.log('\n✅ MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('=' .repeat(80));

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log(`⏱️  Total Duration: ${duration} seconds`);
    console.log(`📊 Summary:`);
    console.log(`   • Categories Created: ${Object.keys(createdCategories).length}`);
    console.log(`   • Relationships Established: ${Object.keys(relationships).length}`);
    console.log(`   • Subcategories Updated: ${cleanup.subcategoryCount}`);
    console.log(`   • Products Verified: ${products.updateCount}/${products.totalProducts}`);

    console.log('\n🌐 Verification URLs:');
    console.log('   • Categories: http://localhost:1337/api/categories');
    console.log('   • Product Categories: http://localhost:1337/api/product-categories');
    console.log('   • Products: http://localhost:1337/api/products');
    console.log('   • Admin Panel: http://localhost:1337/admin');

    // Save final migration state
    await createBackup('migration_complete', {
      migrationState,
      summary: {
        duration,
        categoriesCreated: Object.keys(createdCategories).length,
        relationshipsEstablished: Object.keys(relationships).length,
        subcategoriesUpdated: cleanup.subcategoryCount,
        productsVerified: products.updateCount
      }
    });

    console.log('\n🎉 Category system migration completed successfully!');

  } catch (error) {
    console.error('\n❌ MIGRATION FAILED');
    console.error('=' .repeat(80));
    console.error(`Error: ${error.message}`);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Check Strapi server status');
    console.error('2. Verify API permissions');
    console.error('3. Check database connectivity');
    console.error('4. Review migration backups in ./migration-backups/');

    process.exit(1);
  }
}

// Run the complete migration
if (require.main === module) {
  runCompleteMigration();
}

module.exports = {
  runCompleteMigration,
  step1_populateCategories,
  step2_establishRelationships,
  step3_cleanupProductCategories,
  step4_updateProductRelationships
};
