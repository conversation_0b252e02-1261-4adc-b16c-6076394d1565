/**
 * Migrate category data from product_categories to categories collection
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error with ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

async function migrateCategories() {
  console.log('🔄 Starting category data migration...');
  console.log('📋 Moving main categories from product-categories to categories collection');
  
  try {
    // Step 1: Get all product-categories that are main categories
    console.log('\n📁 Fetching main categories from product-categories...');
    const productCategories = await strapiRequest('/product-categories?filters[category_type][$eq]=main&pagination[pageSize]=100');
    const mainCategories = productCategories.data || [];
    
    console.log(`✅ Found ${mainCategories.length} main categories to migrate`);
    
    if (mainCategories.length === 0) {
      console.log('⚠️ No main categories found to migrate');
      return;
    }

    // Step 2: Create categories in the new categories collection
    console.log('\n🏗️ Creating categories in categories collection...');
    const createdCategories = {};
    
    for (const productCategory of mainCategories) {
      try {
        const categoryData = {
          name: productCategory.attributes?.name || productCategory.name,
          slug: productCategory.attributes?.slug || productCategory.slug,
          description: productCategory.attributes?.description || productCategory.description,
          short_description: productCategory.attributes?.short_description || productCategory.short_description,
          featured: productCategory.attributes?.featured || productCategory.featured || false,
          active: productCategory.attributes?.active !== false,
          sort_order: productCategory.attributes?.sort_order || productCategory.sort_order || 0,
          meta_title: productCategory.attributes?.meta_title || productCategory.meta_title,
          meta_description: productCategory.attributes?.meta_description || productCategory.meta_description,
        };

        const result = await strapiRequest('/categories', 'POST', categoryData);
        createdCategories[productCategory.id] = result.data;
        
        console.log(`✅ Created category: ${categoryData.name}`);
      } catch (error) {
        console.log(`⚠️ Category ${productCategory.attributes?.name || productCategory.name} might already exist or failed to create`);
      }
    }

    // Step 3: Update subcategories to reference the new categories
    console.log('\n🔗 Updating subcategories to reference new categories...');
    const subcategories = await strapiRequest('/product-categories?filters[category_type][$eq]=sub&pagination[pageSize]=100');
    const subCategoriesList = subcategories.data || [];
    
    for (const subcategory of subCategoriesList) {
      try {
        const parentId = subcategory.attributes?.parent?.data?.id || subcategory.parent?.id;
        const newCategory = Object.values(createdCategories).find(cat => 
          cat.attributes?.slug === mainCategories.find(mc => mc.id === parentId)?.attributes?.slug
        );

        if (newCategory) {
          const updateData = {
            category: newCategory.id
          };

          await strapiRequest(`/product-categories/${subcategory.id}`, 'PUT', updateData);
          console.log(`✅ Updated subcategory: ${subcategory.attributes?.name || subcategory.name}`);
        }
      } catch (error) {
        console.log(`⚠️ Failed to update subcategory: ${subcategory.attributes?.name || subcategory.name}`);
      }
    }

    // Step 4: Clean up - Remove main categories from product-categories
    console.log('\n🧹 Cleaning up main categories from product-categories...');
    for (const productCategory of mainCategories) {
      try {
        // Note: We'll skip deletion for now to preserve data integrity
        console.log(`ℹ️ Keeping main category in product-categories for reference: ${productCategory.attributes?.name || productCategory.name}`);
      } catch (error) {
        console.log(`⚠️ Could not remove: ${productCategory.attributes?.name || productCategory.name}`);
      }
    }

    console.log('\n🎉 Category migration completed successfully!');
    console.log('\n📊 Migration Summary:');
    console.log(`- Categories created: ${Object.keys(createdCategories).length}`);
    console.log(`- Subcategories updated: ${subCategoriesList.length}`);
    console.log('\n🌐 Next steps:');
    console.log('1. Verify categories in Strapi admin: http://localhost:1337/admin');
    console.log('2. Test API endpoints: /api/categories and /api/product-categories');
    console.log('3. Update frontend to use new category structure');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  migrateCategories();
}

module.exports = { migrateCategories };
