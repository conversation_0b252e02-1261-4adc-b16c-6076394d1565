/**
 * Migrate existing category data to the new structure
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('already exists')) {
      console.log(`⚠️ Item already exists, skipping...`);
      return null;
    }
    console.error(`Error with ${method} ${endpoint}:`, error.response?.data || error.message);
    return null;
  }
}

// Main categories that should be moved to categories collection
const mainCategoryNames = [
  'Electronics',
  'Fashion', 
  'Home & Garden',
  'Sports & Fitness',
  'Books & Media',
  'Health & Beauty',
  'Automotive',
  'Toys & Games'
];

async function migrateExistingData() {
  console.log('🔄 Migrating existing category data to new structure...');
  console.log('📋 Moving main categories to categories collection and linking subcategories');
  
  try {
    // Step 1: Get all existing product-categories
    console.log('\n📁 Fetching all product-categories...');
    const allProductCategories = await strapiRequest('/product-categories?pagination[pageSize]=100');
    const productCategories = allProductCategories.data || [];
    
    console.log(`✅ Found ${productCategories.length} product-categories`);
    
    // Step 2: Identify main categories and subcategories
    const mainCategories = productCategories.filter(cat => 
      mainCategoryNames.includes(cat.name) && !cat.isSubcategory
    );
    
    const subCategories = productCategories.filter(cat => 
      cat.isSubcategory === true
    );
    
    console.log(`📊 Identified ${mainCategories.length} main categories and ${subCategories.length} subcategories`);
    
    // Step 3: Create main categories in categories collection
    console.log('\n🏗️ Creating main categories in categories collection...');
    const createdCategories = {};
    
    for (const mainCat of mainCategories) {
      try {
        const categoryData = {
          name: mainCat.name,
          slug: mainCat.slug,
          description: mainCat.description || `${mainCat.name} category`,
          short_description: mainCat.short_description || `Browse ${mainCat.name.toLowerCase()} products`,
          featured: mainCat.featured || false,
          active: mainCat.active !== false,
          sort_order: mainCat.sort_order || 0,
          meta_title: mainCat.meta_title,
          meta_description: mainCat.meta_description
        };

        const result = await strapiRequest('/categories', 'POST', categoryData);
        if (result) {
          createdCategories[mainCat.name] = result.data;
          console.log(`✅ Created category: ${mainCat.name}`);
        } else {
          console.log(`ℹ️ Category might already exist: ${mainCat.name}`);
          // Try to find existing category
          const existing = await strapiRequest(`/categories?filters[slug][$eq]=${mainCat.slug}`);
          if (existing.data && existing.data.length > 0) {
            createdCategories[mainCat.name] = existing.data[0];
            console.log(`✅ Found existing category: ${mainCat.name}`);
          }
        }
      } catch (error) {
        console.log(`⚠️ Could not process main category ${mainCat.name}`);
      }
    }

    // Step 4: Update subcategories to link to main categories
    console.log('\n🔗 Linking subcategories to main categories...');
    
    // Define subcategory to main category mapping
    const subcategoryMapping = {
      'Smartphones': 'Electronics',
      'Laptops': 'Electronics',
      'Accessories': 'Electronics', // Electronics accessories
      'Tablets': 'Electronics',
      "Men's Clothing": 'Fashion',
      "Women's Clothing": 'Fashion',
      'Footwear': 'Fashion',
      'Furniture': 'Home & Garden',
      'Kitchen': 'Home & Garden',
      'Decor': 'Home & Garden',
      'Garden': 'Home & Garden',
      'Fitness Equipment': 'Sports & Fitness',
      'Outdoor Sports': 'Sports & Fitness',
      'Activewear': 'Sports & Fitness',
      'Fiction': 'Books & Media'
    };
    
    for (const subCat of subCategories) {
      try {
        const parentCategoryName = subcategoryMapping[subCat.name];
        const parentCategory = createdCategories[parentCategoryName];
        
        if (parentCategory) {
          const updateData = {
            category: parentCategory.id
          };

          const result = await strapiRequest(`/product-categories/${subCat.id}`, 'PUT', updateData);
          if (result) {
            console.log(`✅ Linked subcategory: ${subCat.name} → ${parentCategoryName}`);
          } else {
            console.log(`⚠️ Could not link subcategory: ${subCat.name}`);
          }
        } else {
          console.log(`⚠️ Parent category not found for: ${subCat.name}`);
        }
      } catch (error) {
        console.log(`⚠️ Error linking subcategory: ${subCat.name}`);
      }
    }

    console.log('\n🎉 Data migration completed successfully!');
    console.log('\n📊 Migration Summary:');
    console.log(`- Categories created: ${Object.keys(createdCategories).length}`);
    console.log(`- Subcategories processed: ${subCategories.length}`);
    console.log('\n🌐 Verification:');
    console.log('- Categories: http://localhost:1337/api/categories');
    console.log('- Product Categories: http://localhost:1337/api/product-categories');
    console.log('- Admin Panel: http://localhost:1337/admin');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  migrateExistingData();
}

module.exports = { migrateExistingData };
