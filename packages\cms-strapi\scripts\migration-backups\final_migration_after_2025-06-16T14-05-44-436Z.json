{"updateCount": 0, "errorCount": 17, "results": [{"name": "Test Electronics", "parent": "none", "status": "skipped"}, {"name": "Electronics", "parent": "none", "status": "skipped"}, {"name": "Fashion", "parent": "none", "status": "skipped"}, {"name": "Home & Garden", "parent": "none", "status": "skipped"}, {"name": "Sports & Fitness", "parent": "none", "status": "skipped"}, {"name": "Books & Media", "parent": "none", "status": "skipped"}, {"name": "Health & Beauty", "parent": "none", "status": "skipped"}, {"name": "Automotive", "parent": "none", "status": "skipped"}, {"name": "Toys & Games", "parent": "none", "status": "skipped"}, {"name": "Smartphones", "parent": "Electronics", "status": "failed", "error": "Request failed with status code 400"}, {"name": "Laptops", "parent": "Electronics", "status": "failed", "error": ""}, {"name": "Accessories", "parent": "Electronics", "status": "failed", "error": ""}, {"name": "Tablets", "parent": "Electronics", "status": "failed", "error": ""}, {"name": "Men's Clothing", "parent": "Fashion", "status": "failed", "error": ""}, {"name": "Women's Clothing", "parent": "Fashion", "status": "failed", "error": ""}, {"name": "Footwear", "parent": "Fashion", "status": "failed", "error": ""}, {"name": "Accessories", "parent": "Electronics", "status": "failed", "error": ""}, {"name": "Furniture", "parent": "Home & Garden", "status": "failed", "error": ""}, {"name": "Kitchen", "parent": "Home & Garden", "status": "failed", "error": ""}, {"name": "Decor", "parent": "Home & Garden", "status": "failed", "error": ""}, {"name": "Garden", "parent": "Home & Garden", "status": "failed", "error": ""}, {"name": "Fitness Equipment", "parent": "Sports & Fitness", "status": "failed", "error": "Request failed with status code 400"}, {"name": "Outdoor Sports", "parent": "Sports & Fitness", "status": "failed", "error": "Request failed with status code 400"}, {"name": "Activewear", "parent": "Sports & Fitness", "status": "failed", "error": "Request failed with status code 400"}, {"name": "Fiction", "parent": "Books & Media", "status": "failed", "error": "Request failed with status code 400"}, {"name": "Non-Fiction", "parent": "Books & Media", "status": "failed", "error": "Request failed with status code 400"}, {"name": "Digital Media", "parent": "none", "status": "skipped"}]}