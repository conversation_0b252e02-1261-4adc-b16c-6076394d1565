{"categories": {"Electronics": {"id": 2, "documentId": "mjo6dudqqhugv91dp6xy7qhp", "name": "Electronics", "description": "Electronic devices, gadgets, and technology products", "short_description": "Browse the latest electronics and tech gadgets", "slug": "electronics", "featured": true, "active": true, "sort_order": 1, "meta_title": "Electronics - ON<PERSON> Seller", "meta_description": "Shop electronics, smartphones, laptops, and tech accessories", "createdAt": "2025-06-16T12:56:53.994Z", "updatedAt": "2025-06-16T12:56:53.994Z", "publishedAt": "2025-06-16T12:56:54.733Z"}, "Fashion": {"id": 4, "documentId": "zxkdf6q3b2i83hlb05pmucnc", "name": "Fashion", "description": "Clothing, footwear, and fashion accessories", "short_description": "Discover trendy fashion and clothing", "slug": "fashion", "featured": true, "active": true, "sort_order": 2, "meta_title": "Fashion - <PERSON><PERSON>", "meta_description": "Shop fashion, clothing, footwear, and accessories", "createdAt": "2025-06-16T12:56:55.362Z", "updatedAt": "2025-06-16T12:56:55.362Z", "publishedAt": "2025-06-16T12:56:55.411Z"}, "Home & Garden": {"id": 6, "documentId": "s6gz77ymv8t62s0oaimwd4aw", "name": "Home & Garden", "description": "Home improvement, furniture, and garden products", "short_description": "Transform your home and garden", "slug": "home-garden", "featured": true, "active": true, "sort_order": 3, "meta_title": "Home & Garden - ONDC Seller", "meta_description": "Shop furniture, home decor, kitchen, and garden products", "createdAt": "2025-06-16T12:56:56.009Z", "updatedAt": "2025-06-16T12:56:56.009Z", "publishedAt": "2025-06-16T12:56:56.048Z"}, "Sports & Fitness": {"id": 8, "documentId": "vrpsz1yu4ke1ty3vxrze5s72", "name": "Sports & Fitness", "description": "Sports equipment, fitness gear, and activewear", "short_description": "Stay active with sports and fitness products", "slug": "sports-fitness", "featured": false, "active": true, "sort_order": 4, "meta_title": "Sports & Fitness - <PERSON><PERSON> Seller", "meta_description": "Shop sports equipment, fitness gear, and activewear", "createdAt": "2025-06-16T12:56:56.648Z", "updatedAt": "2025-06-16T12:56:56.648Z", "publishedAt": "2025-06-16T12:56:56.697Z"}, "Books & Media": {"id": 10, "documentId": "qmf480lh4zz4y6ensbncuu9m", "name": "Books & Media", "description": "Books, magazines, and digital media", "short_description": "Explore books and media content", "slug": "books-media", "featured": false, "active": true, "sort_order": 5, "meta_title": "Books & Media - ON<PERSON> Seller", "meta_description": "Shop books, magazines, and digital media", "createdAt": "2025-06-16T12:56:57.329Z", "updatedAt": "2025-06-16T12:56:57.329Z", "publishedAt": "2025-06-16T12:56:57.367Z"}, "Health & Beauty": {"id": 12, "documentId": "vds2exb5v1hxy30jvnlqboee", "name": "Health & Beauty", "description": "Health products, cosmetics, and personal care", "short_description": "Health and beauty essentials", "slug": "health-beauty", "featured": false, "active": true, "sort_order": 6, "meta_title": "Health & Beauty - ONDC Seller", "meta_description": "Shop health products, cosmetics, and personal care items", "createdAt": "2025-06-16T12:56:57.959Z", "updatedAt": "2025-06-16T12:56:57.959Z", "publishedAt": "2025-06-16T12:56:58.073Z"}, "Automotive": {"id": 14, "documentId": "vgl94x4yqter6dwbfaiu5nc2", "name": "Automotive", "description": "Car accessories, parts, and automotive products", "short_description": "Automotive parts and accessories", "slug": "automotive", "featured": false, "active": true, "sort_order": 7, "meta_title": "Automotive - ONDC Seller", "meta_description": "Shop car accessories, parts, and automotive products", "createdAt": "2025-06-16T12:56:58.881Z", "updatedAt": "2025-06-16T12:56:58.881Z", "publishedAt": "2025-06-16T12:56:58.920Z"}, "Toys & Games": {"id": 16, "documentId": "ys0os55tcmbqpbzxo2e56v16", "name": "Toys & Games", "description": "Toys, games, and entertainment products", "short_description": "Fun toys and games for all ages", "slug": "toys-games", "featured": false, "active": true, "sort_order": 8, "meta_title": "Toys & Games - <PERSON><PERSON> Seller", "meta_description": "Shop toys, games, and entertainment products", "createdAt": "2025-06-16T12:56:59.511Z", "updatedAt": "2025-06-16T12:56:59.511Z", "publishedAt": "2025-06-16T12:56:59.555Z"}}, "verification": {"data": [{"id": 2, "documentId": "mjo6dudqqhugv91dp6xy7qhp", "name": "Electronics", "description": "Electronic devices, gadgets, and technology products", "short_description": "Browse the latest electronics and tech gadgets", "slug": "electronics", "featured": true, "active": true, "sort_order": 1, "meta_title": "Electronics - ON<PERSON> Seller", "meta_description": "Shop electronics, smartphones, laptops, and tech accessories", "createdAt": "2025-06-16T12:56:53.994Z", "updatedAt": "2025-06-16T12:56:53.994Z", "publishedAt": "2025-06-16T12:56:54.733Z"}, {"id": 4, "documentId": "zxkdf6q3b2i83hlb05pmucnc", "name": "Fashion", "description": "Clothing, footwear, and fashion accessories", "short_description": "Discover trendy fashion and clothing", "slug": "fashion", "featured": true, "active": true, "sort_order": 2, "meta_title": "Fashion - <PERSON><PERSON>", "meta_description": "Shop fashion, clothing, footwear, and accessories", "createdAt": "2025-06-16T12:56:55.362Z", "updatedAt": "2025-06-16T12:56:55.362Z", "publishedAt": "2025-06-16T12:56:55.411Z"}, {"id": 6, "documentId": "s6gz77ymv8t62s0oaimwd4aw", "name": "Home & Garden", "description": "Home improvement, furniture, and garden products", "short_description": "Transform your home and garden", "slug": "home-garden", "featured": true, "active": true, "sort_order": 3, "meta_title": "Home & Garden - ONDC Seller", "meta_description": "Shop furniture, home decor, kitchen, and garden products", "createdAt": "2025-06-16T12:56:56.009Z", "updatedAt": "2025-06-16T12:56:56.009Z", "publishedAt": "2025-06-16T12:56:56.048Z"}, {"id": 8, "documentId": "vrpsz1yu4ke1ty3vxrze5s72", "name": "Sports & Fitness", "description": "Sports equipment, fitness gear, and activewear", "short_description": "Stay active with sports and fitness products", "slug": "sports-fitness", "featured": false, "active": true, "sort_order": 4, "meta_title": "Sports & Fitness - <PERSON><PERSON> Seller", "meta_description": "Shop sports equipment, fitness gear, and activewear", "createdAt": "2025-06-16T12:56:56.648Z", "updatedAt": "2025-06-16T12:56:56.648Z", "publishedAt": "2025-06-16T12:56:56.697Z"}, {"id": 10, "documentId": "qmf480lh4zz4y6ensbncuu9m", "name": "Books & Media", "description": "Books, magazines, and digital media", "short_description": "Explore books and media content", "slug": "books-media", "featured": false, "active": true, "sort_order": 5, "meta_title": "Books & Media - ON<PERSON> Seller", "meta_description": "Shop books, magazines, and digital media", "createdAt": "2025-06-16T12:56:57.329Z", "updatedAt": "2025-06-16T12:56:57.329Z", "publishedAt": "2025-06-16T12:56:57.367Z"}, {"id": 12, "documentId": "vds2exb5v1hxy30jvnlqboee", "name": "Health & Beauty", "description": "Health products, cosmetics, and personal care", "short_description": "Health and beauty essentials", "slug": "health-beauty", "featured": false, "active": true, "sort_order": 6, "meta_title": "Health & Beauty - ONDC Seller", "meta_description": "Shop health products, cosmetics, and personal care items", "createdAt": "2025-06-16T12:56:57.959Z", "updatedAt": "2025-06-16T12:56:57.959Z", "publishedAt": "2025-06-16T12:56:58.073Z"}, {"id": 14, "documentId": "vgl94x4yqter6dwbfaiu5nc2", "name": "Automotive", "description": "Car accessories, parts, and automotive products", "short_description": "Automotive parts and accessories", "slug": "automotive", "featured": false, "active": true, "sort_order": 7, "meta_title": "Automotive - ONDC Seller", "meta_description": "Shop car accessories, parts, and automotive products", "createdAt": "2025-06-16T12:56:58.881Z", "updatedAt": "2025-06-16T12:56:58.881Z", "publishedAt": "2025-06-16T12:56:58.920Z"}, {"id": 16, "documentId": "ys0os55tcmbqpbzxo2e56v16", "name": "Toys & Games", "description": "Toys, games, and entertainment products", "short_description": "Fun toys and games for all ages", "slug": "toys-games", "featured": false, "active": true, "sort_order": 8, "meta_title": "Toys & Games - <PERSON><PERSON> Seller", "meta_description": "Shop toys, games, and entertainment products", "createdAt": "2025-06-16T12:56:59.511Z", "updatedAt": "2025-06-16T12:56:59.511Z", "publishedAt": "2025-06-16T12:56:59.555Z"}], "meta": {"pagination": {"page": 1, "pageSize": 100, "pageCount": 1, "total": 8}}}}