{"Electronics": {"category": {"id": 2, "documentId": "mjo6dudqqhugv91dp6xy7qhp", "name": "Electronics", "description": "Electronic devices, gadgets, and technology products", "short_description": "Browse the latest electronics and tech gadgets", "slug": "electronics", "featured": true, "active": true, "sort_order": 1, "meta_title": "Electronics - ON<PERSON> Seller", "meta_description": "Shop electronics, smartphones, laptops, and tech accessories", "createdAt": "2025-06-16T12:56:53.994Z", "updatedAt": "2025-06-16T12:56:53.994Z", "publishedAt": "2025-06-16T12:56:54.733Z"}, "children": [20, 22, 24, 26, 34]}, "Fashion": {"category": {"id": 4, "documentId": "zxkdf6q3b2i83hlb05pmucnc", "name": "Fashion", "description": "Clothing, footwear, and fashion accessories", "short_description": "Discover trendy fashion and clothing", "slug": "fashion", "featured": true, "active": true, "sort_order": 2, "meta_title": "Fashion - <PERSON><PERSON>", "meta_description": "Shop fashion, clothing, footwear, and accessories", "createdAt": "2025-06-16T12:56:55.362Z", "updatedAt": "2025-06-16T12:56:55.362Z", "publishedAt": "2025-06-16T12:56:55.411Z"}, "children": [28, 30, 32]}, "Home & Garden": {"category": {"id": 6, "documentId": "s6gz77ymv8t62s0oaimwd4aw", "name": "Home & Garden", "description": "Home improvement, furniture, and garden products", "short_description": "Transform your home and garden", "slug": "home-garden", "featured": true, "active": true, "sort_order": 3, "meta_title": "Home & Garden - ONDC Seller", "meta_description": "Shop furniture, home decor, kitchen, and garden products", "createdAt": "2025-06-16T12:56:56.009Z", "updatedAt": "2025-06-16T12:56:56.009Z", "publishedAt": "2025-06-16T12:56:56.048Z"}, "children": [36, 38, 40, 42]}, "Sports & Fitness": {"category": {"id": 8, "documentId": "vrpsz1yu4ke1ty3vxrze5s72", "name": "Sports & Fitness", "description": "Sports equipment, fitness gear, and activewear", "short_description": "Stay active with sports and fitness products", "slug": "sports-fitness", "featured": false, "active": true, "sort_order": 4, "meta_title": "Sports & Fitness - <PERSON><PERSON> Seller", "meta_description": "Shop sports equipment, fitness gear, and activewear", "createdAt": "2025-06-16T12:56:56.648Z", "updatedAt": "2025-06-16T12:56:56.648Z", "publishedAt": "2025-06-16T12:56:56.697Z"}, "children": [44, 46, 48]}, "Books & Media": {"category": {"id": 10, "documentId": "qmf480lh4zz4y6ensbncuu9m", "name": "Books & Media", "description": "Books, magazines, and digital media", "short_description": "Explore books and media content", "slug": "books-media", "featured": false, "active": true, "sort_order": 5, "meta_title": "Books & Media - ON<PERSON> Seller", "meta_description": "Shop books, magazines, and digital media", "createdAt": "2025-06-16T12:56:57.329Z", "updatedAt": "2025-06-16T12:56:57.329Z", "publishedAt": "2025-06-16T12:56:57.367Z"}, "children": [50, 52]}}