/**
 * <PERSON><PERSON>t to populate Strapi CMS with comprehensive e-commerce categories
 * 
 * This script creates a complete category hierarchy for the ONDC Seller Platform
 */

const axios = require('axios');

const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN || 'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

// Comprehensive e-commerce category structure
const categories = [
  // Main Categories
  {
    name: 'Electronics',
    slug: 'electronics',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Latest gadgets and tech devices including smartphones, laptops, accessories, audio equipment, and gaming gear.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 1
  },
  {
    name: 'Fashion & Apparel',
    slug: 'fashion-apparel',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Trendy clothing and accessories for men, women, and children. Discover the latest fashion trends and styles.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 2
  },
  {
    name: 'Home & Garden',
    slug: 'home-garden',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Furniture, home decor, kitchen essentials, and garden tools to make your home beautiful and functional.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 3
  },
  {
    name: 'Health & Beauty',
    slug: 'health-beauty',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Skincare, makeup, health supplements, and personal care products for your wellness and beauty needs.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 4
  },
  {
    name: 'Sports & Outdoors',
    slug: 'sports-outdoors',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Fitness equipment, outdoor gear, sports apparel, and everything you need for an active lifestyle.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 5
  },
  {
    name: 'Books & Media',
    slug: 'books-media',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Books, movies, music, games, and educational materials for entertainment and learning.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 6
  },
  {
    name: 'Automotive',
    slug: 'automotive',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Car accessories, tools, parts, and automotive essentials for vehicle maintenance and enhancement.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 7
  },
  {
    name: 'Food & Beverages',
    slug: 'food-beverages',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Snacks, beverages, organic foods, and gourmet items for your culinary adventures.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 8
  },

  // Electronics Subcategories
  {
    name: 'Smartphones',
    slug: 'smartphones',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Latest smartphones with cutting-edge technology and features.',
            type: 'text'
          }
        ]
      }
    ],
    featured: true,
    sortOrder: 11,
    parentSlug: 'electronics'
  },
  {
    name: 'Laptops & Computers',
    slug: 'laptops-computers',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'High-performance laptops, desktops, and computer accessories.',
            type: 'text'
          }
        ]
      }
    ],
    featured: false,
    sortOrder: 12,
    parentSlug: 'electronics'
  },
  {
    name: 'Audio & Headphones',
    slug: 'audio-headphones',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Premium audio equipment, headphones, and sound systems.',
            type: 'text'
          }
        ]
      }
    ],
    featured: false,
    sortOrder: 13,
    parentSlug: 'electronics'
  },
  {
    name: 'Gaming',
    slug: 'gaming',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Gaming consoles, accessories, and equipment for gamers.',
            type: 'text'
          }
        ]
      }
    ],
    featured: false,
    sortOrder: 14,
    parentSlug: 'electronics'
  },

  // Fashion Subcategories
  {
    name: 'Men\'s Clothing',
    slug: 'mens-clothing',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Stylish clothing for men including shirts, pants, suits, and casual wear.',
            type: 'text'
          }
        ]
      }
    ],
    featured: false,
    sortOrder: 21,
    parentSlug: 'fashion-apparel'
  },
  {
    name: 'Women\'s Clothing',
    slug: 'womens-clothing',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Fashion-forward clothing for women including dresses, tops, and accessories.',
            type: 'text'
          }
        ]
      }
    ],
    featured: false,
    sortOrder: 22,
    parentSlug: 'fashion-apparel'
  },
  {
    name: 'Shoes & Footwear',
    slug: 'shoes-footwear',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Comfortable and stylish footwear for all occasions.',
            type: 'text'
          }
        ]
      }
    ],
    featured: false,
    sortOrder: 23,
    parentSlug: 'fashion-apparel'
  },
  {
    name: 'Accessories',
    slug: 'accessories',
    description: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Fashion accessories including bags, jewelry, and watches.',
            type: 'text'
          }
        ]
      }
    ],
    featured: false,
    sortOrder: 24,
    parentSlug: 'fashion-apparel'
  }
];

async function createCategory(categoryData) {
  try {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${API_TOKEN}`
    };

    const response = await axios.post(
      `${STRAPI_URL}/api/product-categories`,
      { data: categoryData },
      { headers }
    );

    console.log(`✅ Created category: ${categoryData.name}`);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('already exists')) {
      console.log(`ℹ️ Category already exists: ${categoryData.name}`);
      return null;
    }
    console.error(`❌ Error creating category ${categoryData.name}:`, error.response?.data || error.message);
    throw error;
  }
}

async function populateCategories() {
  console.log('🚀 Starting category population...');
  console.log('=' .repeat(60));

  try {
    const createdCategories = {};

    // First pass: Create main categories
    console.log('\n📁 Step 1: Creating main categories...');
    for (const category of categories.filter(c => !c.parentSlug)) {
      const result = await createCategory(category);
      if (result) {
        createdCategories[category.slug] = result.data;
      }
      // Wait a moment between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Second pass: Create subcategories with parent relationships
    console.log('\n📂 Step 2: Creating subcategories...');
    for (const category of categories.filter(c => c.parentSlug)) {
      const parentCategory = createdCategories[category.parentSlug];
      if (parentCategory) {
        const categoryWithParent = {
          ...category,
          parent: parentCategory.id
        };
        delete categoryWithParent.parentSlug;

        const result = await createCategory(categoryWithParent);
        if (result) {
          createdCategories[category.slug] = result.data;
        }
      } else {
        console.log(`⚠️ Parent category not found for: ${category.name}`);
      }
      // Wait a moment between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 Category population completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`✅ Total categories processed: ${categories.length}`);
    console.log(`✅ Main categories: ${categories.filter(c => !c.parentSlug).length}`);
    console.log(`✅ Subcategories: ${categories.filter(c => c.parentSlug).length}`);
    
    console.log('\n🔗 Categories created:');
    Object.keys(createdCategories).forEach(slug => {
      console.log(`• ${slug}`);
    });

  } catch (error) {
    console.error('\n💥 Failed to populate categories:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure Strapi server is running on http://localhost:1339');
    console.log('2. Check if API token is valid');
    console.log('3. Verify product-categories content type exists');
    console.log('4. Check Strapi server logs for detailed errors');
    
    process.exit(1);
  }
}

// Run the population if this script is executed directly
if (require.main === module) {
  populateCategories();
}

module.exports = { populateCategories, categories };
