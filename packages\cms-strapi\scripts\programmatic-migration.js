/**
 * Programmatic Strapi CMS Content Migration
 * This script uses the Strapi API to create and update all static pages
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1339';

// Complete page data for all 6 pages
const allPages = [
  {
    title: 'About Us',
    slug: 'about-us',
    content: `<h2>Welcome to ONDC Seller Platform</h2>
<p>We are a marketplace connecting buyers and sellers across India through the Open Network for Digital Commerce (ONDC).</p>

<h2>Our Mission</h2>
<p>Our mission is to democratize digital commerce in India by providing a platform that enables sellers of all sizes to reach customers across the country.</p>

<h2>What We Offer</h2>
<ul>
<li>Seamless integration with ONDC network</li>
<li>Comprehensive seller tools and analytics</li>
<li>Multi-channel inventory management</li>
<li>Secure payment processing</li>
<li>24/7 customer support</li>
</ul>

<h2>Our Vision</h2>
<p>To create an inclusive digital commerce ecosystem that empowers every seller in India to participate in the digital economy and reach customers nationwide.</p>`,
    excerpt:
      'Learn about ONDC Seller Platform - connecting buyers and sellers across India through the Open Network for Digital Commerce.',
    metaTitle: 'About Us - ONDC Seller Platform',
    metaDescription:
      'Discover how ONDC Seller Platform is democratizing digital commerce in India by connecting sellers with customers nationwide.',
    status: 'published',
    template: 'about',
    featured: false,
    author: 'Admin',
    isUpdate: true, // This page exists and needs updating
    existingId: 2,
  },
  {
    title: 'Contact Us',
    slug: 'contact',
    content: `<h2>Get in Touch</h2>
<p>Have questions about the ONDC Seller Platform? We're here to help!</p>

<h3>Contact Information</h3>
<p><strong>Email:</strong> <EMAIL></p>
<p><strong>Phone:</strong> +91 **********</p>
<p><strong>Address:</strong><br>
123 Commerce Street<br>
Tech Park, Bangalore<br>
Karnataka, India</p>

<h3>Business Hours</h3>
<p>Monday - Friday: 9:00 AM - 6:00 PM IST<br>
Saturday: 10:00 AM - 2:00 PM IST<br>
Sunday: Closed</p>

<h3>Support</h3>
<p>For technical support, please use our contact form or email us directly. We typically respond within 24 hours during business days.</p>`,
    excerpt:
      'Contact ONDC Seller Platform for support, questions, or business inquiries. We are here to help you succeed.',
    metaTitle: 'Contact Us - ONDC Seller Platform',
    metaDescription:
      'Get in touch with ONDC Seller Platform support team. Find our contact information, business hours, and support options.',
    status: 'published',
    template: 'contact',
    featured: false,
    author: 'Admin',
    isUpdate: false,
  },
  {
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content: `<h2>Privacy Policy</h2>
<p><strong>Last updated:</strong> June 10, 2025</p>

<h3>1. Information We Collect</h3>
<p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>

<h3>2. How We Use Your Information</h3>
<p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

<h3>3. Information Sharing</h3>
<p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

<h3>4. Data Security</h3>
<p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

<h3>5. Your Rights</h3>
<p>You have the right to access, update, or delete your personal information. Contact us if you wish to exercise these rights.</p>

<h3>6. Contact Us</h3>
<p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>`,
    excerpt:
      'Our privacy policy explains how we collect, use, and protect your personal information on the ONDC Seller Platform.',
    metaTitle: 'Privacy Policy - ONDC Seller Platform',
    metaDescription:
      'Read our privacy policy to understand how ONDC Seller Platform collects, uses, and protects your personal information.',
    status: 'published',
    template: 'default',
    featured: false,
    author: 'Admin',
    isUpdate: false,
  },
  {
    title: 'Terms and Conditions',
    slug: 'terms',
    content: `<h2>Terms and Conditions</h2>
<p><strong>Last updated:</strong> June 10, 2025</p>

<h3>1. Acceptance of Terms</h3>
<p>By accessing and using the ONDC Seller Platform, you accept and agree to be bound by the terms and provision of this agreement.</p>

<h3>2. Use License</h3>
<p>Permission is granted to temporarily download one copy of the materials on ONDC Seller Platform for personal, non-commercial transitory viewing only.</p>

<h3>3. Disclaimer</h3>
<p>The materials on ONDC Seller Platform are provided on an 'as is' basis. ONDC Seller Platform makes no warranties, expressed or implied.</p>

<h3>4. Limitations</h3>
<p>In no event shall ONDC Seller Platform or its suppliers be liable for any damages arising out of the use or inability to use the materials.</p>

<h3>5. Accuracy of Materials</h3>
<p>The materials appearing on ONDC Seller Platform could include technical, typographical, or photographic errors.</p>

<h3>6. Links</h3>
<p>ONDC Seller Platform has not reviewed all of the sites linked to our platform and is not responsible for the contents of any such linked site.</p>

<h3>7. Modifications</h3>
<p>ONDC Seller Platform may revise these terms of service at any time without notice.</p>`,
    excerpt:
      'Read our terms and conditions to understand the rules and regulations for using the ONDC Seller Platform.',
    metaTitle: 'Terms and Conditions - ONDC Seller Platform',
    metaDescription:
      'Review the terms and conditions for using ONDC Seller Platform. Understand your rights and responsibilities.',
    status: 'published',
    template: 'default',
    featured: false,
    author: 'Admin',
    isUpdate: false,
  },
  {
    title: 'Frequently Asked Questions',
    slug: 'faq',
    content: `<h2>Frequently Asked Questions</h2>
<p>Find answers to common questions about ONDC Marketplace</p>

<h3>General</h3>
<h4>What is ONDC Seller?</h4>
<p>ONDC Seller is a comprehensive e-commerce platform built on the Open Network for Digital Commerce, enabling sellers to reach customers across India through a unified digital marketplace.</p>

<h4>How do I create a seller account?</h4>
<p>You can create a seller account by clicking the 'Sign Up' button in the header and filling out the registration form with your business information and required documents.</p>

<h4>Is it free to use ONDC Seller?</h4>
<p>ONDC Seller offers competitive pricing for sellers with transparent fee structures. Contact our sales team for detailed pricing information based on your business needs.</p>

<h3>Orders & Shipping</h3>
<h4>How can I track my order?</h4>
<p>You can track your order by visiting the 'My Orders' section in your account or using the 'Track Order' link in the header with your order number.</p>

<h4>What are the shipping charges?</h4>
<p>Shipping charges vary by seller and location. We offer free shipping on orders over ₹500 from participating sellers.</p>

<h4>How long does delivery take?</h4>
<p>Delivery times vary by location and seller. Most orders are delivered within 2-7 business days.</p>

<h3>Returns & Refunds</h3>
<h4>What is your return policy?</h4>
<p>We offer a 7-day return policy for most items. Products must be in original condition with tags and packaging intact.</p>

<h4>How do I return an item?</h4>
<p>You can initiate a return from your 'My Orders' section. Select the item you want to return and follow the instructions.</p>

<h4>When will I receive my refund?</h4>
<p>Refunds are processed within 5-7 business days after we receive and verify the returned item.</p>

<h3>Payments</h3>
<h4>What payment methods do you accept?</h4>
<p>We accept all major credit/debit cards, UPI, net banking, and digital wallets like Paytm, PhonePe, and Google Pay.</p>

<h4>Is my payment information secure?</h4>
<p>Yes, we use industry-standard encryption and security measures to protect your payment information.</p>

<h4>Can I pay cash on delivery?</h4>
<p>Cash on delivery is available for select products and locations. You'll see this option during checkout if available.</p>`,
    excerpt:
      'Find answers to frequently asked questions about ONDC Seller Platform, orders, shipping, returns, and payments.',
    metaTitle: 'FAQ - ONDC Seller Platform',
    metaDescription:
      'Get answers to frequently asked questions about ONDC Seller Platform, including account setup, orders, shipping, and support.',
    status: 'published',
    template: 'default',
    featured: true, // FAQ is featured
    author: 'Admin',
    isUpdate: false,
  },
  {
    title: 'Help & Support',
    slug: 'help',
    content: `<h2>Help & Support</h2>
<p>Welcome to our Help Center. Find answers to your questions and get the support you need.</p>

<h3>Getting Started</h3>
<h4>Setting up your seller account</h4>
<p>Learn how to create and configure your seller account on the ONDC platform.</p>

<h4>Adding your first product</h4>
<p>Step-by-step guide to listing your products and making them available to customers.</p>

<h4>Managing inventory</h4>
<p>Keep track of your stock levels and manage your inventory efficiently.</p>

<h3>Order Management</h3>
<h4>Processing orders</h4>
<p>Learn how to view, process, and fulfill customer orders.</p>

<h4>Shipping and delivery</h4>
<p>Understand shipping options and how to manage deliveries.</p>

<h4>Handling returns</h4>
<p>Process returns and refunds according to our policies.</p>

<h3>Account & Settings</h3>
<h4>Profile management</h4>
<p>Update your business information and account settings.</p>

<h4>Payment settings</h4>
<p>Configure your payment methods and payout preferences.</p>

<h4>Notification preferences</h4>
<p>Manage how and when you receive notifications.</p>

<h3>Need More Help?</h3>
<p>If you can't find what you're looking for, our support team is here to help:</p>
<ul>
<li>Email: <EMAIL></li>
<li>Phone: +91 **********</li>
<li>Live Chat: Available 9 AM - 6 PM IST</li>
</ul>`,
    excerpt:
      'Get help and support for using the ONDC Seller Platform. Find guides, tutorials, and contact information.',
    metaTitle: 'Help & Support - ONDC Seller Platform',
    metaDescription:
      'Access help and support resources for ONDC Seller Platform. Find guides, tutorials, and contact our support team.',
    status: 'published',
    template: 'default',
    featured: false,
    author: 'Admin',
    isUpdate: false,
  },
];

async function updateExistingPage(pageData) {
  try {
    console.log(`🔄 Updating existing page: ${pageData.title} (ID: ${pageData.existingId})`);

    const response = await axios.put(
      `${STRAPI_URL}/api/pages/${pageData.existingId}`,
      {
        data: {
          title: pageData.title,
          slug: pageData.slug,
          content: pageData.content,
          excerpt: pageData.excerpt,
          metaTitle: pageData.metaTitle,
          metaDescription: pageData.metaDescription,
          status: pageData.status,
          template: pageData.template,
          featured: pageData.featured,
          author: pageData.author,
          publishedAt: new Date().toISOString(),
        },
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    console.log(`✅ Updated page: ${pageData.title}`);
    return response.data;
  } catch (error) {
    console.error(
      `❌ Error updating page ${pageData.title}:`,
      error.response?.data || error.message
    );
    return null;
  }
}

async function createNewPage(pageData) {
  try {
    console.log(`🆕 Creating new page: ${pageData.title}`);

    const response = await axios.post(
      `${STRAPI_URL}/api/pages`,
      {
        data: {
          title: pageData.title,
          slug: pageData.slug,
          content: pageData.content,
          excerpt: pageData.excerpt,
          metaTitle: pageData.metaTitle,
          metaDescription: pageData.metaDescription,
          status: pageData.status,
          template: pageData.template,
          featured: pageData.featured,
          author: pageData.author,
          publishedAt: new Date().toISOString(),
        },
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    console.log(`✅ Created page: ${pageData.title}`);
    return response.data;
  } catch (error) {
    console.error(
      `❌ Error creating page ${pageData.title}:`,
      error.response?.data || error.message
    );
    return null;
  }
}

async function runProgrammaticMigration() {
  console.log('🚀 Starting Programmatic Strapi CMS Migration');
  console.log('='.repeat(60));

  let successCount = 0;
  let errorCount = 0;

  for (const page of allPages) {
    try {
      let result;

      if (page.isUpdate) {
        result = await updateExistingPage(page);
      } else {
        result = await createNewPage(page);
      }

      if (result) {
        successCount++;
      } else {
        errorCount++;
      }

      // Add delay to avoid overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`❌ Unexpected error processing ${page.title}:`, error.message);
      errorCount++;
    }
  }

  console.log('\n📊 MIGRATION SUMMARY');
  console.log('='.repeat(30));
  console.log(`✅ Successful: ${successCount}/${allPages.length}`);
  console.log(`❌ Errors: ${errorCount}/${allPages.length}`);

  if (successCount === allPages.length) {
    console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('All 6 pages have been created/updated in Strapi CMS.');
  } else {
    console.log('\n⚠️ MIGRATION PARTIALLY COMPLETED');
    console.log('Some pages may need manual intervention.');
  }

  // Verify the results
  await verifyMigration();
}

async function verifyMigration() {
  console.log('\n🔍 VERIFYING MIGRATION RESULTS');
  console.log('='.repeat(40));

  try {
    const response = await axios.get(`${STRAPI_URL}/api/pages`);
    const pages = response.data.data;

    console.log(`📊 Total pages in Strapi: ${pages.length}/6`);
    console.log('\n📄 Pages found:');

    pages.forEach(page => {
      const hasRichContent = page.content && page.content.length > 100;
      const hasMetadata = page.excerpt && page.metaTitle && page.metaDescription;

      console.log(`   ${page.status === 'published' ? '🟢' : '🟡'} ${page.title} (${page.slug})`);
      console.log(
        `      Content: ${hasRichContent ? '✅' : '❌'} (${page.content?.length || 0} chars)`
      );
      console.log(`      Metadata: ${hasMetadata ? '✅' : '❌'}`);
      console.log(`      Template: ${page.template || 'not set'}`);
      console.log(`      Featured: ${page.featured ? '⭐' : '○'}`);
    });

    // Test frontend integration
    console.log('\n🌐 Testing Frontend Integration:');
    const testSlugs = ['about-us', 'contact', 'privacy-policy', 'terms', 'faq', 'help'];

    for (const slug of testSlugs) {
      try {
        const frontendResponse = await axios.get(`http://localhost:3001/${slug}`, {
          timeout: 5000,
        });

        if (frontendResponse.status === 200) {
          const hasRichContent =
            frontendResponse.data.includes('<h2>') || frontendResponse.data.includes('<h3>');
          console.log(`   ✅ /${slug} - ${hasRichContent ? 'Strapi content' : 'Fallback content'}`);
        }
      } catch (error) {
        console.log(`   ❌ /${slug} - Error: ${error.message}`);
      }
    }
  } catch (error) {
    console.error('❌ Error verifying migration:', error.message);
  }
}

// Run the migration
runProgrammaticMigration().catch(console.error);
