{"categories": [{"id": 2, "documentId": "mjo6dudqqhugv91dp6xy7qhp", "name": "Electronics", "description": "Electronic devices, gadgets, and technology products", "short_description": "Browse the latest electronics and tech gadgets", "slug": "electronics", "featured": true, "active": true, "sort_order": 1, "meta_title": "Electronics - ON<PERSON> Seller", "meta_description": "Shop electronics, smartphones, laptops, and tech accessories", "createdAt": "2025-06-16T12:56:53.994Z", "updatedAt": "2025-06-16T12:56:53.994Z", "publishedAt": "2025-06-16T12:56:54.733Z"}, {"id": 4, "documentId": "zxkdf6q3b2i83hlb05pmucnc", "name": "Fashion", "description": "Clothing, footwear, and fashion accessories", "short_description": "Discover trendy fashion and clothing", "slug": "fashion", "featured": true, "active": true, "sort_order": 2, "meta_title": "Fashion - <PERSON><PERSON>", "meta_description": "Shop fashion, clothing, footwear, and accessories", "createdAt": "2025-06-16T12:56:55.362Z", "updatedAt": "2025-06-16T12:56:55.362Z", "publishedAt": "2025-06-16T12:56:55.411Z"}, {"id": 6, "documentId": "s6gz77ymv8t62s0oaimwd4aw", "name": "Home & Garden", "description": "Home improvement, furniture, and garden products", "short_description": "Transform your home and garden", "slug": "home-garden", "featured": true, "active": true, "sort_order": 3, "meta_title": "Home & Garden - ONDC Seller", "meta_description": "Shop furniture, home decor, kitchen, and garden products", "createdAt": "2025-06-16T12:56:56.009Z", "updatedAt": "2025-06-16T12:56:56.009Z", "publishedAt": "2025-06-16T12:56:56.048Z"}, {"id": 8, "documentId": "vrpsz1yu4ke1ty3vxrze5s72", "name": "Sports & Fitness", "description": "Sports equipment, fitness gear, and activewear", "short_description": "Stay active with sports and fitness products", "slug": "sports-fitness", "featured": false, "active": true, "sort_order": 4, "meta_title": "Sports & Fitness - <PERSON><PERSON> Seller", "meta_description": "Shop sports equipment, fitness gear, and activewear", "createdAt": "2025-06-16T12:56:56.648Z", "updatedAt": "2025-06-16T12:56:56.648Z", "publishedAt": "2025-06-16T12:56:56.697Z"}, {"id": 10, "documentId": "qmf480lh4zz4y6ensbncuu9m", "name": "Books & Media", "description": "Books, magazines, and digital media", "short_description": "Explore books and media content", "slug": "books-media", "featured": false, "active": true, "sort_order": 5, "meta_title": "Books & Media - ON<PERSON> Seller", "meta_description": "Shop books, magazines, and digital media", "createdAt": "2025-06-16T12:56:57.329Z", "updatedAt": "2025-06-16T12:56:57.329Z", "publishedAt": "2025-06-16T12:56:57.367Z"}, {"id": 12, "documentId": "vds2exb5v1hxy30jvnlqboee", "name": "Health & Beauty", "description": "Health products, cosmetics, and personal care", "short_description": "Health and beauty essentials", "slug": "health-beauty", "featured": false, "active": true, "sort_order": 6, "meta_title": "Health & Beauty - ONDC Seller", "meta_description": "Shop health products, cosmetics, and personal care items", "createdAt": "2025-06-16T12:56:57.959Z", "updatedAt": "2025-06-16T12:56:57.959Z", "publishedAt": "2025-06-16T12:56:58.073Z"}, {"id": 14, "documentId": "vgl94x4yqter6dwbfaiu5nc2", "name": "Automotive", "description": "Car accessories, parts, and automotive products", "short_description": "Automotive parts and accessories", "slug": "automotive", "featured": false, "active": true, "sort_order": 7, "meta_title": "Automotive - ONDC Seller", "meta_description": "Shop car accessories, parts, and automotive products", "createdAt": "2025-06-16T12:56:58.881Z", "updatedAt": "2025-06-16T12:56:58.881Z", "publishedAt": "2025-06-16T12:56:58.920Z"}, {"id": 16, "documentId": "ys0os55tcmbqpbzxo2e56v16", "name": "Toys & Games", "description": "Toys, games, and entertainment products", "short_description": "Fun toys and games for all ages", "slug": "toys-games", "featured": false, "active": true, "sort_order": 8, "meta_title": "Toys & Games - <PERSON><PERSON> Seller", "meta_description": "Shop toys, games, and entertainment products", "createdAt": "2025-06-16T12:56:59.511Z", "updatedAt": "2025-06-16T12:56:59.511Z", "publishedAt": "2025-06-16T12:56:59.555Z"}], "productCategories": [{"id": 2, "documentId": "oo93h1g18f4f06a4b0r6lp3q", "name": "Test Electronics", "description": null, "slug": "test-electronics", "isSubcategory": false, "createdAt": "2025-06-16T10:14:09.637Z", "updatedAt": "2025-06-16T10:14:09.637Z", "publishedAt": "2025-06-16T10:14:09.687Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 4, "documentId": "v0nps44r1chn2h29bhqtz25g", "name": "Electronics", "description": null, "slug": "electronics", "isSubcategory": false, "createdAt": "2025-06-16T10:18:14.697Z", "updatedAt": "2025-06-16T10:18:14.697Z", "publishedAt": "2025-06-16T10:18:14.752Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 6, "documentId": "f1e0g783iavmdgf31hb0dd0w", "name": "Fashion", "description": null, "slug": "fashion", "isSubcategory": false, "createdAt": "2025-06-16T10:18:14.871Z", "updatedAt": "2025-06-16T10:18:14.871Z", "publishedAt": "2025-06-16T10:18:14.912Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 8, "documentId": "kji2kd4m1cfw16qb4yi20yep", "name": "Home & Garden", "description": null, "slug": "home-garden", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.018Z", "updatedAt": "2025-06-16T10:18:15.018Z", "publishedAt": "2025-06-16T10:18:15.065Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 10, "documentId": "tvky96h4s9tlbbgl0krws3ow", "name": "Sports & Fitness", "description": null, "slug": "sports-fitness", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.171Z", "updatedAt": "2025-06-16T10:18:15.171Z", "publishedAt": "2025-06-16T10:18:15.214Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 12, "documentId": "j2fz5390i1q9r8r0cj69uyjk", "name": "Books & Media", "description": null, "slug": "books-media", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.317Z", "updatedAt": "2025-06-16T10:18:15.317Z", "publishedAt": "2025-06-16T10:18:15.350Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 14, "documentId": "k174f4hse7hmnqssp4p35lnq", "name": "Health & Beauty", "description": null, "slug": "health-beauty", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.467Z", "updatedAt": "2025-06-16T10:18:15.467Z", "publishedAt": "2025-06-16T10:18:15.512Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 16, "documentId": "yh4f5be2jn3xhrjxcj3eo2tz", "name": "Automotive", "description": null, "slug": "automotive", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.585Z", "updatedAt": "2025-06-16T10:18:15.585Z", "publishedAt": "2025-06-16T10:18:15.632Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 18, "documentId": "jhmp3qvnbk9wsic40siyfegi", "name": "Toys & Games", "description": null, "slug": "toys-games", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.703Z", "updatedAt": "2025-06-16T10:18:15.703Z", "publishedAt": "2025-06-16T10:18:15.739Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 20, "documentId": "mjstbi0x5dhkq1yb3nuvoqxk", "name": "Smartphones", "description": null, "slug": "smartphones", "isSubcategory": true, "createdAt": "2025-06-16T10:18:15.887Z", "updatedAt": "2025-06-16T10:18:15.887Z", "publishedAt": "2025-06-16T10:18:15.923Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 22, "documentId": "mhatqp7w15luan85yxl0ogt6", "name": "Laptops", "description": null, "slug": "laptops", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.026Z", "updatedAt": "2025-06-16T10:18:16.026Z", "publishedAt": "2025-06-16T10:18:16.068Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 24, "documentId": "ngx36p3sc84ipijf5b983qmv", "name": "Accessories", "description": null, "slug": "electronics-accessories", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.176Z", "updatedAt": "2025-06-16T10:18:16.176Z", "publishedAt": "2025-06-16T10:18:16.217Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 26, "documentId": "cu0wzt2l6zupedumrr8dklz3", "name": "Tablets", "description": null, "slug": "tablets", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.306Z", "updatedAt": "2025-06-16T10:18:16.306Z", "publishedAt": "2025-06-16T10:18:16.353Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 28, "documentId": "a0hrio1978hosl60xtay5hm7", "name": "Men's Clothing", "description": null, "slug": "mens-clothing", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.501Z", "updatedAt": "2025-06-16T10:18:16.501Z", "publishedAt": "2025-06-16T10:18:16.538Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 30, "documentId": "st7jwzn2peb0954kfbk8f4cd", "name": "Women's Clothing", "description": null, "slug": "womens-clothing", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.628Z", "updatedAt": "2025-06-16T10:18:16.628Z", "publishedAt": "2025-06-16T10:18:16.670Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 32, "documentId": "pe4iz9l34btytmjjh8mzkd4o", "name": "Footwear", "description": null, "slug": "footwear", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.761Z", "updatedAt": "2025-06-16T10:18:16.761Z", "publishedAt": "2025-06-16T10:18:16.800Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 34, "documentId": "y5h9ja7cg7y558nzg3442ii6", "name": "Accessories", "description": null, "slug": "fashion-accessories", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.897Z", "updatedAt": "2025-06-16T10:18:16.897Z", "publishedAt": "2025-06-16T10:18:16.944Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 36, "documentId": "i8rg26uwu7ac98kp4t404ge8", "name": "Furniture", "description": null, "slug": "furniture", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.031Z", "updatedAt": "2025-06-16T10:18:17.031Z", "publishedAt": "2025-06-16T10:18:17.070Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 38, "documentId": "gaoo81ugja8at2h2pn6qdca8", "name": "Kitchen", "description": null, "slug": "kitchen", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.149Z", "updatedAt": "2025-06-16T10:18:17.149Z", "publishedAt": "2025-06-16T10:18:17.189Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 40, "documentId": "bgc5s3xohgzzy9y7malotgoa", "name": "Decor", "description": null, "slug": "decor", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.321Z", "updatedAt": "2025-06-16T10:18:17.321Z", "publishedAt": "2025-06-16T10:18:17.364Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 42, "documentId": "lj2z373g6p9ujgaure7x88mj", "name": "Garden", "description": null, "slug": "garden", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.471Z", "updatedAt": "2025-06-16T10:18:17.471Z", "publishedAt": "2025-06-16T10:18:17.513Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 44, "documentId": "rkpw6wquc9m60hnq10u8vvey", "name": "Fitness Equipment", "description": null, "slug": "fitness-equipment", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.596Z", "updatedAt": "2025-06-16T10:18:17.596Z", "publishedAt": "2025-06-16T10:18:17.636Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 46, "documentId": "i65evticjmz289cq1jb2u0ro", "name": "Outdoor Sports", "description": null, "slug": "outdoor-sports", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.717Z", "updatedAt": "2025-06-16T10:18:17.717Z", "publishedAt": "2025-06-16T10:18:17.754Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 48, "documentId": "jhjq75jmxenp7trsne6nkmb3", "name": "Activewear", "description": null, "slug": "activewear", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.844Z", "updatedAt": "2025-06-16T10:18:17.844Z", "publishedAt": "2025-06-16T10:18:17.884Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 50, "documentId": "taihftpblogv4bub86gw6y0y", "name": "Fiction", "description": null, "slug": "fiction", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.954Z", "updatedAt": "2025-06-16T10:18:17.954Z", "publishedAt": "2025-06-16T10:18:17.992Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}], "products": [{"id": 2, "documentId": "a078fpup8wzczqkadqpycyrk", "name": "Test Product", "description": null, "short_description": null, "price": 999, "sale_price": null, "sku": "TEST-001", "inventory_quantity": null, "product_status": null, "featured": null, "tags": null, "weight": null, "createdAt": "2025-06-16T10:14:10.031Z", "updatedAt": "2025-06-16T10:14:10.031Z", "publishedAt": "2025-06-16T10:14:10.094Z"}, {"id": 4, "documentId": "r7uwik36dok5j3h2o6je8nai", "name": "iPhone 15 Pro", "description": null, "short_description": "Latest iPhone with A17 Pro chip and titanium design", "price": 134900, "sale_price": 129900, "sku": "IPHONE-15-PRO-128", "inventory_quantity": 25, "product_status": "Published", "featured": true, "tags": "iPhone, Apple, Smartphone, Premium", "weight": 0.19, "createdAt": "2025-06-16T10:18:20.919Z", "updatedAt": "2025-06-16T10:18:20.919Z", "publishedAt": "2025-06-16T10:18:20.980Z"}, {"id": 6, "documentId": "tvw1hyhtrphorfsaovbqg7ll", "name": "Samsung Galaxy S24 Ultra", "description": null, "short_description": "Flagship Android phone with S Pen and AI features", "price": 129999, "sale_price": 124999, "sku": "SAMSUNG-S24-ULTRA-256", "inventory_quantity": 30, "product_status": "Published", "featured": true, "tags": "Samsung, Android, Smartphone, S Pen", "weight": 0.23, "createdAt": "2025-06-16T10:18:21.060Z", "updatedAt": "2025-06-16T10:18:21.060Z", "publishedAt": "2025-06-16T10:18:21.114Z"}, {"id": 8, "documentId": "d2imr1e8it6216t7jz08undv", "name": "OnePlus 12", "description": null, "short_description": "Flagship killer with Snapdragon 8 Gen 3", "price": 64999, "sale_price": 59999, "sku": "ONEPLUS-12-256", "inventory_quantity": 40, "product_status": "Published", "featured": false, "tags": "OnePlus, Android, Flagship, Fast Charging", "weight": 0.22, "createdAt": "2025-06-16T10:18:21.180Z", "updatedAt": "2025-06-16T10:18:21.180Z", "publishedAt": "2025-06-16T10:18:21.225Z"}, {"id": 10, "documentId": "k2g19bbjyprvuekh12tz8r82", "name": "Google Pixel 8", "description": null, "short_description": "AI-powered photography and pure Android experience", "price": 75999, "sale_price": 69999, "sku": "PIXEL-8-128", "inventory_quantity": 35, "product_status": "Published", "featured": false, "tags": "Google, Pixel, AI Camera, Android", "weight": 0.2, "createdAt": "2025-06-16T10:18:21.293Z", "updatedAt": "2025-06-16T10:18:21.293Z", "publishedAt": "2025-06-16T10:18:21.341Z"}, {"id": 12, "documentId": "g5npqql6upd7cybzh3rft1ys", "name": "Xiaomi 14", "description": null, "short_description": "Premium smartphone with Leica cameras", "price": 69999, "sale_price": 64999, "sku": "XIAOMI-14-256", "inventory_quantity": 50, "product_status": "Published", "featured": false, "tags": "<PERSON><PERSON>, <PERSON><PERSON>, Camera, Value", "weight": 0.19, "createdAt": "2025-06-16T10:18:21.411Z", "updatedAt": "2025-06-16T10:18:21.411Z", "publishedAt": "2025-06-16T10:18:21.459Z"}, {"id": 14, "documentId": "af0fbtsdscqjrbc96u6owv0i", "name": "MacBook Air M3", "description": null, "short_description": "13-inch laptop with M3 chip and all-day battery", "price": 114900, "sale_price": 109900, "sku": "MACBOOK-AIR-M3-256", "inventory_quantity": 20, "product_status": "Published", "featured": true, "tags": "Apple, MacBook, M3, Lapt<PERSON>", "weight": 1.24, "createdAt": "2025-06-16T10:18:21.551Z", "updatedAt": "2025-06-16T10:18:21.551Z", "publishedAt": "2025-06-16T10:18:21.610Z"}, {"id": 16, "documentId": "tik5u32b87z92chzfxhft045", "name": "Dell XPS 13", "description": null, "short_description": "Premium ultrabook with Intel Core i7", "price": 89999, "sale_price": 84999, "sku": "DELL-XPS13-I7-512", "inventory_quantity": 15, "product_status": "Published", "featured": true, "tags": "Dell, XPS, Intel, Ultrabook", "weight": 1.27, "createdAt": "2025-06-16T10:18:21.696Z", "updatedAt": "2025-06-16T10:18:21.696Z", "publishedAt": "2025-06-16T10:18:21.746Z"}, {"id": 18, "documentId": "ha6cyelnqu4io8oworp8fsrc", "name": "HP Spectre x360", "description": null, "short_description": "2-in-1 convertible laptop with touch display", "price": 94999, "sale_price": 89999, "sku": "HP-SPECTRE-X360-512", "inventory_quantity": 18, "product_status": "Published", "featured": false, "tags": "HP, <PERSON><PERSON>re, 2-in-1, Convertible", "weight": 1.34, "createdAt": "2025-06-16T10:18:21.812Z", "updatedAt": "2025-06-16T10:18:21.812Z", "publishedAt": "2025-06-16T10:18:21.865Z"}, {"id": 20, "documentId": "hc1vt2ieet39b1uham1a8yup", "name": "ASUS ROG Strix G15", "description": null, "short_description": "Gaming laptop with RTX 4060 and RGB lighting", "price": 79999, "sale_price": 74999, "sku": "ASUS-ROG-G15-RTX4060", "inventory_quantity": 12, "product_status": "Published", "featured": false, "tags": "ASUS, ROG, Gaming, RTX", "weight": 2.3, "createdAt": "2025-06-16T10:18:21.923Z", "updatedAt": "2025-06-16T10:18:21.923Z", "publishedAt": "2025-06-16T10:18:21.963Z"}, {"id": 22, "documentId": "tcj0qdq1ho7l8qozohwtve0d", "name": "Lenovo ThinkPad X1 Carbon", "description": null, "short_description": "Business laptop with military-grade durability", "price": 124999, "sale_price": 119999, "sku": "LENOVO-X1-CARBON-512", "inventory_quantity": 10, "product_status": "Published", "featured": false, "tags": "Lenovo, ThinkPad, Business, Durable", "weight": 1.12, "createdAt": "2025-06-16T10:18:22.028Z", "updatedAt": "2025-06-16T10:18:22.028Z", "publishedAt": "2025-06-16T10:18:22.066Z"}, {"id": 24, "documentId": "c83pkqhg0ynl3dbrp37aq4ub", "name": "Cotton Casual Shirt", "description": null, "short_description": "Comfortable cotton shirt for everyday wear", "price": 1299, "sale_price": 999, "sku": "SHIRT-COTTON-M-BLUE", "inventory_quantity": 100, "product_status": "Published", "featured": true, "tags": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Men", "weight": 0.25, "createdAt": "2025-06-16T10:18:22.129Z", "updatedAt": "2025-06-16T10:18:22.129Z", "publishedAt": "2025-06-16T10:18:22.181Z"}, {"id": 26, "documentId": "feywgjh9750ktt49eoiwcxgd", "name": "Formal Blazer", "description": null, "short_description": "Elegant blazer for formal occasions", "price": 3999, "sale_price": 3499, "sku": "BLAZER-FORMAL-M-BLACK", "inventory_quantity": 50, "product_status": "Published", "featured": true, "tags": "Blazer, Formal, Business, Men", "weight": 0.8, "createdAt": "2025-06-16T10:18:22.271Z", "updatedAt": "2025-06-16T10:18:22.271Z", "publishedAt": "2025-06-16T10:18:22.315Z"}, {"id": 28, "documentId": "l39zyqxt86jgoojze6wtzmhy", "name": "<PERSON><PERSON>", "description": null, "short_description": "Classic blue denim jeans with perfect fit", "price": 2499, "sale_price": 1999, "sku": "JEANS-DENIM-M-BLUE-32", "inventory_quantity": 75, "product_status": "Published", "featured": false, "tags": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "weight": 0.6, "createdAt": "2025-06-16T10:18:22.428Z", "updatedAt": "2025-06-16T10:18:22.428Z", "publishedAt": "2025-06-16T10:18:22.514Z"}, {"id": 30, "documentId": "ukmc4pfvzjo4sprb7uobzy8m", "name": "Polo T-Shirt", "description": null, "short_description": "Classic polo shirt in premium cotton", "price": 899, "sale_price": 699, "sku": "POLO-COTTON-M-WHITE", "inventory_quantity": 120, "product_status": "Published", "featured": false, "tags": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, C<PERSON>ual", "weight": 0.2, "createdAt": "2025-06-16T10:18:22.756Z", "updatedAt": "2025-06-16T10:18:22.756Z", "publishedAt": "2025-06-16T10:18:22.803Z"}, {"id": 32, "documentId": "uyueit4whsid9kwdzm0o9sbf", "name": "Leather Jacket", "description": null, "short_description": "Genuine leather jacket with modern styling", "price": 8999, "sale_price": 7999, "sku": "JACKET-LEATHER-M-BLACK", "inventory_quantity": 25, "product_status": "Published", "featured": true, "tags": "Jacket, Leather, Premium, Style", "weight": 1.2, "createdAt": "2025-06-16T10:18:22.908Z", "updatedAt": "2025-06-16T10:18:22.908Z", "publishedAt": "2025-06-16T10:18:23.347Z"}, {"id": 34, "documentId": "odc3p4ui5u1vi9kljnlp77rt", "name": "Modern Sofa Set", "description": null, "short_description": "3-seater sofa with premium upholstery", "price": 45999, "sale_price": 39999, "sku": "SOFA-3SEAT-GREY", "inventory_quantity": 8, "product_status": "Published", "featured": true, "tags": "Sofa, Furniture, Living Room, Comfort", "weight": 85, "createdAt": "2025-06-16T10:18:23.450Z", "updatedAt": "2025-06-16T10:18:23.450Z", "publishedAt": "2025-06-16T10:18:23.513Z"}, {"id": 36, "documentId": "niptm13lrnv9xssd2clu2xk8", "name": "Dining Table Set", "description": null, "short_description": "6-seater wooden dining table with chairs", "price": 32999, "sale_price": 28999, "sku": "DINING-6SEAT-WOOD", "inventory_quantity": 5, "product_status": "Published", "featured": true, "tags": "Dining Table, Wood, Furniture, Family", "weight": 120, "createdAt": "2025-06-16T10:18:23.711Z", "updatedAt": "2025-06-16T10:18:23.711Z", "publishedAt": "2025-06-16T10:18:23.755Z"}, {"id": 38, "documentId": "kfrfhnflrf8yq3kry2m5hpe1", "name": "Office Chair", "description": null, "short_description": "Ergonomic office chair with lumbar support", "price": 8999, "sale_price": 7499, "sku": "CHAIR-OFFICE-ERGONOMIC", "inventory_quantity": 30, "product_status": "Published", "featured": false, "tags": "Chair, Office, Ergonomic, Comfort", "weight": 15, "createdAt": "2025-06-16T10:18:23.915Z", "updatedAt": "2025-06-16T10:18:23.915Z", "publishedAt": "2025-06-16T10:18:23.961Z"}, {"id": 50, "documentId": "keb8wcph3gopq423y9k14l53", "name": "Treadmill", "description": null, "short_description": "Electric treadmill with incline and programs", "price": 89999, "sale_price": 79999, "sku": "TREADMILL-ELECTRIC-PRO", "inventory_quantity": 3, "product_status": "Published", "featured": true, "tags": "Treadmill, Cardio, Home Gym, Electric", "weight": 85, "createdAt": "2025-06-16T10:18:24.675Z", "updatedAt": "2025-06-16T10:18:24.675Z", "publishedAt": "2025-06-16T10:18:24.723Z"}, {"id": 40, "documentId": "pa0edpx7k54r9ih66k7wc457", "name": "Bookshelf", "description": null, "short_description": "5-tier wooden bookshelf for storage", "price": 4999, "sale_price": 4299, "sku": "BOOKSHELF-5TIER-WOOD", "inventory_quantity": 20, "product_status": "Published", "featured": false, "tags": "Bookshelf, Storage, Wood, Organization", "weight": 25, "createdAt": "2025-06-16T10:18:24.028Z", "updatedAt": "2025-06-16T10:18:24.028Z", "publishedAt": "2025-06-16T10:18:24.079Z"}, {"id": 44, "documentId": "vcxrfdes099jgi7rb6xz7j0e", "name": "Adjustable <PERSON><PERSON><PERSON><PERSON>", "description": null, "short_description": "Space-saving adjustable dumbbells 5-50 lbs", "price": 24999, "sale_price": 21999, "sku": "DUMBBELL-ADJ-50LB", "inventory_quantity": 12, "product_status": "Published", "featured": true, "tags": "<PERSON><PERSON><PERSON><PERSON>, Fitness, Home Gym, Adjustable", "weight": 25, "createdAt": "2025-06-16T10:18:24.258Z", "updatedAt": "2025-06-16T10:18:24.258Z", "publishedAt": "2025-06-16T10:18:24.308Z"}, {"id": 48, "documentId": "p2lui8phnko313qdr2p1icho", "name": "Resistance Bands Set", "description": null, "short_description": "Complete resistance bands set with accessories", "price": 2499, "sale_price": 1999, "sku": "RESISTANCE-BANDS-SET", "inventory_quantity": 40, "product_status": "Published", "featured": false, "tags": "Resistance Bands, Fitness, Portable, Workout", "weight": 1.5, "createdAt": "2025-06-16T10:18:24.513Z", "updatedAt": "2025-06-16T10:18:24.513Z", "publishedAt": "2025-06-16T10:18:24.575Z"}, {"id": 52, "documentId": "nwpx962ubmkyftuu2vh0cpa0", "name": "Exercise Bike", "description": null, "short_description": "Stationary exercise bike with digital display", "price": 19999, "sale_price": 17999, "sku": "BIKE-EXERCISE-DIGITAL", "inventory_quantity": 8, "product_status": "Published", "featured": false, "tags": "Exercise Bike, Cardio, Fitness, Stationary", "weight": 35, "createdAt": "2025-06-16T10:18:24.815Z", "updatedAt": "2025-06-16T10:18:24.815Z", "publishedAt": "2025-06-16T10:18:24.868Z"}, {"id": 42, "documentId": "x9k9s5q12vv3094vtrykjj9r", "name": "Coffee Table", "description": null, "short_description": "Glass-top coffee table with metal frame", "price": 6999, "sale_price": 5999, "sku": "TABLE-COFFEE-GLASS", "inventory_quantity": 15, "product_status": "Published", "featured": false, "tags": "Coffee Table, Glass, Modern, Living Room", "weight": 18, "createdAt": "2025-06-16T10:18:24.145Z", "updatedAt": "2025-06-16T10:18:24.145Z", "publishedAt": "2025-06-16T10:18:24.193Z"}], "mainCategoriesInPC": [{"id": 2, "documentId": "oo93h1g18f4f06a4b0r6lp3q", "name": "Test Electronics", "description": null, "slug": "test-electronics", "isSubcategory": false, "createdAt": "2025-06-16T10:14:09.637Z", "updatedAt": "2025-06-16T10:14:09.637Z", "publishedAt": "2025-06-16T10:14:09.687Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 4, "documentId": "v0nps44r1chn2h29bhqtz25g", "name": "Electronics", "description": null, "slug": "electronics", "isSubcategory": false, "createdAt": "2025-06-16T10:18:14.697Z", "updatedAt": "2025-06-16T10:18:14.697Z", "publishedAt": "2025-06-16T10:18:14.752Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 6, "documentId": "f1e0g783iavmdgf31hb0dd0w", "name": "Fashion", "description": null, "slug": "fashion", "isSubcategory": false, "createdAt": "2025-06-16T10:18:14.871Z", "updatedAt": "2025-06-16T10:18:14.871Z", "publishedAt": "2025-06-16T10:18:14.912Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 8, "documentId": "kji2kd4m1cfw16qb4yi20yep", "name": "Home & Garden", "description": null, "slug": "home-garden", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.018Z", "updatedAt": "2025-06-16T10:18:15.018Z", "publishedAt": "2025-06-16T10:18:15.065Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 10, "documentId": "tvky96h4s9tlbbgl0krws3ow", "name": "Sports & Fitness", "description": null, "slug": "sports-fitness", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.171Z", "updatedAt": "2025-06-16T10:18:15.171Z", "publishedAt": "2025-06-16T10:18:15.214Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 12, "documentId": "j2fz5390i1q9r8r0cj69uyjk", "name": "Books & Media", "description": null, "slug": "books-media", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.317Z", "updatedAt": "2025-06-16T10:18:15.317Z", "publishedAt": "2025-06-16T10:18:15.350Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 14, "documentId": "k174f4hse7hmnqssp4p35lnq", "name": "Health & Beauty", "description": null, "slug": "health-beauty", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.467Z", "updatedAt": "2025-06-16T10:18:15.467Z", "publishedAt": "2025-06-16T10:18:15.512Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 16, "documentId": "yh4f5be2jn3xhrjxcj3eo2tz", "name": "Automotive", "description": null, "slug": "automotive", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.585Z", "updatedAt": "2025-06-16T10:18:15.585Z", "publishedAt": "2025-06-16T10:18:15.632Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 18, "documentId": "jhmp3qvnbk9wsic40siyfegi", "name": "Toys & Games", "description": null, "slug": "toys-games", "isSubcategory": false, "createdAt": "2025-06-16T10:18:15.703Z", "updatedAt": "2025-06-16T10:18:15.703Z", "publishedAt": "2025-06-16T10:18:15.739Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}], "subcategoriesInPC": [{"id": 20, "documentId": "mjstbi0x5dhkq1yb3nuvoqxk", "name": "Smartphones", "description": null, "slug": "smartphones", "isSubcategory": true, "createdAt": "2025-06-16T10:18:15.887Z", "updatedAt": "2025-06-16T10:18:15.887Z", "publishedAt": "2025-06-16T10:18:15.923Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 22, "documentId": "mhatqp7w15luan85yxl0ogt6", "name": "Laptops", "description": null, "slug": "laptops", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.026Z", "updatedAt": "2025-06-16T10:18:16.026Z", "publishedAt": "2025-06-16T10:18:16.068Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 24, "documentId": "ngx36p3sc84ipijf5b983qmv", "name": "Accessories", "description": null, "slug": "electronics-accessories", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.176Z", "updatedAt": "2025-06-16T10:18:16.176Z", "publishedAt": "2025-06-16T10:18:16.217Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 26, "documentId": "cu0wzt2l6zupedumrr8dklz3", "name": "Tablets", "description": null, "slug": "tablets", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.306Z", "updatedAt": "2025-06-16T10:18:16.306Z", "publishedAt": "2025-06-16T10:18:16.353Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 28, "documentId": "a0hrio1978hosl60xtay5hm7", "name": "Men's Clothing", "description": null, "slug": "mens-clothing", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.501Z", "updatedAt": "2025-06-16T10:18:16.501Z", "publishedAt": "2025-06-16T10:18:16.538Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 30, "documentId": "st7jwzn2peb0954kfbk8f4cd", "name": "Women's Clothing", "description": null, "slug": "womens-clothing", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.628Z", "updatedAt": "2025-06-16T10:18:16.628Z", "publishedAt": "2025-06-16T10:18:16.670Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 32, "documentId": "pe4iz9l34btytmjjh8mzkd4o", "name": "Footwear", "description": null, "slug": "footwear", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.761Z", "updatedAt": "2025-06-16T10:18:16.761Z", "publishedAt": "2025-06-16T10:18:16.800Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 34, "documentId": "y5h9ja7cg7y558nzg3442ii6", "name": "Accessories", "description": null, "slug": "fashion-accessories", "isSubcategory": true, "createdAt": "2025-06-16T10:18:16.897Z", "updatedAt": "2025-06-16T10:18:16.897Z", "publishedAt": "2025-06-16T10:18:16.944Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 36, "documentId": "i8rg26uwu7ac98kp4t404ge8", "name": "Furniture", "description": null, "slug": "furniture", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.031Z", "updatedAt": "2025-06-16T10:18:17.031Z", "publishedAt": "2025-06-16T10:18:17.070Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 38, "documentId": "gaoo81ugja8at2h2pn6qdca8", "name": "Kitchen", "description": null, "slug": "kitchen", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.149Z", "updatedAt": "2025-06-16T10:18:17.149Z", "publishedAt": "2025-06-16T10:18:17.189Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 40, "documentId": "bgc5s3xohgzzy9y7malotgoa", "name": "Decor", "description": null, "slug": "decor", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.321Z", "updatedAt": "2025-06-16T10:18:17.321Z", "publishedAt": "2025-06-16T10:18:17.364Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 42, "documentId": "lj2z373g6p9ujgaure7x88mj", "name": "Garden", "description": null, "slug": "garden", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.471Z", "updatedAt": "2025-06-16T10:18:17.471Z", "publishedAt": "2025-06-16T10:18:17.513Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 44, "documentId": "rkpw6wquc9m60hnq10u8vvey", "name": "Fitness Equipment", "description": null, "slug": "fitness-equipment", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.596Z", "updatedAt": "2025-06-16T10:18:17.596Z", "publishedAt": "2025-06-16T10:18:17.636Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 46, "documentId": "i65evticjmz289cq1jb2u0ro", "name": "Outdoor Sports", "description": null, "slug": "outdoor-sports", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.717Z", "updatedAt": "2025-06-16T10:18:17.717Z", "publishedAt": "2025-06-16T10:18:17.754Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 48, "documentId": "jhjq75jmxenp7trsne6nkmb3", "name": "Activewear", "description": null, "slug": "activewear", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.844Z", "updatedAt": "2025-06-16T10:18:17.844Z", "publishedAt": "2025-06-16T10:18:17.884Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}, {"id": 50, "documentId": "taihftpblogv4bub86gw6y0y", "name": "Fiction", "description": null, "slug": "fiction", "isSubcategory": true, "createdAt": "2025-06-16T10:18:17.954Z", "updatedAt": "2025-06-16T10:18:17.954Z", "publishedAt": "2025-06-16T10:18:17.992Z", "short_description": null, "featured": null, "active": null, "sort_order": null, "meta_title": null, "meta_description": null}]}