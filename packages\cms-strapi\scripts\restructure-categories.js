/**
 * Restructure Categories System
 * 1. Remove parent categories from Product Categories collection
 * 2. Update parent field to link to Categories collection (using 'category' field)
 * 3. Link products to subcategories
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Enhanced request function
async function strapiRequest(endpoint, method = 'GET', data = null, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${API_BASE}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = { data };
      }

      const response = await axios(config);
      return { success: true, data: response.data };
    } catch (error) {
      console.log(`   Attempt ${attempt}/${retries} failed for ${method} ${endpoint}`);
      
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || 'Unknown error';
        console.log(`   ❌ HTTP ${status}: ${message}`);
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }
      
      if (attempt === retries) {
        return { success: false, error: error.message };
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics',
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Digital Media': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

function logProgress(step, message, type = 'info') {
  const timestamp = new Date().toISOString();
  const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${symbols[type]} [${timestamp}] ${step}: ${message}`);
}

async function createBackup(step, data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, 'restructure-backups');
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const backupFile = path.join(backupDir, `${step}_${timestamp}.json`);
  fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
  console.log(`   📁 Backup created: ${backupFile}`);
  return backupFile;
}

// Step 1: Analyze current data
async function analyzeCurrentData() {
  logProgress('ANALYZE', 'Analyzing current data structure', 'info');
  
  try {
    // Get Categories
    const categoriesResult = await strapiRequest('/categories');
    if (!categoriesResult.success) {
      throw new Error('Failed to fetch categories');
    }
    
    // Get Product Categories
    const productCategoriesResult = await strapiRequest('/product-categories');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }
    
    // Get Products
    const productsResult = await strapiRequest('/products');
    if (!productsResult.success) {
      throw new Error('Failed to fetch products');
    }
    
    const categories = categoriesResult.data.data || [];
    const productCategories = productCategoriesResult.data.data || [];
    const products = productsResult.data.data || [];
    
    // Analyze product categories
    const mainCategoriesInPC = productCategories.filter(pc => 
      pc.category_type === 'main' || (!pc.isSubcategory && !subcategoryMapping[pc.name])
    );
    const subcategoriesInPC = productCategories.filter(pc => 
      pc.category_type === 'sub' || pc.isSubcategory || subcategoryMapping[pc.name]
    );
    
    logProgress('ANALYZE', `Found ${categories.length} categories, ${productCategories.length} product categories, ${products.length} products`, 'success');
    logProgress('ANALYZE', `Main categories in PC to remove: ${mainCategoriesInPC.length}`, 'info');
    logProgress('ANALYZE', `Subcategories in PC to keep: ${subcategoriesInPC.length}`, 'info');
    
    await createBackup('analysis', { categories, productCategories, products, mainCategoriesInPC, subcategoriesInPC });
    
    return { categories, productCategories, products, mainCategoriesInPC, subcategoriesInPC };
    
  } catch (error) {
    logProgress('ANALYZE', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// Step 2: Remove parent categories from Product Categories
async function removeParentCategories(mainCategoriesInPC) {
  logProgress('REMOVE', 'Removing parent categories from Product Categories collection', 'info');
  
  let removedCount = 0;
  const removedItems = [];
  
  for (const mainCategory of mainCategoriesInPC) {
    logProgress('REMOVE', `Removing: ${mainCategory.name}`, 'info');
    
    const result = await strapiRequest(`/product-categories/${mainCategory.documentId}`, 'DELETE');
    
    if (result.success) {
      logProgress('REMOVE', `✅ Removed: ${mainCategory.name}`, 'success');
      removedCount++;
      removedItems.push(mainCategory);
    } else {
      logProgress('REMOVE', `❌ Failed to remove: ${mainCategory.name}: ${result.error}`, 'error');
    }
    
    await new Promise(resolve => setTimeout(resolve, 300));
  }
  
  logProgress('REMOVE', `Removal completed. Removed: ${removedCount} items`, 'success');
  return { removedCount, removedItems };
}

// Step 3: Update subcategories to link to Categories collection
async function updateSubcategoryLinks(subcategoriesInPC, categories) {
  logProgress('LINK', 'Updating subcategories to link to Categories collection', 'info');
  
  // Create category lookup map
  const categoryMap = {};
  categories.forEach(cat => {
    categoryMap[cat.name] = cat;
  });
  
  let updateCount = 0;
  let errorCount = 0;
  const results = [];
  
  for (const subcat of subcategoriesInPC) {
    const parentCategoryName = subcategoryMapping[subcat.name];
    
    if (parentCategoryName && categoryMap[parentCategoryName]) {
      logProgress('LINK', `Linking: ${subcat.name} → ${parentCategoryName}`, 'info');
      
      // Update using the 'category' field to link to Categories collection
      const payload = {
        isSubcategory: true,
        category: categoryMap[parentCategoryName].documentId
      };
      
      const result = await strapiRequest(`/product-categories/${subcat.documentId}`, 'PUT', payload);
      
      if (result.success) {
        logProgress('LINK', `✅ Successfully linked: ${subcat.name}`, 'success');
        updateCount++;
        results.push({ name: subcat.name, parent: parentCategoryName, status: 'success' });
      } else {
        logProgress('LINK', `❌ Failed to link: ${subcat.name}: ${result.error}`, 'error');
        errorCount++;
        results.push({ name: subcat.name, parent: parentCategoryName, status: 'failed', error: result.error });
      }
    } else {
      logProgress('LINK', `Skipping unmapped category: ${subcat.name}`, 'warning');
      results.push({ name: subcat.name, parent: 'none', status: 'skipped' });
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  logProgress('LINK', `Linking completed. Updated: ${updateCount}, Errors: ${errorCount}`, 'success');
  return { updateCount, errorCount, results };
}

// Step 4: Link products to subcategories
async function linkProductsToSubcategories(products, subcategoriesInPC) {
  logProgress('PRODUCTS', 'Linking products to subcategories', 'info');
  
  // Create subcategory lookup map
  const subcategoryMap = {};
  subcategoriesInPC.forEach(subcat => {
    subcategoryMap[subcat.name.toLowerCase()] = subcat;
  });
  
  let linkedCount = 0;
  let errorCount = 0;
  
  for (const product of products) {
    // Try to match product to subcategory based on name
    let matchedSubcategory = null;
    
    // Simple matching logic - can be enhanced
    const productName = product.name.toLowerCase();
    
    // Check for direct matches or keywords
    for (const [subcatName, subcatData] of Object.entries(subcategoryMap)) {
      if (productName.includes(subcatName.toLowerCase()) || 
          productName.includes(subcatName.split(' ')[0].toLowerCase())) {
        matchedSubcategory = subcatData;
        break;
      }
    }
    
    // Fallback matching based on product name keywords
    if (!matchedSubcategory) {
      if (productName.includes('phone') || productName.includes('mobile')) {
        matchedSubcategory = subcategoryMap['smartphones'];
      } else if (productName.includes('laptop') || productName.includes('computer')) {
        matchedSubcategory = subcategoryMap['laptops'];
      } else if (productName.includes('shirt') || productName.includes('clothing')) {
        matchedSubcategory = subcategoryMap["men's clothing"] || subcategoryMap["women's clothing"];
      } else if (productName.includes('book')) {
        matchedSubcategory = subcategoryMap['fiction'] || subcategoryMap['non-fiction'];
      }
    }
    
    if (matchedSubcategory) {
      logProgress('PRODUCTS', `Linking product: ${product.name} → ${matchedSubcategory.name}`, 'info');
      
      const payload = {
        categories: [matchedSubcategory.documentId]
      };
      
      const result = await strapiRequest(`/products/${product.documentId}`, 'PUT', payload);
      
      if (result.success) {
        linkedCount++;
      } else {
        logProgress('PRODUCTS', `❌ Failed to link product: ${product.name}`, 'error');
        errorCount++;
      }
    } else {
      logProgress('PRODUCTS', `No subcategory match for: ${product.name}`, 'warning');
    }
    
    await new Promise(resolve => setTimeout(resolve, 300));
  }
  
  logProgress('PRODUCTS', `Product linking completed. Linked: ${linkedCount}, Errors: ${errorCount}`, 'success');
  return { linkedCount, errorCount };
}

// Step 5: Verify the restructured system
async function verifyRestructuredSystem() {
  logProgress('VERIFY', 'Verifying restructured system', 'info');
  
  try {
    // Get updated data
    const categoriesResult = await strapiRequest('/categories');
    const productCategoriesResult = await strapiRequest('/product-categories');
    const productsResult = await strapiRequest('/products');
    
    const categories = categoriesResult.data.data || [];
    const productCategories = productCategoriesResult.data.data || [];
    const products = productsResult.data.data || [];
    
    console.log('\n📊 RESTRUCTURED SYSTEM SUMMARY:');
    console.log('-' .repeat(50));
    console.log(`📁 Categories Collection: ${categories.length} items`);
    console.log(`📦 Product Categories Collection: ${productCategories.length} items (subcategories only)`);
    console.log(`🛍️ Products Collection: ${products.length} items`);
    
    // Verify subcategories
    const subcategories = productCategories.filter(pc => pc.isSubcategory);
    console.log(`🔗 Subcategories: ${subcategories.length}`);
    
    logProgress('VERIFY', 'Verification completed successfully', 'success');
    return { categories, productCategories, products, subcategories };
    
  } catch (error) {
    logProgress('VERIFY', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// Main execution function
async function runRestructuring() {
  console.log('🔄 CATEGORY SYSTEM RESTRUCTURING');
  console.log('=' .repeat(80));
  console.log('📋 Step 1: Remove parent categories from Product Categories');
  console.log('📋 Step 2: Update subcategories to link to Categories collection');
  console.log('📋 Step 3: Link products to subcategories');
  console.log('📋 Step 4: Verify restructured system');
  console.log('=' .repeat(80));
  
  const startTime = Date.now();
  
  try {
    // Test connectivity
    logProgress('INIT', 'Testing API connectivity...', 'info');
    const healthCheck = await strapiRequest('/categories');
    if (!healthCheck.success) {
      throw new Error('Cannot connect to Strapi API. Please ensure Strapi is running.');
    }
    logProgress('INIT', 'API connectivity confirmed', 'success');
    
    // Step 1: Analyze current data
    console.log('\n🔍 STEP 1: ANALYZE CURRENT DATA');
    console.log('-' .repeat(50));
    const { categories, productCategories, products, mainCategoriesInPC, subcategoriesInPC } = await analyzeCurrentData();
    
    // Step 2: Remove parent categories
    console.log('\n🗑️ STEP 2: REMOVE PARENT CATEGORIES');
    console.log('-' .repeat(50));
    const removeResult = await removeParentCategories(mainCategoriesInPC);
    
    // Step 3: Update subcategory links
    console.log('\n🔗 STEP 3: UPDATE SUBCATEGORY LINKS');
    console.log('-' .repeat(50));
    const linkResult = await updateSubcategoryLinks(subcategoriesInPC, categories);
    
    // Step 4: Link products to subcategories
    console.log('\n🛍️ STEP 4: LINK PRODUCTS TO SUBCATEGORIES');
    console.log('-' .repeat(50));
    const productLinkResult = await linkProductsToSubcategories(products, subcategoriesInPC);
    
    // Step 5: Verify restructured system
    console.log('\n✅ STEP 5: VERIFY RESTRUCTURED SYSTEM');
    console.log('-' .repeat(50));
    const verifyResult = await verifyRestructuredSystem();
    
    // Final summary
    console.log('\n✅ RESTRUCTURING COMPLETED!');
    console.log('=' .repeat(80));
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log(`📊 Results:`);
    console.log(`   • Parent categories removed: ${removeResult.removedCount}`);
    console.log(`   • Subcategories linked: ${linkResult.updateCount}`);
    console.log(`   • Products linked: ${productLinkResult.linkedCount}`);
    console.log(`   • Final subcategories count: ${verifyResult.subcategories.length}`);
    
    console.log('\n🌐 Verification URLs:');
    console.log('   • Categories: http://localhost:1337/api/categories');
    console.log('   • Product Categories: http://localhost:1337/api/product-categories');
    console.log('   • Products: http://localhost:1337/api/products');
    console.log('   • Admin Panel: http://localhost:1337/admin');
    
    console.log('\n🎉 CATEGORY SYSTEM SUCCESSFULLY RESTRUCTURED!');
    
  } catch (error) {
    console.error('\n❌ RESTRUCTURING FAILED');
    console.error('=' .repeat(80));
    console.error(`Error: ${error.message}`);
    
    process.exit(1);
  }
}

// Run the restructuring
if (require.main === module) {
  runRestructuring();
}

module.exports = { runRestructuring };
