/**
 * Final Verification of Restructured Category System
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function finalVerification() {
  console.log('🔍 FINAL VERIFICATION OF RESTRUCTURED CATEGORY SYSTEM');
  console.log('=' .repeat(70));
  
  try {
    // Test API connectivity
    console.log('\n📡 Testing API Connectivity...');
    const healthCheck = await axios.get(`${API_BASE}/categories`, { timeout: 5000 });
    console.log('✅ API is accessible');
    
    // Get all collections
    const categoriesResponse = await axios.get(`${API_BASE}/categories`);
    const productCategoriesResponse = await axios.get(`${API_BASE}/product-categories`);
    const productsResponse = await axios.get(`${API_BASE}/products`);
    
    const categories = categoriesResponse.data.data || [];
    const productCategories = productCategoriesResponse.data.data || [];
    const products = productsResponse.data.data || [];
    
    console.log('\n📊 COLLECTION SUMMARY:');
    console.log('-' .repeat(40));
    console.log(`📁 Categories Collection: ${categories.length} items`);
    console.log(`📦 Product Categories Collection: ${productCategories.length} items`);
    console.log(`🛍️ Products Collection: ${products.length} items`);
    
    // Analyze Categories Collection
    console.log('\n📁 CATEGORIES COLLECTION ANALYSIS:');
    console.log('-' .repeat(40));
    categories.forEach((cat, index) => {
      console.log(`${index + 1}. ${cat.name} (${cat.slug})`);
    });
    
    // Analyze Product Categories Collection
    console.log('\n📦 PRODUCT CATEGORIES COLLECTION ANALYSIS:');
    console.log('-' .repeat(40));
    const subcategories = productCategories.filter(pc => pc.isSubcategory);
    const nonSubcategories = productCategories.filter(pc => !pc.isSubcategory);
    
    console.log(`✅ Subcategories: ${subcategories.length}`);
    console.log(`⚠️ Non-subcategories: ${nonSubcategories.length}`);
    
    if (subcategories.length > 0) {
      console.log('\n🔗 SUBCATEGORIES LIST:');
      subcategories.forEach((subcat, index) => {
        console.log(`${index + 1}. ${subcat.name} (isSubcategory: ${subcat.isSubcategory})`);
      });
    }
    
    if (nonSubcategories.length > 0) {
      console.log('\n⚠️ NON-SUBCATEGORIES (should be reviewed):');
      nonSubcategories.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.name} (isSubcategory: ${cat.isSubcategory})`);
      });
    }
    
    // Check for expected subcategories
    const expectedSubcategories = [
      'Smartphones', 'Laptops', 'Tablets', 'Accessories',
      "Men's Clothing", "Women's Clothing", 'Footwear',
      'Furniture', 'Kitchen', 'Decor', 'Garden',
      'Fitness Equipment', 'Outdoor Sports', 'Activewear',
      'Fiction', 'Non-Fiction', 'Digital Media'
    ];
    
    console.log('\n✅ EXPECTED SUBCATEGORIES VERIFICATION:');
    console.log('-' .repeat(40));
    const foundSubcategories = [];
    const missingSubcategories = [];
    
    expectedSubcategories.forEach(expected => {
      const found = productCategories.find(pc => pc.name === expected);
      if (found) {
        foundSubcategories.push(expected);
        console.log(`✅ ${expected} - Found`);
      } else {
        missingSubcategories.push(expected);
        console.log(`❌ ${expected} - Missing`);
      }
    });
    
    // Products analysis
    console.log('\n🛍️ PRODUCTS COLLECTION ANALYSIS:');
    console.log('-' .repeat(40));
    console.log(`Total products: ${products.length}`);
    
    // Schema verification
    console.log('\n🔧 SCHEMA VERIFICATION:');
    console.log('-' .repeat(40));
    
    if (productCategories.length > 0) {
      const samplePC = productCategories[0];
      const hasIsSubcategory = 'isSubcategory' in samplePC;
      const hasCategoryType = 'category_type' in samplePC;
      const hasCategory = 'category' in samplePC;
      const hasParent = 'parent' in samplePC;
      
      console.log(`✅ isSubcategory field: ${hasIsSubcategory ? 'Present' : 'Missing'}`);
      console.log(`${hasCategoryType ? '⚠️' : '✅'} category_type field: ${hasCategoryType ? 'Present (should be removed)' : 'Removed'}`);
      console.log(`✅ category field: ${hasCategory ? 'Present' : 'Missing'}`);
      console.log(`${hasParent ? '⚠️' : '✅'} parent field: ${hasParent ? 'Present (should be removed)' : 'Removed'}`);
    }
    
    // Final assessment
    console.log('\n🎯 FINAL ASSESSMENT:');
    console.log('=' .repeat(70));
    
    const assessmentPoints = [
      {
        check: categories.length === 8,
        message: `Categories collection has 8 main categories: ${categories.length === 8 ? '✅ PASS' : '❌ FAIL'}`
      },
      {
        check: productCategories.length >= 16,
        message: `Product Categories collection has subcategories only: ${productCategories.length >= 16 ? '✅ PASS' : '❌ FAIL'}`
      },
      {
        check: subcategories.length >= 16,
        message: `Subcategories properly marked: ${subcategories.length >= 16 ? '✅ PASS' : '❌ FAIL'}`
      },
      {
        check: foundSubcategories.length >= 14,
        message: `Expected subcategories found: ${foundSubcategories.length >= 14 ? '✅ PASS' : '❌ FAIL'}`
      },
      {
        check: products.length >= 20,
        message: `Products collection maintained: ${products.length >= 20 ? '✅ PASS' : '❌ FAIL'}`
      }
    ];
    
    assessmentPoints.forEach(point => {
      console.log(point.message);
    });
    
    const allPassed = assessmentPoints.every(point => point.check);
    
    console.log('\n🏆 OVERALL RESULT:');
    console.log(`${allPassed ? '🎉 RESTRUCTURING SUCCESSFUL!' : '⚠️ SOME ISSUES DETECTED'}`);
    
    if (allPassed) {
      console.log('\n✅ The category system has been successfully restructured:');
      console.log('   • Main categories are in Categories collection');
      console.log('   • Subcategories are in Product Categories collection');
      console.log('   • Schema has been cleaned up');
      console.log('   • Data integrity maintained');
    } else {
      console.log('\n⚠️ Please review the issues above and complete any missing steps.');
    }
    
    console.log('\n🌐 API Endpoints for verification:');
    console.log(`   • Categories: ${API_BASE}/categories`);
    console.log(`   • Product Categories: ${API_BASE}/product-categories`);
    console.log(`   • Products: ${API_BASE}/products`);
    console.log(`   • Admin Panel: ${STRAPI_URL}/admin`);
    
    return {
      categories: categories.length,
      productCategories: productCategories.length,
      subcategories: subcategories.length,
      products: products.length,
      foundSubcategories: foundSubcategories.length,
      missingSubcategories: missingSubcategories.length,
      allPassed
    };
    
  } catch (error) {
    console.error('❌ VERIFICATION FAILED:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Ensure Strapi is running: npm run develop');
    console.error('2. Check API permissions');
    console.error('3. Verify database connection');
    return null;
  }
}

// Run verification
if (require.main === module) {
  finalVerification();
}

module.exports = { finalVerification };
