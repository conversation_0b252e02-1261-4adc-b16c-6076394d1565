/**
 * Master Seeding Script for Strapi CMS
 * 
 * This script runs all seeding operations in the correct order:
 * 1. Categories and subcategories
 * 2. Products with category relationships
 * 3. Banners and static pages
 */

const { seedSampleData } = require('./seed-sample-data');
const { seedProducts } = require('./seed-products');

async function seedAll() {
  console.log('🚀 Starting complete Strapi CMS data seeding...');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Seed basic content (categories, banners, pages)
    console.log('\n📋 Phase 1: Creating categories, banners, and pages...');
    await seedSampleData();
    
    // Wait a moment for data to be fully created
    console.log('\n⏳ Waiting for data to be processed...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 2: Seed products with category relationships
    console.log('\n📋 Phase 2: Creating products with category relationships...');
    await seedProducts();
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 COMPLETE! All sample data has been successfully created!');
    console.log('=' .repeat(60));
    
    console.log('\n📊 SUMMARY:');
    console.log('✅ Main Categories: 8 categories created');
    console.log('✅ Subcategories: 17 subcategories created');
    console.log('✅ Products: 25+ products across all subcategories');
    console.log('✅ Homepage Banners: 5 promotional banners');
    console.log('✅ Static Pages: 2 content pages (About, Privacy)');
    
    console.log('\n🌐 NEXT STEPS:');
    console.log('1. Visit http://localhost:1337/admin to manage content');
    console.log('2. Test the frontend at http://localhost:3000');
    console.log('3. Verify dynamic content is loading correctly');
    
    console.log('\n📝 CONTENT MANAGEMENT:');
    console.log('- Add more products through Strapi admin panel');
    console.log('- Upload product images via Media Library');
    console.log('- Customize banners and page content');
    console.log('- Configure SEO settings for pages');
    
  } catch (error) {
    console.error('\n❌ SEEDING FAILED:', error.message);
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('1. Ensure Strapi CMS is running on http://localhost:1337');
    console.log('2. Check that all content types are properly configured');
    console.log('3. Verify database connection is working');
    console.log('4. Try running individual seeding scripts separately');
    process.exit(1);
  }
}

// Run the complete seeding process
if (require.main === module) {
  seedAll();
}

module.exports = { seedAll };
