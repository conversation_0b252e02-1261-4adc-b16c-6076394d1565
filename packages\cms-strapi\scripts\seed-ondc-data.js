'use strict';

/**
 * ONDC Seller Platform Data Seeding Script
 * Creates sample e-commerce data for categories, banners, pages, and products
 */

// Sample data for ONDC platform
const ondcData = {
  categories: [
    // Main Categories
    { name: 'Electronics', slug: 'electronics', description: 'Latest gadgets, devices, and electronic accessories', isSubcategory: false },
    { name: 'Fashion & Apparel', slug: 'fashion-apparel', description: 'Trendy clothing, shoes, and fashion accessories', isSubcategory: false },
    { name: 'Home & Garden', slug: 'home-garden', description: 'Furniture, decor, and garden essentials for your home', isSubcategory: false },
    { name: 'Health & Beauty', slug: 'health-beauty', description: 'Skincare, cosmetics, and wellness products', isSubcategory: false },
    { name: 'Sports & Outdoors', slug: 'sports-outdoors', description: 'Athletic gear, outdoor equipment, and fitness accessories', isSubcategory: false },
    { name: 'Books & Media', slug: 'books-media', description: 'Books, magazines, and digital media content', isSubcategory: false },
  ],
  
  subcategories: [
    // Electronics subcategories
    { name: 'Smartphones', slug: 'smartphones', description: 'Latest smartphones and mobile devices', isSubcategory: true, parentSlug: 'electronics' },
    { name: 'Laptops', slug: 'laptops', description: 'Laptops and notebooks for work and gaming', isSubcategory: true, parentSlug: 'electronics' },
    { name: 'Audio', slug: 'audio', description: 'Headphones, speakers, and audio equipment', isSubcategory: true, parentSlug: 'electronics' },
    
    // Fashion subcategories
    { name: "Men's Clothing", slug: 'mens-clothing', description: 'Shirts, pants, suits, and casual wear for men', isSubcategory: true, parentSlug: 'fashion-apparel' },
    { name: "Women's Clothing", slug: 'womens-clothing', description: 'Dresses, tops, bottoms, and formal wear for women', isSubcategory: true, parentSlug: 'fashion-apparel' },
    { name: 'Shoes & Footwear', slug: 'shoes-footwear', description: 'Sneakers, boots, sandals, and formal shoes', isSubcategory: true, parentSlug: 'fashion-apparel' },
    
    // Home & Garden subcategories
    { name: 'Furniture', slug: 'furniture', description: 'Sofas, chairs, tables, and bedroom furniture', isSubcategory: true, parentSlug: 'home-garden' },
    { name: 'Home Decor', slug: 'home-decor', description: 'Wall art, lighting, rugs, and decorative items', isSubcategory: true, parentSlug: 'home-garden' },
  ],
  
  banners: [
    {
      title: 'Welcome to ONDC Seller Platform',
      subtitle: 'Start Your Journey',
      description: "Join India's Open Network for Digital Commerce and reach millions of customers",
      buttonText: 'Get Started',
      buttonLink: '/dashboard',
      backgroundColor: 'bg-gradient-to-r from-blue-600 to-purple-600',
      active: true,
      position: 1,
    },
    {
      title: 'Boost Your Sales',
      subtitle: 'Expand Your Reach',
      description: 'Connect with customers across India through our comprehensive seller platform',
      buttonText: 'View Products',
      buttonLink: '/products',
      backgroundColor: 'bg-gradient-to-r from-green-500 to-teal-600',
      active: true,
      position: 2,
    },
    {
      title: 'Premium Quality Products',
      subtitle: 'Trusted by Millions',
      description: 'Discover high-quality products from verified sellers across India',
      buttonText: 'Shop Now',
      buttonLink: '/categories',
      backgroundColor: 'bg-gradient-to-r from-purple-500 to-pink-600',
      active: true,
      position: 3,
    },
  ],
  
  pages: [
    {
      title: 'About Us',
      slug: 'about-us',
      content: 'Welcome to ONDC Seller Platform. We connect buyers and sellers across India through the Open Network for Digital Commerce. Our mission is to democratize digital commerce and empower every seller to reach customers nationwide.',
      excerpt: 'Learn about ONDC Seller Platform and our mission to democratize digital commerce in India.',
      status: 'published',
    },
    {
      title: 'Privacy Policy',
      slug: 'privacy-policy',
      content: 'Our privacy policy explains how we collect, use, and protect your personal information on the ONDC Seller Platform. We are committed to maintaining the privacy and security of your data.',
      excerpt: 'Read our privacy policy to understand how we handle your data.',
      status: 'published',
    },
    {
      title: 'Terms of Service',
      slug: 'terms-of-service',
      content: 'Terms and conditions for using the ONDC Seller Platform. By using our platform, you agree to these terms and conditions.',
      excerpt: 'Review our terms of service for platform usage guidelines.',
      status: 'published',
    },
  ],
  
  products: [
    // Electronics products
    { name: 'Premium Wireless Headphones', description: 'High-quality wireless headphones with noise cancellation', price: 299.99, categorySlug: 'electronics' },
    { name: 'Smart Fitness Watch', description: 'Advanced fitness tracking with heart rate monitor', price: 199.99, categorySlug: 'electronics' },
    { name: 'Wireless Charging Pad', description: 'Fast wireless charging for compatible devices', price: 49.99, categorySlug: 'electronics' },
    
    // Fashion products
    { name: 'Organic Cotton T-Shirt', description: 'Comfortable organic cotton t-shirt in various colors', price: 29.99, categorySlug: 'fashion-apparel' },
    { name: 'Designer Denim Jeans', description: 'Premium denim jeans with modern fit', price: 89.99, categorySlug: 'fashion-apparel' },
    { name: 'Running Sneakers', description: 'Lightweight running shoes with cushioned sole', price: 119.99, categorySlug: 'fashion-apparel' },
    
    // Home & Garden products
    { name: 'Premium Coffee Maker', description: 'Professional-grade coffee maker with programmable features', price: 199.99, categorySlug: 'home-garden' },
    { name: 'Ergonomic Office Chair', description: 'Comfortable office chair with lumbar support', price: 299.99, categorySlug: 'home-garden' },
    { name: 'LED Desk Lamp', description: 'Adjustable LED desk lamp with multiple brightness levels', price: 49.99, categorySlug: 'home-garden' },
  ],
};

async function seedONDCData() {
  const shouldImportSeedData = await isFirstRun();

  if (shouldImportSeedData) {
    try {
      console.log('🚀 Setting up ONDC Seller Platform data...');
      await importONDCData();
      console.log('✅ ONDC data seeding completed successfully!');
    } catch (error) {
      console.log('❌ Could not import ONDC seed data');
      console.error(error);
    }
  } else {
    console.log('⚠️ Seed data has already been imported. Clear your database to reimport.');
  }
}

async function isFirstRun() {
  const pluginStore = strapi.store({
    environment: strapi.config.environment,
    type: 'type',
    name: 'ondc-setup',
  });
  const initHasRun = await pluginStore.get({ key: 'ondcInitHasRun' });
  await pluginStore.set({ key: 'ondcInitHasRun', value: true });
  return !initHasRun;
}

async function setPublicPermissions(newPermissions) {
  // Find the ID of the public role
  const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
    where: { type: 'public' },
  });

  // Create the new permissions and link them to the public role
  const allPermissionsToCreate = [];
  Object.keys(newPermissions).map((controller) => {
    const actions = newPermissions[controller];
    const permissionsToCreate = actions.map((action) => {
      return strapi.query('plugin::users-permissions.permission').create({
        data: {
          action: `api::${controller}.${controller}.${action}`,
          role: publicRole.id,
        },
      });
    });
    allPermissionsToCreate.push(...permissionsToCreate);
  });
  await Promise.all(allPermissionsToCreate);
}

// Create an entry
async function createEntry({ model, entry }) {
  try {
    const created = await strapi.documents(`api::${model}.${model}`).create({
      data: { ...entry, publishedAt: Date.now() },
    });
    console.log(`✅ Created ${model}: ${entry.name || entry.title}`);
    return created;
  } catch (error) {
    console.error(`❌ Error creating ${model}:`, error.message);
    return null;
  }
}

async function importCategories() {
  console.log('📂 Creating categories...');
  const createdCategories = new Map();
  
  // Create main categories first
  for (const category of ondcData.categories) {
    const created = await createEntry({ model: 'product-category', entry: category });
    if (created) {
      createdCategories.set(category.slug, created);
    }
  }
  
  // Create subcategories with parent references
  for (const subcategory of ondcData.subcategories) {
    const parent = createdCategories.get(subcategory.parentSlug);
    if (parent) {
      const subcategoryData = { ...subcategory, parent: parent.id };
      delete subcategoryData.parentSlug;
      await createEntry({ model: 'product-category', entry: subcategoryData });
    }
  }
  
  return createdCategories;
}

async function importBanners() {
  console.log('🎨 Creating banners...');
  for (const banner of ondcData.banners) {
    await createEntry({ model: 'banner', entry: banner });
  }
}

async function importPages() {
  console.log('📄 Creating pages...');
  for (const page of ondcData.pages) {
    await createEntry({ model: 'page', entry: page });
  }
}

async function importProducts(categories) {
  console.log('🛍️ Creating products...');
  for (const product of ondcData.products) {
    const category = categories.get(product.categorySlug);
    if (category) {
      const productData = { ...product, categories: [category.id] };
      delete productData.categorySlug;
      await createEntry({ model: 'product', entry: productData });
    }
  }
}

async function importONDCData() {
  // Set public permissions
  await setPublicPermissions({
    'product-category': ['find', 'findOne'],
    'product': ['find', 'findOne'],
    'banner': ['find', 'findOne'],
    'page': ['find', 'findOne'],
  });

  // Create all entries
  const categories = await importCategories();
  await importBanners();
  await importPages();
  await importProducts(categories);
}

async function main() {
  const { createStrapi, compileStrapi } = require('@strapi/strapi');

  const appContext = await compileStrapi();
  const app = await createStrapi(appContext).load();

  app.log.level = 'error';

  await seedONDCData();
  await app.destroy();

  process.exit(0);
}

main().catch((error) => {
  console.error('❌ Seeding failed:', error);
  process.exit(1);
});
