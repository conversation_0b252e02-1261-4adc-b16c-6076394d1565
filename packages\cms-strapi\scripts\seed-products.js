/**
 * Strapi CMS Product Data Seeding Script
 * 
 * This script populates the Strapi CMS with comprehensive product data
 * across all categories and subcategories.
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Helper function to make API requests
async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      config.data = { data };
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error with ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Sample products data organized by subcategory
const PRODUCTS_BY_SUBCATEGORY = {
  'smartphones': [
    {
      name: 'iPhone 15 Pro',
      short_description: 'Latest iPhone with A17 Pro chip and titanium design',
      description: 'The iPhone 15 Pro features the powerful A17 Pro chip, a customizable Action Button, and a more versatile Pro camera system. Built with titanium for durability and lightness.',
      price: 134900,
      sale_price: 129900,
      sku: 'IPHONE-15-PRO-128',
      inventory_quantity: 25,
      product_status: 'Published',
      featured: true,
      tags: 'iPhone, Apple, Smartphone, Premium',
      weight: 0.187,
    },
    {
      name: 'Samsung Galaxy S24 Ultra',
      short_description: 'Flagship Android phone with S Pen and AI features',
      description: 'Samsung Galaxy S24 Ultra with built-in S Pen, advanced AI photography, and stunning 6.8-inch Dynamic AMOLED display.',
      price: 129999,
      sale_price: 124999,
      sku: 'SAMSUNG-S24-ULTRA-256',
      inventory_quantity: 30,
      product_status: 'Published',
      featured: true,
      tags: 'Samsung, Android, Smartphone, S Pen',
      weight: 0.232,
    },
    {
      name: 'OnePlus 12',
      short_description: 'Flagship killer with Snapdragon 8 Gen 3',
      description: 'OnePlus 12 delivers flagship performance with Snapdragon 8 Gen 3, 120Hz display, and ultra-fast charging.',
      price: 64999,
      sale_price: 59999,
      sku: 'ONEPLUS-12-256',
      inventory_quantity: 40,
      product_status: 'Published',
      featured: false,
      tags: 'OnePlus, Android, Flagship, Fast Charging',
      weight: 0.220,
    },
    {
      name: 'Google Pixel 8',
      short_description: 'AI-powered photography and pure Android experience',
      description: 'Google Pixel 8 with advanced AI photography, Magic Eraser, and the latest Android updates directly from Google.',
      price: 75999,
      sale_price: 69999,
      sku: 'PIXEL-8-128',
      inventory_quantity: 35,
      product_status: 'Published',
      featured: false,
      tags: 'Google, Pixel, AI Camera, Android',
      weight: 0.197,
    },
    {
      name: 'Xiaomi 14',
      short_description: 'Premium smartphone with Leica cameras',
      description: 'Xiaomi 14 features Leica-tuned cameras, Snapdragon 8 Gen 3, and premium build quality at an affordable price.',
      price: 69999,
      sale_price: 64999,
      sku: 'XIAOMI-14-256',
      inventory_quantity: 50,
      product_status: 'Published',
      featured: false,
      tags: 'Xiaomi, Leica, Camera, Value',
      weight: 0.193,
    },
  ],
  'laptops': [
    {
      name: 'MacBook Air M3',
      short_description: '13-inch laptop with M3 chip and all-day battery',
      description: 'MacBook Air with M3 chip delivers incredible performance and up to 18 hours of battery life in a remarkably thin and light design.',
      price: 114900,
      sale_price: 109900,
      sku: 'MACBOOK-AIR-M3-256',
      inventory_quantity: 20,
      product_status: 'Published',
      featured: true,
      tags: 'Apple, MacBook, M3, Laptop',
      weight: 1.24,
    },
    {
      name: 'Dell XPS 13',
      short_description: 'Premium ultrabook with Intel Core i7',
      description: 'Dell XPS 13 with 13th Gen Intel Core i7, stunning InfinityEdge display, and premium carbon fiber build.',
      price: 89999,
      sale_price: 84999,
      sku: 'DELL-XPS13-I7-512',
      inventory_quantity: 15,
      product_status: 'Published',
      featured: true,
      tags: 'Dell, XPS, Intel, Ultrabook',
      weight: 1.27,
    },
    {
      name: 'HP Spectre x360',
      short_description: '2-in-1 convertible laptop with touch display',
      description: 'HP Spectre x360 convertible laptop with 360-degree hinge, touch display, and premium design for work and creativity.',
      price: 94999,
      sale_price: 89999,
      sku: 'HP-SPECTRE-X360-512',
      inventory_quantity: 18,
      product_status: 'Published',
      featured: false,
      tags: 'HP, Spectre, 2-in-1, Convertible',
      weight: 1.34,
    },
    {
      name: 'ASUS ROG Strix G15',
      short_description: 'Gaming laptop with RTX 4060 and RGB lighting',
      description: 'ASUS ROG Strix G15 gaming laptop with NVIDIA RTX 4060, AMD Ryzen 7, and customizable RGB lighting.',
      price: 79999,
      sale_price: 74999,
      sku: 'ASUS-ROG-G15-RTX4060',
      inventory_quantity: 12,
      product_status: 'Published',
      featured: false,
      tags: 'ASUS, ROG, Gaming, RTX',
      weight: 2.30,
    },
    {
      name: 'Lenovo ThinkPad X1 Carbon',
      short_description: 'Business laptop with military-grade durability',
      description: 'Lenovo ThinkPad X1 Carbon with military-grade durability, excellent keyboard, and long battery life for professionals.',
      price: 124999,
      sale_price: 119999,
      sku: 'LENOVO-X1-CARBON-512',
      inventory_quantity: 10,
      product_status: 'Published',
      featured: false,
      tags: 'Lenovo, ThinkPad, Business, Durable',
      weight: 1.12,
    },
  ],
  'mens-clothing': [
    {
      name: 'Cotton Casual Shirt',
      short_description: 'Comfortable cotton shirt for everyday wear',
      description: 'Premium quality cotton casual shirt with modern fit, perfect for office or casual outings. Available in multiple colors.',
      price: 1299,
      sale_price: 999,
      sku: 'SHIRT-COTTON-M-BLUE',
      inventory_quantity: 100,
      product_status: 'Published',
      featured: true,
      tags: 'Shirt, Cotton, Casual, Men',
      weight: 0.25,
    },
    {
      name: 'Formal Blazer',
      short_description: 'Elegant blazer for formal occasions',
      description: 'Sophisticated formal blazer crafted from premium fabric, perfect for business meetings and formal events.',
      price: 3999,
      sale_price: 3499,
      sku: 'BLAZER-FORMAL-M-BLACK',
      inventory_quantity: 50,
      product_status: 'Published',
      featured: true,
      tags: 'Blazer, Formal, Business, Men',
      weight: 0.8,
    },
    {
      name: 'Denim Jeans',
      short_description: 'Classic blue denim jeans with perfect fit',
      description: 'High-quality denim jeans with classic blue wash, comfortable fit, and durable construction for everyday wear.',
      price: 2499,
      sale_price: 1999,
      sku: 'JEANS-DENIM-M-BLUE-32',
      inventory_quantity: 75,
      product_status: 'Published',
      featured: false,
      tags: 'Jeans, Denim, Casual, Men',
      weight: 0.6,
    },
    {
      name: 'Polo T-Shirt',
      short_description: 'Classic polo shirt in premium cotton',
      description: 'Timeless polo shirt made from premium cotton with ribbed collar and cuffs, perfect for smart casual look.',
      price: 899,
      sale_price: 699,
      sku: 'POLO-COTTON-M-WHITE',
      inventory_quantity: 120,
      product_status: 'Published',
      featured: false,
      tags: 'Polo, T-Shirt, Cotton, Casual',
      weight: 0.2,
    },
    {
      name: 'Leather Jacket',
      short_description: 'Genuine leather jacket with modern styling',
      description: 'Premium genuine leather jacket with modern cut, perfect for adding edge to your style. Fully lined with quality hardware.',
      price: 8999,
      sale_price: 7999,
      sku: 'JACKET-LEATHER-M-BLACK',
      inventory_quantity: 25,
      product_status: 'Published',
      featured: true,
      tags: 'Jacket, Leather, Premium, Style',
      weight: 1.2,
    },
  ],
  'furniture': [
    {
      name: 'Modern Sofa Set',
      short_description: '3-seater sofa with premium upholstery',
      description: 'Elegant 3-seater sofa set with premium fabric upholstery, solid wood frame, and comfortable cushioning perfect for living room.',
      price: 45999,
      sale_price: 39999,
      sku: 'SOFA-3SEAT-GREY',
      inventory_quantity: 8,
      product_status: 'Published',
      featured: true,
      tags: 'Sofa, Furniture, Living Room, Comfort',
      weight: 85,
    },
    {
      name: 'Dining Table Set',
      short_description: '6-seater wooden dining table with chairs',
      description: 'Beautiful 6-seater dining table set made from solid wood with matching chairs, perfect for family dining.',
      price: 32999,
      sale_price: 28999,
      sku: 'DINING-6SEAT-WOOD',
      inventory_quantity: 5,
      product_status: 'Published',
      featured: true,
      tags: 'Dining Table, Wood, Furniture, Family',
      weight: 120,
    },
    {
      name: 'Office Chair',
      short_description: 'Ergonomic office chair with lumbar support',
      description: 'Professional ergonomic office chair with adjustable height, lumbar support, and breathable mesh back for all-day comfort.',
      price: 8999,
      sale_price: 7499,
      sku: 'CHAIR-OFFICE-ERGONOMIC',
      inventory_quantity: 30,
      product_status: 'Published',
      featured: false,
      tags: 'Chair, Office, Ergonomic, Comfort',
      weight: 15,
    },
    {
      name: 'Bookshelf',
      short_description: '5-tier wooden bookshelf for storage',
      description: 'Spacious 5-tier wooden bookshelf perfect for books, decorative items, and storage. Easy to assemble.',
      price: 4999,
      sale_price: 4299,
      sku: 'BOOKSHELF-5TIER-WOOD',
      inventory_quantity: 20,
      product_status: 'Published',
      featured: false,
      tags: 'Bookshelf, Storage, Wood, Organization',
      weight: 25,
    },
    {
      name: 'Coffee Table',
      short_description: 'Glass-top coffee table with metal frame',
      description: 'Modern glass-top coffee table with sleek metal frame, perfect centerpiece for your living room.',
      price: 6999,
      sale_price: 5999,
      sku: 'TABLE-COFFEE-GLASS',
      inventory_quantity: 15,
      product_status: 'Published',
      featured: false,
      tags: 'Coffee Table, Glass, Modern, Living Room',
      weight: 18,
    },
  ],
  'fitness-equipment': [
    {
      name: 'Adjustable Dumbbells',
      short_description: 'Space-saving adjustable dumbbells 5-50 lbs',
      description: 'Professional adjustable dumbbells that replace 15 sets of weights. Quick weight adjustment from 5 to 50 lbs per dumbbell.',
      price: 24999,
      sale_price: 21999,
      sku: 'DUMBBELL-ADJ-50LB',
      inventory_quantity: 12,
      product_status: 'Published',
      featured: true,
      tags: 'Dumbbells, Fitness, Home Gym, Adjustable',
      weight: 25,
    },
    {
      name: 'Yoga Mat',
      short_description: 'Premium non-slip yoga mat with alignment lines',
      description: 'High-quality yoga mat with superior grip, alignment lines, and eco-friendly materials. Perfect for yoga, pilates, and stretching.',
      price: 1999,
      sale_price: 1499,
      sku: 'YOGA-MAT-PREMIUM',
      inventory_quantity: 50,
      product_status: 'Published',
      featured: true,
      tags: 'Yoga, Mat, Fitness, Exercise',
      weight: 1.2,
    },
    {
      name: 'Resistance Bands Set',
      short_description: 'Complete resistance bands set with accessories',
      description: 'Complete resistance bands set with 5 resistance levels, door anchor, handles, and ankle straps for full-body workouts.',
      price: 2499,
      sale_price: 1999,
      sku: 'RESISTANCE-BANDS-SET',
      inventory_quantity: 40,
      product_status: 'Published',
      featured: false,
      tags: 'Resistance Bands, Fitness, Portable, Workout',
      weight: 1.5,
    },
    {
      name: 'Treadmill',
      short_description: 'Electric treadmill with incline and programs',
      description: 'Professional electric treadmill with multiple workout programs, incline adjustment, and heart rate monitoring.',
      price: 89999,
      sale_price: 79999,
      sku: 'TREADMILL-ELECTRIC-PRO',
      inventory_quantity: 3,
      product_status: 'Published',
      featured: true,
      tags: 'Treadmill, Cardio, Home Gym, Electric',
      weight: 85,
    },
    {
      name: 'Exercise Bike',
      short_description: 'Stationary exercise bike with digital display',
      description: 'Comfortable stationary exercise bike with adjustable seat, digital display, and multiple resistance levels.',
      price: 19999,
      sale_price: 17999,
      sku: 'BIKE-EXERCISE-DIGITAL',
      inventory_quantity: 8,
      product_status: 'Published',
      featured: false,
      tags: 'Exercise Bike, Cardio, Fitness, Stationary',
      weight: 35,
    },
  ],
};

// Main product seeding function
// async function seedProducts() {
//   console.log('🛍️ Starting product data seeding...');
  
//   try {
//     // First, get all categories (both main and sub)
//     const categoriesResponse = await strapiRequest('/product-categories');
//     const allCategories = categoriesResponse.data || [];

//     console.log(`📊 Found ${allCategories.length} categories in database`);

//     // Create a mapping of subcategory slug to parent category
//     const subcategoryToParent = {};

//     // Map subcategories to their parent categories
//     for (const category of allCategories) {

//       const categorySlug = category.attributes?.slug || category.slug;
//       const isSubcategory = category.attributes?.isSubcategory || category.isSubcategory;
//       const parentId = category.attributes?.parent?.data?.id || category.parent;
//       console.log("category::::::",category.isSubcategory?category:'parent')
//       if (isSubcategory && parentId) {
//         const parentCategory = allCategories.find(cat => cat.id === parentId);
//         if (parentCategory) {
//           subcategoryToParent[categorySlug] = {
//             subcategory: category,
//             parentCategory: parentCategory
//           };
//         }
//       }
//     }

//     let totalProductsCreated = 0;

//     for (const [subcategorySlug, products] of Object.entries(PRODUCTS_BY_SUBCATEGORY)) {
//       console.log(`\n📦 Creating products for subcategory: ${subcategorySlug}`);

//       // Find the subcategory and its parent category
//       const categoryMapping = subcategoryToParent[subcategorySlug];
//       console.log({categoryMapping,subcategorySlug,subcategoryToParent});
      
//       if (!categoryMapping) {
//         console.log(`⚠️ Subcategory ${subcategorySlug} not found, skipping products`);
//         continue;
//       }

//       const { subcategory, parentCategory } = categoryMapping;

//       for (const product of products) {
//         try {
//           // Add category relationships to product
//           const productData = {
//             ...product,
//             categories: [subcategory.id, parentCategory.id], // Link to both subcategory and parent
//           };

//           const result = await strapiRequest('/products', 'POST', productData);
//           console.log(`✅ Created product: ${product.name}`);
//           totalProductsCreated++;
//         } catch (error) {
//           console.log(`⚠️ Product ${product.name} might already exist`);
//         }
//       }
//     }
    
//     console.log(`\n🎉 Product seeding completed!`);
//     console.log(`📊 Total products created: ${totalProductsCreated}`);
//     console.log(`📂 Products distributed across ${Object.keys(PRODUCTS_BY_SUBCATEGORY).length} subcategories`);
    
//   } catch (error) {
//     console.error('❌ Error during product seeding:', error);
//     process.exit(1);
//   }
// }

// Add this at the top of seedProducts function
async function seedProducts() {
  try {
    console.log('🔄 Fetching categories from Strapi...');
    
    // Get all categories and subcategories
    const allCategoriesResponse = await strapiRequest('/categories?populate=*');
    const allCategories = allCategoriesResponse.data;

    const subcategoryToParent = {};

    // Map subcategories to their parent categories
    for (const category of allCategories) {
      const categorySlug = category.attributes.slug;
      const isSubcategory = category.attributes.isSubcategory;
      const parentId = category.attributes.parent?.data?.id;

      if (isSubcategory && parentId) {
        const parentCategory = allCategories.find(cat => cat.id === parentId);
        subcategoryToParent[categorySlug] = {
          id: category.id,
          parentId: parentCategory?.id || null,
          parentSlug: parentCategory?.attributes?.slug || null
        };
      }
    }

    let totalCreated = 0;
    let totalSkipped = 0;

    for (const [subcategorySlug, products] of Object.entries(PRODUCTS_BY_SUBCATEGORY)) {
      console.log(`\n📦 Seeding products under subcategory: ${subcategorySlug}`);

      const relation = subcategoryToParent[subcategorySlug];

      for (const productData of products) {
        try {
          const payload = {
            ...productData,
          };

          // Only assign category if found
          if (relation?.id) {
            payload.category = relation.id;
            payload.main_category = relation.parentId;
          }

          await strapiRequest('/products', 'POST', payload);
          console.log(`✅ Created: ${productData.name} ${relation ? '[with category]' : '[no category]'}`);
          totalCreated++;
        } catch (err) {
          console.error(`❌ Failed to create ${productData.name}: ${err.response?.data?.error?.message || err.message}`);
          totalSkipped++;
        }
      }
    }

    console.log(`\n🎉 Seeding completed.`);
    console.log(`✅ Products created: ${totalCreated}`);
    console.log(`⚠️ Skipped or failed: ${totalSkipped}`);
  } catch (error) {
    console.error('❌ Fatal error during seeding:', error.message);
    throw error;
  }
}


// Run the seeding script
if (require.main === module) {
  seedProducts();
}

module.exports = { seedProducts };
