/**
 * Strapi CMS Sample Data Seeding Script
 * 
 * This script populates the Strapi CMS with comprehensive sample data
 * including categories, subcategories, products, banners, and static pages.
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Helper function to make API requests
async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      config.data = { data };
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error with ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Sample data definitions
const MAIN_CATEGORIES = [
  {
    name: 'Electronics',
    slug: 'electronics',
    description: 'Latest gadgets, smartphones, laptops, and electronic accessories for modern living.',
    short_description: 'Tech gadgets and electronic devices',
    active: true,
    sort_order: 1,
    meta_title: 'Electronics - Latest Gadgets & Tech Devices',
    meta_description: 'Shop the latest electronics including smartphones, laptops, and accessories with fast delivery.',
  },
  {
    name: 'Fashion',
    slug: 'fashion',
    description: 'Trendy clothing, footwear, and accessories for men, women, and children.',
    short_description: 'Trendy clothing and accessories',
    active: true,
    sort_order: 2,
    meta_title: 'Fashion - Trendy Clothing & Accessories',
    meta_description: 'Discover the latest fashion trends with clothing, footwear, and accessories for all.',
  },
  {
    name: 'Home & Garden',
    slug: 'home-garden',
    description: 'Furniture, home decor, kitchen essentials, and gardening supplies.',
    short_description: 'Furniture and home essentials',
    active: true,
    sort_order: 3,
    meta_title: 'Home & Garden - Furniture & Decor',
    meta_description: 'Transform your home with quality furniture, decor, and gardening supplies.',
  },
  {
    name: 'Sports & Fitness',
    slug: 'sports-fitness',
    description: 'Athletic gear, fitness equipment, and sports accessories for active lifestyles.',
    short_description: 'Sports gear and fitness equipment',
    active: true,
    sort_order: 4,
    meta_title: 'Sports & Fitness - Athletic Gear & Equipment',
    meta_description: 'Get fit with our range of sports equipment, fitness gear, and athletic accessories.',
  },
  {
    name: 'Books & Media',
    slug: 'books-media',
    description: 'Books, e-books, audiobooks, movies, and educational materials.',
    short_description: 'Books and digital media',
    active: true,
    sort_order: 5,
    meta_title: 'Books & Media - Educational & Entertainment Content',
    meta_description: 'Explore our collection of books, e-books, audiobooks, and educational materials.',
  },
  {
    name: 'Health & Beauty',
    slug: 'health-beauty',
    description: 'Personal care products, cosmetics, health supplements, and wellness items.',
    short_description: 'Health and beauty products',
    active: true,
    sort_order: 6,
    meta_title: 'Health & Beauty - Personal Care & Wellness',
    meta_description: 'Discover health supplements, beauty products, and personal care essentials.',
  },
  {
    name: 'Automotive',
    slug: 'automotive',
    description: 'Car accessories, motorcycle parts, tools, and automotive maintenance products.',
    short_description: 'Auto parts and accessories',
    active: true,
    sort_order: 7,
    meta_title: 'Automotive - Car Parts & Accessories',
    meta_description: 'Find quality automotive parts, accessories, and maintenance products.',
  },
  {
    name: 'Toys & Games',
    slug: 'toys-games',
    description: 'Educational toys, board games, video games, and entertainment for all ages.',
    short_description: 'Toys and games for all ages',
    active: true,
    sort_order: 8,
    meta_title: 'Toys & Games - Fun & Educational Entertainment',
    meta_description: 'Discover toys, games, and educational entertainment for children and adults.',
  },
];

const SUBCATEGORIES = [
  // Electronics subcategories
  {
    name: 'Smartphones',
    slug: 'smartphones',
    main_category_slug: 'electronics',
    description: 'Latest smartphones from top brands with advanced features.',
    short_description: 'Latest smartphones and mobile devices',
    isSubcategory: true,
    active: true,
    sort_order: 1,
  },
  {
    name: 'Laptops',
    slug: 'laptops',
    main_category_slug: 'electronics',
    description: 'High-performance laptops for work, gaming, and everyday use.',
    short_description: 'Laptops and notebooks',
    isSubcategory: true,
    active: true,
    sort_order: 2,
  },
  {
    name: 'Electronics Accessories',
    slug: 'electronics-accessories',
    main_category_slug: 'electronics',
    description: 'Chargers, cases, headphones, and other electronic accessories.',
    short_description: 'Electronic accessories and peripherals',
    isSubcategory: true,
    active: true,
    sort_order: 3,
  },
  {
    name: 'Tablets',
    slug: 'tablets',
    main_category_slug: 'electronics',
    description: 'Tablets and e-readers for entertainment and productivity.',
    short_description: 'Tablets and e-readers',
    isSubcategory: true,
    active: true,
    sort_order: 4,
  },

  // Fashion subcategories
  {
    name: "Men's Clothing",
    slug: 'mens-clothing',
    main_category_slug: 'fashion',
    description: 'Stylish clothing for men including shirts, pants, and formal wear.',
    short_description: 'Men\'s fashion and clothing',
    isSubcategory: true,
    active: true,
    sort_order: 1,
  },
  {
    name: "Women's Clothing",
    slug: 'womens-clothing',
    main_category_slug: 'fashion',
    description: 'Trendy fashion for women including dresses, tops, and ethnic wear.',
    short_description: 'Women\'s fashion and clothing',
    isSubcategory: true,
    active: true,
    sort_order: 2,
  },
  {
    name: 'Footwear',
    slug: 'footwear',
    main_category_slug: 'fashion',
    description: 'Comfortable and stylish shoes for all occasions.',
    short_description: 'Shoes and footwear',
    isSubcategory: true,
    active: true,
    sort_order: 3,
  },
  {
    name: 'Fashion Accessories',
    slug: 'fashion-accessories',
    main_category_slug: 'fashion',
    description: 'Bags, jewelry, watches, and fashion accessories.',
    short_description: 'Fashion accessories and jewelry',
    isSubcategory: true,
    active: true,
    sort_order: 4,
  },

  // Home & Garden subcategories
  {
    name: 'Furniture',
    slug: 'furniture',
    main_category_slug: 'home-garden',
    description: 'Quality furniture for living room, bedroom, and office spaces.',
    short_description: 'Home and office furniture',
    isSubcategory: true,
    active: true,
    sort_order: 1,
  },
  {
    name: 'Kitchen',
    slug: 'kitchen',
    main_category_slug: 'home-garden',
    description: 'Kitchen appliances, cookware, and dining essentials.',
    short_description: 'Kitchen appliances and cookware',
    isSubcategory: true,
    active: true,
    sort_order: 2,
  },
  {
    name: 'Home Decor',
    slug: 'home-decor',
    main_category_slug: 'home-garden',
    description: 'Home decoration items, lighting, and interior design accessories.',
    short_description: 'Home decoration and lighting',
    isSubcategory: true,
    active: true,
    sort_order: 3,
  },
  {
    name: 'Garden',
    slug: 'garden',
    main_category_slug: 'home-garden',
    description: 'Gardening tools, plants, seeds, and outdoor equipment.',
    short_description: 'Gardening tools and supplies',
    isSubcategory: true,
    active: true,
    sort_order: 4,
  },

  // Sports & Fitness subcategories
  {
    name: 'Fitness Equipment',
    slug: 'fitness-equipment',
    main_category_slug: 'sports-fitness',
    description: 'Home gym equipment, weights, and fitness accessories.',
    short_description: 'Gym and fitness equipment',
    isSubcategory: true,
    active: true,
    sort_order: 1,
  },
  {
    name: 'Outdoor Sports',
    slug: 'outdoor-sports',
    main_category_slug: 'sports-fitness',
    description: 'Equipment for cricket, football, tennis, and outdoor activities.',
    short_description: 'Outdoor sports equipment',
    isSubcategory: true,
    active: true,
    sort_order: 2,
  },
  {
    name: 'Activewear',
    slug: 'activewear',
    main_category_slug: 'sports-fitness',
    description: 'Sports clothing, shoes, and athletic apparel.',
    short_description: 'Sports clothing and apparel',
    isSubcategory: true,
    active: true,
    sort_order: 3,
  },

  // Books & Media subcategories
  {
    name: 'Fiction',
    slug: 'fiction',
    main_category_slug: 'books-media',
    description: 'Novels, short stories, and fictional literature.',
    short_description: 'Fiction books and novels',
    isSubcategory: true,
    active: true,
    sort_order: 1,
  },
  {
    name: 'Non-Fiction',
    slug: 'non-fiction',
    main_category_slug: 'books-media',
    description: 'Educational books, biographies, and informational content.',
    short_description: 'Non-fiction and educational books',
    isSubcategory: true,
    active: true,
    sort_order: 2,
  },
  {
    name: 'Digital Media',
    slug: 'digital-media',
    main_category_slug: 'books-media',
    description: 'E-books, audiobooks, and digital content.',
    short_description: 'Digital books and media',
    isSubcategory: true,
    active: true,
    sort_order: 3,
  },
];

const BANNERS = [
  {
    title: 'Welcome to ONDC Marketplace',
    subtitle: 'Discover Amazing Products',
    description: 'Shop from thousands of verified sellers across India with guaranteed quality and fast delivery.',
    buttonText: 'Shop Now',
    buttonLink: '/categories',
    backgroundColor: 'bg-gradient-to-r from-blue-600 to-purple-600',
    active: true,
    position: 1,
  },
  {
    title: 'Electronics Sale',
    subtitle: 'Up to 50% Off',
    description: 'Get the latest smartphones, laptops, and gadgets at unbeatable prices.',
    buttonText: 'Browse Electronics',
    buttonLink: '/categories/electronics',
    backgroundColor: 'bg-gradient-to-r from-green-500 to-blue-500',
    active: true,
    position: 2,
  },
  {
    title: 'Fashion Week Special',
    subtitle: 'Trending Styles',
    description: 'Discover the latest fashion trends with exclusive collections from top brands.',
    buttonText: 'Explore Fashion',
    buttonLink: '/categories/fashion',
    backgroundColor: 'bg-gradient-to-r from-pink-500 to-rose-500',
    active: true,
    position: 3,
  },
  {
    title: 'Home & Garden',
    subtitle: 'Transform Your Space',
    description: 'Beautiful furniture and decor items to make your house a home.',
    buttonText: 'Shop Home',
    buttonLink: '/categories/home-garden',
    backgroundColor: 'bg-gradient-to-r from-amber-500 to-orange-500',
    active: true,
    position: 4,
  },
  {
    title: 'Free Shipping',
    subtitle: 'On Orders Above ₹999',
    description: 'Enjoy free shipping across India on all orders above ₹999. No hidden charges.',
    buttonText: 'Start Shopping',
    buttonLink: '/products',
    backgroundColor: 'bg-gradient-to-r from-teal-500 to-cyan-500',
    active: true,
    position: 5,
  },
];

const PAGES = [
  {
    title: 'About Us',
    slug: 'about-us',
    content: `
      <h2>Welcome to ONDC Seller Platform</h2>
      <p>We are a leading marketplace connecting buyers and sellers across India through the Open Network for Digital Commerce (ONDC). Our platform empowers small and medium businesses to reach customers nationwide while providing buyers with access to quality products at competitive prices.</p>
      
      <h3>Our Mission</h3>
      <p>To democratize digital commerce in India by creating an inclusive ecosystem where every seller, regardless of size, can participate in the digital economy and reach customers across the country.</p>
      
      <h3>Why Choose Us?</h3>
      <ul>
        <li><strong>Verified Sellers:</strong> All our sellers are verified and committed to quality</li>
        <li><strong>Secure Payments:</strong> Multiple payment options with secure transactions</li>
        <li><strong>Fast Delivery:</strong> Quick and reliable delivery across India</li>
        <li><strong>Customer Support:</strong> 24/7 customer support for all your queries</li>
      </ul>
      
      <h3>Our Values</h3>
      <p>We believe in transparency, quality, and customer satisfaction. Our platform is built on trust and we work continuously to provide the best shopping experience for our customers.</p>
    `,
    excerpt: 'Learn about ONDC Seller Platform and our mission to democratize digital commerce in India.',
    metaTitle: 'About Us - ONDC Seller Platform',
    metaDescription: 'Discover how ONDC Seller Platform is democratizing digital commerce in India by connecting verified sellers with customers nationwide.',
    status: 'published',
    template: 'about',
    featured: false,
  },
  {
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content: `
      <h2>Privacy Policy</h2>
      <p><em>Last updated: ${new Date().toLocaleDateString()}</em></p>
      
      <h3>Information We Collect</h3>
      <p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>
      
      <h3>How We Use Your Information</h3>
      <ul>
        <li>To process and fulfill your orders</li>
        <li>To communicate with you about your account or transactions</li>
        <li>To provide customer support</li>
        <li>To improve our services and user experience</li>
      </ul>
      
      <h3>Information Sharing</h3>
      <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
      
      <h3>Data Security</h3>
      <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
      
      <h3>Contact Us</h3>
      <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL></p>
    `,
    excerpt: 'Our commitment to protecting your privacy and personal information.',
    metaTitle: 'Privacy Policy - ONDC Seller Platform',
    metaDescription: 'Learn how ONDC Seller Platform protects your privacy and handles your personal information.',
    status: 'published',
    template: 'default',
    featured: false,
  },
];

// Main seeding function
async function seedSampleData() {
  console.log('🌱 Starting Strapi CMS sample data seeding...');
  
  try {
    // Step 1: Create main categories
    console.log('\n📁 Creating main categories...');
    const createdCategories = {};
    
    for (const category of MAIN_CATEGORIES) {
      try {
        const categoryData = {
          ...category,
          // category_type: 'main',
          isSubcategory: false,
        };
        const result = await strapiRequest('/product-categories', 'POST', categoryData);
        createdCategories[category.slug] = result.data;
        console.log(`✅ Created main category: ${category.name}`);
      } catch (error) {
        console.log(`⚠️ Main category ${category.name} might already exist`);
      }
    }
    
    // Step 2: Create subcategories with parent relationships
    console.log('\n📂 Creating subcategories...');

    // First, fetch all main categories to get their IDs
    const mainCategoriesResponse = await strapiRequest('/product-categories?filters[isSubcategory][$eq]=false');
    const allMainCategories = mainCategoriesResponse.data || [];

    for (const subcategory of SUBCATEGORIES) {
      try {
        // Find main category
        const mainCategory = allMainCategories.find(cat =>
          (cat.attributes?.slug || cat.slug) === subcategory.main_category_slug
        );
        console.log({mainCategory,subcategory});

        if (mainCategory) {
          const subcategoryData = {
            name: subcategory.name,
            slug: subcategory.slug,
            description: subcategory.description,
            // category_type: 'sub',
            isSubcategory: true,
            parent: mainCategory.id,
          };

          const result = await strapiRequest('/product-categories', 'POST', subcategoryData);
          console.log(`✅ Created subcategory: ${subcategory.name}`);
        } else {
          console.log(`⚠️ Main category not found for: ${subcategory.name}`);
        }
      } catch (error) {
        console.log(`⚠️ Subcategory ${subcategory.name} might already exist`);
      }
    }
    
    // Step 3: Create banners
    console.log('\n🎨 Creating homepage banners...');
    
    for (const banner of BANNERS) {
      try {
        const result = await strapiRequest('/banners', 'POST', banner);
        console.log(`✅ Created banner: ${banner.title}`);
      } catch (error) {
        console.log(`⚠️ Banner ${banner.title} might already exist`);
      }
    }
    
    // Step 4: Create static pages
    console.log('\n📄 Creating static pages...');
    
    for (const page of PAGES) {
      try {
        const result = await strapiRequest('/pages', 'POST', page);
        console.log(`✅ Created page: ${page.title}`);
      } catch (error) {
        console.log(`⚠️ Page ${page.title} might already exist`);
      }
    }
    
    console.log('\n🎉 Sample data seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Main Categories: ${MAIN_CATEGORIES.length} main categories`);
    console.log(`- Subcategories: ${SUBCATEGORIES.length} subcategories`);
    console.log(`- Banners: ${BANNERS.length} homepage banners`);
    console.log(`- Pages: ${PAGES.length} static pages`);
    console.log('\n🌐 Visit http://localhost:1337/admin to manage your content!');
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  }
}

async function updateSubcategoryData(){
  try {
    const mainCategoriesResponse = await strapiRequest('/product-categories?filters[isSubcategory][$eq]=false');
    const allMainCategories = mainCategoriesResponse.data || [];

    const subCategoriesResponse = await strapiRequest('/product-categories?filters[isSubcategory][$eq]=true');
    const allSubCategories = subCategoriesResponse.data || [];
    for (const subcategory of SUBCATEGORIES) {
      try {
        // Find main category
        const mainCategory = allMainCategories.find(cat =>
          (cat.attributes?.slug || cat.slug) === subcategory.main_category_slug
        );
      
        const subcategory_id=allSubCategories.find(res=>res.name===subcategory.name);

        if (mainCategory) {
          const subcategoryData = {
            name: subcategory.name,
            slug: subcategory.slug,
            description: subcategory.description,
            // category_type: 'sub',
            isSubcategory: true,
            parent: mainCategory.documentId,
          };
          console.log({mainCategory,subcategoryData,"id":subcategory_id.documentId});

          await strapiRequest(`/product-categories/${subcategory_id.documentId}`, 'PUT', subcategoryData);
          console.log(`✅ Updated subcategory: ${subcategory.name}`);
        } else {
          console.log(`⚠️ Main category not found for: ${subcategory.name}`);
        }
      } catch (error) {
        console.log(`⚠️ Subcategory ${subcategory.name} might already exist`);
      }
    }
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  }
}

// Run the seeding script
if (require.main === module) {
  seedSampleData();
  // updateSubcategoryData();
}

module.exports = { seedSampleData };
module.exports = { updateSubcategoryData };
