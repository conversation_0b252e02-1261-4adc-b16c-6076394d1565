/**
 * Set up API permissions for the categories collection
 */

const fs = require('fs');
const path = require('path');

async function setupCategoryPermissions() {
  console.log('🔐 Setting up API permissions for categories collection...');
  
  try {
    // Path to the bootstrap file
    const bootstrapPath = path.join(__dirname, '..', 'src', 'index.ts');
    
    // Read the current bootstrap file
    let bootstrapContent = '';
    if (fs.existsSync(bootstrapPath)) {
      bootstrapContent = fs.readFileSync(bootstrapPath, 'utf8');
    } else {
      console.log('📝 Creating new bootstrap file...');
      bootstrapContent = `export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/*{ strapi }*/) {},

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  bootstrap({ strapi }) {
    // Set up API permissions for public access
    setApiPermissions(strapi);
  },
};

async function setApiPermissions(strapi) {
  console.log('🔐 Setting up API permissions...');
  
  try {
    const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
      where: { type: 'public' }
    });

    if (!publicRole) {
      console.log('❌ Public role not found');
      return;
    }

    // Set permissions for categories
    await strapi.query('plugin::users-permissions.permission').updateMany({
      where: {
        role: publicRole.id,
        action: { $in: ['api::category.category.find', 'api::category.category.findOne'] }
      },
      data: { enabled: true }
    });

    console.log('✅ Category API permissions set for public access');

    // Set permissions for other collections
    const collections = [
      'api::banner.banner',
      'api::page.page', 
      'api::product-category.product-category',
      'api::product.product'
    ];

    for (const collection of collections) {
      await strapi.query('plugin::users-permissions.permission').updateMany({
        where: {
          role: publicRole.id,
          action: { $in: [\`\${collection}.find\`, \`\${collection}.findOne\`] }
        },
        data: { enabled: true }
      });
      console.log(\`✅ \${collection.split('.')[1]} API permissions set for public access\`);
    }

  } catch (error) {
    console.error('❌ Error setting up API permissions:', error);
  }
}
`;
    }

    // Check if the permissions setup is already in the file
    if (!bootstrapContent.includes('Category API permissions')) {
      // Add the permissions setup to the bootstrap function
      const permissionsCode = `
    // Set up API permissions for categories
    try {
      const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
        where: { type: 'public' }
      });

      if (publicRole) {
        // Set permissions for categories
        await strapi.query('plugin::users-permissions.permission').updateMany({
          where: {
            role: publicRole.id,
            action: { $in: ['api::category.category.find', 'api::category.category.findOne'] }
          },
          data: { enabled: true }
        });
        console.log('✅ Category API permissions set for public access');
      }
    } catch (error) {
      console.error('❌ Error setting up category permissions:', error);
    }`;

      // Insert the permissions code into the bootstrap function
      if (bootstrapContent.includes('bootstrap({ strapi }) {')) {
        bootstrapContent = bootstrapContent.replace(
          'bootstrap({ strapi }) {',
          `bootstrap({ strapi }) {${permissionsCode}`
        );
      } else {
        // Create a new bootstrap function
        bootstrapContent = `export default {
  register() {},
  bootstrap({ strapi }) {${permissionsCode}
  },
};`;
      }

      // Write the updated bootstrap file
      fs.writeFileSync(bootstrapPath, bootstrapContent);
      console.log('✅ Updated bootstrap file with category permissions');
    } else {
      console.log('ℹ️ Category permissions already configured');
    }

    console.log('🎉 Category permissions setup completed!');
    console.log('📝 Please restart Strapi for the permissions to take effect');
    
  } catch (error) {
    console.error('❌ Error setting up category permissions:', error);
  }
}

// Run the setup
if (require.main === module) {
  setupCategoryPermissions();
}

module.exports = { setupCategoryPermissions };
