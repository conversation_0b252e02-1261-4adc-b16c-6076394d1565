# PowerShell script for ONDC Seller CMS Setup

# Function to write colored output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Write-ColorOutput Yellow "🚀 Starting ONDC Seller CMS Setup..."

# Check if Docker is installed and running
try {
    $dockerVersion = docker --version
    Write-ColorOutput Green "✅ Docker is installed: $dockerVersion"
}
catch {
    Write-ColorOutput Red "❌ Docker is not installed. Please install Docker Desktop first."
    exit 1
}

# Check if Docker is running
try {
    $dockerInfo = docker info
    Write-ColorOutput Green "✅ Docker is running"
}
catch {
    Write-ColorOutput Red "❌ Docker is not running. Please start Docker Desktop."
    exit 1
}

# Create .env file if it doesn't exist
if (-not (Test-Path .env)) {
    Write-ColorOutput Yellow "📝 Creating .env file..."
    @"
HOST=0.0.0.0
PORT=1337
APP_KEYS=toBeModified1,toBeModified2
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
JWT_SECRET=tobemodified

# Database
DATABASE_CLIENT=postgres
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=strapi_cms
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=strapi_password
DATABASE_SSL=false
"@ | Out-File -FilePath .env -Encoding UTF8
    Write-ColorOutput Green "✅ .env file created"
}

# Create data directory for PostgreSQL if it doesn't exist
if (-not (Test-Path "data/postgres")) {
    Write-ColorOutput Yellow "📁 Creating data directory for PostgreSQL..."
    New-Item -ItemType Directory -Force -Path "data/postgres" | Out-Null
    Write-ColorOutput Green "✅ Data directory created"
}

# Stop any existing containers
Write-ColorOutput Yellow "🛑 Stopping any existing containers..."
docker-compose down -v

# Start the services
Write-ColorOutput Yellow "🚀 Starting Docker containers..."
docker-compose up -d

# Wait for PostgreSQL to be ready
Write-ColorOutput Yellow "⏳ Waiting for PostgreSQL to be ready..."
$postgresReady = $false
while (-not $postgresReady) {
    try {
        $result = docker-compose exec -T postgres pg_isready -h localhost -p 5432 -U strapi
        if ($result -match "accepting connections") {
            $postgresReady = $true
            Write-ColorOutput Green "✅ PostgreSQL is ready"
        }
    }
    catch {
        Write-ColorOutput Yellow "⏳ Waiting for PostgreSQL..."
        Start-Sleep -Seconds 2
    }
}

# Wait for Strapi to be ready
Write-ColorOutput Yellow "⏳ Waiting for Strapi to be ready..."
$strapiReady = $false
while (-not $strapiReady) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:1339/admin" -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            $strapiReady = $true
            Write-ColorOutput Green "✅ Strapi is ready"
        }
    }
    catch {
        Write-ColorOutput Yellow "⏳ Waiting for Strapi..."
        Start-Sleep -Seconds 5
    }
}

Write-ColorOutput Green "✅ Setup completed successfully!"
Write-ColorOutput Green "🌐 Strapi Admin Panel: http://localhost:1339/admin"
Write-ColorOutput Green "📊 PostgreSQL is running on localhost:5432"
Write-ColorOutput Yellow "📝 Database credentials:"
Write-Output "   Database: strapi_cms"
Write-Output "   Username: strapi"
Write-Output "   Password: strapi_password"

# Show logs
Write-ColorOutput Yellow "📋 Showing container logs (press Ctrl+C to exit)..."
docker-compose logs -f 