#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 Starting ONDC Seller CMS Setup...${NC}"

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker Desktop first.${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker Desktop.${NC}"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}📝 Creating .env file...${NC}"
    cat > .env << EOL
HOST=0.0.0.0
PORT=1337
APP_KEYS=toBeModified1,toBeModified2
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
JWT_SECRET=tobemodified

# Database
DATABASE_CLIENT=postgres
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=strapi_cms
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=strapi_password
DATABASE_SSL=false
EOL
    echo -e "${GREEN}✅ .env file created${NC}"
fi

# Create data directory for PostgreSQL if it doesn't exist
if [ ! -d "data/postgres" ]; then
    echo -e "${YELLOW}📁 Creating data directory for PostgreSQL...${NC}"
    mkdir -p data/postgres
    echo -e "${GREEN}✅ Data directory created${NC}"
fi

# Stop any existing containers
echo -e "${YELLOW}🛑 Stopping any existing containers...${NC}"
docker-compose down -v

# Start the services
echo -e "${YELLOW}🚀 Starting Docker containers...${NC}"
docker-compose up -d

# Wait for PostgreSQL to be ready
echo -e "${YELLOW}⏳ Waiting for PostgreSQL to be ready...${NC}"
until docker-compose exec postgres pg_isready -h localhost -p 5432 -U strapi; do
    echo -e "${YELLOW}⏳ Waiting for PostgreSQL...${NC}"
    sleep 2
done

# Wait for Strapi to be ready
echo -e "${YELLOW}⏳ Waiting for Strapi to be ready...${NC}"
until curl -s http://localhost:1339/admin > /dev/null; do
    echo -e "${YELLOW}⏳ Waiting for Strapi...${NC}"
    sleep 5
done

echo -e "${GREEN}✅ Setup completed successfully!${NC}"
echo -e "${GREEN}🌐 Strapi Admin Panel: http://localhost:1339/admin${NC}"
echo -e "${GREEN}📊 PostgreSQL is running on localhost:5432${NC}"
echo -e "${YELLOW}📝 Database credentials:${NC}"
echo -e "   Database: strapi_cms"
echo -e "   Username: strapi"
echo -e "   Password: strapi_password"

# Show logs
echo -e "${YELLOW}📋 Showing container logs (press Ctrl+C to exit)...${NC}"
docker-compose logs -f 