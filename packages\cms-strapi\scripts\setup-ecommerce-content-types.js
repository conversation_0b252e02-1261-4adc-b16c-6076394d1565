/**
 * Master script to set up all e-commerce content types in Strapi CMS
 *
 * This script creates Categories and Products content types with proper relationships
 * and sets up API permissions for public access.
 */

const { createCategoryContentType } = require('./create-categories-content-type');
const { createProductContentType } = require('./create-products-content-type');
const { populateCategories } = require('./populate-categories');
const { populateProducts } = require('./populate-products');

async function setupEcommerceContentTypes() {
  console.log('🚀 Starting e-commerce content types setup...');
  console.log('='.repeat(60));

  try {
    // Step 1: Create Category content type
    console.log('\n📁 Step 1: Creating Category content type...');
    await createCategoryContentType();

    // Wait a moment for Strapi to process
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 2: Create Product content type
    console.log('\n📦 Step 2: Creating Product content type...');
    await createProductContentType();

    // Wait a moment for Strapi to process
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('\n' + '='.repeat(60));
    console.log('🎉 E-commerce content types setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Category content type created with hierarchical structure');
    console.log('✅ Product content type created with comprehensive fields');
    console.log('✅ API permissions configured for public access');
    console.log('✅ Relationships established between categories and products');

    console.log('\n🔗 Available API endpoints:');
    console.log('• GET /api/categories - List all categories');
    console.log('• GET /api/categories/:id - Get category by ID');
    console.log('• GET /api/products - List all products');
    console.log('• GET /api/products/:id - Get product by ID');

    // Step 3: Populate Categories
    console.log('\n📁 Step 3: Populating categories...');
    await populateCategories();

    // Wait a moment for categories to be processed
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 4: Populate Products
    console.log('\n📦 Step 4: Populating products...');
    await populateProducts();

    console.log('\n📝 Next steps:');
    console.log('1. Access Strapi admin to verify content and relationships');
    console.log('2. Test API endpoints with populated data');
    console.log('3. Verify frontend integration with real CMS data');
    console.log('4. Test category and product pages');
  } catch (error) {
    console.error('\n💥 Failed to setup e-commerce content types:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure Strapi server is running on http://localhost:1339');
    console.log('2. Check if STRAPI_API_TOKEN is set correctly');
    console.log('3. Verify Strapi admin permissions');
    console.log('4. Check Strapi server logs for detailed errors');

    process.exit(1);
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupEcommerceContentTypes();
}

module.exports = { setupEcommerceContentTypes };
