/**
 * Setup Network Access Script
 * This script helps configure Strapi for access from other machines on the network
 */

const os = require('os');
const fs = require('fs');
const path = require('path');

function setupNetworkAccess() {
  console.log('🌐 STRAPI NETWORK ACCESS SETUP');
  console.log('=' .repeat(50));
  
  try {
    // Get network interfaces
    const interfaces = os.networkInterfaces();
    const networkInfo = [];
    
    console.log('\n📍 Available Network Interfaces:');
    
    Object.keys(interfaces).forEach(interfaceName => {
      const interfaceInfo = interfaces[interfaceName];
      
      interfaceInfo.forEach(details => {
        if (details.family === 'IPv4' && !details.internal) {
          networkInfo.push({
            interface: interfaceName,
            address: details.address,
            netmask: details.netmask
          });
          
          console.log(`\n🔗 ${interfaceName}:`);
          console.log(`   IP Address: ${details.address}`);
          console.log(`   Strapi URL: http://${details.address}:1337`);
          console.log(`   Admin Panel: http://${details.address}:1337/admin`);
        }
      });
    });
    
    if (networkInfo.length === 0) {
      console.log('❌ No external network interfaces found.');
      console.log('💡 Make sure you are connected to a network (WiFi/Ethernet)');
      return;
    }
    
    const primaryIP = networkInfo[0].address;
    
    // Create network access guide
    const guideContent = `# Strapi Network Access Guide

## Current Network Configuration
- **Primary IP**: ${primaryIP}
- **Strapi Port**: 1337
- **Server Host**: 0.0.0.0 (allows external connections)

## Access URLs
- **Admin Panel**: http://${primaryIP}:1337/admin
- **API Base**: http://${primaryIP}:1337/api
- **Banners API**: http://${primaryIP}:1337/api/banners
- **Products API**: http://${primaryIP}:1337/api/products
- **Categories API**: http://${primaryIP}:1337/api/product-categories

## From Other Devices

### Mobile Phone/Tablet
1. Connect to the same WiFi network
2. Open browser and go to: http://${primaryIP}:1337/admin
3. Login with your admin credentials

### Another Computer
1. Ensure both computers are on the same network
2. Access: http://${primaryIP}:1337/admin
3. Use API endpoints for development

## Firewall Configuration

### Windows
1. Open Windows Defender Firewall
2. Click "Allow an app or feature through Windows Defender Firewall"
3. Click "Change Settings" then "Allow another app"
4. Browse to Node.js executable or add port 1337

### macOS
1. System Preferences > Security & Privacy > Firewall
2. Click "Firewall Options"
3. Add Node.js or allow incoming connections

### Linux
\`\`\`bash
sudo ufw allow 1337
sudo ufw reload
\`\`\`

## Testing Connection
Test from another device:
\`\`\`bash
curl http://${primaryIP}:1337/api
\`\`\`

## Security Considerations
⚠️ **IMPORTANT**: This setup is for development only!

For production:
- Use HTTPS with SSL certificates
- Configure proper authentication
- Use environment variables for sensitive data
- Consider VPN or SSH tunneling
- Implement rate limiting and security headers

## Troubleshooting

### Cannot Connect from Other Device
1. Check firewall settings
2. Verify both devices are on same network
3. Try disabling firewall temporarily for testing
4. Check if Strapi is running: http://localhost:1337

### API Returns 404
1. Verify Strapi is fully started
2. Check collection names in API URLs
3. Ensure data exists in collections

### Admin Panel Won't Load
1. Clear browser cache
2. Try incognito/private mode
3. Check browser console for errors
4. Verify admin user exists

## Network Information
${networkInfo.map(info => `- ${info.interface}: ${info.address}`).join('\n')}

Generated on: ${new Date().toISOString()}
`;
    
    const guidePath = path.join(__dirname, '..', 'NETWORK-ACCESS-GUIDE.md');
    fs.writeFileSync(guidePath, guideContent);
    
    console.log('\n✅ NETWORK ACCESS CONFIGURED!');
    console.log('=' .repeat(50));
    console.log(`📱 Access from other devices: http://${primaryIP}:1337/admin`);
    console.log(`📋 Full guide saved: NETWORK-ACCESS-GUIDE.md`);
    
    console.log('\n🔥 Quick Test:');
    console.log(`   From another device, try: http://${primaryIP}:1337/api`);
    
    console.log('\n⚠️  Security Reminder:');
    console.log('   This configuration is for DEVELOPMENT only!');
    console.log('   Do not use in production without proper security measures.');
    
    return {
      primaryIP,
      networkInfo,
      urls: {
        admin: `http://${primaryIP}:1337/admin`,
        api: `http://${primaryIP}:1337/api`,
        banners: `http://${primaryIP}:1337/api/banners`
      }
    };
    
  } catch (error) {
    console.error('❌ Network setup failed:', error.message);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  setupNetworkAccess();
}

module.exports = { setupNetworkAccess };
