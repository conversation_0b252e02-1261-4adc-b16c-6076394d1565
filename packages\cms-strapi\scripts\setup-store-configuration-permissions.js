/**
 * Setup Store Configuration Permissions Script
 * 
 * This script sets up the necessary permissions for the store-configurations content type
 * to allow public API access for creating store configuration entries.
 */

const axios = require('axios');
const readline = require('readline');

const STRAPI_URL = 'http://localhost:1337';

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Prompt user for input
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

/**
 * Create admin user if none exists
 */
async function createAdminUser() {
  console.log('🔧 Setting up admin user...');
  
  try {
    // Check if admin user already exists
    const response = await axios.get(`${STRAPI_URL}/admin/init`);
    
    if (response.data.hasAdmin) {
      console.log('✅ Admin user already exists');
      return null;
    }

    console.log('📝 No admin user found. Creating one...');
    
    const firstname = await askQuestion('Enter admin first name (default: Admin): ') || 'Admin';
    const lastname = await askQuestion('Enter admin last name (default: User): ') || 'User';
    const email = await askQuestion('Enter admin email (default: <EMAIL>): ') || '<EMAIL>';
    const password = await askQuestion('Enter admin password (default: admin123): ') || 'admin123';

    const adminData = {
      firstname,
      lastname,
      email,
      password
    };

    const createResponse = await axios.post(`${STRAPI_URL}/admin/register-admin`, adminData);
    
    if (createResponse.status === 201) {
      console.log('✅ Admin user created successfully');
      return { email, password };
    }
  } catch (error) {
    console.error('❌ Error creating admin user:', error.response?.data || error.message);
    return null;
  }
}

/**
 * Login and get JWT token
 */
async function loginAdmin(email, password) {
  try {
    console.log('🔐 Logging in as admin...');
    
    const response = await axios.post(`${STRAPI_URL}/admin/login`, {
      email,
      password
    });

    if (response.data.data.token) {
      console.log('✅ Admin login successful');
      return response.data.data.token;
    }
  } catch (error) {
    console.error('❌ Admin login failed:', error.response?.data || error.message);
    return null;
  }
}

/**
 * Set up permissions for store-configurations
 */
async function setupPermissions(token) {
  try {
    console.log('🔧 Setting up permissions for store-configurations...');

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Get the public role ID
    const rolesResponse = await axios.get(`${STRAPI_URL}/admin/users-permissions/roles`, { headers });
    const publicRole = rolesResponse.data.roles.find(role => role.type === 'public');
    
    if (!publicRole) {
      throw new Error('Public role not found');
    }

    console.log(`📋 Found public role with ID: ${publicRole.id}`);

    // Set permissions for store-configurations
    const permissionsData = {
      permissions: {
        'api::store-configuration.store-configuration': {
          controllers: {
            'store-configuration': {
              create: {
                enabled: true,
                policy: ''
              },
              find: {
                enabled: true,
                policy: ''
              },
              findOne: {
                enabled: true,
                policy: ''
              },
              update: {
                enabled: true,
                policy: ''
              },
              delete: {
                enabled: true,
                policy: ''
              }
            }
          }
        }
      }
    };

    const updateResponse = await axios.put(
      `${STRAPI_URL}/admin/users-permissions/roles/${publicRole.id}`,
      permissionsData,
      { headers }
    );

    if (updateResponse.status === 200) {
      console.log('✅ Permissions set successfully for store-configurations');
      return true;
    }
  } catch (error) {
    console.error('❌ Error setting up permissions:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test the API permissions
 */
async function testPermissions() {
  try {
    console.log('🧪 Testing API permissions...');

    // Test GET request
    const getResponse = await axios.get(`${STRAPI_URL}/api/store-configurations`);
    console.log('✅ GET /api/store-configurations - Success');

    // Test POST request with sample data
    const testData = {
      data: {
        store_name: 'Test Store',
        store_description: 'Test Description',
        gst_number: '27AABCU9603R1ZX',
        address_line_1: '123 Test Street',
        city: 'Mumbai',
        state: 'Maharashtra',
        pincode: '400001',
        phone: '+91 9876543210',
        email: '<EMAIL>',
        business_type: 'individual',
        business_category: 'electronics',
        user_id: 'test-user-123',
        created_by_user: 'test-user-123'
      }
    };

    const postResponse = await axios.post(`${STRAPI_URL}/api/store-configurations`, testData);
    
    if (postResponse.status === 200 || postResponse.status === 201) {
      console.log('✅ POST /api/store-configurations - Success');
      
      // Clean up test data
      if (postResponse.data.data.id) {
        await axios.delete(`${STRAPI_URL}/api/store-configurations/${postResponse.data.data.id}`);
        console.log('🧹 Test data cleaned up');
      }
    }

    return true;
  } catch (error) {
    console.error('❌ API test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🚀 Starting Store Configuration Permissions Setup...\n');

  try {
    // Step 1: Create admin user if needed
    const adminCredentials = await createAdminUser();
    
    // Step 2: Get admin credentials
    let email, password;
    if (adminCredentials) {
      email = adminCredentials.email;
      password = adminCredentials.password;
    } else {
      email = await askQuestion('Enter admin email: ');
      password = await askQuestion('Enter admin password: ');
    }

    // Step 3: Login and get token
    const token = await loginAdmin(email, password);
    if (!token) {
      throw new Error('Failed to get admin token');
    }

    // Step 4: Setup permissions
    const permissionsSet = await setupPermissions(token);
    if (!permissionsSet) {
      throw new Error('Failed to set permissions');
    }

    // Step 5: Test permissions
    const testPassed = await testPermissions();
    if (!testPassed) {
      throw new Error('Permission tests failed');
    }

    console.log('\n🎉 Store Configuration permissions setup completed successfully!');
    console.log('✅ You can now create store configurations via the API');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
