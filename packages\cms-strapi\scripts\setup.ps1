# PowerShell script for Strapi CMS Setup

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Test-DockerRunning {
    try {
        $null = docker info
        return $true
    }
    catch {
        return $false
    }
}

function Start-Setup {
    Write-ColorOutput Yellow "[START] Starting Strapi CMS Setup..."

    # Check Docker
    if (-not (Test-DockerRunning)) {
        Write-ColorOutput Red "[ERROR] Docker is not running. Please start Docker Desktop."
        exit 1
    }
    Write-ColorOutput Green "[OK] Docker is running"

    # Stop any existing containers
    Write-ColorOutput Yellow "[INFO] Stopping any existing containers..."
    docker-compose down -v

    # Start services
    Write-ColorOutput Yellow "[INFO] Starting services..."
    docker-compose up -d

    # Wait for services to be ready
    Write-ColorOutput Yellow "[INFO] Waiting for services to be ready..."
    $maxAttempts = 60
    $attempt = 0
    $strapiReady = $false

    while (-not $strapiReady -and $attempt -lt $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:1339/admin" -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                $strapiReady = $true
                Write-ColorOutput Green "[OK] Strapi is ready!"
            }
        }
        catch {
            $attempt++
            Write-ColorOutput Yellow "[INFO] Waiting for Strapi... (Attempt $attempt of $maxAttempts)"
            Start-Sleep -Seconds 5
        }
    }

    if (-not $strapiReady) {
        Write-ColorOutput Red "[ERROR] Strapi failed to start within the timeout period"
        exit 1
    }

    Write-ColorOutput Green "[OK] Setup completed successfully!"
    Write-ColorOutput Green "[INFO] Strapi Admin Panel: http://localhost:1339/admin"
    Write-ColorOutput Yellow "[INFO] Database credentials:"
    Write-Output "   Database: strapi_cms"
    Write-Output "   Username: strapi"
    Write-Output "   Password: strapi_password"
    Write-ColorOutput Yellow "[INFO] Useful commands:"
    Write-Output "   - View logs: docker-compose logs -f"
    Write-Output "   - Stop services: docker-compose down"
    Write-Output "   - Restart services: docker-compose restart"
    Write-Output "   - Clean everything: docker-compose down -v"
}

# Run the setup
Start-Setup 