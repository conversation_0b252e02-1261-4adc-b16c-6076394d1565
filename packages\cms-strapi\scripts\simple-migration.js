/**
 * Simplified migration script focusing on product categories first
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

// Enhanced request function
async function strapiRequest(endpoint, method = 'GET', data = null, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const config = {
        method,
        url: `${API_BASE}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = { data };
      }

      const response = await axios(config);
      return { success: true, data: response.data };
    } catch (error) {
      console.log(`   Attempt ${attempt}/${retries} failed for ${method} ${endpoint}`);
      
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || 'Unknown error';
        console.log(`   ❌ HTTP ${status}: ${message}`);
        
        if (status === 400 && message.includes('already exists')) {
          return { success: true, data: null, exists: true };
        }
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }
      
      if (attempt === retries) {
        return { success: false, error: error.message };
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics',
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

function logProgress(step, message, type = 'info') {
  const timestamp = new Date().toISOString();
  const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
  console.log(`${symbols[type]} [${timestamp}] ${step}: ${message}`);
}

async function createBackup(step, data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(__dirname, 'migration-backups');
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const backupFile = path.join(backupDir, `${step}_${timestamp}.json`);
  fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
  console.log(`   📁 Backup created: ${backupFile}`);
  return backupFile;
}

// STEP 1: Update Product Categories with Parent Relationships
async function updateProductCategories() {
  logProgress('STEP 1', 'Starting product categories update', 'info');
  
  try {
    // Get all categories first
    logProgress('STEP 1', 'Fetching main categories...', 'info');
    const categoriesResult = await strapiRequest('/categories?pagination[pageSize]=100');
    if (!categoriesResult.success) {
      throw new Error('Failed to fetch categories');
    }
    
    const categories = categoriesResult.data.data || [];
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat;
    });
    
    logProgress('STEP 1', `Found ${categories.length} main categories`, 'success');
    
    // Get all product categories
    logProgress('STEP 1', 'Fetching product categories...', 'info');
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }
    
    const productCategories = productCategoriesResult.data.data || [];
    await createBackup('product_categories_before', productCategories);
    
    logProgress('STEP 1', `Found ${productCategories.length} product categories`, 'success');
    
    let updateCount = 0;
    let subcategoryCount = 0;
    
    for (const subcat of productCategories) {
      const parentCategoryName = subcategoryMapping[subcat.name];
      
      if (parentCategoryName && categoryMap[parentCategoryName]) {
        logProgress('STEP 1', `Updating subcategory: ${subcat.name} → ${parentCategoryName}`, 'info');
        
        const updatePayload = {
          isSubcategory: true,
          parent: categoryMap[parentCategoryName].documentId
        };
        
        const result = await strapiRequest(`/product-categories/${subcat.documentId}`, 'PUT', updatePayload);
        
        if (result.success) {
          logProgress('STEP 1', `✅ Successfully updated: ${subcat.name}`, 'success');
          updateCount++;
          subcategoryCount++;
        } else {
          logProgress('STEP 1', `❌ Failed to update: ${subcat.name}: ${result.error}`, 'error');
        }
      } else {
        logProgress('STEP 1', `Skipping unmapped category: ${subcat.name}`, 'warning');
      }
      
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    await createBackup('product_categories_after', { updateCount, subcategoryCount });
    logProgress('STEP 1', `Product categories update completed. Updated: ${updateCount} subcategories`, 'success');
    return { updateCount, subcategoryCount };
    
  } catch (error) {
    logProgress('STEP 1', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// STEP 2: Verify the relationships
async function verifyRelationships() {
  logProgress('STEP 2', 'Starting relationships verification', 'info');
  
  try {
    // Get updated product categories with parent relationships
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=100&populate=parent');
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }
    
    const productCategories = productCategoriesResult.data.data || [];
    let verifiedCount = 0;
    
    console.log('\n📋 RELATIONSHIP VERIFICATION:');
    console.log('-' .repeat(50));
    
    for (const subcat of productCategories) {
      if (subcat.parent) {
        console.log(`   ✅ ${subcat.name} → ${subcat.parent.name}`);
        verifiedCount++;
      } else if (subcategoryMapping[subcat.name]) {
        console.log(`   ❌ ${subcat.name} → (no parent set)`);
      } else {
        console.log(`   ⚪ ${subcat.name} → (not a subcategory)`);
      }
    }
    
    logProgress('STEP 2', `Verification completed. ${verifiedCount} relationships verified`, 'success');
    return { verifiedCount, totalCategories: productCategories.length };
    
  } catch (error) {
    logProgress('STEP 2', `Failed: ${error.message}`, 'error');
    throw error;
  }
}

// Main function
async function runSimpleMigration() {
  console.log('🔄 SIMPLE MIGRATION: PRODUCT CATEGORIES UPDATE');
  console.log('=' .repeat(60));
  console.log('📋 Focus: Update product categories with parent relationships');
  console.log('=' .repeat(60));
  
  const startTime = Date.now();
  
  try {
    // Test connectivity first
    logProgress('INIT', 'Testing API connectivity...', 'info');
    const healthCheck = await strapiRequest('/categories');
    
    if (!healthCheck.success) {
      throw new Error('Cannot connect to Strapi API. Please ensure Strapi is running.');
    }
    
    logProgress('INIT', 'API connectivity confirmed', 'success');
    
    // Step 1: Update Product Categories
    console.log('\n🔄 STEP 1: UPDATE PRODUCT CATEGORIES');
    console.log('-' .repeat(40));
    const updateResult = await updateProductCategories();
    
    // Step 2: Verify Relationships
    console.log('\n🔍 STEP 2: VERIFY RELATIONSHIPS');
    console.log('-' .repeat(40));
    const verifyResult = await verifyRelationships();
    
    // Final summary
    console.log('\n✅ SIMPLE MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('=' .repeat(60));
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log(`📊 Summary:`);
    console.log(`   • Subcategories Updated: ${updateResult.subcategoryCount}`);
    console.log(`   • Relationships Verified: ${verifyResult.verifiedCount}`);
    
    console.log('\n🌐 Verification URLs:');
    console.log('   • Categories: http://localhost:1337/api/categories');
    console.log('   • Product Categories: http://localhost:1337/api/product-categories?populate=parent');
    console.log('   • Admin Panel: http://localhost:1337/admin');
    
    console.log('\n🎉 Product categories now have proper parent relationships!');
    
  } catch (error) {
    console.error('\n❌ SIMPLE MIGRATION FAILED');
    console.error('=' .repeat(60));
    console.error(`Error: ${error.message}`);
    
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runSimpleMigration();
}

module.exports = { runSimpleMigration, updateProductCategories, verifyRelationships };
