/**
 * Simple Parent Linking Script
 * Links subcategories to parent categories using a simplified approach
 */

const { execSync } = require('child_process');

// Subcategory to main category mapping
const subcategoryMapping = {
  'Smartphones': 'Electronics',
  'Laptops': 'Electronics',
  'Tablets': 'Electronics',
  'Accessories': 'Electronics',
  "Men's Clothing": 'Fashion',
  "Women's Clothing": 'Fashion',
  'Footwear': 'Fashion',
  'Furniture': 'Home & Garden',
  'Kitchen': 'Home & Garden',
  'Decor': 'Home & Garden',
  'Garden': 'Home & Garden',
  'Fitness Equipment': 'Sports & Fitness',
  'Outdoor Sports': 'Sports & Fitness',
  'Activewear': 'Sports & Fitness',
  'Fiction': 'Books & Media',
  'Non-Fiction': 'Books & Media',
  'Magazines': 'Books & Media',
  'Digital Media': 'Books & Media',
  'Skincare': 'Health & Beauty',
  'Makeup': 'Health & Beauty',
  'Personal Care': 'Health & Beauty',
  'Car Parts': 'Automotive',
  'Car Accessories': 'Automotive',
  'Board Games': 'Toys & Games',
  'Action Figures': 'Toys & Games',
  'Educational Toys': 'Toys & Games'
};

function executeCommand(command) {
  try {
    const result = execSync(command, { encoding: 'utf8', timeout: 30000 });
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

function parseJsonResponse(response) {
  try {
    // Clean up the PowerShell response and parse JSON
    const cleanResponse = response.replace(/\s+/g, ' ').trim();
    const jsonMatch = cleanResponse.match(/\{.*\}/s);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    return null;
  } catch (error) {
    console.log('Failed to parse JSON:', error.message);
    return null;
  }
}

async function simpleParentLinking() {
  console.log('🔗 SIMPLE PARENT LINKING SCRIPT');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Get Categories
    console.log('\n📁 Getting Categories...');
    const categoriesCommand = `powershell -Command "Invoke-RestMethod -Uri 'http://localhost:1337/api/categories' -Method Get | ConvertTo-Json -Depth 2"`;
    const categoriesResult = executeCommand(categoriesCommand);
    
    if (!categoriesResult.success) {
      throw new Error('Failed to fetch categories');
    }
    
    const categoriesData = parseJsonResponse(categoriesResult.data);
    if (!categoriesData || !categoriesData.data) {
      throw new Error('Invalid categories response');
    }
    
    const categories = categoriesData.data;
    console.log(`✅ Found ${categories.length} categories`);
    
    // Create category lookup
    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat;
      console.log(`   📁 ${cat.name} (${cat.documentId})`);
    });
    
    // Step 2: Get Product Categories
    console.log('\n📦 Getting Product Categories...');
    const productCategoriesCommand = `powershell -Command "Invoke-RestMethod -Uri 'http://localhost:1337/api/product-categories' -Method Get | ConvertTo-Json -Depth 2"`;
    const productCategoriesResult = executeCommand(productCategoriesCommand);
    
    if (!productCategoriesResult.success) {
      throw new Error('Failed to fetch product categories');
    }
    
    const productCategoriesData = parseJsonResponse(productCategoriesResult.data);
    if (!productCategoriesData || !productCategoriesData.data) {
      throw new Error('Invalid product categories response');
    }
    
    const productCategories = productCategoriesData.data;
    console.log(`✅ Found ${productCategories.length} product categories`);
    
    // Step 3: Create parent categories in Product Categories collection
    console.log('\n🏗️ Creating parent categories...');
    const createdParents = {};
    
    for (const [subcatName, parentName] of Object.entries(subcategoryMapping)) {
      if (!createdParents[parentName] && categoryMap[parentName]) {
        console.log(`Creating parent: ${parentName}`);
        
        // Check if parent already exists
        const checkCommand = `powershell -Command "Invoke-RestMethod -Uri 'http://localhost:1337/api/product-categories?filters[name][\$eq]=${encodeURIComponent(parentName)}&filters[isSubcategory][\$eq]=false' -Method Get | ConvertTo-Json -Depth 2"`;
        const checkResult = executeCommand(checkCommand);
        
        if (checkResult.success) {
          const checkData = parseJsonResponse(checkResult.data);
          if (checkData && checkData.data && checkData.data.length > 0) {
            createdParents[parentName] = checkData.data[0].documentId;
            console.log(`   ✅ Parent already exists: ${parentName}`);
            continue;
          }
        }
        
        // Create parent category
        const parentData = {
          name: parentName,
          slug: parentName.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
          description: `Main ${parentName} category`,
          isSubcategory: false,
          active: true,
          sort_order: 1,
          category: categoryMap[parentName].documentId
        };
        
        const createCommand = `powershell -Command "
          $body = @{
            data = @{
              name = '${parentData.name}'
              slug = '${parentData.slug}'
              description = '${parentData.description}'
              isSubcategory = $false
              active = $true
              sort_order = 1
              category = '${parentData.category}'
            }
          } | ConvertTo-Json -Depth 3
          
          Invoke-RestMethod -Uri 'http://localhost:1337/api/product-categories' -Method Post -Body $body -ContentType 'application/json' | ConvertTo-Json -Depth 2
        "`;
        
        const createResult = executeCommand(createCommand);
        
        if (createResult.success) {
          const createData = parseJsonResponse(createResult.data);
          if (createData && createData.data) {
            createdParents[parentName] = createData.data.documentId;
            console.log(`   ✅ Created parent: ${parentName} (${createData.data.documentId})`);
          }
        } else {
          console.log(`   ❌ Failed to create parent: ${parentName}`);
        }
        
        // Wait between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // Step 4: Link subcategories to parents
    console.log('\n🔗 Linking subcategories to parents...');
    let linkedCount = 0;
    let errorCount = 0;
    
    for (const subcat of productCategories) {
      const parentName = subcategoryMapping[subcat.name];
      
      if (parentName && createdParents[parentName] && categoryMap[parentName]) {
        console.log(`Linking: ${subcat.name} → ${parentName}`);
        
        const updateCommand = `powershell -Command "
          $body = @{
            data = @{
              isSubcategory = $true
              parent = '${createdParents[parentName]}'
              category = '${categoryMap[parentName].documentId}'
            }
          } | ConvertTo-Json -Depth 3
          
          Invoke-RestMethod -Uri 'http://localhost:1337/api/product-categories/${subcat.documentId}' -Method Put -Body $body -ContentType 'application/json' | ConvertTo-Json -Depth 2
        "`;
        
        const updateResult = executeCommand(updateCommand);
        
        if (updateResult.success) {
          console.log(`   ✅ Successfully linked: ${subcat.name}`);
          linkedCount++;
        } else {
          console.log(`   ❌ Failed to link: ${subcat.name}`);
          errorCount++;
        }
        
        // Wait between requests
        await new Promise(resolve => setTimeout(resolve, 500));
      } else {
        console.log(`Skipping: ${subcat.name} (no parent mapping or parent not created)`);
      }
    }
    
    // Step 5: Verification
    console.log('\n✅ PARENT LINKING COMPLETED!');
    console.log('=' .repeat(60));
    console.log(`📊 Results:`);
    console.log(`   • Successfully linked: ${linkedCount}`);
    console.log(`   • Errors: ${errorCount}`);
    console.log(`   • Parents created: ${Object.keys(createdParents).length}`);
    
    console.log('\n🌐 Verification URLs:');
    console.log('   • Product Categories: http://localhost:1337/api/product-categories');
    console.log('   • Admin Panel: http://localhost:1337/admin');
    
    if (linkedCount > 0) {
      console.log('\n🎉 SUBCATEGORIES SUCCESSFULLY LINKED TO PARENT CATEGORIES!');
    }
    
  } catch (error) {
    console.error('❌ PARENT LINKING FAILED:', error.message);
  }
}

// Add delay function
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the linking
if (require.main === module) {
  simpleParentLinking();
}

module.exports = { simpleParentLinking };
