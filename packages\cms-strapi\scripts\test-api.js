/**
 * Test Strapi API connectivity and data structure
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function testAPI() {
  console.log('🔍 Testing Strapi API connectivity...');
  
  try {
    // Test basic connectivity
    console.log('\n📡 Testing basic connectivity...');
    const healthCheck = await axios.get(`${STRAPI_URL}/_health`);
    console.log('✅ Strapi is running and healthy');
    
    // Test product-categories endpoint
    console.log('\n📁 Testing product-categories endpoint...');
    const categoriesResponse = await axios.get(`${API_BASE}/product-categories`);
    console.log(`✅ Categories endpoint working - ${categoriesResponse.data.data?.length || 0} categories found`);
    
    if (categoriesResponse.data.data && categoriesResponse.data.data.length > 0) {
      const firstCategory = categoriesResponse.data.data[0];
      console.log('📋 First category structure:', {
        id: firstCategory.id,
        name: firstCategory.attributes?.name || firstCategory.name,
        slug: firstCategory.attributes?.slug || firstCategory.slug,
        category_type: firstCategory.attributes?.category_type || firstCategory.category_type,
        isSubcategory: firstCategory.attributes?.isSubcategory || firstCategory.isSubcategory,
      });
    }
    
    // Test products endpoint
    console.log('\n🛍️ Testing products endpoint...');
    const productsResponse = await axios.get(`${API_BASE}/products`);
    console.log(`✅ Products endpoint working - ${productsResponse.data.data?.length || 0} products found`);
    
    // Test banners endpoint
    console.log('\n🎨 Testing banners endpoint...');
    const bannersResponse = await axios.get(`${API_BASE}/banners`);
    console.log(`✅ Banners endpoint working - ${bannersResponse.data.data?.length || 0} banners found`);
    
    // Test pages endpoint
    console.log('\n📄 Testing pages endpoint...');
    const pagesResponse = await axios.get(`${API_BASE}/pages`);
    console.log(`✅ Pages endpoint working - ${pagesResponse.data.data?.length || 0} pages found`);
    
    console.log('\n🎉 All API endpoints are working correctly!');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testAPI();
}

module.exports = { testAPI };
