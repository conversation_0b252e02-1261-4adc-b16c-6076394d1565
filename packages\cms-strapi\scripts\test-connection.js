const axios = require('axios');

async function testConnection() {
  try {
    console.log('Testing Strapi connection...');
    const response = await axios.get('http://localhost:1337/api/categories', {
      timeout: 5000
    });
    console.log('✅ Connection successful');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Connection failed');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

testConnection();
