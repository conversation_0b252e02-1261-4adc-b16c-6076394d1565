/**
 * Test Hierarchical Category Schema with Parent-Child Relationships
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = { data };
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      details: error.response?.data
    };
  }
}

function logTest(testName, result, details = '') {
  const status = result ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${testName}`);
  if (details) {
    console.log(`   ${details}`);
  }
}

function logSection(title) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🔍 ${title}`);
  console.log(`${'='.repeat(60)}`);
}

async function testHierarchicalSchema() {
  console.log('🧪 HIERARCHICAL CATEGORY SCHEMA TESTING');
  console.log('=' .repeat(80));
  console.log('Testing updated schema with parent-child relationships and product linking');
  console.log('=' .repeat(80));
  
  const startTime = Date.now();
  let testResults = {};
  
  try {
    // Test 1: Schema Structure Verification
    logSection('SCHEMA STRUCTURE VERIFICATION');
    
    const productCategoriesResult = await strapiRequest('/product-categories?pagination[pageSize]=1');
    logTest('Product Categories API Access', productCategoriesResult.success,
      productCategoriesResult.success ? 'API accessible' : productCategoriesResult.error);
    
    if (productCategoriesResult.success && productCategoriesResult.data.data.length > 0) {
      const sampleCategory = productCategoriesResult.data.data[0];
      const fields = Object.keys(sampleCategory);
      
      const hasParent = fields.includes('parent');
      const hasCategory = fields.includes('category');
      const hasIsSubcategory = fields.includes('isSubcategory');
      
      logTest('Parent Field Present', hasParent, hasParent ? 'Self-referential parent field exists' : 'Parent field missing');
      logTest('Category Field Present', hasCategory, hasCategory ? 'Category link field exists' : 'Category field missing');
      logTest('isSubcategory Field Present', hasIsSubcategory, hasIsSubcategory ? 'Subcategory flag exists' : 'isSubcategory field missing');
      
      testResults.schemaValid = hasParent && hasCategory && hasIsSubcategory;
    } else {
      testResults.schemaValid = false;
    }
    
    // Test 2: Create Test Hierarchy
    logSection('HIERARCHICAL RELATIONSHIP TESTING');
    
    // Create main category
    const mainCategoryData = {
      name: 'Test Electronics Main',
      slug: 'test-electronics-main',
      isSubcategory: false,
      description: 'Main electronics category for testing',
      active: true,
      sort_order: 1
    };
    
    const createMainResult = await strapiRequest('/product-categories', 'POST', mainCategoryData);
    logTest('Create Main Category', createMainResult.success,
      createMainResult.success ? `Created: ${createMainResult.data.data.name}` : createMainResult.error);
    
    let mainCategoryId = null;
    let mainCategoryDocumentId = null;
    
    if (createMainResult.success) {
      mainCategoryId = createMainResult.data.data.id;
      mainCategoryDocumentId = createMainResult.data.data.documentId;
    }
    
    // Create subcategory with parent relationship
    if (mainCategoryDocumentId) {
      const subcategoryData = {
        name: 'Test Smartphones Sub',
        slug: 'test-smartphones-sub',
        isSubcategory: true,
        description: 'Smartphones subcategory for testing',
        active: true,
        sort_order: 1,
        parent: mainCategoryDocumentId
      };
      
      const createSubResult = await strapiRequest('/product-categories', 'POST', subcategoryData);
      logTest('Create Subcategory with Parent', createSubResult.success,
        createSubResult.success ? `Created: ${createSubResult.data.data.name}` : createSubResult.error);
      
      if (createSubResult.success) {
        const subcategoryId = createSubResult.data.data.id;
        const subcategoryDocumentId = createSubResult.data.data.documentId;
        
        // Test 3: Verify Parent-Child Relationship
        logSection('PARENT-CHILD RELATIONSHIP VERIFICATION');
        
        // Check parent -> children relationship
        const parentWithChildrenResult = await strapiRequest(`/product-categories/${mainCategoryDocumentId}?populate=children`);
        logTest('Parent -> Children Relationship', parentWithChildrenResult.success,
          parentWithChildrenResult.success && parentWithChildrenResult.data.data.children?.length > 0 ? 
          `Found ${parentWithChildrenResult.data.data.children.length} children` : 'No children found');
        
        // Check child -> parent relationship
        const childWithParentResult = await strapiRequest(`/product-categories/${subcategoryDocumentId}?populate=parent`);
        logTest('Child -> Parent Relationship', childWithParentResult.success,
          childWithParentResult.success && childWithParentResult.data.data.parent ? 
          `Parent: ${childWithParentResult.data.data.parent.name}` : 'No parent found');
        
        testResults.hierarchyWorking = parentWithChildrenResult.success && childWithParentResult.success;
        
        // Test 4: Product-Category Relationship
        logSection('PRODUCT-CATEGORY RELATIONSHIP TESTING');
        
        // Create test product
        const productData = {
          name: 'Test iPhone',
          description: 'Test product for category linking',
          short_description: 'Test iPhone product',
          price: 999.99,
          sku: 'TEST-IPHONE-001',
          inventory_quantity: 10,
          categories: [subcategoryDocumentId]
        };
        
        const createProductResult = await strapiRequest('/products', 'POST', productData);
        logTest('Create Product with Category Link', createProductResult.success,
          createProductResult.success ? `Created: ${createProductResult.data.data.name}` : createProductResult.error);
        
        if (createProductResult.success) {
          const productDocumentId = createProductResult.data.data.documentId;
          
          // Verify product -> categories relationship
          const productWithCategoriesResult = await strapiRequest(`/products/${productDocumentId}?populate=categories`);
          logTest('Product -> Categories Relationship', productWithCategoriesResult.success,
            productWithCategoriesResult.success && productWithCategoriesResult.data.data.categories?.length > 0 ? 
            `Linked to ${productWithCategoriesResult.data.data.categories.length} categories` : 'No categories linked');
          
          // Verify category -> products relationship
          const categoryWithProductsResult = await strapiRequest(`/product-categories/${subcategoryDocumentId}?populate=products`);
          logTest('Category -> Products Relationship', categoryWithProductsResult.success,
            categoryWithProductsResult.success && categoryWithProductsResult.data.data.products?.length > 0 ? 
            `Has ${categoryWithProductsResult.data.data.products.length} products` : 'No products linked');
          
          testResults.productLinkingWorking = productWithCategoriesResult.success && categoryWithProductsResult.success;
          
          // Cleanup test product
          await strapiRequest(`/products/${productDocumentId}`, 'DELETE');
          logTest('Cleanup Test Product', true, 'Test product deleted');
        }
        
        // Cleanup test subcategory
        await strapiRequest(`/product-categories/${subcategoryDocumentId}`, 'DELETE');
        logTest('Cleanup Test Subcategory', true, 'Test subcategory deleted');
      }
    }
    
    // Cleanup test main category
    if (mainCategoryDocumentId) {
      await strapiRequest(`/product-categories/${mainCategoryDocumentId}`, 'DELETE');
      logTest('Cleanup Test Main Category', true, 'Test main category deleted');
    }
    
    // Test 5: Complex Hierarchy Testing
    logSection('COMPLEX HIERARCHY TESTING');
    
    // Test multiple levels of hierarchy
    const level1Data = {
      name: 'Electronics Level 1',
      slug: 'electronics-level-1',
      isSubcategory: false,
      description: 'Level 1 category',
      active: true
    };
    
    const level1Result = await strapiRequest('/product-categories', 'POST', level1Data);
    
    if (level1Result.success) {
      const level1DocumentId = level1Result.data.data.documentId;
      
      const level2Data = {
        name: 'Mobile Devices Level 2',
        slug: 'mobile-devices-level-2',
        isSubcategory: true,
        description: 'Level 2 category',
        active: true,
        parent: level1DocumentId
      };
      
      const level2Result = await strapiRequest('/product-categories', 'POST', level2Data);
      
      if (level2Result.success) {
        const level2DocumentId = level2Result.data.data.documentId;
        
        const level3Data = {
          name: 'Smartphones Level 3',
          slug: 'smartphones-level-3',
          isSubcategory: true,
          description: 'Level 3 category',
          active: true,
          parent: level2DocumentId
        };
        
        const level3Result = await strapiRequest('/product-categories', 'POST', level3Data);
        
        logTest('3-Level Hierarchy Creation', level3Result.success,
          level3Result.success ? 'Successfully created 3-level hierarchy' : level3Result.error);
        
        if (level3Result.success) {
          const level3DocumentId = level3Result.data.data.documentId;
          
          // Test deep hierarchy navigation
          const deepHierarchyResult = await strapiRequest(`/product-categories/${level3DocumentId}?populate[parent][populate]=parent`);
          logTest('Deep Hierarchy Navigation', deepHierarchyResult.success,
            deepHierarchyResult.success ? 'Can navigate up the hierarchy' : 'Navigation failed');
          
          testResults.complexHierarchyWorking = deepHierarchyResult.success;
          
          // Cleanup
          await strapiRequest(`/product-categories/${level3DocumentId}`, 'DELETE');
          await strapiRequest(`/product-categories/${level2DocumentId}`, 'DELETE');
        }
      }
      
      await strapiRequest(`/product-categories/${level1DocumentId}`, 'DELETE');
    }
    
    // Final Summary
    logSection('TEST SUMMARY');
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`⏱️  Test Duration: ${duration} seconds`);
    console.log(`\n📊 Test Results:`);
    console.log(`   • Schema Structure: ${testResults.schemaValid ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   • Hierarchical Relationships: ${testResults.hierarchyWorking ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   • Product Linking: ${testResults.productLinkingWorking ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   • Complex Hierarchy: ${testResults.complexHierarchyWorking ? '✅ PASS' : '❌ FAIL'}`);
    
    const overallSuccess = testResults.schemaValid && 
                          testResults.hierarchyWorking && 
                          testResults.productLinkingWorking &&
                          testResults.complexHierarchyWorking;
    
    console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ HIERARCHICAL SCHEMA WORKING' : '❌ ISSUES DETECTED'}`);
    
    if (overallSuccess) {
      console.log('\n🎉 The hierarchical category system with parent-child relationships is fully functional!');
      console.log('\n✅ Features Confirmed:');
      console.log('   • Self-referential parent-child relationships in product_category');
      console.log('   • Many-to-many product-category relationships');
      console.log('   • Multi-level hierarchy support');
      console.log('   • Bidirectional relationship navigation');
    } else {
      console.log('\n⚠️ Some issues were detected. Please review the test results above.');
    }
    
    console.log('\n🌐 API Endpoints:');
    console.log(`   • Product Categories: ${API_BASE}/product-categories`);
    console.log(`   • Products: ${API_BASE}/products`);
    console.log(`   • Admin Panel: ${STRAPI_URL}/admin`);
    
  } catch (error) {
    console.error('\n❌ TEST EXECUTION FAILED');
    console.error(`Error: ${error.message}`);
  }
}

// Run the tests
if (require.main === module) {
  testHierarchicalSchema();
}

module.exports = { testHierarchicalSchema };
