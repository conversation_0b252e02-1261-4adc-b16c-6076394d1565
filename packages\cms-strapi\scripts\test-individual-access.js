/**
 * Test individual item access to determine correct identifier
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function strapiRequest(endpoint) {
  try {
    const response = await axios.get(`${API_BASE}${endpoint}`, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 10000,
    });
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      responseData: error.response?.data
    };
  }
}

async function testIndividualAccess() {
  console.log('🧪 TESTING INDIVIDUAL ITEM ACCESS');
  console.log('=' .repeat(50));
  
  // Get categories list first
  const categoriesResult = await strapiRequest('/categories');
  if (!categoriesResult.success) {
    console.log('❌ Failed to get categories list');
    return;
  }
  
  const categories = categoriesResult.data.data || [];
  if (categories.length === 0) {
    console.log('❌ No categories found');
    return;
  }
  
  const firstCategory = categories[0];
  console.log('\n📋 First category details:');
  console.log(`   ID: ${firstCategory.id}`);
  console.log(`   DocumentID: ${firstCategory.documentId}`);
  console.log(`   Name: ${firstCategory.name}`);
  
  // Test access with ID
  console.log('\n🔍 Testing access with ID...');
  const idResult = await strapiRequest(`/categories/${firstCategory.id}`);
  if (idResult.success) {
    console.log('✅ Access with ID: SUCCESS');
  } else {
    console.log(`❌ Access with ID: FAILED (${idResult.status})`);
  }
  
  // Test access with documentId
  console.log('\n🔍 Testing access with documentId...');
  const docIdResult = await strapiRequest(`/categories/${firstCategory.documentId}`);
  if (docIdResult.success) {
    console.log('✅ Access with documentId: SUCCESS');
    console.log('   Response structure:', Object.keys(docIdResult.data.data || {}));
  } else {
    console.log(`❌ Access with documentId: FAILED (${docIdResult.status})`);
  }
  
  // Test product categories too
  console.log('\n📦 Testing Product Categories...');
  const productCategoriesResult = await strapiRequest('/product-categories');
  if (productCategoriesResult.success) {
    const productCategories = productCategoriesResult.data.data || [];
    if (productCategories.length > 0) {
      const firstProdCat = productCategories[0];
      console.log(`\n📋 First product category: ID=${firstProdCat.id}, DocumentID=${firstProdCat.documentId}, Name=${firstProdCat.name}`);
      
      // Test with documentId
      const prodDocIdResult = await strapiRequest(`/product-categories/${firstProdCat.documentId}`);
      if (prodDocIdResult.success) {
        console.log('✅ Product Category access with documentId: SUCCESS');
      } else {
        console.log(`❌ Product Category access with documentId: FAILED (${prodDocIdResult.status})`);
      }
    }
  }
  
  console.log('\n🎯 TEST COMPLETE');
}

// Run the test
if (require.main === module) {
  testIndividualAccess().catch(console.error);
}

module.exports = { testIndividualAccess };
