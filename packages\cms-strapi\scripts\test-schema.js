/**
 * Test script to understand the actual Strapi content type schemas
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';
const API_BASE = `${STRAPI_URL}/api`;

async function testSchema() {
  console.log('🔍 Testing Strapi content type schemas...');
  
  try {
    // Test 1: Create a simple category
    console.log('\n📁 Testing product-categories schema...');
    try {
      const categoryData = {
        name: 'Test Electronics',
        slug: 'test-electronics',
        description: 'Test category for electronics'
      };
      
      const categoryResponse = await axios.post(`${API_BASE}/product-categories`, {
        data: categoryData
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✅ Category created successfully:', categoryResponse.data);
    } catch (error) {
      console.log('❌ Category creation failed:', error.response?.data || error.message);
    }
    
    // Test 2: Create a simple banner
    console.log('\n🎨 Testing banners schema...');
    try {
      const bannerData = {
        title: 'Test Banner',
        description: 'Test banner description',
        active: true,
        position: 1
      };
      
      const bannerResponse = await axios.post(`${API_BASE}/banners`, {
        data: bannerData
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✅ Banner created successfully:', bannerResponse.data);
    } catch (error) {
      console.log('❌ Banner creation failed:', error.response?.data || error.message);
    }
    
    // Test 3: Create a simple product
    console.log('\n🛍️ Testing products schema...');
    try {
      const productData = {
        name: 'Test Product',
        description: 'Test product description',
        price: 999,
        sku: 'TEST-001'
      };
      
      const productResponse = await axios.post(`${API_BASE}/products`, {
        data: productData
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✅ Product created successfully:', productResponse.data);
    } catch (error) {
      console.log('❌ Product creation failed:', error.response?.data || error.message);
    }
    
    // Test 4: Create a simple page
    console.log('\n📄 Testing pages schema...');
    try {
      const pageData = {
        title: 'Test Page',
        slug: 'test-page',
        content: 'Test page content'
      };
      
      const pageResponse = await axios.post(`${API_BASE}/pages`, {
        data: pageData
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✅ Page created successfully:', pageResponse.data);
    } catch (error) {
      console.log('❌ Page creation failed:', error.response?.data || error.message);
    }
    
    // Test 5: Fetch all data to see what was created
    console.log('\n📊 Fetching all created data...');
    
    const [categories, banners, products, pages] = await Promise.all([
      axios.get(`${API_BASE}/product-categories`),
      axios.get(`${API_BASE}/banners`),
      axios.get(`${API_BASE}/products`),
      axios.get(`${API_BASE}/pages`)
    ]);
    
    console.log('Categories:', categories.data.data?.length || 0);
    console.log('Banners:', banners.data.data?.length || 0);
    console.log('Products:', products.data.data?.length || 0);
    console.log('Pages:', pages.data.data?.length || 0);
    
  } catch (error) {
    console.error('❌ Schema test failed:', error.message);
  }
}

testSchema();
