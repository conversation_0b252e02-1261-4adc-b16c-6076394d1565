/**
 * <PERSON><PERSON><PERSON> to verify the Strapi content migration progress
 * This script checks which pages exist and their content status
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1339';

// Expected pages after migration
const expectedPages = [
  { slug: 'about-us', title: 'About Us', template: 'about' },
  { slug: 'contact', title: 'Contact Us', template: 'contact' },
  { slug: 'privacy-policy', title: 'Privacy Policy', template: 'default' },
  { slug: 'terms', title: 'Terms and Conditions', template: 'default' },
  { slug: 'faq', title: 'Frequently Asked Questions', template: 'default', featured: true },
  { slug: 'help', title: 'Help & Support', template: 'default' },
];

async function checkPageExists(slug) {
  try {
    const response = await axios.get(
      `${STRAPI_URL}/api/pages?filters[slug][$eq]=${slug}&populate=*`
    );
    
    if (response.data.data.length > 0) {
      const page = response.data.data[0];
      return {
        exists: true,
        title: page.title,
        slug: page.slug,
        content: page.content,
        excerpt: page.excerpt,
        metaTitle: page.metaTitle,
        status: page.status,
        template: page.template,
        featured: page.featured,
        author: page.author,
        hasRichContent: page.content && page.content.length > 50,
        hasMetadata: !!(page.excerpt && page.metaTitle),
      };
    }
    
    return { exists: false };
  } catch (error) {
    console.error(`Error checking page ${slug}:`, error.message);
    return { exists: false, error: error.message };
  }
}

async function verifyMigration() {
  console.log('🔍 Verifying Strapi Content Migration Progress...\n');
  
  let totalPages = 0;
  let migratedPages = 0;
  let richContentPages = 0;
  let metadataPages = 0;
  
  for (const expectedPage of expectedPages) {
    totalPages++;
    console.log(`📄 Checking: ${expectedPage.title} (${expectedPage.slug})`);
    
    const pageStatus = await checkPageExists(expectedPage.slug);
    
    if (pageStatus.exists) {
      migratedPages++;
      console.log(`   ✅ EXISTS in Strapi`);
      console.log(`   📝 Title: "${pageStatus.title}"`);
      console.log(`   🏷️  Status: ${pageStatus.status || 'not set'}`);
      console.log(`   📋 Template: ${pageStatus.template || 'not set'}`);
      console.log(`   ⭐ Featured: ${pageStatus.featured || false}`);
      console.log(`   👤 Author: ${pageStatus.author || 'not set'}`);
      
      if (pageStatus.hasRichContent) {
        richContentPages++;
        console.log(`   📄 Content: ✅ Rich content (${pageStatus.content.length} chars)`);
      } else {
        console.log(`   📄 Content: ⚠️  Basic content (${pageStatus.content?.length || 0} chars)`);
      }
      
      if (pageStatus.hasMetadata) {
        metadataPages++;
        console.log(`   🔍 SEO: ✅ Has excerpt and meta title`);
      } else {
        console.log(`   🔍 SEO: ⚠️  Missing metadata`);
      }
      
    } else {
      console.log(`   ❌ NOT FOUND in Strapi`);
      if (pageStatus.error) {
        console.log(`   🚨 Error: ${pageStatus.error}`);
      }
    }
    
    console.log(''); // Empty line for readability
  }
  
  // Summary
  console.log('📊 MIGRATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total Expected Pages: ${totalPages}`);
  console.log(`Pages in Strapi: ${migratedPages}/${totalPages} (${Math.round(migratedPages/totalPages*100)}%)`);
  console.log(`Rich Content: ${richContentPages}/${migratedPages} (${migratedPages > 0 ? Math.round(richContentPages/migratedPages*100) : 0}%)`);
  console.log(`SEO Metadata: ${metadataPages}/${migratedPages} (${migratedPages > 0 ? Math.round(metadataPages/migratedPages*100) : 0}%)`);
  
  if (migratedPages === totalPages && richContentPages === totalPages && metadataPages === totalPages) {
    console.log('\n🎉 MIGRATION COMPLETE! All pages are in Strapi with rich content and metadata.');
  } else {
    console.log('\n⚠️  MIGRATION IN PROGRESS. Some pages still need to be added or updated.');
    
    // Show what's missing
    console.log('\n📋 TODO LIST:');
    for (const expectedPage of expectedPages) {
      const pageStatus = await checkPageExists(expectedPage.slug);
      if (!pageStatus.exists) {
        console.log(`   🔲 CREATE: ${expectedPage.title} (${expectedPage.slug})`);
      } else if (!pageStatus.hasRichContent) {
        console.log(`   🔲 UPDATE CONTENT: ${expectedPage.title} (${expectedPage.slug})`);
      } else if (!pageStatus.hasMetadata) {
        console.log(`   🔲 ADD METADATA: ${expectedPage.title} (${expectedPage.slug})`);
      }
    }
  }
}

async function testFrontendPages() {
  console.log('\n🌐 Testing Frontend Page Loading...\n');
  
  for (const expectedPage of expectedPages) {
    try {
      const response = await axios.get(`http://localhost:3001/${expectedPage.slug}`, {
        timeout: 5000,
        validateStatus: (status) => status < 500, // Accept 404s as valid responses
      });
      
      if (response.status === 200) {
        console.log(`✅ ${expectedPage.slug}: Frontend loads successfully`);
      } else {
        console.log(`⚠️  ${expectedPage.slug}: Frontend returns ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${expectedPage.slug}: Frontend error - ${error.message}`);
    }
  }
}

// Run verification
async function main() {
  await verifyMigration();
  await testFrontendPages();
  
  console.log('\n🔗 Quick Links:');
  console.log('   Strapi Admin: http://localhost:1339/admin/content-manager/collection-types/api::page.page');
  console.log('   Frontend: http://localhost:3001');
  console.log('   Content Reference: ./STRAPI_CONTENT_REFERENCE.md');
}

main().catch(console.error);
