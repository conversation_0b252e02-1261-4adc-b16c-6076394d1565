{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "Page", "description": "Static pages for the ONDC Seller Platform"}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "slug": {"type": "uid", "targetField": "title", "required": true, "unique": true}, "content": {"type": "richtext", "required": true}, "excerpt": {"type": "text", "maxLength": 500}, "metaTitle": {"type": "string", "maxLength": 60}, "metaDescription": {"type": "text", "maxLength": 160}, "status": {"type": "enumeration", "enum": ["draft", "published", "archived"], "default": "draft", "required": true}, "template": {"type": "enumeration", "enum": ["default", "landing", "contact", "about"], "default": "default", "required": true}, "featured": {"type": "boolean", "default": false}, "publishedAt": {"type": "datetime"}, "viewCount": {"type": "integer", "default": 0, "min": 0}, "author": {"type": "string", "required": true, "default": "Admin"}, "featured_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}