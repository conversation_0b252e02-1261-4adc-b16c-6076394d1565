{"kind": "collectionType", "collectionName": "store_configurations", "info": {"singularName": "store-configuration", "pluralName": "store-configurations", "displayName": "Store Configuration", "description": "Store configuration data for onboarding process"}, "options": {"draftAndPublish": true}, "attributes": {"store_name": {"type": "string", "required": true, "maxLength": 255}, "store_handle": {"type": "uid", "targetField": "store_name", "required": true, "unique": true}, "store_description": {"type": "text", "required": true, "maxLength": 1000}, "gst_number": {"type": "string", "required": true, "maxLength": 15, "minLength": 15}, "store_logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "store_theme": {"type": "enumeration", "enum": ["modern", "classic", "minimal", "vibrant", "elegant", "bold"], "default": "modern", "required": true}, "address_line_1": {"type": "string", "required": true, "maxLength": 255}, "address_line_2": {"type": "string", "required": false, "maxLength": 255}, "city": {"type": "string", "required": true, "maxLength": 100}, "state": {"type": "string", "required": true, "maxLength": 100}, "pincode": {"type": "string", "required": true, "maxLength": 10}, "country": {"type": "string", "required": true, "default": "India", "maxLength": 100}, "phone": {"type": "string", "required": true, "maxLength": 15}, "email": {"type": "email", "required": true}, "website": {"type": "string", "required": false, "maxLength": 255}, "business_type": {"type": "enumeration", "enum": ["individual", "partnership", "private_limited", "public_limited", "llp", "proprietorship"], "required": true}, "business_category": {"type": "enumeration", "enum": ["fashion", "electronics", "home_garden", "health_beauty", "sports_outdoors", "books_media", "food_beverages", "automotive", "toys_games", "jewelry_accessories", "other"], "required": true}, "onboarding_completed": {"type": "boolean", "default": false, "required": true}, "onboarding_step": {"type": "integer", "default": 1, "min": 1, "max": 3, "required": true}, "user_id": {"type": "string", "required": true}, "medusa_store_id": {"type": "string", "required": false}, "store_settings": {"type": "json", "required": false}, "social_media": {"type": "json", "required": false}, "operating_hours": {"type": "json", "required": false}, "shipping_zones": {"type": "json", "required": false}, "payment_methods": {"type": "json", "required": false}, "tax_settings": {"type": "json", "required": false}, "notification_preferences": {"type": "json", "required": false}, "store_status": {"type": "enumeration", "enum": ["draft", "pending_review", "active", "inactive", "suspended"], "default": "draft", "required": true}, "created_by_user": {"type": "string", "required": true}, "last_updated_by": {"type": "string", "required": false}}}