import type { Core } from '@strapi/strapi';

export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/* { strapi }: { strapi: Core.Strapi } */) {},

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  async bootstrap({ strapi }: { strapi: Core.Strapi }) {
    // Set permissions for public access to banners API
    try {
      const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
        where: { type: 'public' },
      });

      if (publicRole) {
        // Check if permissions already exist, if not create them
        const findPermission = await strapi.query('plugin::users-permissions.permission').findOne({
          where: {
            role: publicRole.id,
            action: 'api::banner.banner.find',
          },
        });

        const findOnePermission = await strapi
          .query('plugin::users-permissions.permission')
          .findOne({
            where: {
              role: publicRole.id,
              action: 'api::banner.banner.findOne',
            },
          });

        // Update or create find permission
        if (findPermission) {
          await strapi.query('plugin::users-permissions.permission').update({
            where: { id: findPermission.id },
            data: { enabled: true },
          });
        } else {
          await strapi.query('plugin::users-permissions.permission').create({
            data: {
              action: 'api::banner.banner.find',
              role: publicRole.id,
              enabled: true,
            },
          });
        }

        // Update or create findOne permission
        if (findOnePermission) {
          await strapi.query('plugin::users-permissions.permission').update({
            where: { id: findOnePermission.id },
            data: { enabled: true },
          });
        } else {
          await strapi.query('plugin::users-permissions.permission').create({
            data: {
              action: 'api::banner.banner.findOne',
              role: publicRole.id,
              enabled: true,
            },
          });
        }

        console.log('✅ Banner API permissions set for public access');
      }
    } catch (error) {
      console.error('❌ Error setting banner API permissions:', error);
    }

    // Set permissions for Page API
    try {
      const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
        where: { type: 'public' },
      });

      if (publicRole) {
        // Production permissions: only read access for public
        const pagePermissions = ['find', 'findOne'];

        for (const action of pagePermissions) {
          const existingPermission = await strapi
            .query('plugin::users-permissions.permission')
            .findOne({
              where: {
                role: publicRole.id,
                action: `api::page.page.${action}`,
              },
            });

          if (existingPermission) {
            await strapi.query('plugin::users-permissions.permission').update({
              where: { id: existingPermission.id },
              data: { enabled: true },
            });
          } else {
            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: `api::page.page.${action}`,
                role: publicRole.id,
                enabled: true,
              },
            });
          }
        }

        console.log('✅ Page API permissions set for public access');
      }
    } catch (error) {
      console.error('❌ Error setting page API permissions:', error);
    }

    // Set permissions for Product Category API
    try {
      const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
        where: { type: 'public' },
      });

      if (publicRole) {
        // Production permissions: only read access for public
        const categoryPermissions = ['find', 'findOne'];

        for (const action of categoryPermissions) {
          const existingPermission = await strapi
            .query('plugin::users-permissions.permission')
            .findOne({
              where: {
                role: publicRole.id,
                action: `api::product-category.product-category.${action}`,
              },
            });

          if (existingPermission) {
            await strapi.query('plugin::users-permissions.permission').update({
              where: { id: existingPermission.id },
              data: { enabled: true },
            });
          } else {
            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: `api::product-category.product-category.${action}`,
                role: publicRole.id,
                enabled: true,
              },
            });
          }
        }

        console.log('✅ Product Category API permissions set for public access');
      }
    } catch (error) {
      console.error('❌ Error setting product category API permissions:', error);
    }

    // Set permissions for Product API
    try {
      const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
        where: { type: 'public' },
      });

      if (publicRole) {
        // Production permissions: only read access for public
        const productPermissions = ['find', 'findOne'];

        for (const action of productPermissions) {
          const existingPermission = await strapi
            .query('plugin::users-permissions.permission')
            .findOne({
              where: {
                role: publicRole.id,
                action: `api::product.product.${action}`,
              },
            });

          if (existingPermission) {
            await strapi.query('plugin::users-permissions.permission').update({
              where: { id: existingPermission.id },
              data: { enabled: true },
            });
          } else {
            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: `api::product.product.${action}`,
                role: publicRole.id,
                enabled: true,
              },
            });
          }
        }

        console.log('✅ Product API permissions set for public access');
      }
    } catch (error) {
      console.error('❌ Error setting product API permissions:', error);
    }
  },
};
