const { createServer } = require('http');
const { parse } = require('url');
const { WebSocketServer } = require('ws');

const PORT = process.env.MCP_PORT || 3001;

// Create HTTP server
const server = createServer();
const wss = new WebSocketServer({ noServer: true });

// Handle WebSocket connections
wss.on('connection', (ws) => {
  console.log('MCP Client connected');

  // Handle incoming messages
  ws.on('message', (message) => {
    try {
      const msg = JSON.parse(message);
      console.log('Received MCP message:', msg);
      
      // Handle different MCP message types
      switch (msg.type) {
        case 'initialize':
          ws.send(JSON.stringify({
            type: 'initialized',
            version: '1.0.0',
            capabilities: ['query', 'mutate']
          }));
          break;
        // Add more message handlers as needed
      }
    } catch (err) {
      console.error('Error processing MCP message:', err);
    }
  });

  // Handle client disconnection
  ws.on('close', () => {
    console.log('MCP Client disconnected');
  });
});

// Handle HTTP server upgrade to WebSocket
server.on('upgrade', (request, socket, head) => {
  const { pathname } = parse(request.url);
  
  if (pathname === '/mcp') {
    wss.handleUpgrade(request, socket, head, (ws) => {
      wss.emit('connection', ws, request);
    });
  } else {
    socket.destroy();
  }
});

// Start the server
server.listen(PORT, () => {
  console.log(`MCP Server running on port ${PORT}`);
});

module.exports = server;
