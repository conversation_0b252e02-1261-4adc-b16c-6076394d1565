const axios = require('axios');
const fs = require('fs');

// Configuration
const BASE_URL = 'http://localhost:1339';
const OUTPUT_DIR = './api-responses';

// Create output directory if it doesn't exist
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR);
}

// Helper function to make API requests
async function makeRequest(endpoint, params = {}) {
  try {
    const url = `${BASE_URL}${endpoint}`;
    console.log(`Making request to: ${url}`);
    
    const response = await axios.get(url, { params });
    return response.data;
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error.response ? error.response.data : error.message);
    return null;
  }
}

// Helper function to save response to file
function saveResponse(endpoint, data) {
  if (!data) return;
  
  const filename = endpoint.replace(/\//g, '_').replace(/\?/g, '_').replace(/=/g, '_');
  const filePath = `${OUTPUT_DIR}/${filename}.json`;
  
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  console.log(`Response saved to ${filePath}`);
}

// Test all endpoints
async function testAllEndpoints() {
  console.log('Testing API endpoints...');
  
  // Test endpoints
  const endpoints = [
    // Sellers
    '/api/sellers',
    '/api/sellers?populate=products',
    
    // Product Categories
    '/api/product-categories',
    '/api/product-categories?populate[0]=parent&populate[1]=children',
    
    // Products
    '/api/products',
    '/api/products?populate[0]=seller&populate[1]=categories',
    '/api/products?populate=*',
    '/api/products?filters[featured][$eq]=true',
    
    // Customers
    '/api/customers',
    '/api/customers?populate=orders',
    
    // Orders
    '/api/orders',
    '/api/orders?populate[0]=customer&populate[1]=order_items',
    
    // Order Items
    '/api/order-items',
    '/api/order-items?populate[0]=product&populate[1]=order',
    
    // Banners
    '/api/banners',
    '/api/banners?filters[active][$eq]=true&populate=image',
    
    // Pages
    '/api/pages',
    '/api/pages?filters[slug][$eq]=about-us'
  ];
  
  // Test each endpoint
  for (const endpoint of endpoints) {
    const data = await makeRequest(endpoint);
    saveResponse(endpoint, data);
  }
  
  console.log('All endpoints tested!');
}

// Run the tests
testAllEndpoints();
