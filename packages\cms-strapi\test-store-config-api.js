/**
 * Test Store Configuration API
 */

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337';

async function testStoreConfigAPI() {
  console.log('🧪 Testing Store Configuration API...');

  try {
    // Test GET request
    console.log('📋 Testing GET /api/store-configurations...');
    const getResponse = await axios.get(`${STRAPI_URL}/api/store-configurations`);
    console.log('✅ GET request successful:', getResponse.status);
    console.log('📊 Current entries:', getResponse.data.data.length);

    // Test POST request with sample data
    console.log('\n📝 Testing POST /api/store-configurations...');
    const testData = {
      data: {
        store_name: 'Test Electronics Store',
        store_handle: 'test-electronics-store',
        store_description:
          'A premium electronics store offering the latest gadgets and accessories',
        gst_number: '27AABCU9603R1ZX',
        address_line_1: '123 Electronics Street',
        city: 'Mumbai',
        state: 'Maharashtra',
        pincode: '400001',
        phone: '+91 9876543210',
        email: '<EMAIL>',
        business_type: 'individual',
        business_category: 'electronics',
        user_id: 'test-user-123',
        created_by_user: 'test-user-123',
        payment_methods: ['cod', 'upi', 'credit_card', 'debit_card'],
      },
    };

    const postResponse = await axios.post(`${STRAPI_URL}/api/store-configurations`, testData);

    if (postResponse.status === 200 || postResponse.status === 201) {
      console.log('✅ POST request successful:', postResponse.status);
      console.log('📄 Created entry ID:', postResponse.data.data.id);

      // Clean up test data
      if (postResponse.data.data.id) {
        console.log('\n🧹 Cleaning up test data...');
        await axios.delete(`${STRAPI_URL}/api/store-configurations/${postResponse.data.data.id}`);
        console.log('✅ Test data cleaned up');
      }
    }

    console.log('\n🎉 All API tests passed!');
    return true;
  } catch (error) {
    console.error('\n❌ API test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
    return false;
  }
}

// Run the test
if (require.main === module) {
  testStoreConfigAPI();
}

module.exports = { testStoreConfigAPI };
