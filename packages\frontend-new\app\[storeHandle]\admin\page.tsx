// app/[storeHandle]/admin/page.tsx
"use client";

import { useProducts } from "@/hooks/useProducts";

export default function AdminPage() {
  const { data: products, isLoading, error } = useProducts();

  if (isLoading) return <p>Loading...</p>;
  if (error) return <p>Failed to fetch products</p>;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Products</h1>
      <ul className="space-y-2">
        {products?.map((product: any) => (
          <li key={product.id} className="p-4 bg-white shadow rounded-md">
            {product.title}
          </li>
        ))}
      </ul>
    </div>
  );
}
