// lib/api/client.ts
export const medusaClient = async <T>(
  endpoint: string,
  options: RequestInit = {},
): Promise<T> => {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_MEDUSA_API_URL}${endpoint}`,
    {
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      credentials: "include",
    },
  );

  if (!res.ok) {
    const error = await res.json();
    throw new Error(error.message || "Medusa API error");
  }

  return res.json();
};

export const strapiClient = async <T>(
  endpoint: string,
  options: RequestInit = {},
): Promise<T> => {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_STRAPI_API_URL}${endpoint}`,
    {
      ...options,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_TOKEN}`,
        ...options.headers,
      },
    },
  );

  if (!res.ok) {
    const error = await res.json();
    throw new Error(error.message || "Strapi API error");
  }

  return res.json();
};
