// lib/api/medusa/auth.ts
import { medusaClient } from "../client";

export const loginAdmin = (data: { email: string; password: string }) => {
  return medusaClient("/admin/auth", {
    method: "POST",
    body: JSON.stringify(data),
  });
};

export const registerAdmin = (data: { email: string; password: string }) => {
  return medusaClient("/admin/users", {
    method: "POST",
    body: JSON.stringify(data),
  });
};

export const resetPassword = (data: { token: string; password: string }) => {
  return medusaClient("/admin/reset-password", {
    method: "POST",
    body: JSON.stringify(data),
  });
};
