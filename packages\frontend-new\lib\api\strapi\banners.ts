import { strapiFetcher } from "./index";

export const getBanners = (storeId: number) =>
  strapiFetcher(`/banners?filters[store][id][$eq]=${storeId}&populate=*`);

export const createBanner = (data: any, token: string) =>
  strapiFetcher("/banners", "POST", { data }, token);

export const updateBanner = (id: number, data: any, token: string) =>
  strapiFetcher(`/banners/${id}`, "PUT", { data }, token);

export const deleteBanner = (id: number, token: string) =>
  strapiFetcher(`/banners/${id}`, "DELETE", undefined, token);
