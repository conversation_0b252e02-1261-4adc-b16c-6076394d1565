// lib/api/strapi/index.ts

const STRAPI_BASE_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL;

export const strapiFetcher = async <T>(
  path: string,
  method: "GET" | "POST" | "PUT" | "DELETE" = "GET",
  body?: unknown,
  token?: string,
): Promise<T> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (token) headers["Authorization"] = `Bearer ${token}`;

  const response = await fetch(`${STRAPI_BASE_URL}${path}`, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error?.message || "Strapi API Error");
  }

  return await response.json();
};
