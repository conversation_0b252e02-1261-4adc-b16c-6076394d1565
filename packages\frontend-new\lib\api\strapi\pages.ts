import { strapiFetcher } from "./index";

export const getStaticPages = (storeId: number) =>
  strapiFetcher(`/static-pages?filters[store][id][$eq]=${storeId}&populate=*`);

export const getPageBySlug = (slug: string) =>
  strapiFetcher(`/static-pages?filters[slug][$eq]=${slug}&populate=*`);

export const createPage = (data: any, token: string) =>
  strapiFetcher("/static-pages", "POST", { data }, token);

export const updatePage = (id: number, data: any, token: string) =>
  strapiFetcher(`/static-pages/${id}`, "PUT", { data }, token);

export const deletePage = (id: number, token: string) =>
  strapiFetcher(`/static-pages/${id}`, "DELETE", undefined, token);
