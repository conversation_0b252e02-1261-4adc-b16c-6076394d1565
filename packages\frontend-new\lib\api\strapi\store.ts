// lib/api/strapi/store.ts
import { strapiFetcher } from "./index";

export const getStoreDetails = (storeHandle: string) =>
  strapiFetcher(`/stores?filters[handle][$eq]=${storeHandle}&populate=*`);

export const updateStoreDetails = (id: number, data: any, token: string) =>
  strapiFetcher(`/stores/${id}`, "PUT", { data }, token);

export const createStore = (data: any, token: string) =>
  strapiFetcher("/stores", "POST", { data }, token);
