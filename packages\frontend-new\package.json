{"name": "multi-tenant-ecommerce-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.14", "@tanstack/react-query": "^5.20.0", "axios": "^1.6.8", "clsx": "^2.1.0", "next": "14.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.3", "zustand": "^4.5.2", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "20.11.30", "@types/react": "18.2.46", "@types/react-dom": "18.2.19", "autoprefixer": "^10.4.17", "eslint": "^8.57.0", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "prettier": "^3.2.5", "tailwindcss": "^3.4.3", "typescript": "5.3.3"}}