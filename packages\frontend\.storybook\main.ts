/**
 * Storybook Main Configuration
 *
 * Configuration for Storybook component development environment
 */

import type { StorybookConfig } from '@storybook/nextjs';
import path from 'path';

const config: StorybookConfig = {
  // Story files
  stories: [
    '../stories/**/*.stories.@(js|jsx|ts|tsx|mdx)',
    '../components/**/*.stories.@(js|jsx|ts|tsx|mdx)',
    '../app/**/*.stories.@(js|jsx|ts|tsx|mdx)',
  ],

  // Addons
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-a11y',
    '@storybook/addon-viewport',
    '@storybook/addon-backgrounds',
    '@storybook/addon-controls',
    '@storybook/addon-docs',
    '@storybook/addon-measure',
    '@storybook/addon-outline',
    '@storybook/addon-toolbars',
    '@storybook/addon-storysource',
    'storybook-dark-mode',
    '@chromatic-com/storybook',
  ],

  // Framework
  framework: {
    name: '@storybook/nextjs',
    options: {},
  },

  // TypeScript configuration
  typescript: {
    check: false,
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      propFilter: prop =>
        prop.parent ? !/node_modules/.test(prop.parent.fileName) : true,
    },
  },

  // Webpack configuration
  webpackFinal: async config => {
    // Add path aliases
    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        '@': path.resolve(__dirname, '../'),
        '@/components': path.resolve(__dirname, '../components'),
        '@/lib': path.resolve(__dirname, '../lib'),
        '@/hooks': path.resolve(__dirname, '../hooks'),
        '@/context': path.resolve(__dirname, '../context'),
        '@/contexts': path.resolve(__dirname, '../contexts'),
        '@/types': path.resolve(__dirname, '../types'),
        '@/utils': path.resolve(__dirname, '../utils'),
      };
    }

    // Handle CSS modules
    const cssRule = config.module?.rules?.find(rule => {
      if (typeof rule === 'object' && rule.test instanceof RegExp) {
        return rule.test.test('.css');
      }
      return false;
    });

    if (cssRule && typeof cssRule === 'object') {
      cssRule.exclude = /\.module\.css$/;
    }

    // Add CSS modules support
    config.module?.rules?.push({
      test: /\.module\.css$/,
      use: [
        'style-loader',
        {
          loader: 'css-loader',
          options: {
            modules: {
              localIdentName: '[name]__[local]--[hash:base64:5]',
            },
          },
        },
        'postcss-loader',
      ],
    });

    return config;
  },

  // Static directories
  staticDirs: ['../public'],

  // Features
  features: {
    experimentalRSC: true,
  },

  // Documentation
  docs: {
    autodocs: 'tag',
    defaultName: 'Documentation',
  },

  // Build configuration
  core: {
    disableTelemetry: true,
  },

  // Environment variables
  env: config => ({
    ...config,
    STORYBOOK: 'true',
    NEXT_PUBLIC_APP_ENV: 'storybook',
  }),
};

export default config;
