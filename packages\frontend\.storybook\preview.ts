/**
 * Storybook Preview Configuration
 * 
 * Global configuration for all stories
 */

import type { Preview } from '@storybook/react';
import { themes } from '@storybook/theming';
import '../app/globals.css';

// Mock Next.js router for Storybook
import { initialize, mswDecorator } from 'msw-storybook-addon';

// Initialize MSW
initialize();

const preview: Preview = {
  // Global parameters
  parameters: {
    // Actions
    actions: { argTypesRegex: '^on[A-Z].*' },
    
    // Controls
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
      expanded: true,
      sort: 'requiredFirst',
    },

    // Backgrounds
    backgrounds: {
      default: 'light',
      values: [
        {
          name: 'light',
          value: '#ffffff',
        },
        {
          name: 'dark',
          value: '#1a1a1a',
        },
        {
          name: 'gray',
          value: '#f5f5f5',
        },
      ],
    },

    // Viewport
    viewport: {
      viewports: {
        mobile: {
          name: 'Mobile',
          styles: {
            width: '375px',
            height: '667px',
          },
        },
        tablet: {
          name: 'Tablet',
          styles: {
            width: '768px',
            height: '1024px',
          },
        },
        desktop: {
          name: 'Desktop',
          styles: {
            width: '1024px',
            height: '768px',
          },
        },
        large: {
          name: 'Large Desktop',
          styles: {
            width: '1440px',
            height: '900px',
          },
        },
      },
    },

    // Documentation
    docs: {
      theme: themes.light,
      source: {
        state: 'open',
      },
    },

    // Dark mode
    darkMode: {
      dark: { ...themes.dark, appBg: 'black' },
      light: { ...themes.normal, appBg: 'white' },
      stylePreview: true,
    },

    // Layout
    layout: 'centered',

    // Options
    options: {
      storySort: {
        order: [
          'Introduction',
          'Design System',
          ['Colors', 'Typography', 'Spacing', 'Icons'],
          'Components',
          ['Basic', 'Forms', 'Navigation', 'Layout', 'Feedback'],
          'Pages',
          'Examples',
        ],
      },
    },

    // Accessibility
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'focus-trap',
            enabled: true,
          },
        ],
      },
    },
  },

  // Global decorators
  decorators: [
    // MSW decorator for API mocking
    mswDecorator,

    // Theme provider decorator
    (Story, context) => {
      const { theme } = context.globals;
      
      return (
        <div className={theme === 'dark' ? 'dark' : ''}>
          <div className="min-h-screen bg-background text-foreground">
            <Story />
          </div>
        </div>
      );
    },

    // Auth context decorator
    (Story) => {
      // Mock auth context for stories
      const mockAuthContext = {
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Storybook User',
          role: 'admin',
          avatar: '/images/avatars/default.svg',
          permissions: ['*'],
          lastLogin: new Date(),
          isActive: true,
          token: 'storybook-token',
        },
        isLoading: false,
        isAuthenticated: true,
        login: () => Promise.resolve(),
        logout: () => {},
        hasPermission: () => true,
        isAdmin: true,
      };

      return (
        <div data-auth-context={JSON.stringify(mockAuthContext)}>
          <Story />
        </div>
      );
    },

    // Tenant context decorator
    (Story) => {
      // Mock tenant context for stories
      const mockTenantContext = {
        selectedTenant: {
          id: 'storybook-tenant',
          name: 'Storybook Tenant',
          slug: 'storybook',
          domain: 'storybook.ondc.com',
          settings: {
            theme: 'default',
            currency: 'INR',
            timezone: 'Asia/Kolkata',
          },
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        loading: false,
        setSelectedTenant: () => {},
      };

      return (
        <div data-tenant-context={JSON.stringify(mockTenantContext)}>
          <Story />
        </div>
      );
    },

    // Responsive container decorator
    (Story, context) => {
      const { viewport } = context.parameters;
      const isMobile = viewport?.defaultViewport === 'mobile';
      
      return (
        <div className={`p-4 ${isMobile ? 'max-w-sm' : 'max-w-4xl'} mx-auto`}>
          <Story />
        </div>
      );
    },
  ],

  // Global types
  globalTypes: {
    theme: {
      description: 'Global theme for components',
      defaultValue: 'light',
      toolbar: {
        title: 'Theme',
        icon: 'paintbrush',
        items: [
          { value: 'light', title: 'Light', icon: 'sun' },
          { value: 'dark', title: 'Dark', icon: 'moon' },
        ],
        dynamicTitle: true,
      },
    },
    locale: {
      description: 'Internationalization locale',
      defaultValue: 'en',
      toolbar: {
        title: 'Locale',
        icon: 'globe',
        items: [
          { value: 'en', title: 'English', right: '🇺🇸' },
          { value: 'hi', title: 'Hindi', right: '🇮🇳' },
        ],
        dynamicTitle: true,
      },
    },
  },

  // Tags
  tags: ['autodocs'],
};

export default preview;
