/**
 * Categories Database API Route
 * 
 * Handles CRUD operations for categories using PostgreSQL database
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/client';

// GET /api/categories-db - Get all categories with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Query parameters
    const tenantId = searchParams.get('tenant_id') || '550e8400-e29b-41d4-a716-446655440000'; // Default to demo tenant
    const parentId = searchParams.get('parent_id');
    const limit = parseInt(searchParams.get('limit') || '50');

    console.log('🚀 Categories DB API: Fetching categories...');
    console.log('📊 Parameters:', { tenantId, parentId, limit });

    // Fetch categories using database client
    const categories = await db.getCategories({
      tenant_id: tenantId,
      parent_id: parentId === 'null' ? null : parentId,
      limit
    });

    console.log('✅ Categories DB API: Successfully fetched categories');
    console.log('📊 Categories count:', categories?.length || 0);

    // Transform categories to match frontend expectations
    const transformedCategories = (categories || []).map(category => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      parent_id: category.parent_id,
      sort_order: category.sort_order,
      is_active: category.is_active,
      metadata: category.metadata,
      created_at: category.created_at,
      updated_at: category.updated_at
    }));

    return NextResponse.json({
      data: transformedCategories,
      categories: transformedCategories, // For backward compatibility
      meta: {
        total: transformedCategories.length,
        tenant_id: tenantId
      }
    });

  } catch (error) {
    console.error('Categories DB API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch categories',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/categories-db - Create a new category
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const {
      tenant_id = '550e8400-e29b-41d4-a716-446655440000', // Default to demo tenant
      name,
      slug,
      description,
      parent_id,
      sort_order = 0,
      metadata = {}
    } = body;

    console.log('🚀 Creating new category:', { name, slug });

    // Validate required fields
    if (!name || !slug) {
      return NextResponse.json(
        { error: 'Name and slug are required' },
        { status: 400 }
      );
    }

    // Create new category using database client
    const results = await db.query(
      `INSERT INTO categories (
        tenant_id, name, slug, description, parent_id, sort_order, metadata, is_active
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8
      ) RETURNING *`,
      [
        tenant_id, name, slug, description, parent_id, sort_order, 
        JSON.stringify(metadata), true
      ]
    );

    const category = results[0];

    console.log('✅ Category created successfully:', category.id);

    return NextResponse.json({ 
      data: category,
      category 
    }, { status: 201 });

  } catch (error) {
    console.error('Categories DB POST API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create category',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
