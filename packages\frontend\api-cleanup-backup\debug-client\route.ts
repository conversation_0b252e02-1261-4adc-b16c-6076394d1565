import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('🔍 Debug API called from client');

    return NextResponse.json({
      success: true,
      message: 'Debug API working',
      timestamp: new Date().toISOString(),
      server: 'Next.js API Route',
    });
  } catch (error) {
    console.error('❌ Debug API error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
