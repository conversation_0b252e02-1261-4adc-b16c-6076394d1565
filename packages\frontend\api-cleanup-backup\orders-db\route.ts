/**
 * Orders Database API Route
 * 
 * Handles CRUD operations for orders using PostgreSQL database
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/client';

// GET /api/orders-db - Get all orders with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Query parameters
    const tenantId = searchParams.get('tenant_id') || '550e8400-e29b-41d4-a716-446655440000'; // Default to demo tenant
    const customerId = searchParams.get('customer_id');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    console.log('🚀 Orders DB API: Fetching orders...');
    console.log('📊 Parameters:', { tenantId, customerId, status, page, limit });

    // Fetch orders using database client
    const orders = await db.getOrders({
      tenant_id: tenantId,
      customer_id: customerId || undefined,
      status: status || undefined,
      limit,
      offset
    });

    console.log('✅ Orders DB API: Successfully fetched orders');
    console.log('📊 Orders count:', orders?.length || 0);

    // Transform orders to match frontend expectations
    const transformedOrders = (orders || []).map(order => ({
      id: order.id,
      order_number: order.order_number,
      status: order.status,
      total_amount: order.total_amount,
      currency: order.currency,
      customer: {
        id: order.customer_id,
        first_name: order.first_name,
        last_name: order.last_name,
        email: order.customer_email
      },
      items: order.items || [],
      shipping_address: order.shipping_address,
      billing_address: order.billing_address,
      payment_status: order.payment_status,
      fulfillment_status: order.fulfillment_status,
      notes: order.notes,
      metadata: order.metadata,
      created_at: order.created_at,
      updated_at: order.updated_at
    }));

    // Get total count for pagination (simplified for now)
    const totalCount = orders.length; // This would need a separate count query in production

    return NextResponse.json({
      data: transformedOrders,
      orders: transformedOrders, // For backward compatibility
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      },
      meta: {
        pagination: {
          page,
          pageSize: limit,
          pageCount: Math.ceil(totalCount / limit),
          total: totalCount
        }
      }
    });

  } catch (error) {
    console.error('Orders DB API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch orders',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/orders-db - Create a new order
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const {
      tenant_id = '550e8400-e29b-41d4-a716-446655440000', // Default to demo tenant
      customer_id,
      order_number,
      status = 'pending',
      total_amount,
      currency = 'INR',
      items = [],
      shipping_address,
      billing_address,
      payment_status = 'pending',
      fulfillment_status = 'pending',
      notes,
      metadata = {}
    } = body;

    console.log('🚀 Creating new order:', { order_number, total_amount, customer_id });

    // Validate required fields
    if (!customer_id || !total_amount || !items.length) {
      return NextResponse.json(
        { error: 'Customer ID, total amount, and items are required' },
        { status: 400 }
      );
    }

    // Generate order number if not provided
    const finalOrderNumber = order_number || `ORD-${Date.now()}`;

    // Create new order using database client
    const results = await db.query(
      `INSERT INTO orders (
        tenant_id, customer_id, order_number, status, total_amount, currency,
        shipping_address, billing_address, payment_status, fulfillment_status,
        notes, metadata
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
      ) RETURNING *`,
      [
        tenant_id, customer_id, finalOrderNumber, status, parseFloat(total_amount), currency,
        JSON.stringify(shipping_address), JSON.stringify(billing_address),
        payment_status, fulfillment_status, notes, JSON.stringify(metadata)
      ]
    );

    const order = results[0];

    // Create order items
    if (items.length > 0) {
      const itemInserts = items.map((item: any, index: number) => 
        `($1, $${2 + index * 6}, $${3 + index * 6}, $${4 + index * 6}, $${5 + index * 6}, $${6 + index * 6}, $${7 + index * 6})`
      ).join(', ');

      const itemValues = items.flatMap((item: any) => [
        order.id,
        item.name,
        item.sku || null,
        parseInt(item.quantity),
        parseFloat(item.unit_price),
        parseFloat(item.total_price || item.quantity * item.unit_price),
        JSON.stringify(item.metadata || {})
      ]);

      await db.query(
        `INSERT INTO order_items (
          order_id, name, sku, quantity, unit_price, total_price, metadata
        ) VALUES ${itemInserts}`,
        itemValues
      );
    }

    console.log('✅ Order created successfully:', order.id);

    return NextResponse.json({ 
      data: order,
      order 
    }, { status: 201 });

  } catch (error) {
    console.error('Orders DB POST API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create order',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/orders-db - Update an order
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('id');

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    console.log('🚀 Updating order:', orderId);

    const {
      status,
      payment_status,
      fulfillment_status,
      notes,
      metadata
    } = body;

    // Build update data
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex}`);
      updateValues.push(status);
      paramIndex++;
    }

    if (payment_status !== undefined) {
      updateFields.push(`payment_status = $${paramIndex}`);
      updateValues.push(payment_status);
      paramIndex++;
    }

    if (fulfillment_status !== undefined) {
      updateFields.push(`fulfillment_status = $${paramIndex}`);
      updateValues.push(fulfillment_status);
      paramIndex++;
    }

    if (notes !== undefined) {
      updateFields.push(`notes = $${paramIndex}`);
      updateValues.push(notes);
      paramIndex++;
    }

    if (metadata !== undefined) {
      updateFields.push(`metadata = $${paramIndex}::jsonb`);
      updateValues.push(JSON.stringify(metadata));
      paramIndex++;
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      );
    }

    updateFields.push(`updated_at = NOW()`);

    const results = await db.query(
      `UPDATE orders SET ${updateFields.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      [...updateValues, orderId]
    );

    const order = results[0];

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    console.log('✅ Order updated successfully:', orderId);

    return NextResponse.json({ 
      data: order,
      order 
    });

  } catch (error) {
    console.error('Orders DB PUT API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update order',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
