/**
 * Products Database API Route
 * 
 * Handles CRUD operations for products using PostgreSQL database
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/client';

// GET /api/products-db - Get all products with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Query parameters
    const tenantId = searchParams.get('tenant_id') || '550e8400-e29b-41d4-a716-446655440000'; // Default to demo tenant
    const status = searchParams.get('status') || 'published';
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    console.log('🚀 Products DB API: Fetching products from PostgreSQL...');
    console.log('📊 Parameters:', { tenantId, status, category, search, page, limit });

    // Fetch products using database client
    const products = await db.getProducts({
      tenant_id: tenantId,
      status,
      search,
      category,
      limit,
      offset
    });

    console.log('✅ Products DB API: Successfully fetched products');
    console.log('📊 Products count:', products?.length || 0);

    // Transform products to match frontend expectations
    const transformedProducts = (products || []).map(product => ({
      id: product.id,
      title: product.name,
      name: product.name,
      description: product.description || '',
      short_description: product.short_description || '',
      thumbnail: product.images?.[0] || `https://via.placeholder.com/300x300/3B82F6/FFFFFF?text=${encodeURIComponent(product.name)}`,
      images: product.images || [],
      handle: product.slug,
      slug: product.slug,
      price: product.price,
      compare_price: product.compare_price,
      sku: product.sku,
      inventory_quantity: product.inventory_quantity,
      status: product.status,
      tags: product.tags || [],
      weight: product.weight,
      dimensions: product.dimensions,
      metadata: product.metadata,
      categories: product.categories || [],
      created_at: product.created_at,
      updated_at: product.updated_at
    }));

    // Get total count for pagination (simplified for now)
    const totalCount = products.length; // This would need a separate count query in production

    return NextResponse.json({
      data: transformedProducts,
      products: transformedProducts, // For backward compatibility
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      },
      meta: {
        pagination: {
          page,
          pageSize: limit,
          pageCount: Math.ceil(totalCount / limit),
          total: totalCount
        }
      }
    });

  } catch (error) {
    console.error('Products DB API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch products',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/products-db - Create a new product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const {
      tenant_id = '550e8400-e29b-41d4-a716-446655440000', // Default to demo tenant
      name,
      slug,
      description,
      short_description,
      sku,
      price,
      compare_price,
      cost_price,
      track_inventory = true,
      inventory_quantity = 0,
      weight,
      dimensions = {},
      images = [],
      tags = [],
      metadata = {},
      seo_title,
      seo_description,
      status = 'draft'
    } = body;

    console.log('🚀 Creating new product:', { name, slug, price });

    // Validate required fields
    if (!name || !slug || price === undefined) {
      return NextResponse.json(
        { error: 'Name, slug, and price are required' },
        { status: 400 }
      );
    }

    // Create new product using database client
    const product = await db.createProduct({
      tenant_id,
      name,
      slug,
      description,
      short_description,
      sku,
      price: parseFloat(price),
      compare_price: compare_price ? parseFloat(compare_price) : undefined,
      cost_price: cost_price ? parseFloat(cost_price) : undefined,
      track_inventory,
      inventory_quantity: parseInt(inventory_quantity),
      weight: weight ? parseFloat(weight) : undefined,
      dimensions,
      images,
      tags,
      metadata,
      seo_title,
      seo_description,
      status
    });

    console.log('✅ Product created successfully:', product.id);

    return NextResponse.json({ 
      data: product,
      product 
    }, { status: 201 });

  } catch (error) {
    console.error('Products DB POST API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create product',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/products-db - Update a product
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('id');

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    console.log('🚀 Updating product:', productId);

    const {
      name,
      slug,
      description,
      short_description,
      sku,
      price,
      compare_price,
      cost_price,
      track_inventory,
      inventory_quantity,
      weight,
      dimensions,
      images,
      tags,
      metadata,
      seo_title,
      seo_description,
      status
    } = body;

    // Build update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (slug !== undefined) updateData.slug = slug;
    if (description !== undefined) updateData.description = description;
    if (short_description !== undefined) updateData.short_description = short_description;
    if (sku !== undefined) updateData.sku = sku;
    if (price !== undefined) updateData.price = parseFloat(price);
    if (compare_price !== undefined) updateData.compare_price = compare_price ? parseFloat(compare_price) : null;
    if (cost_price !== undefined) updateData.cost_price = cost_price ? parseFloat(cost_price) : null;
    if (track_inventory !== undefined) updateData.track_inventory = track_inventory;
    if (inventory_quantity !== undefined) updateData.inventory_quantity = parseInt(inventory_quantity);
    if (weight !== undefined) updateData.weight = weight ? parseFloat(weight) : null;
    if (dimensions !== undefined) updateData.dimensions = dimensions;
    if (images !== undefined) updateData.images = images;
    if (tags !== undefined) updateData.tags = tags;
    if (metadata !== undefined) updateData.metadata = metadata;
    if (seo_title !== undefined) updateData.seo_title = seo_title;
    if (seo_description !== undefined) updateData.seo_description = seo_description;
    if (status !== undefined) updateData.status = status;

    const product = await db.updateProduct(productId, updateData);

    console.log('✅ Product updated successfully:', productId);

    return NextResponse.json({ 
      data: product,
      product 
    });

  } catch (error) {
    console.error('Products DB PUT API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update product',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/products-db - Delete a product
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('id');

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    console.log('🚀 Deleting product:', productId);

    // Soft delete by setting status to archived
    const product = await db.updateProduct(productId, { status: 'archived' });

    console.log('✅ Product deleted successfully:', productId);

    return NextResponse.json({ 
      message: 'Product deleted successfully',
      product 
    });

  } catch (error) {
    console.error('Products DB DELETE API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete product',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
