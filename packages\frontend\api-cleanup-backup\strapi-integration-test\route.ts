import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('🚀 Testing real Strapi integration...');

    // Test categories API
    const categoriesResponse = await fetch(
      'http://localhost:3000/api/categories?featured=true&pageSize=10'
    );
    const categoriesResult = await categoriesResponse.json();

    // Test products API
    const productsResponse = await fetch(
      'http://localhost:3000/api/products?featured=true&pageSize=10'
    );
    const productsResult = await productsResponse.json();

    const result = {
      success: true,
      message: 'Real Strapi integration test successful',
      data: {
        categories: {
          success: categoriesResult.success,
          count: categoriesResult.data?.length || 0,
          items:
            categoriesResult.data?.map((cat: any) => ({
              id: cat.id,
              name: cat.name,
              slug: cat.slug,
              featured: cat.featured,
            })) || [],
        },
        products: {
          success: productsResult.success,
          count: productsResult.data?.length || 0,
          items:
            productsResult.data?.map((prod: any) => ({
              id: prod.id,
              name: prod.name,
              price: prod.price,
              featured: prod.featured,
            })) || [],
        },
      },
      endpoints: {
        categories: '/api/categories',
        products: '/api/products',
        note: 'Using real Strapi API endpoints (not test endpoints)',
      },
      timestamp: new Date().toISOString(),
    };

    console.log('✅ Real integration test result:', result);

    return NextResponse.json(result);
  } catch (error) {
    console.error('❌ Real Strapi integration test failed:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Real Strapi integration test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
