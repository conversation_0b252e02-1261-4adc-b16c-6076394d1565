/**
 * Database Connection Test API
 * 
 * Simple test to verify database connectivity
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing database connection...');
    
    // Create a simple Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
    
    console.log('📊 Supabase URL:', supabaseUrl);
    console.log('🔑 Using anon key:', supabaseKey.substring(0, 20) + '...');
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test 1: Check tenants table
    console.log('🏢 Testing tenants table...');
    const { data: tenants, error: tenantsError } = await supabase
      .from('tenants')
      .select('*')
      .limit(5);
    
    if (tenantsError) {
      console.error('❌ Tenants error:', tenantsError);
      return NextResponse.json({
        error: 'Tenants query failed',
        details: tenantsError.message,
        code: tenantsError.code
      }, { status: 500 });
    }
    
    console.log('✅ Tenants found:', tenants?.length || 0);
    
    // Test 2: Check products table
    console.log('📦 Testing products table...');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(5);
    
    if (productsError) {
      console.error('❌ Products error:', productsError);
      return NextResponse.json({
        error: 'Products query failed',
        details: productsError.message,
        code: productsError.code
      }, { status: 500 });
    }
    
    console.log('✅ Products found:', products?.length || 0);
    
    // Test 3: Check categories table
    console.log('📂 Testing categories table...');
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*')
      .limit(5);
    
    if (categoriesError) {
      console.error('❌ Categories error:', categoriesError);
      return NextResponse.json({
        error: 'Categories query failed',
        details: categoriesError.message,
        code: categoriesError.code
      }, { status: 500 });
    }
    
    console.log('✅ Categories found:', categories?.length || 0);
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      data: {
        tenants: tenants?.length || 0,
        products: products?.length || 0,
        categories: categories?.length || 0
      },
      config: {
        url: supabaseUrl,
        keyPrefix: supabaseKey.substring(0, 20) + '...'
      },
      sampleData: {
        tenants: tenants?.slice(0, 2) || [],
        products: products?.slice(0, 2) || [],
        categories: categories?.slice(0, 2) || []
      }
    });
    
  } catch (error) {
    console.error('💥 Database test error:', error);
    return NextResponse.json({
      error: 'Database test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
