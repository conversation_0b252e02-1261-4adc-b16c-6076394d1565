import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing Strapi API connection...');

    const strapiUrl =
      process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
    const apiToken = process.env.STRAPI_API_TOKEN;

    console.log('Strapi URL:', strapiUrl);
    console.log('API Token exists:', !!apiToken);

    // Test basic connection
    const response = await fetch(`${strapiUrl}/api/pages`, {
      headers: {
        'Content-Type': 'application/json',
        ...(apiToken && { Authorization: `Bearer ${apiToken}` }),
      },
    });

    console.log('Response status:', response.status);
    console.log(
      'Response headers:',
      Object.fromEntries(response.headers.entries())
    );

    if (!response.ok) {
      throw new Error(
        `Strapi API error: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    console.log('Strapi response data:', data);

    return NextResponse.json({
      success: true,
      message: 'Strapi API connection successful',
      data: data,
      config: {
        strapiUrl,
        hasApiToken: !!apiToken,
      },
    });
  } catch (error) {
    console.error('Strapi API test failed:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Strapi API connection failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        config: {
          strapiUrl:
            process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339',
          hasApiToken: !!process.env.STRAPI_API_TOKEN,
        },
      },
      { status: 500 }
    );
  }
}
