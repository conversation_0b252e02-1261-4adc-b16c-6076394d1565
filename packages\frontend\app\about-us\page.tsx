import React from 'react';
import Breadcrumbs from '@/components/Breadcrumbs';
import PageHero from '@/components/ui/PageHero';
import ContentCard from '@/components/ui/ContentCard';
import StyledContent from '@/components/ui/StyledContent';
import { getPageBySlug, type Page } from '@/lib/strapi-api';

// Server-side function to fetch page content
async function getAboutUsPageContent(): Promise<Page | null> {
  try {
    console.log('Server: Fetching about-us page content from Strapi...');
    const content = await getPageBySlug('about-us');
    console.log('Server: Fetched about-us content:', content?.title);
    return content;
  } catch (error) {
    console.error('Server: Error fetching about-us page:', error);
    return null;
  }
}

export default async function AboutUsPage() {
  // Fetch content on the server side
  const pageContent = await getAboutUsPageContent();

  // Fallback content if Strapi content is not available
  const fallbackContent: Page = {
    id: 1,
    title: 'About Us',
    slug: 'about-us',
    content: `<h2>Welcome to ONDC Seller Platform</h2>
<p>We are a marketplace connecting buyers and sellers across India through the Open Network for Digital Commerce (ONDC).</p>

<h2>Our Mission</h2>
<p>Our mission is to democratize digital commerce in India by providing a platform that enables sellers of all sizes to reach customers across the country.</p>

<h2>What We Offer</h2>
<ul>
<li>Seamless integration with ONDC network</li>
<li>Comprehensive seller tools and analytics</li>
<li>Multi-channel inventory management</li>
<li>Secure payment processing</li>
<li>24/7 customer support</li>
</ul>

<h2>Our Vision</h2>
<p>To create an inclusive digital commerce ecosystem that empowers every seller in India to participate in the digital economy and reach customers nationwide.</p>`,
    excerpt:
      'Learn about ONDC Seller Platform - connecting buyers and sellers across India through the Open Network for Digital Commerce.',
    metaTitle: 'About Us - ONDC Seller Platform',
    metaDescription:
      'Discover how ONDC Seller Platform is democratizing digital commerce in India by connecting sellers with customers nationwide.',
    status: 'published',
    template: 'about',
    featured: false,
    author: 'Admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // Use Strapi content if available, otherwise use fallback
  const displayContent = pageContent || fallbackContent;

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Hero Section */}
      <PageHero
        title={displayContent.title}
        description={
          displayContent.excerpt ||
          'Learn about ONDC Seller Platform - connecting buyers and sellers across India through the Open Network for Digital Commerce.'
        }
        icon={
          <svg
            className='w-12 h-12 text-white'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
            />
          </svg>
        }
        gradient='blue'
      />

      {/* Main Content */}
      <div className='px-4 sm:px-6 lg:px-8 py-12'>
        {/* Breadcrumbs */}
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'About Us', href: '/about-us', active: true },
          ]}
        />

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
          {/* Main Content */}
          <div className='lg:col-span-2'>
            <ContentCard variant='elevated' padding='xl'>
              <StyledContent content={displayContent.content} />
            </ContentCard>
          </div>

          {/* Sidebar with additional info */}
          <div className='lg:col-span-1 space-y-6'>
            <ContentCard
              variant='bordered'
              padding='lg'
              className='bg-gradient-to-br from-blue-50 to-indigo-50'
            >
              <h3 className='text-xl font-semibold text-gray-900 mb-4 flex items-center'>
                <svg
                  className='w-6 h-6 mr-2 text-blue-600'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M13 10V3L4 14h7v7l9-11h-7z'
                  />
                </svg>
                Quick Facts
              </h3>
              <div className='space-y-4'>
                <div className='flex items-start space-x-3'>
                  <div className='w-2 h-2 bg-blue-600 rounded-full mt-2'></div>
                  <div>
                    <p className='font-medium text-gray-900'>Founded</p>
                    <p className='text-gray-600'>2024</p>
                  </div>
                </div>
                <div className='flex items-start space-x-3'>
                  <div className='w-2 h-2 bg-blue-600 rounded-full mt-2'></div>
                  <div>
                    <p className='font-medium text-gray-900'>Mission</p>
                    <p className='text-gray-600'>
                      Democratizing digital commerce in India
                    </p>
                  </div>
                </div>
                <div className='flex items-start space-x-3'>
                  <div className='w-2 h-2 bg-blue-600 rounded-full mt-2'></div>
                  <div>
                    <p className='font-medium text-gray-900'>Network</p>
                    <p className='text-gray-600'>
                      Open Network for Digital Commerce (ONDC)
                    </p>
                  </div>
                </div>
              </div>
            </ContentCard>

            <ContentCard variant='default' padding='lg'>
              <h3 className='text-xl font-semibold text-gray-900 mb-4 flex items-center'>
                <svg
                  className='w-6 h-6 mr-2 text-green-600'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                Our Values
              </h3>
              <div className='space-y-3'>
                <div className='flex items-center space-x-2'>
                  <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                  <span className='text-gray-700'>Innovation</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                  <span className='text-gray-700'>Transparency</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                  <span className='text-gray-700'>Inclusivity</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                  <span className='text-gray-700'>Customer Focus</span>
                </div>
              </div>
            </ContentCard>
          </div>
        </div>
      </div>
    </div>
  );
}
