'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  User,
  Package,
  MapPin,
  CreditCard,
  Heart,
  Settings,
  LogOut,
  ChevronRight,
  Calendar,
  Truck,
} from 'lucide-react';
// Using real API data instead of mock data

const AccountDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock current user - in real app, this would come from auth context
  const currentUser = {
    id: '1',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    phone: '+91 **********',
  };
  const userOrders: any[] = []; // TODO: Replace with real API call
  const recentOrders = userOrders.slice(0, 3);

  const menuItems = [
    {
      id: 'overview',
      label: 'Overview',
      icon: User,
      href: '/account/dashboard',
    },
    { id: 'orders', label: 'Orders', icon: Package, href: '/account/orders' },
    {
      id: 'addresses',
      label: 'Addresses',
      icon: MapPin,
      href: '/account/addresses',
    },
    {
      id: 'payment',
      label: 'Payment Methods',
      icon: CreditCard,
      href: '/account/payment',
    },
    {
      id: 'wishlist',
      label: 'Wishlist',
      icon: Heart,
      href: '/account/wishlist',
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      href: '/account/settings',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'shipped':
        return 'bg-blue-100 text-blue-800';
      case 'confirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='px-4 sm:px-6 lg:px-8 py-8'>
        <div className='grid grid-cols-1 lg:grid-cols-4 gap-8'>
          {/* Sidebar */}
          <div className='lg:col-span-1'>
            <div className='bg-white rounded-lg shadow-sm border p-6'>
              <div className='flex items-center space-x-3 mb-6'>
                <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center'>
                  <User className='w-6 h-6 text-blue-600' />
                </div>
                <div>
                  <h2 className='font-semibold text-gray-900'>
                    {currentUser.first_name} {currentUser.last_name}
                  </h2>
                  <p className='text-sm text-gray-600'>{currentUser.email}</p>
                </div>
              </div>

              <nav className='space-y-1'>
                {menuItems.map(item => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.id}
                      href={item.href}
                      className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors ${
                        activeTab === item.id
                          ? 'bg-blue-50 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                      onClick={() => setActiveTab(item.id)}
                    >
                      <Icon className='w-4 h-4' />
                      <span>{item.label}</span>
                    </Link>
                  );
                })}

                <button className='flex items-center space-x-3 px-3 py-2 rounded-lg text-sm text-red-600 hover:bg-red-50 w-full'>
                  <LogOut className='w-4 h-4' />
                  <span>Sign Out</span>
                </button>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className='lg:col-span-3'>
            <div className='space-y-6'>
              {/* Welcome Section */}
              <div className='bg-white rounded-lg shadow-sm border p-6'>
                <h1 className='text-2xl font-bold text-gray-900 mb-2'>
                  Welcome back, {currentUser.first_name}!
                </h1>
                <p className='text-gray-600'>
                  Manage your account, track orders, and update your
                  preferences.
                </p>
              </div>

              {/* Quick Stats */}
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                <div className='bg-white rounded-lg shadow-sm border p-6'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <p className='text-sm font-medium text-gray-600'>
                        Total Orders
                      </p>
                      <p className='text-2xl font-bold text-gray-900'>0</p>
                    </div>
                    <Package className='w-8 h-8 text-blue-600' />
                  </div>
                </div>

                <div className='bg-white rounded-lg shadow-sm border p-6'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <p className='text-sm font-medium text-gray-600'>
                        Total Spent
                      </p>
                      <p className='text-2xl font-bold text-gray-900'>₹0</p>
                    </div>
                    <CreditCard className='w-8 h-8 text-green-600' />
                  </div>
                </div>

                <div className='bg-white rounded-lg shadow-sm border p-6'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <p className='text-sm font-medium text-gray-600'>
                        Saved Addresses
                      </p>
                      <p className='text-2xl font-bold text-gray-900'>0</p>
                    </div>
                    <MapPin className='w-8 h-8 text-purple-600' />
                  </div>
                </div>
              </div>

              {/* Recent Orders */}
              <div className='bg-white rounded-lg shadow-sm border'>
                <div className='p-6 border-b'>
                  <div className='flex items-center justify-between'>
                    <h2 className='text-lg font-semibold text-gray-900'>
                      Recent Orders
                    </h2>
                    <Link
                      href='/account/orders'
                      className='text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1'
                    >
                      <span>View all</span>
                      <ChevronRight className='w-4 h-4' />
                    </Link>
                  </div>
                </div>

                <div className='divide-y'>
                  {recentOrders.length > 0 ? (
                    recentOrders.map(order => (
                      <div key={order.id} className='p-6'>
                        <div className='flex items-center justify-between mb-3'>
                          <div className='flex items-center space-x-3'>
                            <div className='w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center'>
                              <Package className='w-5 h-5 text-gray-600' />
                            </div>
                            <div>
                              <p className='font-medium text-gray-900'>
                                Order #{order.order_number}
                              </p>
                              <p className='text-sm text-gray-600 flex items-center space-x-1'>
                                <Calendar className='w-3 h-3' />
                                <span>
                                  {new Date(
                                    order.created_at
                                  ).toLocaleDateString()}
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className='text-right'>
                            <p className='font-medium text-gray-900'>
                              ₹{order.total.toLocaleString()}
                            </p>
                            <span
                              className={`inline-block px-2 py-1 text-xs rounded-full ${getStatusColor(
                                order.status
                              )}`}
                            >
                              {order.status.charAt(0).toUpperCase() +
                                order.status.slice(1)}
                            </span>
                          </div>
                        </div>

                        <div className='flex items-center justify-between'>
                          <p className='text-sm text-gray-600'>
                            {order.items.length} item
                            {order.items.length > 1 ? 's' : ''}
                          </p>
                          <div className='flex items-center space-x-2'>
                            {order.status === 'shipped' && (
                              <button className='text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1'>
                                <Truck className='w-4 h-4' />
                                <span>Track</span>
                              </button>
                            )}
                            <Link
                              href={`/account/orders/${order.id}`}
                              className='text-gray-600 hover:text-gray-900 text-sm font-medium'
                            >
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className='p-6 text-center'>
                      <Package className='w-12 h-12 text-gray-400 mx-auto mb-3' />
                      <p className='text-gray-600'>No orders yet</p>
                      <Link
                        href='/products'
                        className='text-blue-600 hover:text-blue-700 text-sm font-medium'
                      >
                        Start shopping
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              {/* Quick Actions */}
              <div className='bg-white rounded-lg shadow-sm border p-6'>
                <h2 className='text-lg font-semibold text-gray-900 mb-4'>
                  Quick Actions
                </h2>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <Link
                    href='/account/addresses'
                    className='flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors'
                  >
                    <MapPin className='w-5 h-5 text-gray-600' />
                    <div>
                      <p className='font-medium text-gray-900'>
                        Manage Addresses
                      </p>
                      <p className='text-sm text-gray-600'>
                        Add or edit delivery addresses
                      </p>
                    </div>
                  </Link>

                  <Link
                    href='/account/wishlist'
                    className='flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors'
                  >
                    <Heart className='w-5 h-5 text-gray-600' />
                    <div>
                      <p className='font-medium text-gray-900'>View Wishlist</p>
                      <p className='text-sm text-gray-600'>
                        See your saved items
                      </p>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountDashboard;
