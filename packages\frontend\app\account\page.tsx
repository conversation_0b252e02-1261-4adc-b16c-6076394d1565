'use client';

import { useState } from 'react';
import Link from 'next/link';
import Breadcrumbs from '@/components/Breadcrumbs';
import {
  UserIcon,
  MapPinIcon,
  ClipboardDocumentListIcon,
  HeartIcon,
  Cog6ToothIcon,
  TruckIcon,
  CreditCardIcon,
  BellIcon,
  XMarkIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';
import Grid from '@mui/material/Grid';
import {
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormHelperText,
  Checkbox,
  FormControlLabel,
  Alert,
  Snackbar,
  CircularProgress,
  IconButton,
  Box,
  Typography,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import { Edit as EditIcon } from '@mui/icons-material';

// Sample customer data
const SAMPLE_CUSTOMER = {
  id: 'customer_123456',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+91 **********',
  dateOfBirth: '1990-05-15',
  gender: 'male',
  avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
  joinedDate: '2023-01-15T10:30:00Z',
  lastLogin: '2023-05-20T14:45:00Z',
  preferences: {
    newsletter: true,
    orderUpdates: true,
    promotions: false,
    smsNotifications: true,
  },
  addresses: [
    {
      id: 'addr_1',
      type: 'home',
      firstName: 'John',
      lastName: 'Doe',
      company: '',
      address1: '123 Main Street',
      address2: 'Apartment 4B',
      city: 'Mumbai',
      state: 'Maharashtra',
      postalCode: '400001',
      country: 'India',
      phone: '+91 **********',
      isDefault: true,
    },
    {
      id: 'addr_2',
      type: 'work',
      firstName: 'John',
      lastName: 'Doe',
      company: 'Tech Corp',
      address1: '456 Business Park',
      address2: 'Floor 5',
      city: 'Mumbai',
      state: 'Maharashtra',
      postalCode: '400002',
      country: 'India',
      phone: '+91 **********',
      isDefault: false,
    },
  ],
};

// Customer account tabs
const ACCOUNT_TABS = [
  { id: 'profile', name: 'Profile', icon: UserIcon },
  { id: 'addresses', name: 'Addresses', icon: MapPinIcon },
  { id: 'orders', name: 'Order History', icon: ClipboardDocumentListIcon },
  { id: 'preferences', name: 'Preferences', icon: Cog6ToothIcon },
];

// Form validation schemas
const validateProfile = (data: any) => {
  const errors: any = {};
  if (!data.firstName?.trim()) errors.firstName = 'First name is required';
  if (!data.lastName?.trim()) errors.lastName = 'Last name is required';
  if (!data.email?.trim()) errors.email = 'Email is required';
  else if (!/\S+@\S+\.\S+/.test(data.email)) errors.email = 'Email is invalid';
  if (!data.phone?.trim()) errors.phone = 'Phone number is required';
  return errors;
};

const validateAddress = (data: any) => {
  const errors: any = {};
  if (!data.firstName?.trim()) errors.firstName = 'First name is required';
  if (!data.lastName?.trim()) errors.lastName = 'Last name is required';
  if (!data.address1?.trim()) errors.address1 = 'Address is required';
  if (!data.city?.trim()) errors.city = 'City is required';
  if (!data.state?.trim()) errors.state = 'State is required';
  if (!data.postalCode?.trim()) errors.postalCode = 'Postal code is required';
  if (!data.country?.trim()) errors.country = 'Country is required';
  if (!data.phone?.trim()) errors.phone = 'Phone number is required';
  return errors;
};

export default function AccountPage() {
  const [activeTab, setActiveTab] = useState('profile');
  const [customer, setCustomer] = useState(SAMPLE_CUSTOMER);
  const [isEditing, setIsEditing] = useState(false);
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [editingAddress, setEditingAddress] = useState<any>(null);
  const [deleteConfirmAddress, setDeleteConfirmAddress] = useState<any>(null);
  const [profileErrors, setProfileErrors] = useState<any>({});
  const [addressErrors, setAddressErrors] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });
  const [addressForm, setAddressForm] = useState({
    type: 'home',
    firstName: '',
    lastName: '',
    company: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'India',
    phone: '',
    isDefault: false,
  });

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;

    // Handle nested properties (e.g., preferences.newsletter)
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      const parentValue = customer[parent as keyof typeof customer];
      setCustomer({
        ...customer,
        [parent]: {
          ...(typeof parentValue === 'object' && parentValue !== null
            ? parentValue
            : {}),
          [child]: value === 'true',
        },
      });
    } else {
      setCustomer({
        ...customer,
        [name]: value,
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    // Handle nested properties (e.g., preferences.newsletter)
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      const parentValue = customer[parent as keyof typeof customer];
      setCustomer({
        ...customer,
        [parent]: {
          ...(typeof parentValue === 'object' && parentValue !== null
            ? parentValue
            : {}),
          [child]: checked,
        },
      });
    } else {
      setCustomer({
        ...customer,
        [name]: checked,
      });
    }
  };

  // Handle profile form submission
  const handleProfileSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    const errors = validateProfile(customer);
    setProfileErrors(errors);

    if (Object.keys(errors).length === 0) {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setIsEditing(false);
        setIsProfileDialogOpen(false); // Close the dialog
        setSnackbar({
          open: true,
          message: 'Profile updated successfully!',
          severity: 'success',
        });
      } catch (error) {
        setSnackbar({
          open: true,
          message: 'Failed to update profile',
          severity: 'error',
        });
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle address form submission
  const handleAddressSubmit = async () => {
    const errors = validateAddress(addressForm);
    setAddressErrors(errors);

    if (Object.keys(errors).length === 0) {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (editingAddress) {
          // Update existing address
          const updatedAddresses = customer.addresses.map(addr =>
            addr.id === editingAddress.id
              ? { ...addressForm, id: editingAddress.id }
              : addr
          );
          setCustomer({ ...customer, addresses: updatedAddresses });
          setSnackbar({
            open: true,
            message: 'Address updated successfully!',
            severity: 'success',
          });
        } else {
          // Add new address
          const newAddress = { ...addressForm, id: `addr_${Date.now()}` };
          setCustomer({
            ...customer,
            addresses: [...customer.addresses, newAddress],
          });
          setSnackbar({
            open: true,
            message: 'Address added successfully!',
            severity: 'success',
          });
        }

        handleCloseAddressDialog();
      } catch (error) {
        setSnackbar({
          open: true,
          message: 'Failed to save address',
          severity: 'error',
        });
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle address deletion
  const handleDeleteAddress = async (addressId: string) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedAddresses = customer.addresses.filter(
        addr => addr.id !== addressId
      );
      setCustomer({ ...customer, addresses: updatedAddresses });
      setSnackbar({
        open: true,
        message: 'Address deleted successfully!',
        severity: 'success',
      });
      setDeleteConfirmAddress(null);
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to delete address',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle opening address dialog for editing
  const handleEditAddress = (address: any) => {
    setAddressForm(address);
    setEditingAddress(address);
    setAddressErrors({});
  };

  // Handle opening address dialog for adding
  const handleAddAddress = () => {
    setAddressForm({
      type: 'home',
      firstName: customer.firstName,
      lastName: customer.lastName,
      company: '',
      address1: '',
      address2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'India',
      phone: customer.phone,
      isDefault: false,
    });
    setEditingAddress(null);
    setAddressErrors({});
    setIsAddingAddress(true);
  };

  // Handle closing address dialog
  const handleCloseAddressDialog = () => {
    setIsAddingAddress(false);
    setEditingAddress(null);
    setAddressErrors({});
  };

  // Handle canceling profile edit
  const handleCancelEdit = () => {
    setCustomer(SAMPLE_CUSTOMER); // Reset to original data
    setIsEditing(false);
    setProfileErrors({});
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className='container mx-auto px-4 py-6'>
      {/* Breadcrumbs */}
      <Breadcrumbs
        items={[
          { label: 'Home', href: '/' },
          { label: 'Account', href: '/account', active: true },
        ]}
      />

      <div className='flex flex-col md:flex-row md:items-center md:justify-between mt-6 mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>My Account</h1>
          <p className='mt-2 text-gray-600'>
            Manage your profile, addresses, and preferences
          </p>
        </div>

        <div className='mt-4 md:mt-0'>
          {activeTab === 'profile' &&
            (isEditing ? (
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant='outlined'
                  onClick={handleCancelEdit}
                  disabled={loading}
                  startIcon={<XMarkIcon className='h-4 w-4' />}
                >
                  Cancel
                </Button>
                <Button
                  variant='contained'
                  onClick={handleProfileSubmit}
                  disabled={loading}
                  startIcon={
                    loading ? (
                      <CircularProgress size={16} />
                    ) : (
                      <CheckIcon className='h-4 w-4' />
                    )
                  }
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </Button>
              </Box>
            ) : (
              <Button
                variant='contained'
                onClick={() => setIsEditing(true)}
                startIcon={<UserIcon className='h-4 w-4' />}
              >
                Edit Profile
              </Button>
            ))}
          {activeTab === 'addresses' && (
            <Button
              variant='contained'
              onClick={handleAddAddress}
              startIcon={<MapPinIcon className='h-4 w-4' />}
            >
              Add New Address
            </Button>
          )}
        </div>
      </div>

      <div className='bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden'>
        {/* Account tabs */}
        <div className='border-b border-gray-200'>
          <div className='px-6 overflow-x-auto overflow-y-hidden'>
            <nav className='-mb-px flex space-x-6'>
              {ACCOUNT_TABS.map(tab => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                    whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2
                    ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                  >
                    <IconComponent className='h-4 w-4' />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Account content */}
        <div className='p-6'>
          {activeTab === 'profile' && (
            <div className='space-y-6'>
              {/* Enhanced Profile Header Card */}
              <Card
                sx={{
                  background:
                    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden',
                  borderRadius: 3,
                }}
              >
                {/* Background Pattern */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: -50,
                    right: -50,
                    width: 150,
                    height: 150,
                    background: 'rgba(255,255,255,0.1)',
                    borderRadius: '50%',
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -30,
                    left: -30,
                    width: 100,
                    height: 100,
                    background: 'rgba(255,255,255,0.05)',
                    borderRadius: '50%',
                  }}
                />

                <CardContent sx={{ p: 4, position: 'relative', zIndex: 1 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', md: 'row' },
                      alignItems: 'center',
                      gap: 4,
                    }}
                  >
                    <Box sx={{ position: 'relative' }}>
                      <Box
                        sx={{
                          width: 140,
                          height: 140,
                          borderRadius: '50%',
                          background: 'rgba(255,255,255,0.2)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backdropFilter: 'blur(10px)',
                          border: '3px solid rgba(255,255,255,0.3)',
                          boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                        }}
                      >
                        <img
                          src={customer.avatar}
                          alt={`${customer.firstName} ${customer.lastName}`}
                          style={{
                            width: 130,
                            height: 130,
                            borderRadius: '50%',
                            objectFit: 'cover',
                          }}
                        />
                      </Box>
                    </Box>
                    <Box
                      sx={{ flex: 1, textAlign: { xs: 'center', md: 'left' } }}
                    >
                      <Typography
                        variant='h3'
                        component='h3'
                        sx={{
                          fontWeight: 'bold',
                          mb: 1,
                          textShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        }}
                      >
                        {customer.firstName} {customer.lastName}
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: { xs: 'column', sm: 'row' },
                          alignItems: { xs: 'center', md: 'flex-start' },
                          gap: 2,
                          mb: 3,
                        }}
                      >
                        <Box
                          sx={{
                            px: 3,
                            py: 1,
                            borderRadius: 3,
                            bgcolor: 'rgba(255,255,255,0.2)',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(255,255,255,0.3)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                          }}
                        >
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: '#fbbf24',
                            }}
                          />
                          <Typography
                            variant='body1'
                            sx={{ fontWeight: 'medium' }}
                          >
                            Premium Customer
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            px: 3,
                            py: 1,
                            borderRadius: 3,
                            bgcolor: 'rgba(16, 185, 129, 0.2)',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(16, 185, 129, 0.4)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                          }}
                        >
                          <CheckIcon className='h-4 w-4' />
                          <Typography
                            variant='body1'
                            sx={{ fontWeight: 'medium' }}
                          >
                            Verified Account
                          </Typography>
                        </Box>
                      </Box>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: { xs: 'column', sm: 'row' },
                          gap: 4,
                        }}
                      >
                        <Box sx={{ textAlign: { xs: 'center', md: 'left' } }}>
                          <Typography
                            variant='body2'
                            sx={{ opacity: 0.8, mb: 0.5 }}
                          >
                            📅 Member since
                          </Typography>
                          <Typography
                            variant='h6'
                            sx={{ fontWeight: 'medium' }}
                          >
                            {formatDate(customer.joinedDate)}
                          </Typography>
                        </Box>
                        <Box sx={{ textAlign: { xs: 'center', md: 'left' } }}>
                          <Typography
                            variant='body2'
                            sx={{ opacity: 0.8, mb: 0.5 }}
                          >
                            🕒 Last active
                          </Typography>
                          <Typography
                            variant='h6'
                            sx={{ fontWeight: 'medium' }}
                          >
                            {formatDate(customer.lastLogin)}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              {/* Profile Information View Mode */}
              <Card
                sx={{
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  borderRadius: 3,
                  border: '1px solid rgba(0,0,0,0.05)',
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      mb: 4,
                    }}
                  >
                    <Typography
                      variant='h5'
                      sx={{
                        fontWeight: 'bold',
                        color: 'text.primary',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                      }}
                    >
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: 2,
                          bgcolor: 'primary.main',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                        }}
                      >
                        <UserIcon className='h-5 w-5' />
                      </Box>
                      Personal Information
                    </Typography>
                    <Button
                      variant='outlined'
                      startIcon={<EditIcon />}
                      onClick={() => setIsProfileDialogOpen(true)}
                      sx={{
                        borderRadius: 2,
                        textTransform: 'none',
                        fontWeight: 'medium',
                        px: 3,
                      }}
                    >
                      Edit Profile
                    </Button>
                  </Box>

                  {/* Profile Information Display */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                    <Box sx={{ flex: '1 1 300px', minWidth: '300px' }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          mb: 3,
                        }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 2,
                            bgcolor: 'rgba(103, 126, 234, 0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <UserIcon className='h-6 w-6 text-blue-600' />
                        </Box>
                        <Box>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mb: 0.5 }}
                          >
                            Full Name
                          </Typography>
                          <Typography
                            variant='h6'
                            sx={{ fontWeight: 'medium' }}
                          >
                            {customer.firstName} {customer.lastName}
                          </Typography>
                        </Box>
                      </Box>

                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          mb: 3,
                        }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 2,
                            bgcolor: 'rgba(16, 185, 129, 0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <svg
                            className='h-6 w-6 text-green-600'
                            fill='none'
                            viewBox='0 0 24 24'
                            stroke='currentColor'
                          >
                            <path
                              strokeLinecap='round'
                              strokeLinejoin='round'
                              strokeWidth={2}
                              d='M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
                            />
                          </svg>
                        </Box>
                        <Box>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mb: 0.5 }}
                          >
                            Email Address
                          </Typography>
                          <Typography
                            variant='h6'
                            sx={{ fontWeight: 'medium' }}
                          >
                            {customer.email}
                          </Typography>
                        </Box>
                      </Box>

                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 2 }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 2,
                            bgcolor: 'rgba(245, 158, 11, 0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <svg
                            className='h-6 w-6 text-yellow-600'
                            fill='none'
                            viewBox='0 0 24 24'
                            stroke='currentColor'
                          >
                            <path
                              strokeLinecap='round'
                              strokeLinejoin='round'
                              strokeWidth={2}
                              d='M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z'
                            />
                          </svg>
                        </Box>
                        <Box>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mb: 0.5 }}
                          >
                            Phone Number
                          </Typography>
                          <Typography
                            variant='h6'
                            sx={{ fontWeight: 'medium' }}
                          >
                            {customer.phone}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>

                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          mb: 3,
                        }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 2,
                            bgcolor: 'rgba(139, 69, 19, 0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <svg
                            className='h-6 w-6 text-amber-700'
                            fill='none'
                            viewBox='0 0 24 24'
                            stroke='currentColor'
                          >
                            <path
                              strokeLinecap='round'
                              strokeLinejoin='round'
                              strokeWidth={2}
                              d='M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8m-8 0a4 4 0 00-4 4v2a2 2 0 002 2h12a2 2 0 002-2v-2a4 4 0 00-4-4'
                            />
                          </svg>
                        </Box>
                        <Box>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mb: 0.5 }}
                          >
                            Date of Birth
                          </Typography>
                          <Typography
                            variant='h6'
                            sx={{ fontWeight: 'medium' }}
                          >
                            {formatDate(customer.dateOfBirth)}
                          </Typography>
                        </Box>
                      </Box>

                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          mb: 3,
                        }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 2,
                            bgcolor: 'rgba(168, 85, 247, 0.1)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <svg
                            className='h-6 w-6 text-purple-600'
                            fill='none'
                            viewBox='0 0 24 24'
                            stroke='currentColor'
                          >
                            <path
                              strokeLinecap='round'
                              strokeLinejoin='round'
                              strokeWidth={2}
                              d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
                            />
                          </svg>
                        </Box>
                        <Box>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mb: 0.5 }}
                          >
                            Gender
                          </Typography>
                          <Typography
                            variant='h6'
                            sx={{
                              fontWeight: 'medium',
                              textTransform: 'capitalize',
                            }}
                          >
                            {customer.gender}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Profile Edit Modal */}
          <Dialog
            open={isProfileDialogOpen}
            onClose={() => setIsProfileDialogOpen(false)}
            maxWidth='md'
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: 3,
                boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
              },
            }}
          >
            <DialogTitle
              sx={{
                p: 3,
                borderBottom: '1px solid rgba(0,0,0,0.1)',
                display: 'flex',
                alignItems: 'center',
                gap: 2,
              }}
            >
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 2,
                  bgcolor: 'primary.main',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                }}
              >
                <EditIcon />
              </Box>
              <Box>
                <Typography variant='h5' sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  Edit Profile
                </Typography>
                <Typography variant='body2' color='text.secondary'>
                  Update your personal information
                </Typography>
              </Box>
            </DialogTitle>

            <DialogContent sx={{ p: 4 }}>
              <Box component='form' onSubmit={handleProfileSubmit}>
                {/* Avatar Section */}
                <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
                  <Box sx={{ position: 'relative' }}>
                    <Box
                      sx={{
                        width: 120,
                        height: 120,
                        borderRadius: '50%',
                        background:
                          'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        border: '4px solid white',
                        boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                      }}
                    >
                      <img
                        src={customer.avatar}
                        alt={`${customer.firstName} ${customer.lastName}`}
                        style={{
                          width: 112,
                          height: 112,
                          borderRadius: '50%',
                          objectFit: 'cover',
                        }}
                      />
                    </Box>
                    <input
                      type='file'
                      accept='image/*'
                      style={{ display: 'none' }}
                      id='avatar-upload'
                      onChange={e => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = event => {
                            setCustomer(prev => ({
                              ...prev,
                              avatar: event.target?.result as string,
                            }));
                          };
                          reader.readAsDataURL(file);
                        }
                      }}
                    />
                    <IconButton
                      component='label'
                      htmlFor='avatar-upload'
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        right: 0,
                        bgcolor: 'primary.main',
                        color: 'white',
                        width: 36,
                        height: 36,
                        '&:hover': {
                          bgcolor: 'primary.dark',
                          transform: 'scale(1.1)',
                        },
                        transition: 'all 0.2s ease',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                      }}
                    >
                      <svg
                        className='h-4 w-4'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z'
                        />
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M15 13a3 3 0 11-6 0 3 3 0 016 0z'
                        />
                      </svg>
                    </IconButton>
                  </Box>
                </Box>

                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label='First Name'
                      name='firstName'
                      value={customer.firstName}
                      onChange={handleInputChange}
                      error={!!profileErrors.firstName}
                      helperText={profileErrors.firstName}
                      required
                      variant='outlined'
                      sx={{ borderRadius: 2 }}
                    />
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label='Last Name'
                      name='lastName'
                      value={customer.lastName}
                      onChange={handleInputChange}
                      error={!!profileErrors.lastName}
                      helperText={profileErrors.lastName}
                      required
                      variant='outlined'
                      sx={{ borderRadius: 2 }}
                    />
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label='Email Address'
                      name='email'
                      type='email'
                      value={customer.email}
                      onChange={handleInputChange}
                      error={!!profileErrors.email}
                      helperText={profileErrors.email}
                      required
                      variant='outlined'
                      sx={{ borderRadius: 2 }}
                    />
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label='Phone Number'
                      name='phone'
                      value={customer.phone}
                      onChange={handleInputChange}
                      error={!!profileErrors.phone}
                      helperText={profileErrors.phone}
                      required
                      variant='outlined'
                      sx={{ borderRadius: 2 }}
                    />
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label='Date of Birth'
                      name='dateOfBirth'
                      type='date'
                      value={customer.dateOfBirth}
                      onChange={handleInputChange}
                      variant='outlined'
                      InputLabelProps={{ shrink: true }}
                      sx={{ borderRadius: 2 }}
                    />
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <FormControl fullWidth variant='outlined'>
                      <InputLabel>Gender</InputLabel>
                      <Select
                        name='gender'
                        value={customer.gender}
                        onChange={e => handleInputChange(e as any)}
                        label='Gender'
                        sx={{ borderRadius: 2 }}
                      >
                        <MenuItem value='male'>Male</MenuItem>
                        <MenuItem value='female'>Female</MenuItem>
                        <MenuItem value='other'>Other</MenuItem>
                        <MenuItem value='prefer-not-to-say'>
                          Prefer not to say
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>

            <DialogActions
              sx={{ p: 3, borderTop: '1px solid rgba(0,0,0,0.1)' }}
            >
              <Button
                onClick={() => setIsProfileDialogOpen(false)}
                variant='outlined'
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 'medium',
                  px: 3,
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleProfileSubmit}
                variant='contained'
                disabled={loading}
                startIcon={
                  loading ? (
                    <CircularProgress size={16} />
                  ) : (
                    <CheckIcon className='h-4 w-4' />
                  )
                }
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 'medium',
                  px: 3,
                  ml: 2,
                }}
              >
                {loading ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogActions>
          </Dialog>

          {activeTab === 'addresses' && (
            <Box sx={{ mt: 3 }}>
              <Box sx={{ mb: 4 }}>
                <Typography variant='h5' component='h3' gutterBottom>
                  Saved Addresses
                </Typography>
                <Typography variant='body2' color='text.secondary'>
                  Manage your delivery addresses for faster checkout.
                </Typography>
              </Box>

              <Box sx={{ maxWidth: '1200px', mx: 'auto' }}>
                <Grid container spacing={3}>
                  {customer.addresses.map(address => (
                    <Grid size={{ xs: 12, lg: 6 }} key={address.id}>
                      <Card
                        variant='outlined'
                        sx={{
                          height: '100%',
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                            transform: 'translateY(-2px)',
                          },
                        }}
                      >
                        <CardContent sx={{ p: 3, height: '100%' }}>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'flex-start',
                              height: '100%',
                            }}
                          >
                            <Box sx={{ flex: 1, pr: 2 }}>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                  mb: 2,
                                }}
                              >
                                <Typography
                                  variant='h6'
                                  component='h4'
                                  sx={{
                                    textTransform: 'capitalize',
                                    fontWeight: 'bold',
                                    color: 'primary.main',
                                  }}
                                >
                                  {address.type}
                                </Typography>
                                {address.isDefault && (
                                  <Box
                                    sx={{
                                      px: 1.5,
                                      py: 0.5,
                                      borderRadius: 1,
                                      bgcolor: 'success.main',
                                      color: 'white',
                                      fontSize: '0.75rem',
                                      fontWeight: 'bold',
                                    }}
                                  >
                                    Default
                                  </Box>
                                )}
                              </Box>
                              <Box
                                sx={{
                                  color: 'text.secondary',
                                  lineHeight: 1.6,
                                }}
                              >
                                <Typography
                                  variant='body1'
                                  sx={{
                                    fontWeight: 'medium',
                                    color: 'text.primary',
                                    mb: 1,
                                  }}
                                >
                                  {address.firstName} {address.lastName}
                                </Typography>
                                {address.company && (
                                  <Typography variant='body2' sx={{ mb: 0.5 }}>
                                    {address.company}
                                  </Typography>
                                )}
                                <Typography variant='body2' sx={{ mb: 0.5 }}>
                                  {address.address1}
                                </Typography>
                                {address.address2 && (
                                  <Typography variant='body2' sx={{ mb: 0.5 }}>
                                    {address.address2}
                                  </Typography>
                                )}
                                <Typography variant='body2' sx={{ mb: 0.5 }}>
                                  {address.city}, {address.state}{' '}
                                  {address.postalCode}
                                </Typography>
                                <Typography variant='body2' sx={{ mb: 1 }}>
                                  {address.country}
                                </Typography>
                                <Typography
                                  variant='body2'
                                  sx={{
                                    fontWeight: 'medium',
                                    color: 'primary.main',
                                  }}
                                >
                                  📞 {address.phone}
                                </Typography>
                              </Box>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: 1,
                                minWidth: '80px',
                              }}
                            >
                              <Button
                                size='small'
                                variant='outlined'
                                onClick={() => handleEditAddress(address)}
                                sx={{
                                  fontSize: '0.875rem',
                                  minWidth: '70px',
                                }}
                              >
                                Edit
                              </Button>
                              {!address.isDefault && (
                                <Button
                                  size='small'
                                  variant='outlined'
                                  color='error'
                                  onClick={() =>
                                    setDeleteConfirmAddress(address)
                                  }
                                  sx={{
                                    fontSize: '0.875rem',
                                    minWidth: '70px',
                                  }}
                                >
                                  Delete
                                </Button>
                              )}
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Box>
          )}

          {activeTab === 'orders' && (
            <div className='space-y-6'>
              <div>
                <h3 className='text-lg font-medium leading-6 text-gray-900'>
                  Order History
                </h3>
                <p className='mt-1 text-sm text-gray-500'>
                  View and track your recent orders.
                </p>
              </div>

              <div className='text-center py-12'>
                <ClipboardDocumentListIcon className='mx-auto h-12 w-12 text-gray-400' />
                <h3 className='mt-2 text-sm font-medium text-gray-900'>
                  No orders yet
                </h3>
                <p className='mt-1 text-sm text-gray-500'>
                  Start shopping to see your orders here.
                </p>
                <div className='mt-6'>
                  <Link
                    href='/'
                    className='inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  >
                    Start Shopping
                  </Link>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'wishlist' && (
            <div className='space-y-6'>
              <div>
                <h3 className='text-lg font-medium leading-6 text-gray-900'>
                  My Wishlist
                </h3>
                <p className='mt-1 text-sm text-gray-500'>
                  Save items you love for later.
                </p>
              </div>

              <div className='text-center py-12'>
                <HeartIcon className='mx-auto h-12 w-12 text-gray-400' />
                <h3 className='mt-2 text-sm font-medium text-gray-900'>
                  Your wishlist is empty
                </h3>
                <p className='mt-1 text-sm text-gray-500'>
                  Add items to your wishlist to save them for later.
                </p>
                <div className='mt-6'>
                  <Link
                    href='/'
                    className='inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  >
                    Browse Products
                  </Link>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className='space-y-6'>
              <div>
                <h3 className='text-lg font-medium leading-6 text-gray-900'>
                  Communication Preferences
                </h3>
                <p className='mt-1 text-sm text-gray-500'>
                  Choose how you'd like to receive updates and notifications.
                </p>
              </div>

              <div className='space-y-4'>
                <div className='flex items-center justify-between py-4 border-b border-gray-200'>
                  <div>
                    <h4 className='text-base font-medium text-gray-900'>
                      Newsletter
                    </h4>
                    <p className='text-sm text-gray-500'>
                      Receive our weekly newsletter with new products and offers
                    </p>
                  </div>
                  <div className='flex items-center'>
                    <button
                      type='button'
                      className={`
                      relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                      ${
                        customer.preferences.newsletter
                          ? 'bg-blue-600'
                          : 'bg-gray-200'
                      }
                    `}
                      onClick={() =>
                        setCustomer({
                          ...customer,
                          preferences: {
                            ...customer.preferences,
                            newsletter: !customer.preferences.newsletter,
                          },
                        })
                      }
                    >
                      <span className='sr-only'>Toggle newsletter</span>
                      <span
                        className={`
                        pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200
                        ${
                          customer.preferences.newsletter
                            ? 'translate-x-5'
                            : 'translate-x-0'
                        }
                      `}
                      />
                    </button>
                  </div>
                </div>

                <div className='flex items-center justify-between py-4 border-b border-gray-200'>
                  <div>
                    <h4 className='text-base font-medium text-gray-900'>
                      Order Updates
                    </h4>
                    <p className='text-sm text-gray-500'>
                      Get notified about order status changes
                    </p>
                  </div>
                  <div className='flex items-center'>
                    <button
                      type='button'
                      className={`
                      relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                      ${
                        customer.preferences.orderUpdates
                          ? 'bg-blue-600'
                          : 'bg-gray-200'
                      }
                    `}
                      onClick={() =>
                        setCustomer({
                          ...customer,
                          preferences: {
                            ...customer.preferences,
                            orderUpdates: !customer.preferences.orderUpdates,
                          },
                        })
                      }
                    >
                      <span className='sr-only'>Toggle order updates</span>
                      <span
                        className={`
                        pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200
                        ${
                          customer.preferences.orderUpdates
                            ? 'translate-x-5'
                            : 'translate-x-0'
                        }
                      `}
                      />
                    </button>
                  </div>
                </div>

                <div className='flex items-center justify-between py-4 border-b border-gray-200'>
                  <div>
                    <h4 className='text-base font-medium text-gray-900'>
                      Promotions
                    </h4>
                    <p className='text-sm text-gray-500'>
                      Receive special offers and promotional emails
                    </p>
                  </div>
                  <div className='flex items-center'>
                    <button
                      type='button'
                      className={`
                      relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                      ${
                        customer.preferences.promotions
                          ? 'bg-blue-600'
                          : 'bg-gray-200'
                      }
                    `}
                      onClick={() =>
                        setCustomer({
                          ...customer,
                          preferences: {
                            ...customer.preferences,
                            promotions: !customer.preferences.promotions,
                          },
                        })
                      }
                    >
                      <span className='sr-only'>Toggle promotions</span>
                      <span
                        className={`
                        pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200
                        ${
                          customer.preferences.promotions
                            ? 'translate-x-5'
                            : 'translate-x-0'
                        }
                      `}
                      />
                    </button>
                  </div>
                </div>

                <div className='flex items-center justify-between py-4 border-b border-gray-200'>
                  <div>
                    <h4 className='text-base font-medium text-gray-900'>
                      SMS Notifications
                    </h4>
                    <p className='text-sm text-gray-500'>
                      Receive important updates via SMS
                    </p>
                  </div>
                  <div className='flex items-center'>
                    <button
                      type='button'
                      className={`
                      relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                      ${
                        customer.preferences.smsNotifications
                          ? 'bg-blue-600'
                          : 'bg-gray-200'
                      }
                    `}
                      onClick={() =>
                        setCustomer({
                          ...customer,
                          preferences: {
                            ...customer.preferences,
                            smsNotifications:
                              !customer.preferences.smsNotifications,
                          },
                        })
                      }
                    >
                      <span className='sr-only'>Toggle SMS notifications</span>
                      <span
                        className={`
                        pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200
                        ${
                          customer.preferences.smsNotifications
                            ? 'translate-x-5'
                            : 'translate-x-0'
                        }
                      `}
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Address Dialog */}
      <Dialog
        open={isAddingAddress || !!editingAddress}
        onClose={handleCloseAddressDialog}
        maxWidth='md'
        fullWidth
      >
        <DialogTitle>
          {editingAddress ? 'Edit Address' : 'Add New Address'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <FormControl fullWidth variant='outlined'>
                <InputLabel>Address Type</InputLabel>
                <Select
                  name='type'
                  value={addressForm.type}
                  onChange={e =>
                    setAddressForm({ ...addressForm, type: e.target.value })
                  }
                  label='Address Type'
                >
                  <MenuItem value='home'>Home</MenuItem>
                  <MenuItem value='work'>Work</MenuItem>
                  <MenuItem value='other'>Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={addressForm.isDefault}
                    onChange={e =>
                      setAddressForm({
                        ...addressForm,
                        isDefault: e.target.checked,
                      })
                    }
                  />
                }
                label='Set as default address'
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label='First Name'
                name='firstName'
                value={addressForm.firstName}
                onChange={e =>
                  setAddressForm({ ...addressForm, firstName: e.target.value })
                }
                error={!!addressErrors.firstName}
                helperText={addressErrors.firstName}
                required
                variant='outlined'
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label='Last Name'
                name='lastName'
                value={addressForm.lastName}
                onChange={e =>
                  setAddressForm({ ...addressForm, lastName: e.target.value })
                }
                error={!!addressErrors.lastName}
                helperText={addressErrors.lastName}
                required
                variant='outlined'
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label='Company (Optional)'
                name='company'
                value={addressForm.company}
                onChange={e =>
                  setAddressForm({ ...addressForm, company: e.target.value })
                }
                variant='outlined'
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label='Address Line 1'
                name='address1'
                value={addressForm.address1}
                onChange={e =>
                  setAddressForm({ ...addressForm, address1: e.target.value })
                }
                error={!!addressErrors.address1}
                helperText={addressErrors.address1}
                required
                variant='outlined'
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label='Address Line 2 (Optional)'
                name='address2'
                value={addressForm.address2}
                onChange={e =>
                  setAddressForm({ ...addressForm, address2: e.target.value })
                }
                variant='outlined'
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label='City'
                name='city'
                value={addressForm.city}
                onChange={e =>
                  setAddressForm({ ...addressForm, city: e.target.value })
                }
                error={!!addressErrors.city}
                helperText={addressErrors.city}
                required
                variant='outlined'
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label='State'
                name='state'
                value={addressForm.state}
                onChange={e =>
                  setAddressForm({ ...addressForm, state: e.target.value })
                }
                error={!!addressErrors.state}
                helperText={addressErrors.state}
                required
                variant='outlined'
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label='Postal Code'
                name='postalCode'
                value={addressForm.postalCode}
                onChange={e =>
                  setAddressForm({ ...addressForm, postalCode: e.target.value })
                }
                error={!!addressErrors.postalCode}
                helperText={addressErrors.postalCode}
                required
                variant='outlined'
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <FormControl fullWidth variant='outlined'>
                <InputLabel>Country</InputLabel>
                <Select
                  name='country'
                  value={addressForm.country}
                  onChange={e =>
                    setAddressForm({ ...addressForm, country: e.target.value })
                  }
                  label='Country'
                  error={!!addressErrors.country}
                >
                  <MenuItem value='India'>India</MenuItem>
                  <MenuItem value='United States'>United States</MenuItem>
                  <MenuItem value='United Kingdom'>United Kingdom</MenuItem>
                  <MenuItem value='Canada'>Canada</MenuItem>
                  <MenuItem value='Australia'>Australia</MenuItem>
                </Select>
                {addressErrors.country && (
                  <FormHelperText error>{addressErrors.country}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label='Phone Number'
                name='phone'
                value={addressForm.phone}
                onChange={e =>
                  setAddressForm({ ...addressForm, phone: e.target.value })
                }
                error={!!addressErrors.phone}
                helperText={addressErrors.phone}
                required
                variant='outlined'
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddressDialog} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleAddressSubmit}
            variant='contained'
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : null}
          >
            {loading
              ? 'Saving...'
              : editingAddress
                ? 'Update Address'
                : 'Add Address'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!deleteConfirmAddress}
        onClose={() => setDeleteConfirmAddress(null)}
        maxWidth='sm'
        fullWidth
      >
        <DialogTitle>Delete Address</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this address? This action cannot be
            undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteConfirmAddress(null)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={() => handleDeleteAddress(deleteConfirmAddress.id)}
            color='error'
            variant='contained'
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : null}
          >
            {loading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
}
