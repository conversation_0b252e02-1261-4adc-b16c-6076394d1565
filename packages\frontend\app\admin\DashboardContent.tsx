'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  ShoppingBagIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import ChartCard, {
  CustomerSplit<PERSON>hart,
  RefundRateChart,
  VisitorTrendsChart,
} from '../components/ChartCard';
import { formatCurrency } from '@/utils/formatCurrency';

import { DashboardContentProps } from './types';

export default function DashboardContent({
  stats,
  revenueTrend,
  customerSplit,
  refundRate,
  topProducts,
  recentOrders,
}: DashboardContentProps) {
  return (
    <div className='space-y-6'>
      {/* Page header */}
      <div className='md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8'>
        <div className='flex-1 min-w-0'>
          <h2 className='text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate'>
            Dashboard
          </h2>
          <p className='mt-1 text-sm text-gray-500'>
            Welcome back! Here's what's happening with your store today.
          </p>
        </div>
        <div className='mt-4 flex md:mt-0 md:ml-4'>
          <Link
            href='/admin/products/new'
            className='inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            <PlusIcon className='-ml-1 mr-2 h-5 w-5' />
            Add Product
          </Link>
        </div>
      </div>

      {/* Stats cards */}
      <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 px-4 sm:px-6 md:px-8'>
        {stats.map(stat => (
          <div
            key={stat.name}
            className='bg-white overflow-hidden shadow rounded-lg'
          >
            <div className='p-5'>
              <div className='flex items-center'>
                <div className='flex-shrink-0'>
                  <stat.icon className='h-6 w-6 text-gray-400' />
                </div>
                <div className='ml-5 w-0 flex-1'>
                  <dl>
                    <dt className='text-sm font-medium text-gray-500 truncate'>
                      {stat.name}
                    </dt>
                    <dd className='flex items-baseline'>
                      <div className='text-2xl font-semibold text-gray-900'>
                        {stat.value}
                      </div>
                      <div
                        className={`ml-2 flex items-baseline text-sm font-semibold ${
                          stat.changeType === 'increase'
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        {stat.changeType === 'increase' ? (
                          <ArrowTrendingUpIcon className='self-center flex-shrink-0 h-4 w-4 text-green-500' />
                        ) : (
                          <ArrowTrendingDownIcon className='self-center flex-shrink-0 h-4 w-4 text-red-500' />
                        )}
                        <span className='ml-1'>{stat.change}</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section - Responsive Grid */}
      <div className='grid grid-cols-1 gap-6 xl:grid-cols-2 2xl:grid-cols-4 px-4 sm:px-6 md:px-8'>
        {/* Revenue Trend - Stacked Area Chart */}
        <div className='xl:col-span-1 2xl:col-span-2'>
          <ChartCard
            title='Revenue Trend'
            description='Revenue distribution over time by channel'
            className='h-96'
          >
            <VisitorTrendsChart data={revenueTrend} />
          </ChartCard>
        </div>

        {/* Customer Split - Donut Chart */}
        <div className='xl:col-span-1 2xl:col-span-2'>
          <ChartCard
            title='Customer Split'
            description='Customer distribution by type'
            className='h-96'
          >
            <CustomerSplitChart data={customerSplit} />
          </ChartCard>
        </div>
      </div>

      {/* Enhanced Chart Section - Responsive Grid (Refund Rate - Donut Chart)*/}
      <div className='grid grid-cols-1 gap-6 xl:grid-cols-2 2xl:grid-cols-4 px-4 sm:px-6 md:px-8'>
        <div className='xl:col-span-1 2xl:col-span-2'>
          <ChartCard
            title='Refund Rate'
            description='Refunded vs Completed Orders'
            className='h-96'
          >
            <RefundRateChart data={refundRate} />
          </ChartCard>
        </div>

        {/* Top Products - Table */}
        <div className='xl:col-span-1 2xl:col-span-2'>
          <div className='bg-white shadow rounded-lg'>
            <div className='p-4 sm:p-6'>
              <h3 className='text-lg font-medium leading-6 text-gray-900'>
                Top Products
              </h3>
              <div className='mt-4 flow-root'>
                <div className='-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8'>
                  <div className='inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8'>
                    <table className='min-w-full divide-y divide-gray-300'>
                      <thead>
                        <tr>
                          <th
                            scope='col'
                            className='py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0'
                          >
                            Product
                          </th>
                          <th
                            scope='col'
                            className='px-3 py-3.5 text-left text-sm font-semibold text-gray-900'
                          >
                            Sales
                          </th>
                          <th
                            scope='col'
                            className='px-3 py-3.5 text-left text-sm font-semibold text-gray-900'
                          >
                            Revenue
                          </th>
                        </tr>
                      </thead>
                      <tbody className='divide-y divide-gray-200'>
                        {topProducts.map(product => (
                          <tr key={product.id}>
                            <td className='whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0'>
                              {product.title}
                            </td>
                            <td className='whitespace-nowrap px-3 py-4 text-sm text-gray-500'>
                              {product.sales}
                            </td>
                            <td className='whitespace-nowrap px-3 py-4 text-sm text-gray-500'>
                              {formatCurrency(product.revenue, 'INR')}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Orders Table */}
      <div className='px-4 sm:px-6 md:px-8'>
        <div className='bg-white shadow rounded-lg'>
          <div className='p-4 sm:p-6'>
            <h3 className='text-lg font-medium leading-6 text-gray-900'>
              Recent Orders
            </h3>
            <div className='mt-4 flow-root'>
              <div className='-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8'>
                <div className='inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8'>
                  <table className='min-w-full divide-y divide-gray-300'>
                    <thead>
                      <tr>
                        <th
                          scope='col'
                          className='py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0'
                        >
                          Order ID
                        </th>
                        <th
                          scope='col'
                          className='px-3 py-3.5 text-left text-sm font-semibold text-gray-900'
                        >
                          Customer
                        </th>
                        <th
                          scope='col'
                          className='px-3 py-3.5 text-left text-sm font-semibold text-gray-900'
                        >
                          Total
                        </th>
                        <th
                          scope='col'
                          className='px-3 py-3.5 text-left text-sm font-semibold text-gray-900'
                        >
                          Status
                        </th>
                        <th
                          scope='col'
                          className='px-3 py-3.5 text-left text-sm font-semibold text-gray-900'
                        >
                          Date
                        </th>
                        <th
                          scope='col'
                          className='relative py-3.5 pl-3 pr-4 sm:pr-0'
                        >
                          <span className='sr-only'>View</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className='divide-y divide-gray-200'>
                      {recentOrders.map(order => (
                        <tr key={order.id}>
                          <td className='whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0'>
                            {order.id}
                          </td>
                          <td className='whitespace-nowrap px-3 py-4 text-sm text-gray-500'>
                            <div className='font-medium text-gray-900'>
                              {order.customer}
                            </div>
                            <div className='text-gray-500'>{order.email}</div>
                          </td>
                          <td className='whitespace-nowrap px-3 py-4 text-sm text-gray-500'>
                            {formatCurrency(order.total, 'INR')}
                          </td>
                          <td className='whitespace-nowrap px-3 py-4 text-sm text-gray-500'>
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                order.status === 'Completed'
                                  ? 'bg-green-100 text-green-800'
                                  : order.status === 'Processing'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-gray-100 text-gray-800'
                              }`}
                            >
                              {order.status}
                            </span>
                          </td>
                          <td className='whitespace-nowrap px-3 py-4 text-sm text-gray-500'>
                            {order.date}
                          </td>
                          <td className='relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0'>
                            <Link
                              href={`/admin/orders/${order.id}`}
                              className='text-blue-600 hover:text-blue-900'
                            >
                              <EyeIcon className='h-5 w-5' />
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
