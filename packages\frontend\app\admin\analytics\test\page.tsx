'use client';

import React, { useState, useEffect } from 'react';
import { medusaAdminAPI } from '@/lib/medusa-admin-api';

interface TestResult {
  endpoint: string;
  status: 'loading' | 'success' | 'error';
  data?: any;
  error?: string;
  duration?: number;
}

export default function AnalyticsTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const endpoints = [
    {
      name: 'Dashboard Analytics',
      test: () => medusaAdminAPI.getDashboardAnalytics({ period: '30d' }),
    },
    {
      name: 'Sales Analytics',
      test: () => medusaAdminAPI.getSalesAnalytics({ period: '30d', group_by: 'day' }),
    },
    {
      name: 'Customer Analytics',
      test: () => medusaAdminAPI.getCustomerAnalytics({ period: '30d', segment: 'all' }),
    },
    {
      name: 'Product Analytics',
      test: () => medusaAdminAPI.getProductAnalytics({ period: '30d', limit: 10 }),
    },
    {
      name: 'Inventory Analytics',
      test: () => medusaAdminAPI.getInventoryAnalytics({ low_stock_threshold: 10 }),
    },
    {
      name: 'KPI Analytics',
      test: () => medusaAdminAPI.getKPIAnalytics({ period: '30d', compare_previous: true }),
    },
  ];

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    for (const endpoint of endpoints) {
      const startTime = Date.now();
      
      // Initialize test result
      const testResult: TestResult = {
        endpoint: endpoint.name,
        status: 'loading',
      };
      
      setTestResults(prev => [...prev, testResult]);

      try {
        const data = await endpoint.test();
        const duration = Date.now() - startTime;
        
        setTestResults(prev => 
          prev.map(result => 
            result.endpoint === endpoint.name
              ? { ...result, status: 'success', data, duration }
              : result
          )
        );
      } catch (error) {
        const duration = Date.now() - startTime;
        
        setTestResults(prev => 
          prev.map(result => 
            result.endpoint === endpoint.name
              ? { 
                  ...result, 
                  status: 'error', 
                  error: error instanceof Error ? error.message : 'Unknown error',
                  duration 
                }
              : result
          )
        );
      }
    }

    setIsRunning(false);
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'loading':
        return 'text-yellow-600 bg-yellow-50';
      case 'success':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'loading':
        return '⏳';
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '⚪';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Analytics API Test Suite
          </h1>
          <p className="text-gray-600 mb-6">
            Test all analytics endpoints to ensure they're working correctly with real data.
          </p>
          
          <button
            onClick={runTests}
            disabled={isRunning}
            className={`px-6 py-3 rounded-lg font-medium ${
              isRunning
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isRunning ? 'Running Tests...' : 'Run All Tests'}
          </button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-4">
            {testResults.map((result, index) => (
              <div key={index} className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getStatusIcon(result.status)}</span>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {result.endpoint}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(result.status)}`}>
                      {result.status.toUpperCase()}
                    </span>
                  </div>
                  
                  {result.duration && (
                    <span className="text-sm text-gray-500">
                      {result.duration}ms
                    </span>
                  )}
                </div>

                {result.status === 'loading' && (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span>Testing endpoint...</span>
                  </div>
                )}

                {result.status === 'error' && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="font-medium text-red-800 mb-2">Error Details:</h4>
                    <p className="text-red-700 text-sm font-mono">{result.error}</p>
                  </div>
                )}

                {result.status === 'success' && result.data && (
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h4 className="font-medium text-green-800 mb-2">✅ Success!</h4>
                      <p className="text-green-700 text-sm">
                        Endpoint responded successfully with data.
                      </p>
                    </div>

                    {/* Data Summary */}
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-800 mb-3">Data Summary:</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        {result.endpoint === 'Dashboard Analytics' && result.data.stats && (
                          <>
                            <div>
                              <span className="text-gray-600">Revenue:</span>
                              <div className="font-medium">₹{result.data.stats.totalRevenue?.toLocaleString('en-IN') || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Orders:</span>
                              <div className="font-medium">{result.data.stats.totalOrders || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Customers:</span>
                              <div className="font-medium">{result.data.stats.totalCustomers || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Products:</span>
                              <div className="font-medium">{result.data.stats.totalProducts || 0}</div>
                            </div>
                          </>
                        )}

                        {result.endpoint === 'Sales Analytics' && result.data.summary && (
                          <>
                            <div>
                              <span className="text-gray-600">Total Revenue:</span>
                              <div className="font-medium">₹{result.data.summary.total_revenue?.toLocaleString('en-IN') || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Total Orders:</span>
                              <div className="font-medium">{result.data.summary.total_orders || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Units Sold:</span>
                              <div className="font-medium">{result.data.summary.total_units_sold || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Growth Rate:</span>
                              <div className="font-medium">{result.data.summary.growth_rate?.toFixed(1) || 0}%</div>
                            </div>
                          </>
                        )}

                        {result.endpoint === 'Customer Analytics' && result.data.summary && (
                          <>
                            <div>
                              <span className="text-gray-600">Total Customers:</span>
                              <div className="font-medium">{result.data.summary.total_customers || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">New Customers:</span>
                              <div className="font-medium">{result.data.summary.new_customers || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Retention Rate:</span>
                              <div className="font-medium">{result.data.summary.customer_retention_rate?.toFixed(1) || 0}%</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Avg LTV:</span>
                              <div className="font-medium">₹{result.data.summary.average_customer_lifetime_value?.toLocaleString('en-IN') || 0}</div>
                            </div>
                          </>
                        )}

                        {result.endpoint === 'Product Analytics' && result.data.summary && (
                          <>
                            <div>
                              <span className="text-gray-600">Total Products:</span>
                              <div className="font-medium">{result.data.summary.total_products || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Active Products:</span>
                              <div className="font-medium">{result.data.summary.active_products || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Low Stock Alerts:</span>
                              <div className="font-medium">{result.data.summary.low_stock_alerts || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Conversion Rate:</span>
                              <div className="font-medium">{result.data.summary.average_conversion_rate?.toFixed(1) || 0}%</div>
                            </div>
                          </>
                        )}

                        {result.endpoint === 'Inventory Analytics' && result.data.summary && (
                          <>
                            <div>
                              <span className="text-gray-600">Total Items:</span>
                              <div className="font-medium">{result.data.summary.total_items || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">In Stock:</span>
                              <div className="font-medium">{result.data.summary.in_stock_items || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Low Stock:</span>
                              <div className="font-medium">{result.data.summary.low_stock_items || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Out of Stock:</span>
                              <div className="font-medium">{result.data.summary.out_of_stock_items || 0}</div>
                            </div>
                          </>
                        )}

                        {result.endpoint === 'KPI Analytics' && result.data.summary && (
                          <>
                            <div>
                              <span className="text-gray-600">Total Metrics:</span>
                              <div className="font-medium">{result.data.summary.total_metrics || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">With Targets:</span>
                              <div className="font-medium">{result.data.summary.metrics_with_targets || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Meeting Targets:</span>
                              <div className="font-medium">{result.data.summary.metrics_meeting_targets || 0}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Overall Score:</span>
                              <div className="font-medium">{result.data.performance_score?.overall_score || 0}%</div>
                            </div>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Raw Data Preview */}
                    <details className="bg-gray-50 border border-gray-200 rounded-lg">
                      <summary className="p-4 cursor-pointer font-medium text-gray-800 hover:bg-gray-100">
                        View Raw Response Data
                      </summary>
                      <div className="p-4 border-t border-gray-200">
                        <pre className="text-xs text-gray-600 overflow-auto max-h-64">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </div>
                    </details>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {testResults.length === 0 && !isRunning && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📊</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Ready to Test Analytics APIs
            </h3>
            <p className="text-gray-600">
              Click "Run All Tests" to test all analytics endpoints with real data.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
