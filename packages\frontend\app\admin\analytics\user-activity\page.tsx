'use client';

import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  UsersIcon,
  ClockIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  CursorArrowRaysIcon,
  MapPinIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';
import ChartCard, {
  EnhancedDonut<PERSON>hart,
  VisitorTrendsChart,
} from '../../../components/ChartCard';
import { SalesStatisticsChart } from '../../../components/InteractiveCharts';

interface AnalyticsMetric {
  name: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ComponentType<any>;
  description: string;
}

interface UserActivityData {
  pageViews: number;
  uniqueVisitors: number;
  avgSessionDuration: string;
  bounceRate: string;
  realTimeUsers: number;
}

interface GeographicData {
  country: string;
  users: number;
  percentage: number;
  flag: string;
}

interface DeviceData {
  device: string;
  users: number;
  percentage: number;
  sessions: number;
}

interface PageViewData {
  page: string;
  views: number;
  uniqueViews: number;
  avgTime: string;
  bounceRate: string;
}

export default function UserActivityAnalyticsPage() {
  console.log('inside analytics::::');
  const [dateRange, setDateRange] = useState('7days');
  const [realTimeUsers, setRealTimeUsers] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Mock real-time user counter
  useEffect(() => {
    const updateRealTimeUsers = () => {
      setRealTimeUsers(Math.floor(Math.random() * 50) + 10);
    };

    updateRealTimeUsers();
    const interval = setInterval(updateRealTimeUsers, 5000);

    // Simulate loading
    const loadingTimer = setTimeout(() => setIsLoading(false), 1000);

    return () => {
      clearInterval(interval);
      clearTimeout(loadingTimer);
    };
  }, []);

  const analyticsMetrics: AnalyticsMetric[] = [
    {
      name: 'Page Views',
      value: '45,678',
      change: '+12.5%',
      changeType: 'increase',
      icon: EyeIcon,
      description: 'Total page views in selected period',
    },
    {
      name: 'Unique Visitors',
      value: '8,234',
      change: '+8.2%',
      changeType: 'increase',
      icon: UsersIcon,
      description: 'Unique users who visited your site',
    },
    {
      name: 'Avg Session Duration',
      value: '4m 32s',
      change: '+15.3%',
      changeType: 'increase',
      icon: ClockIcon,
      description: 'Average time users spend on site',
    },
    {
      name: 'Bounce Rate',
      value: '34.2%',
      change: '-5.1%',
      changeType: 'decrease',
      icon: CursorArrowRaysIcon,
      description: 'Percentage of single-page sessions',
    },
  ];

  const geographicData: GeographicData[] = [
    { country: 'India', users: 3456, percentage: 42.0, flag: '🇮🇳' },
    { country: 'United States', users: 1234, percentage: 15.0, flag: '🇺🇸' },
    { country: 'United Kingdom', users: 987, percentage: 12.0, flag: '🇬🇧' },
    { country: 'Canada', users: 654, percentage: 8.0, flag: '🇨🇦' },
    { country: 'Australia', users: 432, percentage: 5.2, flag: '🇦🇺' },
    { country: 'Germany', users: 321, percentage: 3.9, flag: '🇩🇪' },
    { country: 'Others', users: 1150, percentage: 13.9, flag: '🌍' },
  ];

  const deviceData: DeviceData[] = [
    { device: 'Desktop', users: 4567, percentage: 55.4, sessions: 6789 },
    { device: 'Mobile', users: 2890, percentage: 35.1, sessions: 4123 },
    { device: 'Tablet', users: 777, percentage: 9.5, sessions: 1234 },
  ];

  const topPages: PageViewData[] = [
    {
      page: '/',
      views: 12456,
      uniqueViews: 8234,
      avgTime: '3m 45s',
      bounceRate: '28.5%',
    },
    {
      page: '/products',
      views: 8765,
      uniqueViews: 6543,
      avgTime: '5m 12s',
      bounceRate: '22.1%',
    },
    {
      page: '/categories',
      views: 5432,
      uniqueViews: 4321,
      avgTime: '2m 58s',
      bounceRate: '35.7%',
    },
    {
      page: '/about-us',
      views: 3210,
      uniqueViews: 2987,
      avgTime: '4m 23s',
      bounceRate: '31.2%',
    },
    {
      page: '/contact',
      views: 2109,
      uniqueViews: 1876,
      avgTime: '3m 15s',
      bounceRate: '42.8%',
    },
  ];

  const visitorTrendsData = {
    '7days': [
      { label: 'Mon', mobile: 1250, desktop: 2100 },
      { label: 'Tue', mobile: 1400, desktop: 2300 },
      { label: 'Wed', mobile: 1600, desktop: 2500 },
      { label: 'Thu', mobile: 1350, desktop: 2200 },
      { label: 'Fri', mobile: 1800, desktop: 2800 },
      { label: 'Sat', mobile: 2200, desktop: 3200 },
      { label: 'Sun', mobile: 1900, desktop: 2900 },
    ],
    '30days': [
      { label: 'Week 1', mobile: 8500, desktop: 15200 },
      { label: 'Week 2', mobile: 9200, desktop: 16800 },
      { label: 'Week 3', mobile: 8800, desktop: 15900 },
      { label: 'Week 4', mobile: 10100, desktop: 18500 },
    ],
    '3months': [
      { label: 'Month 1', mobile: 35000, desktop: 62000 },
      { label: 'Month 2', mobile: 38000, desktop: 68000 },
      { label: 'Month 3', mobile: 42000, desktop: 75000 },
    ],
  };

  const browserData = [
    { label: 'Chrome', value: 4250, color: '#4285F4' },
    { label: 'Safari', value: 2100, color: '#FF9500' },
    { label: 'Firefox', value: 1200, color: '#FF7139' },
    { label: 'Edge', value: 850, color: '#0078D4' },
    { label: 'Others', value: 600, color: '#6B7280' },
  ];

  const handleExportData = () => {
    // Mock export functionality
    const data = {
      metrics: analyticsMetrics,
      geographic: geographicData,
      devices: deviceData,
      topPages: topPages,
      exportDate: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `user-activity-analytics-${dateRange}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className='space-y-6 px-4 sm:px-6 md:px-8'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
          <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4'>
            {[...Array(4)].map((_, i) => (
              <div key={i} className='bg-white p-6 rounded-lg shadow'>
                <div className='h-4 bg-gray-200 rounded w-3/4 mb-2'></div>
                <div className='h-8 bg-gray-200 rounded w-1/2'></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Page Header */}
      <div className='md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8'>
        <div className='flex-1 min-w-0'>
          <h2 className='text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate'>
            User Activity Analytics
          </h2>
          <p className='mt-1 text-sm text-gray-500'>
            Comprehensive insights into user behavior and engagement patterns
          </p>
        </div>
        <div className='mt-4 flex space-x-3 md:mt-0 md:ml-4'>
          <select
            value={dateRange}
            onChange={e => setDateRange(e.target.value)}
            className='inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            <option value='today'>Today</option>
            <option value='7days'>Last 7 days</option>
            <option value='30days'>Last 30 days</option>
            <option value='3months'>Last 3 months</option>
          </select>
          <button
            onClick={handleExportData}
            className='inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            <ArrowDownTrayIcon className='-ml-1 mr-2 h-5 w-5' />
            Export Data
          </button>
        </div>
      </div>

      {/* Real-time Users Counter */}
      <div className='px-4 sm:px-6 md:px-8'>
        <div className='bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white'>
          <div className='flex items-center justify-between'>
            <div>
              <h3 className='text-lg font-medium'>Real-time Active Users</h3>
              <p className='text-blue-100'>
                Users currently browsing your site
              </p>
            </div>
            <div className='text-right'>
              <div className='text-3xl font-bold'>{realTimeUsers}</div>
              <div className='flex items-center text-blue-100'>
                <div className='w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2'></div>
                Live
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Analytics Metrics */}
      <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 px-4 sm:px-6 md:px-8'>
        {analyticsMetrics.map(metric => (
          <div
            key={metric.name}
            className='bg-white overflow-hidden shadow rounded-lg'
          >
            <div className='p-5'>
              <div className='flex items-center'>
                <div className='flex-shrink-0'>
                  <metric.icon className='h-6 w-6 text-gray-400' />
                </div>
                <div className='ml-5 w-0 flex-1'>
                  <dl>
                    <dt className='text-sm font-medium text-gray-500 truncate'>
                      {metric.name}
                    </dt>
                    <dd className='flex items-baseline'>
                      <div className='text-2xl font-semibold text-gray-900'>
                        {metric.value}
                      </div>
                      <div
                        className={`ml-2 flex items-baseline text-sm font-semibold ${
                          metric.changeType === 'increase'
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        {metric.changeType === 'increase' ? (
                          <ArrowTrendingUpIcon className='self-center flex-shrink-0 h-4 w-4 text-green-500' />
                        ) : (
                          <ArrowTrendingDownIcon className='self-center flex-shrink-0 h-4 w-4 text-red-500' />
                        )}
                        <span className='ml-1'>{metric.change}</span>
                      </div>
                    </dd>
                    <dd className='text-xs text-gray-400 mt-1'>
                      {metric.description}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className='grid grid-cols-1 gap-6 xl:grid-cols-2 px-4 sm:px-6 md:px-8'>
        {/* Visitor Trends Chart */}
        <ChartCard
          title='Visitor Trends'
          description='User activity patterns over time'
          className='h-96'
        >
          <VisitorTrendsChart
            data={
              visitorTrendsData[dateRange as keyof typeof visitorTrendsData] ||
              visitorTrendsData['7days']
            }
          />
        </ChartCard>

        {/* Browser Distribution Chart */}
        <ChartCard
          title='Browser Distribution'
          description='User browser preferences'
          className='h-96'
        >
          <EnhancedDonutChart
            data={browserData}
            centerSubtext='Browsers'
            onSegmentClick={segment => console.log('Browser clicked:', segment)}
          />
        </ChartCard>
      </div>

      {/* Geographic Distribution */}
      <div className='px-4 sm:px-6 md:px-8'>
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            <div className='flex items-center justify-between mb-4'>
              <div>
                <h3 className='text-lg leading-6 font-medium text-gray-900'>
                  Geographic Distribution
                </h3>
                <p className='text-sm text-gray-500'>
                  User locations by country
                </p>
              </div>
              <MapPinIcon className='h-6 w-6 text-gray-400' />
            </div>
            <div className='space-y-3'>
              {geographicData.map(country => (
                <div
                  key={country.country}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center space-x-3'>
                    <span className='text-2xl'>{country.flag}</span>
                    <span className='text-sm font-medium text-gray-900'>
                      {country.country}
                    </span>
                  </div>
                  <div className='flex items-center space-x-4'>
                    <div className='text-right'>
                      <div className='text-sm font-medium text-gray-900'>
                        {country.users.toLocaleString()}
                      </div>
                      <div className='text-xs text-gray-500'>
                        {country.percentage}%
                      </div>
                    </div>
                    <div className='w-24 bg-gray-200 rounded-full h-2'>
                      <div
                        className='bg-blue-600 h-2 rounded-full'
                        style={{ width: `${country.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Device Analytics and Top Pages */}
      <div className='grid grid-cols-1 gap-6 lg:grid-cols-2 px-4 sm:px-6 md:px-8'>
        {/* Device Analytics */}
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            <div className='flex items-center justify-between mb-4'>
              <div>
                <h3 className='text-lg leading-6 font-medium text-gray-900'>
                  Device Analytics
                </h3>
                <p className='text-sm text-gray-500'>User device preferences</p>
              </div>
              <DevicePhoneMobileIcon className='h-6 w-6 text-gray-400' />
            </div>
            <div className='space-y-4'>
              {deviceData.map(device => (
                <div key={device.device} className='border rounded-lg p-4'>
                  <div className='flex items-center justify-between mb-2'>
                    <div className='flex items-center space-x-2'>
                      {device.device === 'Desktop' && (
                        <ComputerDesktopIcon className='h-5 w-5 text-gray-500' />
                      )}
                      {device.device === 'Mobile' && (
                        <DevicePhoneMobileIcon className='h-5 w-5 text-gray-500' />
                      )}
                      {device.device === 'Tablet' && (
                        <DevicePhoneMobileIcon className='h-5 w-5 text-gray-500' />
                      )}
                      <span className='font-medium text-gray-900'>
                        {device.device}
                      </span>
                    </div>
                    <span className='text-sm text-gray-500'>
                      {device.percentage}%
                    </span>
                  </div>
                  <div className='grid grid-cols-2 gap-4 text-sm'>
                    <div>
                      <span className='text-gray-500'>Users:</span>
                      <span className='ml-1 font-medium'>
                        {device.users.toLocaleString()}
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-500'>Sessions:</span>
                      <span className='ml-1 font-medium'>
                        {device.sessions.toLocaleString()}
                      </span>
                    </div>
                  </div>
                  <div className='mt-2 w-full bg-gray-200 rounded-full h-2'>
                    <div
                      className='bg-blue-600 h-2 rounded-full'
                      style={{ width: `${device.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Top Pages */}
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            <div className='flex items-center justify-between mb-4'>
              <div>
                <h3 className='text-lg leading-6 font-medium text-gray-900'>
                  Top Pages
                </h3>
                <p className='text-sm text-gray-500'>Most visited pages</p>
              </div>
              <EyeIcon className='h-6 w-6 text-gray-400' />
            </div>
            <div className='overflow-x-auto'>
              <table className='min-w-full divide-y divide-gray-200'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Page
                    </th>
                    <th className='px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Views
                    </th>
                    <th className='px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Avg Time
                    </th>
                    <th className='px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Bounce Rate
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {topPages.map((page, index) => (
                    <tr
                      key={page.page}
                      className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
                    >
                      <td className='px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                        {page.page}
                      </td>
                      <td className='px-3 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {page.views.toLocaleString()}
                      </td>
                      <td className='px-3 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {page.avgTime}
                      </td>
                      <td className='px-3 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {page.bounceRate}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
