'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useParams } from 'next/navigation';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import Form<PERSON>ield, {
  FormContainer,
  FormSection,
} from '@/components/admin/FormField';
import {
  TagIcon,
  ListBulletIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';
import { useAsyncOperation } from '@/contexts/LoadingContext';
import DetailViewSkeleton from '@/components/skeletons/DetailViewSkeleton';
import { ContentLoader } from '@/components/skeletons/SkeletonBase';

interface AttributeValue {
  id: string;
  value: string;
  label: string;
  sortOrder: number;
}

interface Attribute {
  id: string;
  name: string;
  code: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean' | 'date';
  required: boolean;
  filterable: boolean;
  searchable: boolean;
  sortOrder: number;
  description?: string;
  values: AttributeValue[];
  createdAt: string;
  updatedAt: string;
}

interface AttributeFormData {
  name: string;
  code: string;
  type: string;
  required: boolean;
  filterable: boolean;
  searchable: boolean;
  sortOrder: string;
  description: string;
  values: AttributeValue[];
}

// Mock attribute data - replace with actual API call
const mockAttribute: Attribute = {
  id: '1',
  name: 'Color',
  code: 'color',
  type: 'select',
  required: false,
  filterable: true,
  searchable: true,
  sortOrder: 1,
  description: 'Product color attribute',
  values: [
    { id: '1', value: 'red', label: 'Red', sortOrder: 1 },
    { id: '2', value: 'blue', label: 'Blue', sortOrder: 2 },
    { id: '3', value: 'green', label: 'Green', sortOrder: 3 },
  ],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-15T00:00:00Z',
};

// Attribute edit content component
function AttributeEditContent() {
  const router = useRouter();
  const params = useParams();
  const attributeId = params?.id as string;
  const { executeWithLoading } = useAsyncOperation();

  const [attribute, setAttribute] = useState<Attribute | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<AttributeFormData>({
    name: '',
    code: '',
    type: 'text',
    required: false,
    filterable: false,
    searchable: false,
    sortOrder: '0',
    description: '',
    values: [],
  });

  useEffect(() => {
    const loadAttribute = async () => {
      const result = await executeWithLoading(async () => {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        return mockAttribute;
      }, 'Loading attribute details...');

      if (result) {
        setAttribute(result);
        setFormData({
          name: result.name,
          code: result.code,
          type: result.type,
          required: result.required,
          filterable: result.filterable,
          searchable: result.searchable,
          sortOrder: result.sortOrder.toString(),
          description: result.description || '',
          values: result.values,
        });
      }
      setLoading(false);
    };

    loadAttribute();
  }, [attributeId, executeWithLoading]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked,
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleAddValue = () => {
    const newValue: AttributeValue = {
      id: Date.now().toString(),
      value: '',
      label: '',
      sortOrder: formData.values.length + 1,
    };

    setFormData(prev => ({
      ...prev,
      values: [...prev.values, newValue],
    }));
  };

  const handleValueChange = (
    index: number,
    field: keyof AttributeValue,
    value: string | number
  ) => {
    setFormData(prev => ({
      ...prev,
      values: prev.values.map((val, i) =>
        i === index ? { ...val, [field]: value } : val
      ),
    }));
  };

  const handleRemoveValue = (index: number) => {
    setFormData(prev => ({
      ...prev,
      values: prev.values.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const result = await executeWithLoading(async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Updating attribute:', formData);
      return true;
    }, 'Updating attribute...');

    if (result) {
      alert('Attribute updated successfully!');
      router.push('/admin/attributes');
    }
  };

  const handleDelete = async () => {
    if (
      window.confirm(
        'Are you sure you want to delete this attribute? This action cannot be undone.'
      )
    ) {
      const result = await executeWithLoading(async () => {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500));
        return true;
      }, 'Deleting attribute...');

      if (result) {
        alert('Attribute deleted successfully!');
        router.push('/admin/attributes');
      }
    }
  };

  const breadcrumbs = [
    { label: 'Attributes', href: '/admin/attributes' },
    { label: attribute ? attribute.name : 'Edit Attribute', active: true },
  ];

  const actions = (
    <div className='flex space-x-3'>
      <ActionButton variant='danger' onClick={handleDelete}>
        Delete Attribute
      </ActionButton>
      <ActionButton
        variant='primary'
        onClick={() => {
          const form = document.getElementById(
            'attribute-form'
          ) as HTMLFormElement;
          if (form) form.requestSubmit();
        }}
      >
        Save Changes
      </ActionButton>
    </div>
  );

  const attributeTypes = [
    { value: 'text', label: 'Text' },
    { value: 'number', label: 'Number' },
    { value: 'select', label: 'Select (Single)' },
    { value: 'multiselect', label: 'Select (Multiple)' },
    { value: 'boolean', label: 'Boolean (Yes/No)' },
    { value: 'date', label: 'Date' },
  ];

  if (loading) {
    return <DetailViewSkeleton />;
  }

  if (!attribute) {
    return (
      <div className='text-center py-12'>
        <h2 className='text-xl font-semibold text-gray-900'>
          Attribute not found
        </h2>
        <p className='text-gray-600 mt-2'>
          The attribute you're looking for doesn't exist.
        </p>
        <button
          onClick={() => router.push('/admin/attributes')}
          className='mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700'
        >
          Back to Attributes
        </button>
      </div>
    );
  }

  const showValues =
    formData.type === 'select' || formData.type === 'multiselect';

  return (
    <div className='space-y-6'>
      <PageHeader
        title={`Edit Attribute: ${attribute.name}`}
        description='Update attribute configuration and values'
        breadcrumbs={breadcrumbs}
        actions={actions}
      />

      <ContentLoader isLoading={saving} message='Saving changes...'>
        <FormContainer>
          <form id='attribute-form' onSubmit={handleSubmit}>
            {/* Basic Information */}
            <FormSection
              title='Basic Information'
              description='Attribute name and identification'
            >
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <FormField
                  label='Attribute Name'
                  name='name'
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  placeholder='e.g., Color, Size, Material'
                />
                <FormField
                  label='Attribute Code'
                  name='code'
                  value={formData.code}
                  onChange={handleInputChange}
                  required
                  placeholder='e.g., color, size, material'
                  help='Unique identifier for this attribute (lowercase, no spaces)'
                />
                <FormField
                  label='Attribute Type'
                  name='type'
                  type='select'
                  value={formData.type}
                  onChange={handleInputChange}
                  options={attributeTypes}
                  required
                />
                <FormField
                  label='Sort Order'
                  name='sortOrder'
                  type='number'
                  value={formData.sortOrder}
                  onChange={handleInputChange}
                  placeholder='0'
                  help='Order in which this attribute appears'
                />
                <div className='md:col-span-2'>
                  <FormField
                    label='Description'
                    name='description'
                    type='textarea'
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder='Optional description for this attribute'
                    rows={3}
                  />
                </div>
              </div>
            </FormSection>

            {/* Configuration */}
            <FormSection
              title='Configuration'
              description='Attribute behavior and display options'
            >
              <div className='space-y-4'>
                <div className='flex items-center'>
                  <input
                    type='checkbox'
                    id='required'
                    name='required'
                    checked={formData.required}
                    onChange={handleInputChange}
                    className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                  />
                  <label
                    htmlFor='required'
                    className='ml-2 block text-sm text-gray-900'
                  >
                    Required field
                  </label>
                </div>
                <div className='flex items-center'>
                  <input
                    type='checkbox'
                    id='filterable'
                    name='filterable'
                    checked={formData.filterable}
                    onChange={handleInputChange}
                    className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                  />
                  <label
                    htmlFor='filterable'
                    className='ml-2 block text-sm text-gray-900'
                  >
                    Use for filtering products
                  </label>
                </div>
                <div className='flex items-center'>
                  <input
                    type='checkbox'
                    id='searchable'
                    name='searchable'
                    checked={formData.searchable}
                    onChange={handleInputChange}
                    className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                  />
                  <label
                    htmlFor='searchable'
                    className='ml-2 block text-sm text-gray-900'
                  >
                    Include in search
                  </label>
                </div>
              </div>
            </FormSection>

            {/* Attribute Values */}
            {showValues && (
              <FormSection
                title='Attribute Values'
                description='Define the available options for this attribute'
              >
                <div className='space-y-4'>
                  {formData.values.map((value, index) => (
                    <div
                      key={value.id}
                      className='flex items-center space-x-4 p-4 border border-gray-200 rounded-lg'
                    >
                      <div className='flex-1 grid grid-cols-1 md:grid-cols-3 gap-4'>
                        <FormField
                          label='Value'
                          name={`value-${index}`}
                          value={value.value}
                          onChange={e =>
                            handleValueChange(index, 'value', e.target.value)
                          }
                          placeholder='e.g., red'
                          required
                        />
                        <FormField
                          label='Label'
                          name={`label-${index}`}
                          value={value.label}
                          onChange={e =>
                            handleValueChange(index, 'label', e.target.value)
                          }
                          placeholder='e.g., Red'
                          required
                        />
                        <FormField
                          label='Sort Order'
                          name={`sortOrder-${index}`}
                          type='number'
                          value={value.sortOrder.toString()}
                          onChange={e =>
                            handleValueChange(
                              index,
                              'sortOrder',
                              parseInt(e.target.value) || 0
                            )
                          }
                          placeholder='0'
                        />
                      </div>
                      <button
                        type='button'
                        onClick={() => handleRemoveValue(index)}
                        className='text-red-600 hover:text-red-800 p-2'
                        title='Remove value'
                      >
                        <svg
                          className='h-5 w-5'
                          fill='none'
                          stroke='currentColor'
                          viewBox='0 0 24 24'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M6 18L18 6M6 6l12 12'
                          />
                        </svg>
                      </button>
                    </div>
                  ))}

                  <button
                    type='button'
                    onClick={handleAddValue}
                    className='w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-600 hover:border-gray-400 hover:text-gray-800 transition-colors'
                  >
                    + Add Value
                  </button>
                </div>
              </FormSection>
            )}
          </form>
        </FormContainer>
      </ContentLoader>
    </div>
  );
}

// Main attribute edit page component with loading wrapper
export default function AttributeEditPage() {
  return (
    <Suspense fallback={<DetailViewSkeleton />}>
      <AttributeEditContent />
    </Suspense>
  );
}
