'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  TextField,
  InputAdornment,
  Tooltip,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../theme/mui-theme';
import MuiFormField, {
  MuiFormContainer,
} from '../../../components/admin/MuiFormField';

interface Attribute {
  id: string;
  name: string;
  code: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean' | 'date';
  required: boolean;
  filterable: boolean;
  sortable: boolean;
  options?: string[];
  defaultValue?: string;
  description?: string;
  status: 'active' | 'inactive';
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

// Mock data for attributes
const mockAttributes: Attribute[] = [
  {
    id: '1',
    name: 'Color',
    code: 'color',
    type: 'select',
    required: false,
    filterable: true,
    sortable: true,
    options: ['Red', 'Blue', 'Green', 'Black', 'White'],
    description: 'Product color options',
    status: 'active',
    usageCount: 156,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    name: 'Size',
    code: 'size',
    type: 'select',
    required: true,
    filterable: true,
    sortable: true,
    options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
    description: 'Product size options',
    status: 'active',
    usageCount: 234,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-14T15:45:00Z',
  },
  {
    id: '3',
    name: 'Material',
    code: 'material',
    type: 'text',
    required: false,
    filterable: true,
    sortable: false,
    description: 'Product material description',
    status: 'active',
    usageCount: 89,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-13T12:20:00Z',
  },
  {
    id: '4',
    name: 'Weight',
    code: 'weight',
    type: 'number',
    required: false,
    filterable: false,
    sortable: true,
    description: 'Product weight in grams',
    status: 'active',
    usageCount: 67,
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-12T09:15:00Z',
  },
  {
    id: '5',
    name: 'Brand',
    code: 'brand',
    type: 'select',
    required: true,
    filterable: true,
    sortable: true,
    options: ['Nike', 'Adidas', 'Puma', 'Reebok', 'New Balance'],
    description: 'Product brand',
    status: 'active',
    usageCount: 198,
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-11T14:30:00Z',
  },
];

interface AttributeFormData {
  name: string;
  code: string;
  type: string;
  required: boolean;
  filterable: boolean;
  sortable: boolean;
  options: string;
  defaultValue: string;
  description: string;
  status: string;
}

const initialFormData: AttributeFormData = {
  name: '',
  code: '',
  type: 'text',
  required: false,
  filterable: false,
  sortable: false,
  options: '',
  defaultValue: '',
  description: '',
  status: 'active',
};

export default function MuiAttributesPage() {
  const router = useRouter();
  const [attributes, setAttributes] = useState<Attribute[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [formData, setFormData] = useState<AttributeFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  useEffect(() => {
    // Simulate API call
    const fetchAttributes = async () => {
      setLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setAttributes(mockAttributes);
      } catch (error) {
        console.error('Error fetching attributes:', error);
        setSnackbar({
          open: true,
          message: 'Error loading attributes',
          severity: 'error',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAttributes();
  }, []);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));

      // Auto-generate code from name
      if (name === 'name' && value) {
        const code = value
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '_')
          .replace(/_+/g, '_')
          .replace(/^_|_$/g, '');
        setFormData(prev => ({ ...prev, code }));
      }
    }

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Attribute name is required';
    }

    if (!formData.code.trim()) {
      errors.code = 'Attribute code is required';
    } else if (!/^[a-z0-9_]+$/.test(formData.code)) {
      errors.code =
        'Code must contain only lowercase letters, numbers, and underscores';
    }

    if (
      ['select', 'multiselect'].includes(formData.type) &&
      !formData.options.trim()
    ) {
      errors.options = 'Options are required for select/multiselect types';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newAttribute: Attribute = {
        id: Date.now().toString(),
        name: formData.name,
        code: formData.code,
        type: formData.type as Attribute['type'],
        required: formData.required,
        filterable: formData.filterable,
        sortable: formData.sortable,
        options: ['select', 'multiselect'].includes(formData.type)
          ? formData.options
              .split(',')
              .map(opt => opt.trim())
              .filter(Boolean)
          : undefined,
        defaultValue: formData.defaultValue || undefined,
        description: formData.description || undefined,
        status: formData.status as 'active' | 'inactive',
        usageCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setAttributes(prev => [newAttribute, ...prev]);
      setFormData(initialFormData);
      setShowAddDialog(false);
      setSnackbar({
        open: true,
        message: 'Attribute created successfully',
        severity: 'success',
      });
    } catch (error) {
      console.error('Error creating attribute:', error);
      setSnackbar({
        open: true,
        message: 'Error creating attribute',
        severity: 'error',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (attribute: Attribute) => {
    router.push(`/admin/attributes/${attribute.id}/edit`);
  };

  const handleDelete = async (attribute: Attribute) => {
    if (attribute.usageCount > 0) {
      setSnackbar({
        open: true,
        message: `Cannot delete attribute "${attribute.name}" because it is used by ${attribute.usageCount} products.`,
        severity: 'error',
      });
      return;
    }

    if (
      window.confirm(`Are you sure you want to delete "${attribute.name}"?`)
    ) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setAttributes(prev => prev.filter(a => a.id !== attribute.id));
        setSnackbar({
          open: true,
          message: 'Attribute deleted successfully',
          severity: 'success',
        });
      } catch (error) {
        console.error('Error deleting attribute:', error);
        setSnackbar({
          open: true,
          message: 'Error deleting attribute',
          severity: 'error',
        });
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusChip = (status: Attribute['status']) => {
    return (
      <Chip
        label={status === 'active' ? 'Active' : 'Inactive'}
        color={status === 'active' ? 'success' : 'error'}
        size='small'
        variant='outlined'
      />
    );
  };

  const getTypeChip = (type: Attribute['type']) => {
    const colors = {
      text: 'primary',
      number: 'secondary',
      select: 'success',
      multiselect: 'warning',
      boolean: 'info',
      date: 'error',
    } as const;

    return (
      <Chip
        label={type.charAt(0).toUpperCase() + type.slice(1)}
        color={colors[type] || 'default'}
        size='small'
        variant='outlined'
      />
    );
  };

  // Filter attributes based on search term
  const filteredAttributes = attributes.filter(
    attr =>
      attr.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attr.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attr.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Paginate filtered attributes
  const paginatedAttributes = filteredAttributes.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box
          sx={{
            mb: 3,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box>
            <Typography variant='h4' component='h1' sx={{ mb: 1 }}>
              Attributes
            </Typography>
            <Typography variant='body1' color='text.secondary'>
              Manage product attributes and specifications
            </Typography>
          </Box>
          <Button
            variant='contained'
            startIcon={<AddIcon />}
            onClick={() => setShowAddDialog(true)}
            size='large'
          >
            Add Attribute
          </Button>
        </Box>

        {/* Search and Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <TextField
              fullWidth
              placeholder='Search attributes...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position='start'>
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </CardContent>
        </Card>

        {/* Attributes Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ color: '#374151', fontWeight: 'bold' }}>
                    Attribute
                  </TableCell>
                  <TableCell sx={{ color: '#374151', fontWeight: 'bold' }}>
                    Type
                  </TableCell>
                  <TableCell sx={{ color: '#374151', fontWeight: 'bold' }}>
                    Description
                  </TableCell>
                  <TableCell sx={{ color: '#374151', fontWeight: 'bold' }}>
                    Options
                  </TableCell>
                  <TableCell sx={{ color: '#374151', fontWeight: 'bold' }}>
                    Required
                  </TableCell>
                  <TableCell sx={{ color: '#374151', fontWeight: 'bold' }}>
                    Usage
                  </TableCell>
                  <TableCell sx={{ color: '#374151', fontWeight: 'bold' }}>
                    Status
                  </TableCell>
                  <TableCell sx={{ color: '#374151', fontWeight: 'bold' }}>
                    Updated
                  </TableCell>
                  <TableCell
                    align='right'
                    sx={{ color: '#374151', fontWeight: 'bold' }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={9} align='center'>
                      <Typography>Loading...</Typography>
                    </TableCell>
                  </TableRow>
                ) : paginatedAttributes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} align='center'>
                      <Typography>No attributes found</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedAttributes.map(attribute => (
                    <TableRow key={attribute.id} hover>
                      <TableCell>
                        <Box>
                          <Typography variant='body2' fontWeight='medium'>
                            {attribute.name}
                          </Typography>
                          <Typography variant='caption' color='text.secondary'>
                            {attribute.code}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{getTypeChip(attribute.type)}</TableCell>
                      <TableCell>
                        <Typography
                          variant='body2'
                          sx={{ maxWidth: 200 }}
                          noWrap
                        >
                          {attribute.description || 'No description'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {attribute.options ? (
                          <Typography variant='body2'>
                            {attribute.options.slice(0, 3).join(', ')}
                            {attribute.options.length > 3 &&
                              ` +${attribute.options.length - 3} more`}
                          </Typography>
                        ) : (
                          <Typography variant='body2' color='text.secondary'>
                            —
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={attribute.required ? 'Required' : 'Optional'}
                          color={attribute.required ? 'error' : 'default'}
                          size='small'
                          variant='outlined'
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant='body2' fontWeight='medium'>
                          {attribute.usageCount} products
                        </Typography>
                      </TableCell>
                      <TableCell>{getStatusChip(attribute.status)}</TableCell>
                      <TableCell>
                        <Typography variant='body2' color='text.secondary'>
                          {formatDate(attribute.updatedAt)}
                        </Typography>
                      </TableCell>
                      <TableCell align='right'>
                        <Tooltip title='Edit'>
                          <IconButton
                            size='small'
                            onClick={() => handleEdit(attribute)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title='Delete'>
                          <IconButton
                            size='small'
                            onClick={() => handleDelete(attribute)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component='div'
            count={filteredAttributes.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(event, newPage) => setPage(newPage)}
            onRowsPerPageChange={event => {
              setRowsPerPage(parseInt(event.target.value, 10));
              setPage(0);
            }}
          />
        </Card>

        {/* Add Attribute Dialog */}
        <Dialog
          open={showAddDialog}
          onClose={() => setShowAddDialog(false)}
          maxWidth='md'
          fullWidth
        >
          <DialogTitle>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography variant='h6'>Add New Attribute</Typography>
              <IconButton onClick={() => setShowAddDialog(false)}>
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            <Box component='form' onSubmit={handleSubmit} sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='Attribute Name'
                    name='name'
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder='Enter attribute name'
                    required
                    error={formErrors.name}
                    help='Display name for the attribute'
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='Attribute Code'
                    name='code'
                    value={formData.code}
                    onChange={handleInputChange}
                    placeholder='attribute_code'
                    required
                    error={formErrors.code}
                    help='Unique identifier (auto-generated from name)'
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='Attribute Type'
                    name='type'
                    type='select'
                    value={formData.type}
                    onChange={handleInputChange}
                    options={[
                      { value: 'text', label: 'Text' },
                      { value: 'number', label: 'Number' },
                      { value: 'select', label: 'Select (Single)' },
                      { value: 'multiselect', label: 'Multi-Select' },
                      { value: 'boolean', label: 'Boolean (Yes/No)' },
                      { value: 'date', label: 'Date' },
                    ]}
                    help='Data type for this attribute'
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='Status'
                    name='status'
                    type='select'
                    value={formData.status}
                    onChange={handleInputChange}
                    options={[
                      { value: 'active', label: 'Active' },
                      { value: 'inactive', label: 'Inactive' },
                    ]}
                  />
                </Grid>
                {['select', 'multiselect'].includes(formData.type) && (
                  <Grid size={12}>
                    <MuiFormField
                      label='Options'
                      name='options'
                      type='textarea'
                      rows={3}
                      value={formData.options}
                      onChange={handleInputChange}
                      placeholder='Option 1, Option 2, Option 3'
                      required
                      error={formErrors.options}
                      help='Comma-separated list of options'
                    />
                  </Grid>
                )}
                <Grid size={12}>
                  <MuiFormField
                    label='Description'
                    name='description'
                    type='textarea'
                    rows={2}
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder='Enter attribute description'
                    help='Optional description for this attribute'
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                  <MuiFormField
                    label='Required'
                    name='required'
                    type='checkbox'
                    checked={formData.required}
                    onChange={handleInputChange}
                    help='Must be filled when creating products'
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                  <MuiFormField
                    label='Filterable'
                    name='filterable'
                    type='checkbox'
                    checked={formData.filterable}
                    onChange={handleInputChange}
                    help='Can be used to filter products'
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                  <MuiFormField
                    label='Sortable'
                    name='sortable'
                    type='checkbox'
                    checked={formData.sortable}
                    onChange={handleInputChange}
                    help='Can be used to sort products'
                  />
                </Grid>
                <Grid size={12}>
                  <MuiFormField
                    label='Default Value'
                    name='defaultValue'
                    value={formData.defaultValue}
                    onChange={handleInputChange}
                    placeholder='Enter default value (optional)'
                    help='Default value when creating new products'
                  />
                </Grid>
              </Grid>
            </Box>
          </DialogContent>
          <DialogActions sx={{ p: 3 }}>
            <Button
              onClick={() => {
                setShowAddDialog(false);
                setFormData(initialFormData);
                setFormErrors({});
              }}
              variant='outlined'
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant='contained'
              disabled={submitting}
              startIcon={<AddIcon />}
            >
              {submitting ? 'Creating...' : 'Create Attribute'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
