'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>,
  Alert,
  <PERSON>ack,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Paper,
  Divider,
  Snackbar,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Tune as AttributeIcon,
  Settings as SettingsIcon,
  List as OptionsIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../theme/mui-theme';

interface AttributeFormData {
  name: string;
  code: string;
  type: string;
  required: boolean;
  filterable: boolean;
  sortable: boolean;
  options: string;
  defaultValue: string;
  description: string;
  status: string;
}

const initialFormData: AttributeFormData = {
  name: '',
  code: '',
  type: 'text',
  required: false,
  filterable: false,
  sortable: false,
  options: '',
  defaultValue: '',
  description: '',
  status: 'active',
};

const attributeTypes = [
  { value: 'text', label: 'Text' },
  { value: 'number', label: 'Number' },
  { value: 'select', label: 'Select (Single)' },
  { value: 'multiselect', label: 'Multi-Select' },
  { value: 'boolean', label: 'Boolean (Yes/No)' },
  { value: 'date', label: 'Date' },
];

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

export default function NewAttributePage() {
  const router = useRouter();
  const [formData, setFormData] = useState<AttributeFormData>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));

      // Auto-generate code from name
      if (name === 'name' && value) {
        const code = value
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '_')
          .replace(/_+/g, '_')
          .replace(/^_|_$/g, '');
        setFormData(prev => ({ ...prev, code }));
      }
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSelectChange = (event: any) => {
    const { name, value } = event.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Attribute name is required';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Attribute code is required';
    } else if (!/^[a-z0-9_]+$/.test(formData.code)) {
      newErrors.code =
        'Code must contain only lowercase letters, numbers, and underscores';
    }

    if (
      ['select', 'multiselect'].includes(formData.type) &&
      !formData.options.trim()
    ) {
      newErrors.options = 'Options are required for select/multiselect types';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      setSnackbar({
        open: true,
        message: 'Please fix the errors before submitting',
        severity: 'error',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Attribute data:', formData);

      setSnackbar({
        open: true,
        message: 'Attribute created successfully!',
        severity: 'success',
      });

      // Redirect to attributes list after a short delay
      setTimeout(() => {
        router.push('/admin/attributes');
      }, 1500);
    } catch (error) {
      console.error('Error creating attribute:', error);
      setSnackbar({
        open: true,
        message: 'Error creating attribute',
        severity: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const breadcrumbs = [
    { label: 'Attributes', href: '/admin/attributes' },
    { label: 'New Attribute', active: true },
  ];

  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* Header */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
            mb={2}
          >
            <Box>
              <Typography
                variant='h4'
                component='h1'
                fontWeight='bold'
                color='primary.main'
              >
                New Attribute
              </Typography>
              <Typography variant='body1' color='text.secondary' mt={1}>
                Create a new product attribute for your store
              </Typography>
            </Box>
            <Stack direction='row' spacing={2}>
              <Button
                variant='outlined'
                onClick={() => router.back()}
                startIcon={<CancelIcon />}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                onClick={() => handleSubmit({} as React.FormEvent)}
                disabled={isSubmitting}
                startIcon={<SaveIcon />}
                sx={{ minWidth: 140 }}
              >
                {isSubmitting ? 'Creating...' : 'Create Attribute'}
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* Form */}
        <Box component='form' onSubmit={handleSubmit} sx={{ maxWidth: 1200 }}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <AttributeIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Basic Information
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Essential attribute details and identification
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <TextField
                        fullWidth
                        label='Attribute Name'
                        name='name'
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder='Enter attribute name'
                        required
                        error={Boolean(errors.name)}
                        helperText={
                          errors.name || 'Display name for the attribute'
                        }
                        disabled={isSubmitting}
                        variant='outlined'
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <TextField
                        fullWidth
                        label='Attribute Code'
                        name='code'
                        value={formData.code}
                        onChange={handleInputChange}
                        placeholder='Auto-generated from name'
                        required
                        error={Boolean(errors.code)}
                        helperText={
                          errors.code ||
                          'Unique identifier (auto-generated from name)'
                        }
                        disabled={isSubmitting}
                        variant='outlined'
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <FormControl fullWidth required disabled={isSubmitting}>
                        <InputLabel>Attribute Type</InputLabel>
                        <Select
                          name='type'
                          value={formData.type}
                          onChange={handleSelectChange}
                          label='Attribute Type'
                        >
                          {attributeTypes.map(option => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <FormControl fullWidth disabled={isSubmitting}>
                        <InputLabel>Status</InputLabel>
                        <Select
                          name='status'
                          value={formData.status}
                          onChange={handleSelectChange}
                          label='Status'
                        >
                          {statusOptions.map(option => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid size={12}>
                      <TextField
                        fullWidth
                        label='Description'
                        name='description'
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder='Enter attribute description'
                        helperText='Optional description for this attribute'
                        disabled={isSubmitting}
                        variant='outlined'
                        multiline
                        rows={3}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Options Configuration */}
            {['select', 'multiselect'].includes(formData.type) && (
              <Grid size={12}>
                <Card elevation={2} sx={{ borderRadius: 2 }}>
                  <CardContent sx={{ p: 4 }}>
                    <Stack
                      direction='row'
                      alignItems='center'
                      spacing={2}
                      mb={3}
                    >
                      <OptionsIcon color='primary' />
                      <Box>
                        <Typography variant='h6' fontWeight='bold'>
                          Options Configuration
                        </Typography>
                        <Typography variant='body2' color='text.secondary'>
                          Define available options for select/multiselect
                          attributes
                        </Typography>
                      </Box>
                    </Stack>
                    <Divider sx={{ mb: 3 }} />

                    <Grid container spacing={3}>
                      <Grid size={12}>
                        <TextField
                          fullWidth
                          label='Options'
                          name='options'
                          value={formData.options}
                          onChange={handleInputChange}
                          placeholder='Option 1, Option 2, Option 3'
                          required
                          error={Boolean(errors.options)}
                          helperText={
                            errors.options || 'Comma-separated list of options'
                          }
                          disabled={isSubmitting}
                          variant='outlined'
                          multiline
                          rows={4}
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Attribute Settings */}
            <Grid size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <SettingsIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Attribute Settings
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Configure attribute behavior and properties
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <TextField
                        fullWidth
                        label='Default Value'
                        name='defaultValue'
                        value={formData.defaultValue}
                        onChange={handleInputChange}
                        placeholder='Enter default value (optional)'
                        helperText='Default value when creating new products'
                        disabled={isSubmitting}
                        variant='outlined'
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      {/* Empty grid for spacing */}
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            name='required'
                            checked={formData.required}
                            onChange={handleInputChange}
                            color='primary'
                          />
                        }
                        label='Required'
                        sx={{ mt: 1 }}
                      />
                      <Typography
                        variant='caption'
                        color='text.secondary'
                        display='block'
                      >
                        Must be filled when creating products
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            name='filterable'
                            checked={formData.filterable}
                            onChange={handleInputChange}
                            color='primary'
                          />
                        }
                        label='Filterable'
                        sx={{ mt: 1 }}
                      />
                      <Typography
                        variant='caption'
                        color='text.secondary'
                        display='block'
                      >
                        Can be used to filter products
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            name='sortable'
                            checked={formData.sortable}
                            onChange={handleInputChange}
                            color='primary'
                          />
                        }
                        label='Sortable'
                        sx={{ mt: 1 }}
                      />
                      <Typography
                        variant='caption'
                        color='text.secondary'
                        display='block'
                      >
                        Can be used to sort products
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
            severity={snackbar.severity}
            variant='filled'
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
