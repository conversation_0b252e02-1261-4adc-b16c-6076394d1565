'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import { PlusIcon } from '@heroicons/react/24/outline';

interface Attribute {
  id: string;
  name: string;
  code: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean' | 'date';
  required: boolean;
  filterable: boolean;
  sortable: boolean;
  options?: string[];
  defaultValue?: string;
  description?: string;
  status: 'active' | 'inactive';
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

// Mock data for attributes
const mockAttributes: Attribute[] = [
  {
    id: '1',
    name: 'Color',
    code: 'color',
    type: 'select',
    required: false,
    filterable: true,
    sortable: true,
    options: ['Red', 'Blue', 'Green', 'Black', 'White'],
    description: 'Product color options',
    status: 'active',
    usageCount: 156,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    code: 'size',
    type: 'select',
    required: true,
    filterable: true,
    sortable: true,
    options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
    description: 'Product size options',
    status: 'active',
    usageCount: 234,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-14T15:45:00Z',
  },
  {
    id: '3',
    name: 'Material',
    code: 'material',
    type: 'text',
    required: false,
    filterable: true,
    sortable: false,
    description: 'Product material description',
    status: 'active',
    usageCount: 89,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-13T12:20:00Z',
  },
  {
    id: '4',
    name: 'Weight',
    code: 'weight',
    type: 'number',
    required: false,
    filterable: false,
    sortable: true,
    description: 'Product weight in grams',
    status: 'active',
    usageCount: 67,
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-12T09:15:00Z',
  },
  {
    id: '5',
    name: 'Brand',
    code: 'brand',
    type: 'select',
    required: true,
    filterable: true,
    sortable: true,
    options: ['Nike', 'Adidas', 'Puma', 'Reebok', 'New Balance'],
    description: 'Product brand',
    status: 'active',
    usageCount: 198,
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-11T14:30:00Z',
  },
  {
    id: '6',
    name: 'Warranty',
    code: 'warranty',
    type: 'boolean',
    required: false,
    filterable: true,
    sortable: false,
    description: 'Product warranty availability',
    status: 'inactive',
    usageCount: 23,
    createdAt: '2024-01-06T00:00:00Z',
    updatedAt: '2024-01-10T11:20:00Z',
  },
];

export default function AttributesPage() {
  const router = useRouter();
  const [attributes, setAttributes] = useState<Attribute[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    const fetchAttributes = async () => {
      setLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setAttributes(mockAttributes);
      } catch (error) {
        console.error('Error fetching attributes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAttributes();
  }, []);

  const handleEdit = (attribute: Attribute) => {
    router.push(`/admin/attributes/${attribute.id}`);
  };

  const handleDelete = async (attribute: Attribute) => {
    if (attribute.usageCount > 0) {
      alert(
        `Cannot delete attribute "${attribute.name}" because it is used by ${attribute.usageCount} products.`
      );
      return;
    }

    if (
      window.confirm(`Are you sure you want to delete "${attribute.name}"?`)
    ) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setAttributes(prev => prev.filter(a => a.id !== attribute.id));
      } catch (error) {
        console.error('Error deleting attribute:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: Attribute['status']) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      inactive: { color: 'bg-red-100 text-red-800', label: 'Inactive' },
    };

    const config = statusConfig[status];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const getTypeBadge = (type: Attribute['type']) => {
    const typeConfig = {
      text: { color: 'bg-blue-100 text-blue-800', label: 'Text' },
      number: { color: 'bg-purple-100 text-purple-800', label: 'Number' },
      select: { color: 'bg-green-100 text-green-800', label: 'Select' },
      multiselect: {
        color: 'bg-yellow-100 text-yellow-800',
        label: 'Multi-Select',
      },
      boolean: { color: 'bg-gray-100 text-gray-800', label: 'Boolean' },
      date: { color: 'bg-pink-100 text-pink-800', label: 'Date' },
    };

    const config = typeConfig[type];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const columns = [
    {
      key: 'name',
      label: 'Attribute',
      sortable: true,
      render: (value: string, row: Attribute) => (
        <div>
          <div className='text-sm font-medium text-gray-900'>{value}</div>
          <div className='text-sm text-gray-500'>{row.code}</div>
        </div>
      ),
    },
    {
      key: 'type',
      label: 'Type',
      sortable: true,
      render: (value: Attribute['type']) => getTypeBadge(value),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: string) => (
        <span className='text-sm text-gray-500 max-w-xs truncate block'>
          {value || 'No description'}
        </span>
      ),
    },
    {
      key: 'options',
      label: 'Options',
      render: (value: string[], row: Attribute) => {
        if (!value || value.length === 0)
          return <span className='text-sm text-gray-400'>—</span>;

        const displayOptions = value.slice(0, 3);
        const remainingCount = value.length - 3;

        return (
          <div className='text-sm text-gray-500'>
            {displayOptions.join(', ')}
            {remainingCount > 0 && (
              <span className='text-gray-400'> +{remainingCount} more</span>
            )}
          </div>
        );
      },
    },
    {
      key: 'required',
      label: 'Required',
      render: (value: boolean) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
          }`}
        >
          {value ? 'Required' : 'Optional'}
        </span>
      ),
    },
    {
      key: 'usageCount',
      label: 'Usage',
      sortable: true,
      render: (value: number) => (
        <span className='text-sm font-medium text-gray-900'>
          {value} products
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: Attribute['status']) => getStatusBadge(value),
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{formatDate(value)}</span>
      ),
    },
  ];

  const breadcrumbs = [{ label: 'Attributes', active: true }];

  return (
    <div className='space-y-6'>
      <PageHeader
        title='Attributes'
        description='Manage product attributes and specifications'
        breadcrumbs={breadcrumbs}
        actions={
          <ActionButton href='/admin/attributes/new' icon={PlusIcon}>
            Add Attribute
          </ActionButton>
        }
      />

      <DataTable
        columns={columns}
        data={attributes}
        loading={loading}
        searchable
        filterable
        pagination
        pageSize={10}
        onEdit={handleEdit}
        onDelete={handleDelete}
        emptyMessage='No attributes found. Create your first attribute to get started.'
      />
    </div>
  );
}
