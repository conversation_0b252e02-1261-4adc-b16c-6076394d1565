'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  <PERSON><PERSON>,
  <PERSON>ack,
  TextField,
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Paper,
  Divider,
  FormControlLabel,
  Checkbox,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  Category as CategoryIcon,
  Settings as SettingsIcon,
  Image as ImageIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../../theme/mui-theme';
import { useMedusaBackendCategories } from '@/hooks/useMedusaAdminBackend';
import { MultiSelectDropdown } from '@/components/MultiSelectDropdown';
import { ToastProvider, useToast } from '@/components/common/ToastProvider';
// import ConfirmDialog from '@/components/ConfirmDialog';

interface CategoryFormData {
  name: string;
  slug: string;
  description: string;
  parentId: string; // "" if root
  status: 'active' | 'inactive';
  sortOrder: string; // rank in API, kept as string for <TextField type="number">
  featured: boolean;
  seo: {
    title: string;
    description: string;
    keywords: string;
  };
}

const initialFormData: CategoryFormData = {
  name: '',
  slug: '',
  description: '',
  parentId: '',
  status: 'active',
  sortOrder: '0',
  featured: false,
  seo: { title: '', description: '', keywords: '' },
};

export default function EditCategoryPage() {
  /* ----------------------------------------------------------------
     hooks & state
     ---------------------------------------------------------------- */
  const router = useRouter();
  const params = useParams<{ id: string }>();
  // const toast = useToast();
  const {
    categories, // ← the full tree (used for dropdowns)
    singleCategory,
    fetchSingleCategory,
    updateCategory,
    loading,
    error,
  } = useMedusaBackendCategories();

  const [formData, setFormData] = useState<CategoryFormData>(initialFormData);
  const [childIds, setChildIds] = useState<string[]>([]); // ONLY used when record is a parent
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });
  // Removed delete logic

  /* ----------------------------------------------------------------
     load category (+ all categories list)
     ---------------------------------------------------------------- */
  useEffect(() => {
    if (params?.id) fetchSingleCategory(params.id);
  }, [params?.id, fetchSingleCategory]);

  /* when the API record arrives, push its data into local state */
  useEffect(() => {
    if (!singleCategory) return;

    // Is this a parent (root) or a child?
    const isRoot = singleCategory.parent_category_id === null;

    // Pre-select children if root
    if (isRoot && singleCategory.category_children) {
      setChildIds(singleCategory.category_children.map(c => c.id) || []);
    }

    setFormData({
      name: singleCategory.name ?? '',
      slug: singleCategory.handle ?? '',
      description: singleCategory.description ?? '',
      parentId: singleCategory.parent_category_id ?? '',
      status: (singleCategory as any).is_active ? 'active' : 'inactive',
      sortOrder: (singleCategory as any).rank?.toString() ?? '0',
      featured: (singleCategory as any).is_internal ?? false,
      seo: { title: '', description: '', keywords: '' }, // fill if you store SEO in metadata
    });
  }, [singleCategory]);

  /* ----------------------------------------------------------------
     field helpers
     ---------------------------------------------------------------- */
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    // support nested "seo.title" etc.
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: { ...(prev as any)[parent], [child]: value },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]:
          type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
      }));
    }

    // auto-generate slug from name
    if (name === 'name') {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setFormData(prev => ({ ...prev, slug }));
    }

    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handleSelectChange = (event: any) => {
    setFormData(prev => ({ ...prev, status: event }));
  };

  /* ----------------------------------------------------------------
     validation + submit (stub; replace with real API call)
     ---------------------------------------------------------------- */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Category name is required';
    if (!formData.slug.trim()) newErrors.slug = 'Slug is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      // toast.error('Please fix the errors');
      return;
    }

    setIsSubmitting(true);

    try {
      /* ----------------------------------------------------------------
         Construct payload in Medusa’s shape.
         NOTE: Adjust keys to match your back-end if different.
         ---------------------------------------------------------------- */
      const payload: any = {
        name: formData.name,
        handle: formData.slug,
        description: formData.description,
        is_active: formData.status === 'active',
        parent_category_id: formData.parentId || null,
      };

      await updateCategory(params?.id as string, payload);

      setSnackbar({
        open: true,
        message: 'Category updated!',
        severity: 'success',
      });
    } catch (err) {
      console.error(err);
      setSnackbar({ open: true, message: 'Update failed', severity: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  };

  /* ----------------------------------------------------------------
     derived helpers for render
     ---------------------------------------------------------------- */
  const isRootCategory = !formData.parentId; // parentId === '' ⇒ root

  const categoryOptions = categories
    .filter(c => c.parent_category_id === null) // don’t show the record we’re editing
    .map(c => ({ label: c.name, value: c.id }));

  const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
  ];

  /* ----------------------------------------------------------------
     render
     ---------------------------------------------------------------- */
  if (loading) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box
          sx={{
            minHeight: '100vh',
            p: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography>Loading category...</Typography>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* ------------ header (unchanged) ------------- */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
            mb={2}
          >
            <Box>
              <Typography variant='h4' fontWeight='bold' color='primary.main'>
                Edit Category
              </Typography>
              <Typography color='text.secondary' mt={1}>
                Update category information and settings
              </Typography>
            </Box>
            <Stack direction='row' spacing={2}>
              <Button
                variant='outlined'
                onClick={() => router.back()}
                startIcon={<CancelIcon />}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                onClick={handleSubmit}
                disabled={isSubmitting}
                startIcon={<SaveIcon />}
                sx={{ minWidth: 140 }}
              >
                {isSubmitting ? 'Updating…' : 'Update Category'}
              </Button>
              {/* Delete button removed */}
            </Stack>
            {/* Confirm Delete Dialog removed */}
          </Stack>
        </Paper>

        {/* ------------ form ------------- */}
        <Box component='form' onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* ===== BASIC INFORMATION ================================== */}
            <Grid size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <CategoryIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Basic Information
                      </Typography>
                      <Typography color='text.secondary'>
                        Essential category details and identification
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <TextField
                        fullWidth
                        label='Category Name'
                        name='name'
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        error={Boolean(errors.name)}
                        helperText={errors.name || 'Displayed to customers'}
                        disabled={isSubmitting}
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <TextField
                        fullWidth
                        label='Slug'
                        name='slug'
                        value={formData.slug}
                        onChange={handleInputChange}
                        required
                        error={Boolean(errors.slug)}
                        helperText={errors.slug || 'URL-friendly handle'}
                        disabled={isSubmitting}
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <TextField
                        fullWidth
                        label='Description'
                        name='description'
                        value={formData.description}
                        onChange={handleInputChange}
                        required
                        error={Boolean(errors.description)}
                        helperText={errors.description || ''}
                        disabled={isSubmitting}
                        multiline
                        rows={4}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* ===== CATEGORY SETTINGS ================================== */}
            <Grid size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <SettingsIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Category Settings
                      </Typography>
                      <Typography color='text.secondary'>
                        Configure hierarchy and visibility
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    {/* ── parent / children logic ───────────────────────── */}
                    {isRootCategory ? (
                      <Grid size={{ xs: 12, md: 4 }}>
                        <MultiSelectDropdown
                          label='Sub-categories'
                          multiple
                          value={childIds}
                          options={categoryOptions}
                          onChange={val =>
                            setChildIds(
                              typeof val === 'string' ? val.split(',') : val
                            )
                          }
                          disabled={true}
                        />
                      </Grid>
                    ) : (
                      <Grid size={{ xs: 12, md: 4 }}>
                        <MultiSelectDropdown
                          label='Parent Category'
                          value={formData.parentId}
                          options={categoryOptions}
                          onChange={value => {
                            setFormData(prev => ({
                              ...prev,
                              parentId: value as string,
                            }));
                          }}
                        />
                      </Grid>
                    )}

                    {/* status (is_active) */}
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MultiSelectDropdown
                        label='Status'
                        value={formData.status} // single-select → plain string
                        options={statusOptions}
                        onChange={handleSelectChange}
                        disabled={isSubmitting}
                      />
                    </Grid>

                    {/* rank */}
                    {/* <Grid xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Sort Order"
                        name="sortOrder"
                        type="number"
                        value={formData.sortOrder}
                        onChange={handleInputChange}
                        helperText="Lower numbers appear first"
                        disabled={isSubmitting}
                      />
                    </Grid> */}

                    {/* featured (is_internal) */}
                    {/* <Grid size={{ xs: 12, md: 4 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            name="featured"
                            checked={formData.featured}
                            onChange={handleInputChange}
                          />
                        }
                        label="Internal / Featured"
                        sx={{ mt: 2 }}
                      />
                    </Grid> */}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* ------------ snackbar ------------- */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
            severity={snackbar.severity}
            variant='filled'
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
