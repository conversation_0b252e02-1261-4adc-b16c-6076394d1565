'use client';

import React, { useMemo, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Paper,
  Divider,
  Snackbar,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Category as CategoryIcon,
  Image as ImageIcon,
  Settings as SettingsIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../theme/mui-theme';

import { MultiSelectDropdown } from '@/components/MultiSelectDropdown';
import { useMedusaBackendCategories } from '@/hooks/useMedusaAdminBackend';
import { z } from 'zod';
import medusaAdminAPI from '@/lib/medusa-admin-api';
import { useToast } from '@/components/common/ToastProvider';

/* ------------------------------------------------------------------ */
/* -------------------- type & initial values ----------------------- */
/* ------------------------------------------------------------------ */
interface CategoryFormData {
  name: string;
  slug: string;
  description: string;
  parentId: string; // optional
  status: 'active' | 'inactive' | 'draft';
  sortOrder: string;
  featured: boolean;
  seo: {
    title: string;
    description: string;
    keywords: string;
  };
}

const initialFormData: CategoryFormData = {
  name: '',
  slug: '',
  description: '',
  parentId: '',
  status: 'active',
  sortOrder: '0',
  featured: false,
  seo: { title: '', description: '', keywords: '' },
};

const categorySchema = z.object({
  name: z.string().min(2, 'Name is required'),
  slug: z.string().min(2, 'Slug is required'),
  description: z.string().min(2, 'Description is required'),
  parentId: z.string().optional(),
  status: z.enum(['active', 'inactive', 'draft']),
  sortOrder: z.string(),
  featured: z.boolean(),
  seo: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    keywords: z.string().optional(),
  }),
});

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

/* ================================================================== */
/* -------------------------  component  ----------------------------- */
/* ================================================================== */
export default function CategoryFormPage() {
  // Unified add/edit logic in a single file
  const router = useRouter();
  const toast = useToast();
  const { categories, loading: catsLoading } = useMedusaBackendCategories();
  const parentOptions = useMemo(
    () =>
      categories
        .filter(c => c.parent_category_id === null)
        .map(c => ({ value: c.id, label: c.name })),
    [categories]
  );
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });
  const [formData, setFormData] = useState<CategoryFormData>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentCategoryId, setCurrentCategoryId] = useState<
    string | undefined
  >(undefined);

  useEffect(() => {
    // This is the "new" page, so always start in create mode
    setIsEditMode(false);
    setFormData(initialFormData);
    setCurrentCategoryId(undefined);
  }, []);

  /* ------------ form validation hook ------------------------------ */
  // Zod validation
  const validate = (data: CategoryFormData) => {
    const result = categorySchema.safeParse(data);
    if (!result.success) {
      const fieldErrors: Record<string, string> = {};
      result.error.errors.forEach(e => {
        fieldErrors[e.path.join('.')] = e.message;
      });
      setErrors(fieldErrors);
      return false;
    }
    setErrors({});
    return true;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const setFieldValue = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(e);
    const value = e.target.value;
    if (value) {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setFieldValue('slug', slug);
    }
  };

  const handleNestedChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: { ...(prev as any)[parent], [child]: value },
      }));
    } else {
      handleChange(e as any);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    if (!validate(formData)) {
      setSnackbar({
        open: true,
        message: 'Please fix the errors before submitting',
        severity: 'error',
      });
      setIsSubmitting(false);
      return;
    }
    try {
      const payload: any = {
        name: formData.name,
        handle: formData.slug,
        description: formData.description,
        is_active: formData.status === 'active',
        parent_category_id: formData.parentId || null,
      };
      if (!isEditMode) {
        // Add category
        const res = await medusaAdminAPI.addSingleCategory(payload);
        if (res && (res as any).product_category) {
          const newId = (res as any).product_category.id;
          setCurrentCategoryId(newId);
          setIsEditMode(true);
          toast.success('Category created successfully');
          // Redirect to the edit page for the new category
          router.replace(`/admin/categories/${newId}/edit`);
        } else {
          toast.error('Category not created');
        }
      } else {
        // Edit category
        await medusaAdminAPI.updateSingleCategory(currentCategoryId!, payload);
        toast.success('Category updated successfully');
      }
    } catch (err) {
      toast.error('Error saving category');
      // setToast({ open: true, message: 'Error saving category', severity: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Removed duplicate handleNameChange and handleNestedChange

  /* ===================================================================
     ---------------------------  render  ------------------------------
     =================================================================== */
  // Remove nested ToastProvider, wrap the page with ToastProvider at a higher level (e.g., _app.tsx)
  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* -------------------- header -------------------------------- */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
            mb={2}
          >
            <Box>
              <Typography variant='h4' fontWeight='bold' color='primary.main'>
                {isEditMode ? 'Edit Category' : 'New Category'}
              </Typography>
              <Typography color='text.secondary' mt={1}>
                {isEditMode
                  ? 'Edit the product category details'
                  : 'Create a new product category for your store'}
              </Typography>
            </Box>
            <Stack direction='row' spacing={2}>
              <Button
                variant='outlined'
                startIcon={<CancelIcon />}
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                startIcon={<SaveIcon />}
                disabled={isSubmitting}
                sx={{ minWidth: 140 }}
                onClick={handleSubmit}
              >
                {isSubmitting
                  ? isEditMode
                    ? 'Saving…'
                    : 'Creating…'
                  : isEditMode
                    ? 'Save Changes'
                    : 'Create Category'}
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* -------------------- form ---------------------------------- */}
        <Box component='form' onSubmit={handleSubmit} sx={{ maxWidth: 1200 }}>
          <Grid container spacing={3}>
            {/* ===== Basic info ======================================== */}
            <Grid size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' spacing={2} alignItems='center' mb={3}>
                    <CategoryIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Basic Information
                      </Typography>
                      <Typography color='text.secondary'>
                        Essential category details and identification
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <TextField
                        fullWidth
                        name='name'
                        label='Category Name'
                        value={formData.name}
                        onChange={handleNameChange}
                        required
                        error={Boolean(errors.name)}
                        helperText={errors.name || 'Shown to customers'}
                        disabled={isSubmitting}
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <TextField
                        fullWidth
                        name='slug'
                        label='Slug'
                        value={formData.slug}
                        onChange={handleChange}
                        required
                        error={Boolean(errors.slug)}
                        helperText={errors.slug || 'URL-friendly handle'}
                        disabled={isSubmitting}
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <TextField
                        fullWidth
                        name='description'
                        label='Description'
                        value={formData.description}
                        onChange={handleChange}
                        required
                        multiline
                        rows={4}
                        error={Boolean(errors.description)}
                        helperText={
                          errors.description || 'Visible to customers'
                        }
                        disabled={isSubmitting}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* ===== Organization ====================================== */}
            <Grid size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' spacing={2} alignItems='center' mb={3}>
                    <SettingsIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Organization
                      </Typography>
                      <Typography color='text.secondary'>
                        Category hierarchy and settings
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    {/* Parent selector (optional) */}
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MultiSelectDropdown
                        label='Parent Category'
                        value={formData.parentId}
                        options={parentOptions}
                        onChange={val =>
                          setFieldValue('parentId', val as string)
                        }
                        disabled={isSubmitting || catsLoading}
                      />
                    </Grid>

                    {/* Status */}
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MultiSelectDropdown
                        label='Status'
                        value={formData.status}
                        options={statusOptions}
                        onChange={val =>
                          setFieldValue(
                            'status',
                            val as CategoryFormData['status']
                          )
                        }
                        disabled={isSubmitting}
                      />
                    </Grid>

                    {/* Sort order */}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* ---------------- Snackbar ---------------- */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        >
          <Alert
            severity={snackbar.severity}
            variant='filled'
            onClose={() => setSnackbar(s => ({ ...s, open: false }))}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
