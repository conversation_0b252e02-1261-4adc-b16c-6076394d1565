'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import { ExclamationTriangleIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useMedusaBackendCategories } from '@/hooks/useMedusaAdminBackend';
import ConfirmDialog from '@/components/ConfirmDialog';
import { useToast } from '@/components/common/ToastProvider';
import { Button, Stack, Typography } from '@mui/material';

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: string;
  status: 'active' | 'inactive';
  productsCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export default function CategoriesPage() {
  const router = useRouter();

  const {
    error,
    fetchCategories: listAllCategories,
    categories,
    loading: loadingCategories,
    deleteCategory,
  } = useMedusaBackendCategories();

  const fetchCategories = async () => {
    try {
      await listAllCategories();
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };
  useEffect(() => {
    // Simulate API call

    fetchCategories();
  }, []);

  const handleEdit = (category: Category) => {
    router.push(`/admin/categories/${category.id}/edit`);
  };

  const filteredCategories = useMemo(() => {
    if (!categories) return;
    console.log({ categories });

    return categories
      .sort(
        (a, b) =>
          new Date(String(b.created_at)).getTime() -
          new Date(String(a.created_at)).getTime()
      )
      .map(data => ({
        id: data?.id,
        name: data?.name,
        description: data?.description,
        status: (data as any)?.is_active ? 'active' : 'inactive',
        parentCategory: data?.parent_category?.name,
        updatedAt: data?.updated_at,
      }));
  }, [categories]);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);
  const toast = useToast();

  const handleDelete = (category: Category) => {
    if (category.productsCount > 0) {
      toast.error(
        `Cannot delete category "${category.name}" because it contains ${category.productsCount} products.`
      );
      return;
    }
    setCategoryToDelete(category);
    // Open dialog only after category is set to avoid race condition
    setTimeout(() => setDeleteDialogOpen(true), 0);
  };

  const confirmDelete = async () => {
    if (!categoryToDelete) return;
    setIsDeleting(true);
    try {
      // Replace with your actual delete API call
      await deleteCategory(categoryToDelete.id);
      await listAllCategories();
      toast.success('Category deleted successfully.');
      setDeleteDialogOpen(false);
      setCategoryToDelete(null);
      // Optionally refresh categories list
      await listAllCategories();
    } catch (error) {
      toast.error('Error deleting category');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: Category['status']) => {
    console.log({ status });

    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      inactive: { color: 'bg-red-100 text-red-800', label: 'Inactive' },
    };

    const config = statusConfig[status];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config?.color}`}
      >
        {config?.label}
      </span>
    );
  };

  const columns = [
    {
      key: 'name',
      label: 'Category',
      sortable: true,
      render: (value: string, row: Category) => (
        <div>
          <div className='text-sm font-medium text-gray-900'>{value}</div>
          <div className='text-sm text-gray-500'>{row.slug}</div>
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: string) => (
        <span className='text-sm text-gray-500 max-w-xs truncate block'>
          {value || '-'}
        </span>
      ),
    },
    // {
    //   key: 'productsCount',
    //   label: 'Products',
    //   sortable: true,
    //   render: (value: number) => <span className="text-sm font-medium text-gray-900">{value}</span>,
    // },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: Category['status']) => getStatusBadge(value),
    },
    {
      key: 'parentCategory',
      label: 'Parent Category',
      sortable: true,
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{value || '-'}</span>
      ),
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{formatDate(value)}</span>
      ),
    },
  ];

  const breadcrumbs = [{ label: 'Categories', active: true }];

  return (
    <div className='space-y-6'>
      <PageHeader
        title='Categories'
        description='Organize your products into categories'
        breadcrumbs={breadcrumbs}
        actions={
          <ActionButton href='/admin/categories/new' icon={PlusIcon}>
            Add Category
          </ActionButton>
        }
      />
      <DataTable
        columns={columns}
        data={filteredCategories || []}
        loading={loadingCategories}
        searchable
        filterable
        pagination
        pageSize={10}
        onEdit={handleEdit}
        onDelete={handleDelete}
        emptyMessage='No categories found. Create your first category to get started.'
      />
      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={
          deleteDialogOpen &&
          !!categoryToDelete &&
          Boolean(categoryToDelete.name)
        }
        onClose={() => {
          setDeleteDialogOpen(false);
          setTimeout(() => setCategoryToDelete(null), 200);
        }}
        header={
          <Stack direction='row' alignItems='center' gap={1}>
            <ExclamationTriangleIcon className='h-5 w-5 text-amber-500' />
            <Typography variant='h6'>Delete Category</Typography>
          </Stack>
        }
        body={
          <Typography>
            Are you sure you want to delete{' '}
            <strong>{categoryToDelete?.name}</strong>? This action cannot be
            undone.
          </Typography>
        }
        actions={
          <>
            <Button
              onClick={() => {
                setDeleteDialogOpen(false);
                setTimeout(() => setCategoryToDelete(null), 200);
              }}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              color='error'
              variant='contained'
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting…' : 'Delete'}
            </Button>
          </>
        }
      />
      {/* ToastProvider handles feedback */}
    </div>
  );
}
