'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  TextField,
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Paper,
  Divider,
  Snackbar,
} from '@mui/material';
import {
  Collections as CollectionIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../../theme/mui-theme';

import { useMedusaBackendCollections } from '@/hooks/useMedusaAdminBackend';

/* --------------------------- types & helpers --------------------------- */
interface CollectionFormData {
  title: string;
  handle: string;
  metadata: string; // keep raw JSON as string for a textarea
}

const initialFormData: CollectionFormData = {
  title: '',
  handle: '',
  metadata: '',
};

const ISOtoLocal = (iso?: string) =>
  iso ? new Date(iso).toLocaleString() : '—';

/* ====================================================================== */
/*                               component                                */
/* ====================================================================== */
export default function EditCollectionPage() {
  const router = useRouter();
  const params = useParams<{ id: string }>();

  const { singleCollection, loading, fetchSingleCollection, updateCollection } =
    useMedusaBackendCollections();

  const [formData, setFormData] = useState<CollectionFormData>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  /* ------------------------ fetch on mount ------------------------ */
  useEffect(() => {
    if (params?.id) fetchSingleCollection(params.id);
  }, [params?.id, fetchSingleCollection]);

  /* ------------- put collection data into form state -------------- */
  useEffect(() => {
    if (!singleCollection) return;

    setFormData({
      title: singleCollection.title ?? '',
      handle: singleCollection.handle ?? '',
      metadata: singleCollection.metadata
        ? JSON.stringify(singleCollection.metadata, null, 2)
        : '',
    });
  }, [singleCollection]);

  /* ------------------------- change handlers ---------------------- */
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(e);
    const slug = e.target.value
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-|-$/g, '');
    setFormData(prev => ({ ...prev, handle: slug }));
  };

  /* -------------------------- validation -------------------------- */
  const validate = () => {
    const err: Record<string, string> = {};
    if (!formData.title.trim()) err.title = 'Title is required';
    if (!formData.handle.trim()) err.handle = 'Slug is required';
    try {
      if (formData.metadata.trim()) JSON.parse(formData.metadata); // will throw if invalid JSON
    } catch {
      err.metadata = 'Metadata must be valid JSON';
    }
    setErrors(err);
    return Object.keys(err).length === 0;
  };

  /* ----------------------------- submit --------------------------- */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) {
      setSnackbar({
        open: true,
        message: 'Fix errors before submitting',
        severity: 'error',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await updateCollection(params?.id, {
        title: formData.title,
        handle: formData.handle,
      });

      setSnackbar({
        open: true,
        message: 'Collection saved',
        severity: 'success',
      });
    } catch (err) {
      console.error(err);
      setSnackbar({ open: true, message: 'Save failed', severity: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  };

  /* ------------------------- loading guard ------------------------ */
  if (loading) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box
          sx={{
            minHeight: '100vh',
            p: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography>Loading collection…</Typography>
        </Box>
      </ThemeProvider>
    );
  }

  /* ----------------------------- UI ------------------------------- */
  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* ===== header ===== */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
            mb={2}
          >
            <Box>
              <Typography variant='h4' fontWeight='bold' color='primary.main'>
                Edit Collection
              </Typography>
              <Typography color='text.secondary' mt={1}>
                Update basic information
              </Typography>
            </Box>

            <Stack direction='row' spacing={2}>
              <Button
                variant='outlined'
                startIcon={<CancelIcon />}
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                startIcon={<SaveIcon />}
                disabled={isSubmitting}
                onClick={handleSubmit}
                sx={{ minWidth: 140 }}
              >
                {isSubmitting ? 'Saving…' : 'Save'}
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* ===== form card ===== */}
        <Box component='form' onSubmit={handleSubmit}>
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 4 }}>
              <Stack direction='row' spacing={2} alignItems='center' mb={3}>
                <CollectionIcon color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    Basic Information
                  </Typography>
                  <Typography color='text.secondary'>
                    Collection title and handle
                  </Typography>
                </Box>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    name='title'
                    label='Title'
                    value={formData.title}
                    onChange={handleTitleChange}
                    error={Boolean(errors.title)}
                    helperText={errors.title}
                    disabled={isSubmitting}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    name='handle'
                    label='Slug'
                    value={formData.handle}
                    onChange={handleChange}
                    error={Boolean(errors.handle)}
                    helperText={errors.handle}
                    disabled={isSubmitting}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>

        {/* ===== snackbar ===== */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        >
          <Alert
            onClose={() => setSnackbar(s => ({ ...s, open: false }))}
            severity={snackbar.severity}
            variant='filled'
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
