'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Button,
  Stack,
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Paper,
  Divider,
  Chip,
} from '@mui/material';
import {
  Edit as EditIcon,
  ArrowBack as ArrowBackIcon,
  Collections as CollectionIcon,
  Settings as SettingsIcon,
  Search as SeoIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../theme/mui-theme';

interface Collection {
  id: string;
  name: string;
  slug: string;
  description: string;
  status: 'active' | 'inactive' | 'draft';
  sortOrder: number;
  featured: boolean;
  productsCount: number;
  seo: {
    title?: string;
    description?: string;
    keywords?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Mock collection data
const mockCollection: Collection = {
  id: '1',
  name: 'Summer Collection 2024',
  slug: 'summer-collection-2024',
  description:
    'Discover our latest summer collection featuring lightweight fabrics, vibrant colors, and comfortable designs perfect for the warm season.',
  status: 'active',
  sortOrder: 1,
  featured: true,
  productsCount: 24,
  seo: {
    title: 'Summer Collection 2024 - Latest Fashion Trends',
    description:
      'Shop our exclusive summer collection with the latest fashion trends, lightweight fabrics, and vibrant designs.',
    keywords:
      'summer fashion, lightweight clothing, vibrant colors, summer trends',
  },
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-20T14:45:00Z',
};

export default function CollectionViewPage() {
  const router = useRouter();
  const params = useParams();
  const [collection, setCollection] = useState<Collection | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCollection = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setCollection(mockCollection);
      } catch (error) {
        console.error('Error fetching collection:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCollection();
  }, [params.id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '50vh',
            }}
          >
            <Typography>Loading collection...</Typography>
          </Box>
        </Box>
      </ThemeProvider>
    );
  }

  if (!collection) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '50vh',
            }}
          >
            <Typography>Collection not found</Typography>
          </Box>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* Header */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
          >
            <Box>
              <Stack direction='row' alignItems='center' spacing={2} mb={1}>
                <Button
                  variant='outlined'
                  startIcon={<ArrowBackIcon />}
                  onClick={() => router.push('/admin/collections')}
                >
                  Back to Collections
                </Button>
                <Chip
                  label={collection.status.toUpperCase()}
                  color={getStatusColor(collection.status) as any}
                  size='small'
                />
                {collection.featured && (
                  <Chip
                    label='FEATURED'
                    color='primary'
                    variant='outlined'
                    size='small'
                  />
                )}
              </Stack>
              <Typography
                variant='h4'
                component='h1'
                fontWeight='bold'
                color='primary.main'
              >
                {collection.name}
              </Typography>
              <Typography variant='body1' color='text.secondary' mt={1}>
                Slug: {collection.slug} • {collection.productsCount} products
              </Typography>
            </Box>
            <Button
              variant='contained'
              startIcon={<EditIcon />}
              onClick={() =>
                router.push(`/admin/collections/${collection.id}/edit`)
              }
            >
              Edit Collection
            </Button>
          </Stack>
        </Paper>

        {/* Content */}
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                  <CollectionIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Basic Information
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid size={12}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Collection Name
                    </Typography>
                    <Typography variant='body1' fontWeight='medium'>
                      {collection.name}
                    </Typography>
                  </Grid>
                  <Grid size={12}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Slug
                    </Typography>
                    <Typography
                      variant='body1'
                      fontWeight='medium'
                      sx={{ fontFamily: 'monospace' }}
                    >
                      {collection.slug}
                    </Typography>
                  </Grid>
                  <Grid size={12}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Description
                    </Typography>
                    <Typography variant='body1'>
                      {collection.description}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Settings */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                  <SettingsIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Collection Settings
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid size={12}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Status
                    </Typography>
                    <Chip
                      label={collection.status.toUpperCase()}
                      color={getStatusColor(collection.status) as any}
                      size='small'
                    />
                  </Grid>
                  <Grid size={12}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Sort Order
                    </Typography>
                    <Typography variant='body1' fontWeight='medium'>
                      {collection.sortOrder}
                    </Typography>
                  </Grid>
                  <Grid size={12}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Featured Collection
                    </Typography>
                    <Chip
                      label={collection.featured ? 'YES' : 'NO'}
                      color={collection.featured ? 'primary' : 'default'}
                      size='small'
                    />
                  </Grid>
                  <Grid size={12}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Products Count
                    </Typography>
                    <Typography
                      variant='h6'
                      color='primary.main'
                      fontWeight='bold'
                    >
                      {collection.productsCount} products
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* SEO Information */}
          {collection.seo &&
            (collection.seo.title ||
              collection.seo.description ||
              collection.seo.keywords) && (
              <Grid size={12}>
                <Card elevation={2} sx={{ borderRadius: 2 }}>
                  <CardContent sx={{ p: 4 }}>
                    <Stack
                      direction='row'
                      alignItems='center'
                      spacing={2}
                      mb={3}
                    >
                      <SeoIcon color='primary' />
                      <Typography variant='h6' fontWeight='bold'>
                        SEO Information
                      </Typography>
                    </Stack>
                    <Divider sx={{ mb: 3 }} />

                    <Grid container spacing={3}>
                      {collection.seo.title && (
                        <Grid size={12}>
                          <Typography
                            variant='subtitle2'
                            color='text.secondary'
                            gutterBottom
                          >
                            SEO Title
                          </Typography>
                          <Typography variant='body1'>
                            {collection.seo.title}
                          </Typography>
                        </Grid>
                      )}
                      {collection.seo.description && (
                        <Grid size={12}>
                          <Typography
                            variant='subtitle2'
                            color='text.secondary'
                            gutterBottom
                          >
                            SEO Description
                          </Typography>
                          <Typography variant='body1'>
                            {collection.seo.description}
                          </Typography>
                        </Grid>
                      )}
                      {collection.seo.keywords && (
                        <Grid size={12}>
                          <Typography
                            variant='subtitle2'
                            color='text.secondary'
                            gutterBottom
                          >
                            SEO Keywords
                          </Typography>
                          <Typography variant='body1'>
                            {collection.seo.keywords}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            )}

          {/* Timestamps */}
          <Grid size={12}>
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Typography variant='h6' fontWeight='bold' mb={3}>
                  Timestamps
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Created At
                    </Typography>
                    <Typography variant='body1'>
                      {formatDate(collection.createdAt)}
                    </Typography>
                  </Grid>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      gutterBottom
                    >
                      Last Updated
                    </Typography>
                    <Typography variant='body1'>
                      {formatDate(collection.updatedAt)}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </ThemeProvider>
  );
}
