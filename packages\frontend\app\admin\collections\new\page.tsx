'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Paper,
  Divider,
  Snackbar,
} from '@mui/material';
import {
  Collections as CollectionIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../theme/mui-theme';

import { useMedusaBackendCollections } from '@/hooks/useMedusaAdminBackend';

/* ------------------------------------------------------------------ */
/* --------------------------- types -------------------------------- */
/* ------------------------------------------------------------------ */
interface CollectionFormData {
  title: string;
  handle: string;
  metadata: string; // raw JSON in a textarea
}

const initialFormData: CollectionFormData = {
  title: '',
  handle: '',
  metadata: '',
};

/* ================================================================== */
/* ------------------------- component ------------------------------ */
/* ================================================================== */
export default function NewCollectionPage() {
  const router = useRouter();
  const { createCollection } = useMedusaBackendCollections();

  const [formData, setFormData] = useState<CollectionFormData>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  /* --------------------- change handlers -------------------------- */
  const handleChange =
    (name: keyof CollectionFormData) =>
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
      setFormData(prev => ({ ...prev, [name]: e.target.value }));

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    handleChange('title')(e);

    const slug = value
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-|-$/g, '');
    setFormData(prev => ({ ...prev, handle: slug }));
  };

  /* ----------------------- validation ----------------------------- */
  const validate = () => {
    const err: Record<string, string> = {};
    if (!formData.title.trim()) err.title = 'Title is required';
    if (!formData.handle.trim()) err.handle = 'Slug is required';

    setErrors(err);
    return Object.keys(err).length === 0;
  };

  /* ------------------------ submit -------------------------------- */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) {
      setSnackbar({
        open: true,
        message: 'Fix the errors first',
        severity: 'error',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await createCollection({
        title: formData.title,
        handle: formData.handle,
      });

      setSnackbar({
        open: true,
        message: 'Collection created!',
        severity: 'success',
      });
      router.push('/admin/collections');
    } catch (err) {
      console.error(err);
      setSnackbar({
        open: true,
        message: 'Creation failed',
        severity: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  /* -------------------------- UI ---------------------------------- */
  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* ===== header ===== */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
            mb={2}
          >
            <Box>
              <Typography variant='h4' fontWeight='bold' color='primary.main'>
                New Collection
              </Typography>
              <Typography color='text.secondary' mt={1}>
                Create a new product collection
              </Typography>
            </Box>
            <Stack direction='row' spacing={2}>
              <Button
                variant='outlined'
                startIcon={<CancelIcon />}
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                startIcon={<SaveIcon />}
                sx={{ minWidth: 140 }}
                disabled={isSubmitting}
                onClick={handleSubmit}
              >
                {isSubmitting ? 'Saving…' : 'Create'}
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* ===== form card ===== */}
        <Box component='form' onSubmit={handleSubmit}>
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 4 }}>
              <Stack direction='row' spacing={2} alignItems='center' mb={3}>
                <CollectionIcon color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    Basic Information
                  </Typography>
                  <Typography color='text.secondary'>
                    Collection title and handle
                  </Typography>
                </Box>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    label='Title'
                    value={formData.title}
                    onChange={handleTitleChange}
                    error={Boolean(errors.title)}
                    helperText={errors.title}
                    disabled={isSubmitting}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    label='Slug'
                    value={formData.handle}
                    onChange={handleChange('handle')}
                    error={Boolean(errors.handle)}
                    helperText={errors.handle}
                    disabled={isSubmitting}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>

        {/* ===== snackbar ===== */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        >
          <Alert
            onClose={() => setSnackbar(s => ({ ...s, open: false }))}
            severity={snackbar.severity}
            variant='filled'
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
