'use client';

import React, {
  useState,
  useEffect,
  useMemo,
  Suspense,
  lazy,
  useTransition,
} from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import { PlusIcon, RectangleStackIcon } from '@heroicons/react/24/outline';
import { useMedusaBackendCollections } from '@/hooks/useMedusaAdminBackend';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { Button, Typography, Stack } from '@mui/material';
import { useApiLoadingManager } from '@/hooks/useLoadingManager';

// Lazy load heavy components for better performance
const DataTable = lazy(() => import('@/components/admin/DataTable'));
const ConfirmDialog = lazy(() => import('@/components/ConfirmDialog'));
interface Collection {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  status: 'active' | 'inactive';
  productsCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

// Loading fallback component
const TableLoadingFallback = () => (
  <div className='animate-pulse'>
    <div className='h-8 bg-gray-200 rounded mb-4'></div>
    <div className='space-y-3'>
      {[...Array(5)].map((_, i) => (
        <div key={i} className='h-12 bg-gray-100 rounded'></div>
      ))}
    </div>
  </div>
);

export default function CollectionsPage() {
  const router = useRouter();
  const { callApi, withFormSubmission } = useApiLoadingManager();
  const [dlgOpen, setDlgOpen] = useState(false);
  const [target, setTarget] = useState<Collection | null>(null);
  const [isPending, startTransition] = useTransition();

  const {
    error,
    fetchCollections: listAllCollections,
    loading: loadingCollections,
    collections,
    deleteCollection,
  } = useMedusaBackendCollections();

  const filteredCollections = useMemo(() => {
    if (!collections) return;

    return collections
      .sort(
        (a, b) =>
          new Date(String(b.created_at)).getTime() -
          new Date(String(a.created_at)).getTime()
      )
      .map(data => ({
        id: data?.id,
        name: data?.title,
        handle: data?.handle,
        createdAt: data?.created_at,
        updatedAt: data?.updated_at,
      }));
  }, [collections]);

  useEffect(() => {
    fetchCollections();
  }, []);

  const fetchCollections = async () => {
    try {
      await callApi(
        async () => {
          await listAllCollections();
        },
        {
          message: 'Loading collections...',
          type: 'data',
          showProgress: true,
        }
      );
    } catch (error) {
      console.error('Error fetching collections:', error);
    }
  };
  const handleView = (collection: Collection) => {
    router.push(`/admin/collections/${collection.id}`);
  };

  const handleEdit = (collection: Collection) => {
    router.push(`/admin/collections/${collection.id}/edit`);
  };

  const handleDelete = async (collection: Collection) => {
    if (collection.productsCount > 0) {
      alert(
        `Cannot delete collection "${collection.name}" because it contains ${collection.productsCount} products.`
      );
      return;
    }

    if (
      window.confirm(
        `Are you sure you want to delete collection "${collection.name}"?`
      )
    ) {
      try {
        await withFormSubmission(
          async () => {
            await new Promise(resolve => setTimeout(resolve, 500));
            // setCollections(prev => prev.filter(c => c.id !== collection.id));
          },
          {
            message: `Deleting collection "${collection.name}"...`,
            allowCancel: false,
          }
        );
      } catch (error) {
        console.error('Error deleting collection:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: Collection['status']) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      inactive: { color: 'bg-red-100 text-red-800', label: 'Inactive' },
    };

    const config = statusConfig[status];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const openDelete = (col: Collection) => {
    setTarget(col);
    setDlgOpen(true);
  };
  const closeDelete = () => setDlgOpen(false);

  /* confirm action */
  const confirmDelete = async () => {
    if (!target) return;
    console.log({ target });

    try {
      await withFormSubmission(
        async () => {
          await deleteCollection(target.id); // your API call
          await fetchCollections();
        },
        {
          message: `Deleting collection "${target.name}"...`,
          allowCancel: false,
        }
      );
      closeDelete();
    } catch (error) {
      console.error('Error deleting collection:', error);
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Collection',
      sortable: true,
      render: (value: string, row: Collection) => (
        <div className='flex items-center'>
          <div className='flex-shrink-0 h-8 w-8 mr-3'>
            <RectangleStackIcon className='h-8 w-8 text-blue-500' />
          </div>
          <div>
            <div className='text-sm font-medium text-gray-900'>{value}</div>
            <div className='text-sm text-gray-500'>{row.slug}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'handle',
      label: 'Slug',
      render: (value: string) => (
        <span className='text-sm text-gray-500 max-w-xs truncate block'>
          {value || '-'}
        </span>
      ),
    },
    // {
    //   key: 'productsCount',
    //   label: 'Products',
    //   sortable: true,
    //   render: (value: number) => (
    //     <span className="text-sm font-medium text-gray-900">{value}</span>
    //   ),
    // },
    // {
    //   key: 'status',
    //   label: 'Status',
    //   sortable: true,
    //   render: (value: Collection['status']) => getStatusBadge(value),
    // },
    {
      key: 'createdAt',
      label: 'Created At',
      sortable: true,
      render: (value: number) => (
        <span className='text-sm text-gray-500'>{formatDate(value)}</span>
      ),
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{formatDate(value)}</span>
      ),
    },
  ];

  const breadcrumbs = [{ label: 'Collections', active: true }];

  return (
    <div className='space-y-6'>
      <PageHeader
        title='Collections'
        description='Organize products into curated collections'
        breadcrumbs={breadcrumbs}
        actions={
          <ActionButton href='/admin/collections/new' icon={PlusIcon}>
            Add Collection
          </ActionButton>
        }
      />

      <Suspense fallback={<TableLoadingFallback />}>
        <DataTable
          columns={columns}
          data={filteredCollections}
          loading={loadingCollections}
          searchable
          filterable
          pagination
          pageSize={10}
          // onView={handleView}
          onEdit={handleEdit}
          onDelete={openDelete}
          emptyMessage='No collections found. Create your first collection to get started.'
        />
      </Suspense>

      {/* ---------- at the end of JSX ---------- */}
      <Suspense fallback={<div>Loading dialog...</div>}>
        <ConfirmDialog
          open={dlgOpen}
          onClose={closeDelete}
          header={
            <Stack direction='row' alignItems='center' gap={1}>
              <ExclamationTriangleIcon className='h-5 w-5 text-amber-500' />
              <Typography variant='h6'>Delete collection</Typography>
            </Stack>
          }
          body={
            <Typography>
              Are you sure you want to delete <strong>{target?.name}</strong>?
              This action cannot be undone.
            </Typography>
          }
          actions={
            <>
              <Button onClick={closeDelete}>Cancel</Button>
              <Button color='error' variant='contained' onClick={confirmDelete}>
                Delete
              </Button>
            </>
          }
        />
      </Suspense>
    </div>
  );
}
