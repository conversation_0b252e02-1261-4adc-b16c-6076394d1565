'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  ThemeProvider,
  Box,
  Paper,
  Stack,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Divider,
  Snackbar,
  Alert,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  LocalOffer as PromoIcon,
  Percent as DiscountIcon,
  Settings as SettingsIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

import muiTheme from '../../../../../theme/mui-theme';
import { useMedusaBackendPromotions } from '@/hooks/useMedusaAdminBackend';

/* ------------------------------------------------------------------ */
/* --------------------------- helpers ------------------------------ */
/* ------------------------------------------------------------------ */

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'active', label: 'Active' },
  { value: 'disabled', label: 'Disabled' },
  { value: 'archived', label: 'Archived' },
];

const typeOptions = [
  { value: 'percentage', label: 'Percentage' },
  { value: 'fixed', label: 'Fixed Amount' },
];

/* ------------------------------------------------------------------ */
/* --------------------------- page --------------------------------- */
/* ------------------------------------------------------------------ */

interface FormState {
  code: string;
  description: string;
  type: 'percentage' | 'fixed';
  value: string;
  status: 'draft' | 'active' | 'disabled' | 'archived';
}

const emptyState: FormState = {
  code: '',
  description: '',
  type: 'percentage',
  value: '',
  status: 'draft',
};

export default function EditPromotionPage() {
  const router = useRouter();
  const { id } = useParams<{ id: string }>();

  const {
    singlePromotion,
    loading,
    error,
    fetchSinglePromotion,
    updatePromotion,
  } = useMedusaBackendPromotions();

  const [form, setForm] = useState<FormState>(emptyState);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);
  const [toast, setToast] = useState<{
    open: boolean;
    msg: string;
    sev: 'success' | 'error';
  }>({
    open: false,
    msg: '',
    sev: 'success',
  });

  /* ---------- fetch once ---------- */
  useEffect(() => {
    if (id) fetchSinglePromotion(id);
  }, [id, fetchSinglePromotion]);

  /* ---------- map API → form ---------- */
  useEffect(() => {
    if (!singlePromotion) return;
    setForm({
      code: singlePromotion.code,
      description: '', // Medusa core doesn’t have one; keep for future
      type: singlePromotion.application_method.type as 'percentage' | 'fixed',
      value: String(singlePromotion.application_method.value),
      status: singlePromotion.status,
    });
  }, [singlePromotion]);

  /* ---------- handlers ---------- */
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any) => {
      const { name, value } = e.target;
      setForm(f => ({ ...f, [name]: value }));
      if (formErrors[name]) setFormErrors(err => ({ ...err, [name]: '' }));
    },
    [formErrors]
  );

  const validate = () => {
    const errs: Record<string, string> = {};
    if (!form.code.trim()) errs.code = 'Code required';
    if (
      !form.value.trim() ||
      isNaN(Number(form.value)) ||
      Number(form.value) <= 0
    )
      errs.value = 'Enter a valid number';
    setFormErrors(errs);
    return Object.keys(errs).length === 0;
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id) return;
    if (!validate()) return;

    setSaving(true);
    try {
      await updatePromotion(id, {
        code: form.code,
        status: form.status,
        application_method: {
          type: form.type,
          target_type: 'order',
          value: Number(form.value),
          ...(form.type === 'fixed' && { currency_code: 'inr' }),
        },
      });
      setToast({ open: true, sev: 'success', msg: 'Promotion updated!' });
      await fetchSinglePromotion(id);
    } catch (err) {
      console.error(err);
      setToast({ open: true, sev: 'error', msg: 'Update failed' });
    } finally {
      setSaving(false);
    }
  };

  /* ---------- render ---------- */
  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* header */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
          >
            <Box>
              <Typography variant='h4' fontWeight='bold' color='primary.main'>
                Edit Promotion
              </Typography>
              <Typography color='text.secondary'>
                Update code, value & status
              </Typography>
            </Box>
            <Stack direction='row' spacing={2}>
              <Button
                startIcon={<CancelIcon />}
                onClick={() => router.back()}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                startIcon={<SaveIcon />}
                onClick={onSubmit}
                disabled={saving}
              >
                {saving ? 'Saving…' : 'Save'}
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {loading ? (
          <Typography>Loading…</Typography>
        ) : error ? (
          <Alert severity='error'>{error}</Alert>
        ) : (
          <Box component='form' onSubmit={onSubmit}>
            <Grid container spacing={3}>
              {/* BASIC */}
              <Grid item size={12}>
                <Card elevation={2} sx={{ borderRadius: 2 }}>
                  <CardContent sx={{ p: 4 }}>
                    <Stack direction='row' spacing={2} mb={3}>
                      <PromoIcon color='primary' />
                      <Typography variant='h6' fontWeight='bold'>
                        Basic
                      </Typography>
                    </Stack>
                    <Divider sx={{ mb: 3 }} />

                    <Grid container spacing={3}>
                      <Grid item size={{ xs: 12, md: 4 }}>
                        <TextField
                          label='Promotion Code'
                          name='code'
                          fullWidth
                          value={form.code}
                          onChange={handleChange}
                          error={!!formErrors.code}
                          helperText={formErrors.code}
                        />
                      </Grid>
                      <Grid item size={{ xs: 12, md: 4 }}>
                        <FormControl fullWidth>
                          <InputLabel>Status</InputLabel>
                          <Select
                            label='Status'
                            name='status'
                            value={form.status}
                            onChange={handleChange}
                          >
                            {statusOptions.map(o => (
                              <MenuItem key={o.value} value={o.value}>
                                {o.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* DISCOUNT */}
              <Grid item size={12}>
                <Card elevation={2} sx={{ borderRadius: 2 }}>
                  <CardContent sx={{ p: 4 }}>
                    <Stack direction='row' spacing={2} mb={3}>
                      <DiscountIcon color='primary' />
                      <Typography variant='h6' fontWeight='bold'>
                        Discount
                      </Typography>
                    </Stack>
                    <Divider sx={{ mb: 3 }} />

                    <Grid container spacing={3}>
                      <Grid item size={{ xs: 12, md: 4 }}>
                        <FormControl fullWidth>
                          <InputLabel>Type</InputLabel>
                          <Select
                            name='type'
                            label='Type'
                            value={form.type}
                            onChange={handleChange}
                          >
                            {typeOptions.map(t => (
                              <MenuItem key={t.value} value={t.value}>
                                {t.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item size={{ xs: 12, md: 4 }}>
                        <TextField
                          label='Value'
                          name='value'
                          type='number'
                          fullWidth
                          value={form.value}
                          onChange={handleChange}
                          error={!!formErrors.value}
                          helperText={formErrors.value}
                          InputProps={{
                            endAdornment:
                              form.type === 'percentage' ? '%' : '₹',
                          }}
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* toast */}
        <Snackbar
          open={toast.open}
          autoHideDuration={4000}
          onClose={() => setToast(t => ({ ...t, open: false }))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert severity={toast.sev}>{toast.msg}</Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
