'use client';

import React, { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  ThemeProvider,
  Box,
  Paper,
  Stack,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  LocalOffer as PromoIcon,
  Percent as PercentIcon,
  Schedule as CalendarIcon,
} from '@mui/icons-material';

import muiTheme from '../../../../theme/mui-theme';
import { useMedusaBackendPromotions } from '@/hooks/useMedusaAdminBackend';

/* ------------------------ helpers ------------------------ */

const formatPrice = (cents: number, currency = 'INR') =>
  new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
  }).format(cents / 100);

const formatDate = (iso: string, withTime = false) =>
  new Date(iso).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...(withTime && { hour: '2-digit', minute: '2-digit' }),
  });

const statusColor = (status: string) =>
  ({
    active: 'success',
    draft: 'default',
    disabled: 'warning',
    archived: 'error',
  })[status] ?? 'default';

const discountLabel = (pm: any) => {
  if (!pm) return '—';
  if (pm.type === 'percentage') return `${pm.value}% off`;
  if (pm.type === 'fixed')
    return `${formatPrice(pm.value, pm.currency_code ?? 'INR')} off`;
  return 'Free shipping';
};

/* -------------------------- page ------------------------- */

export default function PromotionViewPage() {
  const router = useRouter();
  const { id } = useParams<{ id: string }>(); // /admin/coupons/[id]

  /* hook gives us data + helpers */
  const { singlePromotion, loading, error, fetchSinglePromotion } =
    useMedusaBackendPromotions();

  /* fetch once on mount / id change */
  useEffect(() => {
    if (id) fetchSinglePromotion(id);
  }, [id, fetchSinglePromotion]);

  /* ---------- render guards ---------- */

  if (loading)
    return (
      <ThemeProvider theme={muiTheme}>
        <Box sx={{ p: 3, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </ThemeProvider>
    );

  if (error)
    return (
      <ThemeProvider theme={muiTheme}>
        <Box sx={{ p: 3 }}>
          <Alert severity='error'>{error}</Alert>
        </Box>
      </ThemeProvider>
    );

  if (!singlePromotion)
    return (
      <ThemeProvider theme={muiTheme}>
        <Box sx={{ p: 3 }}>
          <Typography>Promotion not found.</Typography>
        </Box>
      </ThemeProvider>
    );

  const pm = singlePromotion.application_method;

  /* ----------------- main view ----------------- */
  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* ===== header ===== */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
          >
            <Box>
              <Stack direction='row' alignItems='center' spacing={2} mb={1}>
                <Button
                  variant='outlined'
                  startIcon={<ArrowBackIcon />}
                  onClick={() => router.push('/admin/coupons')}
                >
                  Back to Coupons
                </Button>
                <Chip
                  label={singlePromotion.status.toUpperCase()}
                  color={statusColor(singlePromotion.status) as any}
                  size='small'
                />
              </Stack>
              <Typography variant='h4' fontWeight='bold' color='primary.main'>
                {singlePromotion.code}
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                {singlePromotion.type} • {pm?.target_type}
              </Typography>
            </Box>

            <Button
              variant='contained'
              startIcon={<EditIcon />}
              onClick={() =>
                router.push(`/admin/coupons/${singlePromotion.id}/edit`)
              }
            >
              Edit
            </Button>
          </Stack>
        </Paper>

        {/* ===== details cards ===== */}
        <Grid container spacing={3}>
          {/* Basic */}
          <Grid item xs={12} md={6}>
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} mb={3}>
                  <PromoIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Basic
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant='subtitle2' color='text.secondary'>
                      Promotion Code
                    </Typography>
                    <Typography variant='h6'>{singlePromotion.code}</Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant='subtitle2' color='text.secondary'>
                      Automatic
                    </Typography>
                    <Chip
                      label={singlePromotion.is_automatic ? 'Yes' : 'No'}
                      size='small'
                      color={
                        singlePromotion.is_automatic ? 'success' : 'default'
                      }
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Discount */}
          <Grid item xs={12} md={6}>
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} mb={3}>
                  <PercentIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Discount
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant='subtitle2' color='text.secondary'>
                      Type
                    </Typography>
                    <Typography>
                      {pm?.type === 'percentage'
                        ? 'Percentage'
                        : pm?.type === 'fixed'
                          ? 'Fixed amount'
                          : 'Free shipping'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant='subtitle2' color='text.secondary'>
                      Value
                    </Typography>
                    <Typography variant='h6' color='primary.main'>
                      {pm ? discountLabel(pm) : '—'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Timestamps */}
          <Grid item xs={12}>
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} mb={3}>
                  <CalendarIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Timestamps
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant='subtitle2' color='text.secondary'>
                      Created
                    </Typography>
                    <Typography>
                      {formatDate(singlePromotion.created_at, true)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant='subtitle2' color='text.secondary'>
                      Updated
                    </Typography>
                    <Typography>
                      {formatDate(singlePromotion.updated_at, true)}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </ThemeProvider>
  );
}
