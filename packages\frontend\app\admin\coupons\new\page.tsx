'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  ThemeProvider,
  Box,
  Paper,
  Stack,
  Typography,
  Button,
  Card,
  CardContent,
  Divider,
  Snackbar,
  Alert,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  LocalOffer as CouponIcon,
  Percent as DiscountIcon,
  Timeline as UsageIcon,
  Schedule as ValidityIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import muiTheme from '../../../../theme/mui-theme';

import { useCouponFormValidation } from '@/hooks/useFormValidation';
import { useMedusaBackendPromotions } from '@/hooks/useMedusaAdminBackend';

/* ------------------------------------------------------------------ */
/* --------------------------- constants ---------------------------- */
/* ------------------------------------------------------------------ */

interface FormState {
  code: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed' | 'free_shipping';
  value: string;
  minimumAmount: string;
  maximumDiscount: string;
  usageLimit: string;
  usageLimitPerCustomer: string;
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'disabled' | 'archived';
}
const initialForm: FormState = {
  code: '',
  name: '',
  description: '',
  type: 'percentage',
  value: '',
  minimumAmount: '',
  maximumDiscount: '',
  usageLimit: '',
  usageLimitPerCustomer: '',
  startDate: '',
  endDate: '',
  status: 'draft',
};

const couponTypes = [
  { value: 'percentage', label: 'Percentage Discount' },
  { value: 'fixed', label: 'Fixed Amount Discount' },
  { value: 'free_shipping', label: 'Free Shipping' },
];

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'active', label: 'Active' },
  { value: 'disabled', label: 'Disabled' },
  { value: 'archived', label: 'Archived' },
];

/* ------------------------------------------------------------------ */
/* ---------------------------- page -------------------------------- */
/* ------------------------------------------------------------------ */

export default function NewCouponPage() {
  const router = useRouter();
  const { createPromotion } = useMedusaBackendPromotions();

  const [toast, setToast] = useState<{
    open: boolean;
    msg: string;
    sev: 'success' | 'error';
  }>({ open: false, msg: '', sev: 'success' });

  /* ----- form helper (custom hook) ----- */
  const {
    data: form,
    errors,
    isValid,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldError,
  } = useCouponFormValidation({
    initialData: initialForm,
    onSubmit: async data => {
      try {
        /* -------- build Medusa payload -------- */
        const payload: Record<string, any> = {
          code: data.code.trim(),
          status: data.status,
          type: 'standard',
          application_method: {
            type: data.type,
            target_type: 'order',
          },
        };

        if (data.type === 'percentage')
          payload.application_method.value = Number(data.value);

        if (data.type === 'fixed') {
          payload.application_method.value = Number(data.value);
          payload.application_method.currency_code = 'inr';
        }

        await createPromotion(payload);
        setToast({ open: true, sev: 'success', msg: 'Coupon created!' });
        setTimeout(() => router.push('/admin/coupons'), 1200);
      } catch (err) {
        console.error(err);
        setToast({ open: true, sev: 'error', msg: 'Creation failed' });
        setFieldError('submit', 'Failed to create coupon.');
        throw err;
      }
    },
  });

  /* ----- helpers ----- */
  const generateCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++)
      result += chars[Math.floor(Math.random() * chars.length)];
    setFieldValue('code', result);
  };

  const handleSelect = (name: keyof FormState) => (e: any) =>
    handleChange({ target: { name, value: e.target.value } } as any);

  /* ---------------------------------------------------------------- */

  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* ---- header ---- */}
        <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
          >
            <Box>
              <Typography variant='h4' fontWeight='bold' color='primary.main'>
                New Coupon
              </Typography>
              <Typography color='text.secondary'>
                Create a discount promotion
              </Typography>
            </Box>
            <Stack direction='row' spacing={2}>
              <Button startIcon={<CancelIcon />} onClick={() => router.back()}>
                Cancel
              </Button>
              <Button
                variant='contained'
                startIcon={<SaveIcon />}
                disabled={!isValid || isSubmitting}
                onClick={e => {
                  e.preventDefault();
                  handleSubmit(e);
                }}
              >
                {isSubmitting ? 'Saving…' : 'Create'}
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* ---- form ---- */}
        <Box component='form' onSubmit={handleSubmit} sx={{ maxWidth: 1200 }}>
          <Stack spacing={3}>
            {/* BASIC */}
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} mb={3}>
                  <CouponIcon color='primary' />
                  <Box>
                    <Typography variant='h6' fontWeight='bold'>
                      Basic Information
                    </Typography>
                  </Box>
                </Stack>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, md: 4 }}>
                    <TextField
                      label='Code'
                      name='code'
                      fullWidth
                      value={form.code}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={!!errors.code}
                      helperText={errors.code}
                    />
                    <Button
                      variant='text'
                      size='small'
                      sx={{ mt: 1, textTransform: 'none' }}
                      onClick={generateCode}
                    >
                      🎲 Generate
                    </Button>
                  </Grid>
                  <Grid size={{ xs: 12, md: 4 }}>
                    <TextField
                      label='Name'
                      name='name'
                      fullWidth
                      value={form.name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, md: 4 }}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        label='Status'
                        name='status'
                        value={form.status}
                        onChange={handleSelect('status')}
                      >
                        {statusOptions.map(s => (
                          <MenuItem key={s.value} value={s.value}>
                            {s.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid size={12}>
                    <TextField
                      label='Description'
                      name='description'
                      fullWidth
                      multiline
                      rows={3}
                      value={form.description}
                      onChange={handleChange}
                      onBlur={handleBlur}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* DISCOUNT */}
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} mb={3}>
                  <DiscountIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Discount
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Type</InputLabel>
                      <Select
                        label='Type'
                        name='type'
                        value={form.type}
                        onChange={handleSelect('type')}
                      >
                        {couponTypes.map(t => (
                          <MenuItem key={t.value} value={t.value}>
                            {t.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  {form.type !== 'free_shipping' && (
                    <Grid item xs={12} md={6}>
                      <TextField
                        label='Value'
                        name='value'
                        type='number'
                        fullWidth
                        value={form.value}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={!!errors.value}
                        helperText={errors.value}
                        InputProps={{
                          endAdornment:
                            form.type === 'percentage' ? (
                              '%'
                            ) : (
                              <Typography sx={{ ml: 1 }}>₹</Typography>
                            ),
                        }}
                      />
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>

            {/* STATUS */}
            {/* <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Stack direction="row" spacing={2} mb={3}>
                  <UsageIcon color="primary" />
                  <Typography variant="h6" fontWeight="bold">
                    Status
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />

                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    label="Status"
                    name="status"
                    value={form.status}
                    onChange={handleSelect('status')}
                  >
                    {statusOptions.map(s => (
                      <MenuItem key={s.value} value={s.value}>
                        {s.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </CardContent>
            </Card> */}
          </Stack>
        </Box>

        {/* ---- toast ---- */}
        <Snackbar
          open={toast.open}
          autoHideDuration={4000}
          onClose={() => setToast(t => ({ ...t, open: false }))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert severity={toast.sev}>{toast.msg}</Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
