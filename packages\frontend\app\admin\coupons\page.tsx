'use client';

import React, { useCallback, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import { PlusIcon, TicketIcon } from '@heroicons/react/24/outline';
// import { useQuery } from '@tanstack/react-query';
import { useMedusaBackendPromotions } from '@/hooks/useMedusaAdminBackend';

/* ------------------------------------------------------------------ */
/* --------------------------- types -------------------------------- */
/* ------------------------------------------------------------------ */

type PromotionStatus = 'draft' | 'published' | 'disabled' | 'archived';

interface ApplicationMethod {
  type: 'percentage' | 'fixed' | 'free_shipping';
  value: number;
  currency_code: string | null;
}

interface Promotion {
  id: string;
  code: string;
  type: 'standard' | 'parent';
  status: PromotionStatus;
  created_at: string;
  application_method: ApplicationMethod;
}

/* ------------------------------------------------------------------ */
/* ---------------------- data utilities ---------------------------- */
/* ------------------------------------------------------------------ */

/** helper to format ₹ amounts stored in paise/cents */
const formatPrice = (amount: number, currency = 'INR') =>
  new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
  }).format(amount / 100);

/** translate Medusa promo → row display helpers */
const getDiscountDisplay = (app: ApplicationMethod) => {
  switch (app.type) {
    case 'percentage':
      return `${app.value}% off`;
    case 'fixed':
      return `${formatPrice(app.value, app.currency_code ?? 'INR')} off`;
    case 'free_shipping':
      return 'Free shipping';
    default:
      return '—';
  }
};

const formatDate = (iso: string) =>
  new Date(iso).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

/* ------------------------------------------------------------------ */
/* ------------------------- main page ------------------------------ */
/* ------------------------------------------------------------------ */

export default function CouponsPage() {
  console.log('inside cupons');
  const router = useRouter();

  const { promotions, loading, error, fetchPromotions } =
    useMedusaBackendPromotions();

  /* -------- fetch promotions -------- */
  // const { data = [], isLoading } = useQuery<Promotion[]>({
  //   queryKey: ['admin', 'promotions'],
  //   queryFn: () => fetchPromotions,
  //   suspense: true,
  // });

  const fetchAllPromotions = useCallback(async () => {
    await fetchPromotions();
  }, []);

  useEffect(() => {
    fetchAllPromotions();
  }, []);

  /* -------- handlers -------- */
  const handleView = useCallback(
    (row: Promotion) => router.push(`/admin/coupons/${row.id}`),
    [router]
  );

  const handleEdit = useCallback(
    (row: Promotion) => router.push(`/admin/coupons/${row.id}/edit`),
    [router]
  );

  /* -------- columns -------- */
  const columns = useMemo(
    () => [
      {
        key: 'code',
        label: 'Coupon',
        sortable: true,
        render: (_: any, row: Promotion) => (
          <div className='flex items-center'>
            <TicketIcon className='h-8 w-8 text-blue-500 shrink-0' />
            <div className='ml-3'>
              <div className='text-sm font-medium text-gray-900'>
                {row.code}
              </div>
              <div className='text-xs text-gray-500 capitalize'>
                {row.status}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: 'discount',
        label: 'Discount',
        render: (_: any, row: Promotion) => (
          <span className='text-sm font-medium text-gray-900'>
            {getDiscountDisplay(row.application_method)}
          </span>
        ),
      },
      {
        key: 'created_at',
        label: 'Created',
        sortable: true,
        render: (value: string) => (
          <span className='text-sm text-gray-500'>{formatDate(value)}</span>
        ),
      },
    ],
    []
  );

  return (
    <div className='space-y-6'>
      <PageHeader
        title='Coupons'
        description='Manage discount coupons and promotional offers'
        breadcrumbs={[{ label: 'Coupons', active: true }]}
        actions={
          <ActionButton href='/admin/coupons/new' icon={PlusIcon}>
            Add Coupon
          </ActionButton>
        }
      />

      <DataTable
        // rowKey="id"
        columns={columns}
        data={promotions}
        loading={loading}
        searchable
        pagination
        pageSize={10}
        onView={handleView}
        onEdit={handleEdit}
        /* delete requires usage counts – omit until backend supports it */
        emptyMessage='No coupons found. Create your first coupon to get started.'
      />
    </div>
  );
}
