'use client';

import React, { Suspense, useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import <PERSON><PERSON>ield, {
  FormContainer,
  FormSection,
} from '@/components/admin/FormField';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/admin/MuiFormField';
import { UserIcon, EnvelopeIcon, PhoneIcon } from '@heroicons/react/24/outline';
import DetailViewSkeleton from '@/components/skeletons/DetailViewSkeleton';
import { useMedusaBackendCustomers } from '@/hooks/useMedusaAdminBackend';
import { z } from 'zod';

/* ------------------------------------------------------------------ */
/* 1. Validation schema (only fields that exist in the API)           */
/* ------------------------------------------------------------------ */
const schema = z.object({
  firstName: z.string().max(50, 'First name max 50 chars').optional(),
  lastName: z.string().max(50, 'Last name max 50 chars').optional(),
  email: z.string().email('Enter a valid email'),
  phone: z.string().optional(),
  address: z.object({
    addressName: z.string().optional(),
    company: z.string().optional(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    address1: z.string().optional(),
    address2: z.string().optional(),
    city: z.string().optional(),
    province: z.string().optional(),
    postalCode: z.string().optional(),
    countryCode: z.string().length(2, 'Use 2-letter ISO code').optional(),
    phone: z.string().optional(),
    isDefaultShipping: z.boolean().optional(),
    isDefaultBilling: z.boolean().optional(),
  }),
});

type FormData = z.infer<typeof schema>;

interface ApiCustomer {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email: string;
  phone: string | null;
  has_account: boolean;
  created_at: string;
}

/* ------------------------------------------------------------------ */
/* 2. Edit component                                                  */
/* ------------------------------------------------------------------ */
function CustomerEditContent() {
  const router = useRouter();
  const { id: customerId } = useParams<{ id: string }>();

  // const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [form, setForm] = useState<FormData>({ email: '' });
  const [success, setSuccess] = useState<string>('');
  const { singleCustomer, loading, error, fetchSingleCustomer } =
    useMedusaBackendCustomers();

  useEffect(() => {
    if (!singleCustomer) return;
    setForm({
      firstName: singleCustomer.first_name ?? '',
      lastName: singleCustomer.last_name ?? '',
      email: singleCustomer.email,
      phone: singleCustomer.phone ?? '',
      address: {
        addressName: singleCustomer?.address?.addressName || null,
        company: singleCustomer?.address?.company || null,
        firstName: singleCustomer?.address?.firstName || null,
        lastName: singleCustomer?.address?.lastName || null,
        address1: singleCustomer?.address?.address1 || null,
        address2: singleCustomer?.address?.address2 || null,
        city: singleCustomer?.address?.city || null,
        province: singleCustomer?.address?.province || null,
        postalCode: singleCustomer?.address?.postalCode || null,
        countryCode: singleCustomer?.address?.countryCode || null,
        phone: singleCustomer?.address?.phone || null,
        isDefaultShipping: singleCustomer?.address?.isDefaultShipping ?? false,
        isDefaultBilling: singleCustomer?.address?.isDefaultBilling ?? false,
      },
    });
  }, [singleCustomer]);

  /* ------- fetch customer once ------- */
  useEffect(() => {
    if (!customerId) return;
    (async () => {
      try {
        await fetchSingleCustomer(customerId);
      } catch (e) {
        console.error(e);
      }
    })();
  }, [customerId, router]);

  /* ------- handle change ------- */
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
    setErrors(prev => ({ ...prev, [name]: '' }));
  };

  /* ------- validate & submit ------- */
  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      schema.parse(form);
      setSaving(true);
      setSuccess('');

      // await adminCustomersAPI.updateCustomer(customerId, {
      //   first_name: form.firstName || null,
      //   last_name: form.lastName || null,
      //   email: form.email,
      //   phone: form.phone || null,
      //   address: form.address,
      // });

      setSuccess('Customer updated successfully!');
      setTimeout(() => router.push(`/admin/customers/${customerId}`), 1500);
    } catch (err) {
      if (err instanceof z.ZodError) {
        const map: Record<string, string> = {};
        err.errors.forEach(e => (map[e.path[0] as string] = e.message));
        setErrors(map);
      } else {
        setErrors({ general: 'Update failed, please try again.' });
        console.error(err);
      }
    } finally {
      setSaving(false);
    }
  };

  /* ------- UI states ------- */
  if (loading) return <DetailViewSkeleton />;

  const breadcrumbs = [
    { label: 'Customers', href: '/admin/customers' },
    { label: 'Edit', active: true },
  ];

  const actions = (
    <ActionButton
      onClick={() =>
        (
          document.getElementById('cust-form') as HTMLFormElement
        ).requestSubmit()
      }
      disabled={saving}
    >
      {saving ? 'Saving…' : 'Save'}
    </ActionButton>
  );

  return (
    <div className='space-y-6'>
      <PageHeader
        title='Edit Customer'
        description={form.email}
        breadcrumbs={breadcrumbs}
        actions={actions}
      />

      {success && (
        <div className='bg-green-50 border-l-4 border-green-400 p-4 text-green-700 text-sm'>
          {success}
        </div>
      )}
      {errors.general && (
        <div className='bg-red-50 border-l-4 border-red-400 p-4 text-red-700 text-sm'>
          {errors.general}
        </div>
      )}

      <FormContainer>
        <form id='cust-form' onSubmit={onSubmit}>
          <FormSection title='Basic Information' icon={UserIcon}>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <MuiFormField
                label='First Name'
                name='firstName'
                value={form.firstName}
                onChange={onChange}
                error={errors.firstName}
              />
              <MuiFormField
                label='Last Name'
                name='lastName'
                value={form.lastName}
                onChange={onChange}
                error={errors.lastName}
              />
              <MuiFormField
                label='Email'
                name='email'
                type='email'
                value={form.email}
                onChange={onChange}
                error={errors.email}
                required
              />
              <MuiFormField
                label='Phone'
                name='phone'
                value={form.phone}
                onChange={onChange}
                error={errors.phone}
              />
            </div>
          </FormSection>
          <FormSection title='Address'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <MuiFormField
                label='Address Name'
                name='address.addressName'
                value={form?.address?.addressName}
                onChange={onChange}
              />
              <MuiFormField
                label='Company'
                name='address.company'
                value={form?.address?.company}
                onChange={onChange}
              />

              <MuiFormField
                label='First Name'
                name='address.firstName'
                value={form?.address?.firstName}
                onChange={onChange}
              />
              <MuiFormField
                label='Last Name'
                name='address.lastName'
                value={form?.address?.lastName}
                onChange={onChange}
              />

              <MuiFormField
                label='Address Line 1'
                name='address.address1'
                value={form?.address?.address1}
                onChange={onChange}
              />
              <MuiFormField
                label='Address Line 2'
                name='address.address2'
                value={form?.address?.address2}
                onChange={onChange}
              />

              <MuiFormField
                label='City'
                name='address.city'
                value={form?.address?.city}
                onChange={onChange}
              />
              <MuiFormField
                label='Province / State'
                name='address.province'
                value={form?.address?.province}
                onChange={onChange}
              />

              <MuiFormField
                label='Postal Code'
                name='address.postalCode'
                value={form?.address?.postalCode}
                onChange={onChange}
              />
              <MuiFormField
                label='Country Code (ISO-2)'
                name='address.countryCode'
                value={form?.address?.countryCode}
                onChange={onChange}
              />

              <MuiFormField
                label='Phone'
                name='address.phone'
                value={form?.address?.phone}
                onChange={onChange}
              />

              {/* check-boxes */}
              <MuiFormField
                type='checkbox'
                label='Default Shipping'
                name='address.isDefaultShipping'
                checked={form?.address?.isDefaultShipping}
                onChange={onChange}
              />
              <MuiFormField
                type='checkbox'
                label='Default Billing'
                name='address.isDefaultBilling'
                checked={form?.address?.isDefaultBilling}
                onChange={onChange}
              />
            </div>
          </FormSection>
        </form>
      </FormContainer>
    </div>
  );
}

/* ------------------------------------------------------------------ */
/* 3. Suspense wrapper                                                */
/* ------------------------------------------------------------------ */
export default function CustomerEditPage() {
  return (
    <Suspense fallback={<DetailViewSkeleton />}>
      <CustomerEditContent />
    </Suspense>
  );
}
