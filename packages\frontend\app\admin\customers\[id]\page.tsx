'use client';

import { useRouter, useParams } from 'next/navigation';
import { useEffect } from 'react';

// Redirect component to handle the old route
function CustomerRedirect() {
  const router = useRouter();
  const params = useParams();
  const customerId = params?.id as string;

  useEffect(() => {
    if (customerId) {
      // Redirect to the new view page
      router.replace(`/admin/customers/${customerId}/view`);
    }
  }, [customerId, router]);

  return (
    <div className='flex justify-center items-center min-h-screen'>
      <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
    </div>
  );
}

// Main customer page component that redirects to view
export default function CustomerPage() {
  return <CustomerRedirect />;
}
