'use client';

import React, { useEffect, useState, Suspense, useMemo } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  ShoppingBagIcon,
  CurrencyRupeeIcon,
  PencilIcon,
} from '@heroicons/react/24/outline';
import DetailViewSkeleton from '@/components/skeletons/DetailViewSkeleton';

import {
  useMedusaBackendCustomers,
  useMedusaBackendOrders,
} from '@/hooks/useMedusaAdminBackend';

/* ----------------------- local types ---------------------- */
interface CustomerView {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  hasAccount: boolean;
  registered: string;
  /* optional, derived after order fetch */
  totalOrders?: number;
  totalSpent?: number;
  lastOrderDate?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
}

/* --------------------- main component --------------------- */
function CustomerDetailViewContent() {
  const router = useRouter();
  const { id: customerId } = useParams<{ id: string }>();

  // const [customer, setCustomer] = useState<CustomerView | null>(null);
  // const [orders, setOrders] = useState<any[]>([]);
  // const [loading, setLoading] = useState(true);
  // const [ordersLoading, setOrdersLoading] = useState(false);
  // const [error, setError] = useState<string | null>(null);
  const { singleCustomer, loading, error, fetchSingleCustomer } =
    useMedusaBackendCustomers();
  const {
    loading: ordersLoading,
    error: ordersError,
    fetchOrders,
    orders,
  } = useMedusaBackendOrders();

  const customer = useMemo(() => {
    if (!singleCustomer) return;

    return {
      id: singleCustomer.id,
      firstName: singleCustomer.first_name ?? '',
      lastName: singleCustomer.last_name ?? '',
      email: singleCustomer.email,
      phone: singleCustomer.phone ?? undefined,
      hasAccount: singleCustomer.has_account,
      registered: singleCustomer.created_at,
    };
  }, [singleCustomer]);
  console.log({ singleCustomer, customer });

  /* -------- fetch customer -------- */
  useEffect(() => {
    if (!customerId) return;

    (async () => {
      try {
        await fetchSingleCustomer(customerId);
      } catch (err) {
        console.error(err);
      }
    })();
  }, [customerId]);

  /* -------- fetch recent orders (optional) -------- */
  useEffect(() => {
    if (!customerId) return;
    (async () => {
      try {
        // setOrdersLoading(true);
        await fetchOrders({ customer_id: [customerId] });
        // setOrders(res.data ?? []);
      } catch (e) {
        console.error(e);
        // setOrders([]);
      }
    })();
  }, [customerId]);

  /* ---- helpers ---- */
  const fmtDate = (iso: string) =>
    new Date(iso).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

  const fmtPrice = (p = 0) =>
    new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(p);

  /* ---- UI states ---- */
  if (loading) return <DetailViewSkeleton />;

  if (error || !customer) {
    return (
      <div className='text-center py-12'>
        <p className='text-red-600'>{error ?? 'Customer not found.'}</p>
        <button
          onClick={() => router.push('/admin/customers')}
          className='mt-4 bg-blue-600 text-white px-4 py-2 rounded-md'
        >
          Back to Customers
        </button>
      </div>
    );
  }

  /* derive quick stats from orders */
  const totalOrders = orders.length;
  const totalSpent = orders.reduce((sum, o) => sum + (o.total ?? 0), 0);
  const lastOrderDate = orders[0]?.created_at;

  const breadcrumbs = [
    { label: 'Customers', href: '/admin/customers' },
    { label: customer.email, active: true },
  ];

  const actions = (
    <ActionButton
      onClick={() => router.push(`/admin/customers/${customerId}/edit`)}
      icon={PencilIcon}
    >
      Edit Customer
    </ActionButton>
  );

  return (
    <div className='space-y-6'>
      <PageHeader
        title={
          customer.firstName || customer.lastName
            ? `${customer.firstName} ${customer.lastName}`
            : customer.email
        }
        description='Customer account details'
        breadcrumbs={breadcrumbs}
        actions={actions}
      />

      {/* ------------ INFO CARD ------------- */}
      <div className='bg-white border rounded-lg shadow-sm'>
        <div className='px-6 py-4 border-b'>
          <h3 className='text-lg font-medium flex items-center'>
            <UserIcon className='h-5 w-5 mr-2 text-gray-400' />
            Contact Information
          </h3>
        </div>
        <div className='p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          <div>
            <label className='block text-sm font-medium text-gray-700'>
              Email
            </label>
            <p className='mt-1 text-sm text-gray-900 flex items-center'>
              <EnvelopeIcon className='h-4 w-4 mr-2 text-gray-400' />
              {customer.email}
            </p>
          </div>
          <div>
            <label className='block text-sm font-medium text-gray-700'>
              Phone
            </label>
            <p className='mt-1 text-sm text-gray-900 flex items-center'>
              <PhoneIcon className='h-4 w-4 mr-2 text-gray-400' />
              {customer.phone || '—'}
            </p>
          </div>
          <div>
            <label className='block text-sm font-medium text-gray-700'>
              Registered
            </label>
            <p className='mt-1 text-sm text-gray-900 flex items-center'>
              <CalendarIcon className='h-4 w-4 mr-2 text-gray-400' />
              {fmtDate(customer.registered)}
            </p>
          </div>
        </div>
      </div>

      {/* ------------ STATS CARD (derived) ------------ */}
      <div className='bg-white border rounded-lg shadow-sm'>
        <div className='px-6 py-4 border-b'>
          <h3 className='text-lg font-medium flex items-center'>
            <ShoppingBagIcon className='h-5 w-5 mr-2 text-gray-400' />
            Order Summary
          </h3>
        </div>
        <div className='p-6 grid grid-cols-1 sm:grid-cols-2 gap-6'>
          <div>
            <label className='block text-sm font-medium text-gray-700'>
              Total Orders
            </label>
            <p className='mt-1 text-sm text-gray-900'>{totalOrders}</p>
          </div>
          <div>
            <label className='block text-sm font-medium text-gray-700'>
              Total Spent
            </label>
            <p className='mt-1 text-sm text-gray-900 flex items-center'>
              <CurrencyRupeeIcon className='h-4 w-4 mr-2 text-gray-400' />
              {fmtPrice(totalSpent)}
            </p>
          </div>
          {lastOrderDate && (
            <div>
              <label className='block text-sm font-medium text-gray-700'>
                Last Order
              </label>
              <p className='mt-1 text-sm text-gray-900 flex items-center'>
                <CalendarIcon className='h-4 w-4 mr-2 text-gray-400' />
                {fmtDate(lastOrderDate)}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* ------------ ADDRESS (if any) ------------- */}
      {customer.address && (
        <div className='bg-white border rounded-lg shadow-sm'>
          <div className='px-6 py-4 border-b'>
            <h3 className='text-lg font-medium flex items-center'>
              <MapPinIcon className='h-5 w-5 mr-2 text-gray-400' />
              Address
            </h3>
          </div>
          <div className='p-6 grid grid-cols-1 md:grid-cols-2 gap-6'>
            {customer.address.street && (
              <div>
                <label className='block text-sm font-medium text-gray-700'>
                  Street
                </label>
                <p className='mt-1 text-sm text-gray-900'>
                  {customer.address.street}
                </p>
              </div>
            )}
            <div>
              <label className='block text-sm font-medium text-gray-700'>
                City
              </label>
              <p className='mt-1 text-sm text-gray-900'>
                {customer.address.city}
              </p>
            </div>
            <div>
              <label className='block text-sm font-medium text-gray-700'>
                State
              </label>
              <p className='mt-1 text-sm text-gray-900'>
                {customer.address.state}
              </p>
            </div>
            {customer.address.zip && (
              <div>
                <label className='block text-sm font-medium text-gray-700'>
                  ZIP
                </label>
                <p className='mt-1 text-sm text-gray-900'>
                  {customer.address.zip}
                </p>
              </div>
            )}
            <div>
              <label className='block text-sm font-medium text-gray-700'>
                Country
              </label>
              <p className='mt-1 text-sm text-gray-900'>
                {customer.address.country}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* ------------ RECENT ORDERS ------------- */}
      <div className='bg-white border rounded-lg shadow-sm'>
        <div className='px-6 py-4 border-b flex items-center justify-between'>
          <h3 className='text-lg font-medium flex items-center'>
            <ShoppingBagIcon className='h-5 w-5 mr-2 text-gray-400' />
            Recent Orders
          </h3>
          <Link
            href={`/admin/orders?customer=${customerId}`}
            className='text-sm text-blue-600'
          >
            View All
          </Link>
        </div>
        <div className='p-6'>
          {ordersLoading ? (
            <p className='text-sm text-gray-500'>Loading…</p>
          ) : orders.length ? (
            <div className='space-y-4'>
              {orders.map(o => (
                <div
                  key={o.id}
                  className='border rounded p-4 hover:bg-gray-50 flex justify-between'
                >
                  <div>
                    <Link
                      href={`/admin/orders/${o.id}`}
                      className='text-sm font-medium text-blue-600'
                    >
                      {o.display_id ? `#OD_${o.display_id}` : o.id.slice(-6)}
                    </Link>
                    <p className='text-sm text-gray-500'>
                      {new Date(o.created_at).toLocaleDateString('en-IN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      })}{' '}
                      {o.items?.length ?? 0} items
                    </p>
                  </div>
                  <div className='text-right'>
                    <p className='font-medium text-gray-900'>
                      {fmtPrice(o.total)}
                    </p>
                    <p className='text-sm text-gray-500'>
                      {o.payment_status ?? '—'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className='text-sm text-gray-500'>No recent orders.</p>
          )}
        </div>
      </div>
    </div>
  );
}

/* wrapper with Suspense */
export default function CustomerDetailViewPage() {
  return (
    <Suspense fallback={<DetailViewSkeleton />}>
      <CustomerDetailViewContent />
    </Suspense>
  );
}
