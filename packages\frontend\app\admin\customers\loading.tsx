'use client';

import React from 'react';

/**
 * Simple loading component for customers page
 */
const CustomersLoading = () => (
  <div className='bg-white shadow rounded-lg'>
    <div className='px-6 py-4 border-b border-gray-200'>
      <div className='animate-pulse'>
        <div className='h-6 bg-gray-200 rounded w-32'></div>
      </div>
    </div>
    <div className='divide-y divide-gray-200'>
      {Array.from({ length: 10 }).map((_, index) => (
        <div key={index} className='px-6 py-4'>
          <div className='animate-pulse flex items-center space-x-4'>
            <div className='h-10 w-10 bg-gray-200 rounded-full'></div>
            <div className='flex-1 space-y-2'>
              <div className='h-4 bg-gray-200 rounded w-3/4'></div>
              <div className='h-3 bg-gray-200 rounded w-1/2'></div>
            </div>
            <div className='h-4 bg-gray-200 rounded w-16'></div>
            <div className='h-8 w-8 bg-gray-200 rounded'></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

export default CustomersLoading;
