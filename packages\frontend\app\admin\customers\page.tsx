'use client';

import React, { Suspense, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import PageHeader from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import { CustomersTableSkeleton } from '@/components/skeletons/TableSkeleton';
import { ContentLoader } from '@/components/skeletons/SkeletonBase';
import { useMedusaBackendCustomers } from '@/hooks/useMedusaAdminBackend';
import { UserCircleIcon } from '@heroicons/react/24/outline';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';

/* ────────────────────────────────────────────────────────────────── */
/* local UI type (flattened)                                          */
interface RowCustomer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  hasAccount: boolean;
  registered: string; // ISO
}

/* ────────────────────────────────────────────────────────────────── */
/* table page                                                         */
function CustomersContent() {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoadingBackdrop();

  const {
    customers: raw,
    loading,
    error,
    fetchCustomers,
  } = useMedusaBackendCustomers();

  /* fetch once on mount */
  useEffect(() => {
    fetchCustomers({ limit: 200 });
    hideLoading();
  }, [fetchCustomers]);

  /* map API → row shape */
  const customers: RowCustomer[] = useMemo(
    () =>
      (raw ?? []).map(c => ({
        id: c.id,
        firstName: c.first_name ?? '',
        lastName: c.last_name ?? '',
        email: c.email,
        phone: c.phone ?? '',
        hasAccount: c.has_account,
        registered: c.created_at,
      })),
    [raw]
  );

  /* formatters */
  const fmtDate = (iso?: string) =>
    iso
      ? new Date(iso).toLocaleDateString('en-IN', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })
      : '—';

  /* table columns */
  const columns = [
    {
      key: 'name',
      label: 'Customer',
      sortable: true,
      render: (_: any, row: RowCustomer) => (
        <div className='flex items-center'>
          <div className='h-9 w-9 rounded-full bg-gray-200 flex items-center justify-center'>
            <UserCircleIcon className='h-6 w-6 text-gray-400' />
          </div>
          <div className='ml-3'>
            <div className='font-medium text-gray-900'>
              {row.firstName || row.lastName
                ? `${row.firstName} ${row.lastName}`
                : 'Guest'}
            </div>
            <div className='text-sm text-gray-500'>{row.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'phone',
      label: 'Phone',
      render: (v: string) => v || '—',
    },
    {
      key: 'hasAccount',
      label: 'Account',
      sortable: true,
      render: (v: boolean) => (
        <span
          className={`inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium ${
            v ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}
        >
          {v ? 'Registered' : 'Guest'}
        </span>
      ),
    },
    {
      key: 'registered',
      label: 'Joined',
      sortable: true,
      render: (v: string) => (
        <span className='text-sm text-gray-500'>{fmtDate(v)}</span>
      ),
    },
  ];

  /* row-level actions; adjust as needed */
  const view = (row: RowCustomer) => router.push(`/admin/customers/${row.id}`);
  const edit = (row: RowCustomer) =>
    router.push(`/admin/customers/${row.id}/edit`);

  /* error display */
  if (error) {
    return (
      <div className='p-6'>
        <p className='text-red-600 font-medium'>{error}</p>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <PageHeader
        title='Customers'
        description='Your customer base'
        breadcrumbs={[{ label: 'Customers', active: true }]}
      />

      <ContentLoader isLoading={loading} message='Loading customers…'>
        <DataTable
          columns={columns}
          data={customers}
          loading={loading}
          searchable
          pagination
          pageSize={10}
          onView={view}
          onEdit={edit}
          emptyMessage='No customers yet.'
        />
      </ContentLoader>
    </div>
  );
}

/* suspense wrapper */
export default function CustomersPage() {
  return (
    <Suspense fallback={<CustomersTableSkeleton />}>
      <CustomersContent />
    </Suspense>
  );
}
