'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  Download,
  Upload,
  Plus,
  Edit,
  Eye,
  MapPin,
} from 'lucide-react';
// Using real API data instead of mock data

interface InventoryItem {
  id: string;
  product_id: string;
  product_name: string;
  sku: string;
  location: string;
  quantity_on_hand: number;
  quantity_reserved: number;
  quantity_available: number;
  reorder_point: number;
  reorder_quantity: number;
  cost_per_unit: number;
  last_updated: string;
}

const InventoryManagementPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterLocation, setFilterLocation] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // TODO: Replace with real inventory API data
  const inventoryItems: InventoryItem[] = [
    // Placeholder inventory items - replace with real API call
  ]
    .map((product: any, index) => ({
      id: `inv_${index}`,
      product_id: `prod_${index}`,
      product_name: 'Sample Product',
      sku: `SKU-${index}`,
      location: ['Main Warehouse', 'Store Front', 'Backup Storage'][index % 3],
      quantity_on_hand: Math.floor(Math.random() * 100) + 10,
      quantity_reserved: Math.floor(Math.random() * 20),
      quantity_available: 0, // Will be calculated
      reorder_point: Math.floor(Math.random() * 20) + 5,
      reorder_quantity: Math.floor(Math.random() * 50) + 25,
      cost_per_unit: Math.floor(product.price * 0.6),
      last_updated: new Date(
        Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
      ).toISOString(),
    }))
    .map(item => ({
      ...item,
      quantity_available: item.quantity_on_hand - item.quantity_reserved,
    }));

  const locations = ['all', 'Main Warehouse', 'Store Front', 'Backup Storage'];
  const statusOptions = [
    { value: 'all', label: 'All Items' },
    { value: 'in_stock', label: 'In Stock' },
    { value: 'low_stock', label: 'Low Stock' },
    { value: 'out_of_stock', label: 'Out of Stock' },
    { value: 'needs_reorder', label: 'Needs Reorder' },
  ];

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch =
      item.product_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.sku.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesLocation =
      filterLocation === 'all' || item.location === filterLocation;

    let matchesStatus = true;
    if (filterStatus !== 'all') {
      switch (filterStatus) {
        case 'in_stock':
          matchesStatus = item.quantity_available > item.reorder_point;
          break;
        case 'low_stock':
          matchesStatus =
            item.quantity_available <= item.reorder_point &&
            item.quantity_available > 0;
          break;
        case 'out_of_stock':
          matchesStatus = item.quantity_available <= 0;
          break;
        case 'needs_reorder':
          matchesStatus = item.quantity_available <= item.reorder_point;
          break;
      }
    }

    return matchesSearch && matchesLocation && matchesStatus;
  });

  const getStatusColor = (item: InventoryItem) => {
    if (item.quantity_available <= 0) {
      return 'bg-red-100 text-red-800';
    } else if (item.quantity_available <= item.reorder_point) {
      return 'bg-yellow-100 text-yellow-800';
    } else {
      return 'bg-green-100 text-green-800';
    }
  };

  const getStatusText = (item: InventoryItem) => {
    if (item.quantity_available <= 0) {
      return 'Out of Stock';
    } else if (item.quantity_available <= item.reorder_point) {
      return 'Low Stock';
    } else {
      return 'In Stock';
    }
  };

  const getStatusIcon = (item: InventoryItem) => {
    if (item.quantity_available <= 0) {
      return <AlertTriangle className='w-3 h-3' />;
    } else if (item.quantity_available <= item.reorder_point) {
      return <TrendingDown className='w-3 h-3' />;
    } else {
      return <TrendingUp className='w-3 h-3' />;
    }
  };

  // Calculate summary stats
  const totalItems = inventoryItems.length;
  const inStock = inventoryItems.filter(
    item => item.quantity_available > item.reorder_point
  ).length;
  const lowStock = inventoryItems.filter(
    item =>
      item.quantity_available <= item.reorder_point &&
      item.quantity_available > 0
  ).length;
  const outOfStock = inventoryItems.filter(
    item => item.quantity_available <= 0
  ).length;
  const totalValue = inventoryItems.reduce(
    (sum, item) => sum + item.quantity_on_hand * item.cost_per_unit,
    0
  );

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>
            Inventory Management
          </h1>
          <p className='text-gray-600'>
            Track stock levels, locations, and reorder points
          </p>
        </div>
        <div className='flex space-x-3'>
          <button className='inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors'>
            <Download className='w-4 h-4 mr-2' />
            Export
          </button>
          <button className='inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors'>
            <Upload className='w-4 h-4 mr-2' />
            Import
          </button>
          <Link
            href='/admin/inventory/adjust'
            className='inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors'
          >
            <Plus className='w-4 h-4 mr-2' />
            Adjust Stock
          </Link>
        </div>
      </div>

      {/* Summary Cards */}
      <div className='grid grid-cols-1 md:grid-cols-5 gap-6'>
        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center'>
              <Package className='w-4 h-4 text-blue-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Total Items</p>
              <p className='text-2xl font-bold text-gray-900'>{totalItems}</p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center'>
              <TrendingUp className='w-4 h-4 text-green-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>In Stock</p>
              <p className='text-2xl font-bold text-gray-900'>{inStock}</p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center'>
              <TrendingDown className='w-4 h-4 text-yellow-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Low Stock</p>
              <p className='text-2xl font-bold text-gray-900'>{lowStock}</p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center'>
              <AlertTriangle className='w-4 h-4 text-red-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Out of Stock</p>
              <p className='text-2xl font-bold text-gray-900'>{outOfStock}</p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center'>
              <Package className='w-4 h-4 text-purple-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Total Value</p>
              <p className='text-2xl font-bold text-gray-900'>
                ₹{totalValue.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className='bg-white rounded-lg shadow-sm border p-6'>
        <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
            <input
              type='text'
              placeholder='Search products or SKU...'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            />
          </div>

          <select
            value={filterLocation}
            onChange={e => setFilterLocation(e.target.value)}
            className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
          >
            {locations.map(location => (
              <option key={location} value={location}>
                {location === 'all' ? 'All Locations' : location}
              </option>
            ))}
          </select>

          <select
            value={filterStatus}
            onChange={e => setFilterStatus(e.target.value)}
            className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
          >
            {statusOptions.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          <button className='flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors'>
            <Filter className='w-4 h-4 mr-2' />
            More Filters
          </button>
        </div>
      </div>

      {/* Inventory Table */}
      <div className='bg-white rounded-lg shadow-sm border overflow-hidden'>
        <div className='overflow-x-auto'>
          <table className='min-w-full divide-y divide-gray-200'>
            <thead>
              <tr>
                <th className='px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider'>
                  Product
                </th>
                <th className='px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider'>
                  Location
                </th>
                <th className='px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider'>
                  On Hand
                </th>
                <th className='px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider'>
                  Reserved
                </th>
                <th className='px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider'>
                  Available
                </th>
                <th className='px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider'>
                  Status
                </th>
                <th className='px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider'>
                  Value
                </th>
                <th className='relative px-6 py-3'>
                  <span className='sr-only'>Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className='bg-white divide-y divide-gray-200'>
              {filteredItems.map((item, index) => (
                <tr
                  key={item.id}
                  className={`transition-all duration-200 hover:table-row-hover-gradient ${
                    index % 2 === 0 ? 'bg-white' : 'table-row-even-gradient'
                  }`}
                >
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <div>
                      <div className='text-sm font-medium text-gray-900'>
                        {item.product_name}
                      </div>
                      <div className='text-sm text-gray-500'>{item.sku}</div>
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <div className='flex items-center text-sm text-gray-900'>
                      <MapPin className='w-4 h-4 mr-1 text-gray-400' />
                      {item.location}
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                    {item.quantity_on_hand}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                    {item.quantity_reserved}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                    {item.quantity_available}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                        item
                      )}`}
                    >
                      {getStatusIcon(item)}
                      <span className='ml-1'>{getStatusText(item)}</span>
                    </span>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                    ₹
                    {(
                      item.quantity_on_hand * item.cost_per_unit
                    ).toLocaleString()}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                    <div className='flex items-center justify-end space-x-2'>
                      <Link
                        href={`/admin/inventory/${item.id}`}
                        className='text-blue-600 hover:text-blue-900'
                        title='View details'
                      >
                        <Eye className='w-4 h-4' />
                      </Link>
                      <Link
                        href={`/admin/inventory/${item.id}/edit`}
                        className='text-gray-600 hover:text-gray-900'
                        title='Edit'
                      >
                        <Edit className='w-4 h-4' />
                      </Link>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredItems.length === 0 && (
          <div className='text-center py-12'>
            <Package className='w-12 h-12 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No inventory items found
            </h3>
            <p className='text-gray-600'>
              {searchQuery || filterLocation !== 'all' || filterStatus !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Your inventory is empty'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InventoryManagementPage;
