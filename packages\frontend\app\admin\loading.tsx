'use client';

/**
 * Loading component for admin routes
 * This will be shown during page transitions in the admin area
 */
export default function Loading() {
  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header skeleton */}
      <div className='bg-white shadow-sm border-b border-gray-200'>
        <div className='px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-6'>
            <div className='animate-pulse'>
              <div className='h-8 bg-gray-200 rounded w-48'></div>
            </div>
            <div className='animate-pulse'>
              <div className='h-10 bg-gray-200 rounded w-32'></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content skeleton */}
      <div className='px-4 sm:px-6 lg:px-8 py-8'>
        <div className='animate-pulse space-y-6'>
          {/* Stats cards */}
          <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4'>
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className='bg-white overflow-hidden shadow rounded-lg'
              >
                <div className='p-5'>
                  <div className='flex items-center'>
                    <div className='flex-shrink-0'>
                      <div className='h-6 w-6 bg-gray-200 rounded-full'></div>
                    </div>
                    <div className='ml-5 w-0 flex-1'>
                      <div className='h-4 bg-gray-200 rounded w-20 mb-2'></div>
                      <div className='h-8 bg-gray-200 rounded w-24'></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Charts */}
          <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
            <div className='bg-white shadow rounded-lg p-6'>
              <div className='h-5 bg-gray-200 rounded w-32 mb-4'></div>
              <div className='h-64 bg-gray-100 rounded'></div>
            </div>
            <div className='bg-white shadow rounded-lg p-6'>
              <div className='h-5 bg-gray-200 rounded w-32 mb-4'></div>
              <div className='h-64 bg-gray-100 rounded'></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
