'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  But<PERSON>,
  Stack,
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Paper,
  Divider,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ShoppingCart as OrderIcon,
  Person as CustomerIcon,
  LocalShipping as ShippingIcon,
  Payment as PaymentIcon,
  Receipt as InvoiceIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../theme/mui-theme';
import { useMedusaBackendOrders } from '@/hooks/useMedusaAdminBackend';

/* ------------------------------------------------------------------ */
/* --------- internal UI types (flattened for our component) --------- */
interface OrderItem {
  id: string;
  name: string;
  sku: string;
  quantity: number;
  price: number;
  total: number;
}

interface OrderView {
  id: string;
  orderNumber: string;
  status: string;
  paymentStatus: string;
  customer: { name: string; email: string; phone: string };
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  billingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  items: OrderItem[];
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount: number;
  total: number;
  couponCode?: string;
  paymentMethod: string;
  createdAt: string;
  updatedAt: string;
}

/* ——————————————————————————————————————————————————————————————— */
/* helper functions                                                    */
/* ——————————————————————————————————————————————————————————————— */
const fmtPrice = (cents = 0) =>
  new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
  }).format(cents);

const fmtDate = (iso: string) =>
  new Date(iso).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

const statusColor = (s: string) =>
  (
    ({
      delivered: 'success',
      shipped: 'info',
      processing: 'info',
      confirmed: 'primary',
      pending: 'warning',
      cancelled: 'error',
    }) as any
  )[s] ?? 'default';

const payStatusColor = (s: string) =>
  (
    ({
      paid: 'success',
      pending: 'warning',
      captured: 'success',
      failed: 'error',
      refunded: 'error',
    }) as any
  )[s] ?? 'default';

/* ——————————————————————————————————————————————————————————————— */
/* main component                                                      */
/* ——————————————————————————————————————————————————————————————— */
export default function OrderViewPage() {
  const router = useRouter();
  const params = useParams<{ id: string }>();

  // const [order, setOrder] = useState<OrderView | null>(null);
  // const [loading, setLoading] = useState(true);
  const [snack, setSnack] = useState<{
    open: boolean;
    msg: string;
    sev: 'success' | 'error';
  }>({
    open: false,
    msg: '',
    sev: 'success',
  });
  const { singleOrder, loading, fetchSingleOrder } = useMedusaBackendOrders();

  const order = useMemo(() => {
    if (!singleOrder) return;
    // ---------- transform API → UI shape ----------
    const o = singleOrder as any;

    const toItem = (it: any): OrderItem => ({
      id: it.id,
      name: it.title ?? 'Unnamed product',
      sku: it.variant_sku ?? it.variant?.sku ?? '—',
      quantity: Number(it.quantity) || 1,
      price: it.unit_price ?? 0,
      total: (it.unit_price ?? 0) * (Number(it.quantity) || 1),
    });

    const addressField = (
      addr: any,
      field: 'address_1' | 'city' | 'province' | 'postal_code' | 'country_code'
    ) => addr?.[field] ?? '—';

    const view: OrderView = {
      id: o.id,
      orderNumber: o.display_id
        ? `#${o.display_id}`
        : o.id.slice(0, 7).toUpperCase(),
      status: o.status ?? 'pending',
      paymentStatus: o.payment_status ?? 'pending',
      customer: {
        name: o.customer
          ? `${o.customer.first_name ?? ''} ${
              o.customer.last_name ?? ''
            }`.trim() || 'Guest'
          : 'Guest',
        email: o.email ?? o.customer?.email ?? '—',
        phone: o.shipping_address?.phone ?? '—',
      },
      shippingAddress: {
        street: addressField(o.shipping_address, 'address_1'),
        city: addressField(o.shipping_address, 'city'),
        state: addressField(o.shipping_address, 'province'),
        postalCode: addressField(o.shipping_address, 'postal_code'),
        country: o.shipping_address?.country_code?.toUpperCase() ?? '—',
      },
      billingAddress: {
        street: addressField(o.billing_address, 'address_1'),
        city: addressField(o.billing_address, 'city'),
        state: addressField(o.billing_address, 'province'),
        postalCode: addressField(o.billing_address, 'postal_code'),
        country: o.billing_address?.country_code?.toUpperCase() ?? '—',
      },
      items: (o.items ?? []).map(toItem),
      subtotal: o.subtotal ?? 0,
      shippingCost: o.shipping_total ?? 0,
      tax: o.tax_total ?? 0,
      discount: 0,
      total: o.total ?? 0,
      couponCode: undefined,
      paymentMethod: o.metadata?.payment_method ?? '—',
      createdAt: o.created_at,
      updatedAt: o.updated_at,
    };
    return view;
  }, [singleOrder]);
  console.log({ singleOrder, order });
  /* ---------------- fetch  ---------------- */
  useEffect(() => {
    (async () => {
      if (!params.id) return;

      try {
        await fetchSingleOrder(params.id);
      } catch (err) {
        console.error(err);
        setSnack({ open: true, msg: 'Failed to load order', sev: 'error' });
      }
    })();
  }, [params.id]);

  if (loading) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box
          sx={{
            minHeight: '100vh',
            p: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography>Loading order…</Typography>
        </Box>
      </ThemeProvider>
    );
  }
  if (!order) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box
          sx={{
            minHeight: '100vh',
            p: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography>Order not found</Typography>
        </Box>
      </ThemeProvider>
    );
  }

  /* ----------------------------- UI ------------------------------- */
  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* Header */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
          >
            <Box>
              <Stack direction='row' spacing={2} mb={1} alignItems='center'>
                <Button
                  variant='outlined'
                  startIcon={<ArrowBackIcon />}
                  onClick={() => router.push('/admin/orders')}
                >
                  Back
                </Button>
                <Chip
                  label={order.status.toUpperCase()}
                  color={statusColor(order.status)}
                  size='small'
                />
                <Chip
                  label={`Payment: ${order.paymentStatus.toUpperCase()}`}
                  color={payStatusColor(order.paymentStatus)}
                  size='small'
                  variant='outlined'
                />
              </Stack>
              <Typography variant='h4' fontWeight='bold'>
                Order {order.orderNumber}
              </Typography>
              <Typography variant='body1' color='text.secondary' mt={1}>
                Placed on {fmtDate(order.createdAt)} • Total:{' '}
                {fmtPrice(order.total)}
              </Typography>
            </Box>
            <Button
              variant='contained'
              startIcon={<InvoiceIcon />}
              onClick={() =>
                setSnack({
                  open: true,
                  msg: 'Invoice generation coming soon',
                  sev: 'success',
                })
              }
              disabled
            >
              Generate Invoice
            </Button>
          </Stack>
        </Paper>

        <Grid container spacing={3}>
          {/* Items */}
          <Grid size={12}>
            <Card>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} alignItems='center' mb={3}>
                  <OrderIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Items
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>
                          <strong>Product</strong>
                        </TableCell>
                        <TableCell>
                          <strong>SKU</strong>
                        </TableCell>
                        <TableCell align='center'>
                          <strong>Qty</strong>
                        </TableCell>
                        <TableCell align='right'>
                          <strong>Price</strong>
                        </TableCell>
                        <TableCell align='right'>
                          <strong>Total</strong>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {order.items.map(i => (
                        <TableRow key={i.id}>
                          <TableCell>{i.name}</TableCell>
                          <TableCell sx={{ fontFamily: 'monospace' }}>
                            {i.sku}
                          </TableCell>
                          <TableCell align='center'>{i.quantity}</TableCell>
                          <TableCell align='right'>
                            {fmtPrice(i.price)}
                          </TableCell>
                          <TableCell align='right' sx={{ fontWeight: 'bold' }}>
                            {fmtPrice(i.total)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Summary */}
                <Box
                  sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}
                >
                  <Box sx={{ minWidth: 280 }}>
                    <Stack spacing={1}>
                      <Stack direction='row' justifyContent='space-between'>
                        <Typography>Subtotal:</Typography>
                        <Typography>{fmtPrice(order.subtotal)}</Typography>
                      </Stack>
                      <Stack direction='row' justifyContent='space-between'>
                        <Typography>Shipping:</Typography>
                        <Typography>{fmtPrice(order.shippingCost)}</Typography>
                      </Stack>
                      <Stack direction='row' justifyContent='space-between'>
                        <Typography>Tax:</Typography>
                        <Typography>{fmtPrice(order.tax)}</Typography>
                      </Stack>
                      <Divider />
                      <Stack direction='row' justifyContent='space-between'>
                        <Typography variant='h6' fontWeight='bold'>
                          Total:
                        </Typography>
                        <Typography
                          variant='h6'
                          fontWeight='bold'
                          color='primary.main'
                        >
                          {fmtPrice(order.total)}
                        </Typography>
                      </Stack>
                    </Stack>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Customer */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} alignItems='center' mb={3}>
                  <CustomerIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Customer
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />
                <Typography variant='subtitle2' color='text.secondary'>
                  Name
                </Typography>
                <Typography>{order.customer.name}</Typography>
                <Typography variant='subtitle2' color='text.secondary' mt={2}>
                  Email
                </Typography>
                <Typography>{order.customer.email}</Typography>
                <Typography variant='subtitle2' color='text.secondary' mt={2}>
                  Phone
                </Typography>
                <Typography>{order.customer.phone}</Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Payment */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} alignItems='center' mb={3}>
                  <PaymentIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Payment
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />
                <Typography variant='subtitle2' color='text.secondary'>
                  Method
                </Typography>
                <Typography>{order.paymentMethod}</Typography>
                <Typography variant='subtitle2' color='text.secondary' mt={2}>
                  Status
                </Typography>
                <Chip
                  label={order.paymentStatus.toUpperCase()}
                  color={payStatusColor(order.paymentStatus)}
                  size='small'
                />
                <Typography variant='subtitle2' color='text.secondary' mt={2}>
                  Amount
                </Typography>
                <Typography variant='h6' color='primary.main' fontWeight='bold'>
                  {fmtPrice(order.total)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Shipping */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card>
              <CardContent sx={{ p: 4 }}>
                <Stack direction='row' spacing={2} alignItems='center' mb={3}>
                  <ShippingIcon color='primary' />
                  <Typography variant='h6' fontWeight='bold'>
                    Shipping Address
                  </Typography>
                </Stack>
                <Divider sx={{ mb: 3 }} />
                <Typography>{order.shippingAddress.street}</Typography>
                <Typography>
                  {order.shippingAddress.city}, {order.shippingAddress.state}
                </Typography>
                <Typography>{order.shippingAddress.postalCode}</Typography>
                <Typography>{order.shippingAddress.country}</Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Billing */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card>
              <CardContent sx={{ p: 4 }}>
                <Typography variant='h6' fontWeight='bold' mb={3}>
                  Billing Address
                </Typography>
                <Divider sx={{ mb: 3 }} />
                <Typography>{order.billingAddress.street}</Typography>
                <Typography>
                  {order.billingAddress.city}, {order.billingAddress.state}
                </Typography>
                <Typography>{order.billingAddress.postalCode}</Typography>
                <Typography>{order.billingAddress.country}</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Snackbar
          open={snack.open}
          autoHideDuration={4000}
          onClose={() => setSnack(s => ({ ...s, open: false }))}
        >
          <Alert
            severity={snack.sev}
            variant='filled'
            onClose={() => setSnack(s => ({ ...s, open: false }))}
          >
            {snack.msg}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
