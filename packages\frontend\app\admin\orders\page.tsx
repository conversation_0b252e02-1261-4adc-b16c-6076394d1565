'use client';

import React, { Suspense, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  EyeIcon,
  PrinterIcon,
  TruckIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { Snack<PERSON>, Alert, Button, Typography, Stack } from '@mui/material';

import PageHeader from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import DialogShell from '@/components/admin/DialogShell';
import { OrdersTableSkeleton } from '@/components/skeletons/TableSkeleton';
import { ContentLoader } from '@/components/skeletons/SkeletonBase';
import { useMedusaBackendOrders } from '@/hooks/useMedusaAdminBackend';

/* ——————————————————————————————————————————————————————————————— */
/* helper utilities                                                   */
/* ——————————————————————————————————————————————————————————————— */
const fmtPrice = (cents = 0) =>
  new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
  }).format(cents);

const fmtDate = (iso?: string) =>
  iso
    ? new Date(iso).toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    : '—';

/* map Medusa order status → badge colour + label */
const orderStatus = {
  pending: ['bg-yellow-100 text-yellow-800', 'Pending'],
  processing: ['bg-blue-100 text-blue-800', 'Processing'],
  shipped: ['bg-purple-100 text-purple-800', 'Shipped'],
  delivered: ['bg-green-100 text-green-800', 'Delivered'],
  cancelled: ['bg-red-100 text-red-800', 'Cancelled'],
  completed: ['bg-green-100 text-green-800', 'Completed'],
} as const;

const payStatus = {
  pending: ['bg-yellow-100 text-yellow-800', 'Pending'],
  awaiting: ['bg-yellow-100 text-yellow-800', 'Awaiting'],
  paid: ['bg-green-100 text-green-800', 'Paid'],
  captured: ['bg-green-100 text-green-800', 'Captured'],
  failed: ['bg-red-100 text-red-800', 'Failed'],
  refunded: ['bg-gray-100 text-gray-800', 'Refunded'],
} as const;

const badge = ([color, label]: [string, string]) => (
  <span
    className={`inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}
  >
    {label}
  </span>
);

/* ——————————————————————————————————————————————————————————————— */
/* Orders table + actions                                             */
/* ——————————————————————————————————————————————————————————————— */
function OrdersContent() {
  const router = useRouter();
  const { orders: rawOrders, loading, fetchOrders } = useMedusaBackendOrders();

  /* snackbar for quick feedback */
  const [snack, setSnack] = useState<{
    o: boolean;
    m: string;
    s: 'success' | 'error';
  }>({
    o: false,
    m: '',
    s: 'success',
  });

  useEffect(() => {
    fetchOrders({ limit: 100 });
  }, [fetchOrders]);

  /* Transform Medusa orders to what the table expects */
  const orders = useMemo(() => {
    if (!rawOrders) return [];
    console.log({ rawOrders });

    return rawOrders.map(o => ({
      id: o.id,

      /* display_id becomes “Order #”  */
      orderNumber: o?.display_id ? `#OD_${o.display_id}` : o.id,

      /* customer info (name may be missing for guests) */
      customer: {
        name:
          o.customer && (o.customer.first_name || o.customer.last_name)
            ? `${o.customer.first_name ?? ''} ${
                o.customer.last_name ?? ''
              }`.trim()
            : 'Guest',
        email: o.email ?? o.customer?.email ?? '—',
      },

      status: (o.status ?? 'pending') as keyof typeof orderStatus,

      total: o.summary.current_order_total ?? 0,
      itemsCount: o.items?.length ?? 0,

      createdAt: o.created_at,
      updatedAt: o.updated_at,
    }));
  }, [rawOrders]);

  /* ————— table column definitions ————— */
  const columns = [
    {
      key: 'orderNumber',
      label: 'Order',
      sortable: true,
      render: (val: string, row: any) => (
        <div>
          <div className='text-sm font-medium text-gray-900'>{val}</div>
          <div className='text-sm text-gray-500'>{fmtDate(row.createdAt)}</div>
        </div>
      ),
    },
    {
      key: 'customer',
      label: 'Customer',
      sortable: true,
      render: (val: any) => (
        <div>
          <div className='text-sm font-medium text-gray-900'>{val.name}</div>
          <div className='text-sm text-gray-500'>{val.email}</div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Order Status',
      sortable: true,
      render: (v: keyof typeof orderStatus) =>
        badge(orderStatus[v] ?? ['bg-gray-100 text-gray-800', v]),
    },
    // {
    //   key: 'paymentStatus',
    //   label: 'Payment',
    //   sortable: true,
    //   render: (v: keyof typeof payStatus) =>
    //     badge(payStatus[v] ?? ['bg-gray-100 text-gray-800', v]),
    // },
    {
      key: 'total',
      label: 'Total',
      sortable: true,
      render: (val: number, row: any) => {
        console.log('val:::::::', val, row);
        return (
          <div>
            <div className='text-sm font-medium text-gray-900'>
              {fmtPrice(val)}
            </div>
            <div className='text-sm text-gray-500'>
              {row.itemsCount} item{row.itemsCount !== 1 && 's'}
            </div>
          </div>
        );
      },
    },
    // {
    //   key: 'shippingAddress',
    //   label: 'Ship To',
    //   render: (v: any) => (
    //     <div className="text-sm text-gray-500">
    //       {v.city}, {v.state}
    //     </div>
    //   ),
    // },
  ];

  /* ————— row-level quick actions ————— */
  const customActions = (row: any) => (
    <div className='flex items-center space-x-2'>
      <button
        onClick={() => router.push(`/admin/orders/${row.id}`)}
        title='View'
        className='text-blue-600 hover:text-blue-900'
      >
        <EyeIcon className='h-4 w-4' />
      </button>
      <button
        title='Print'
        className='text-green-600 hover:text-green-900'
        onClick={() =>
          setSnack({
            o: true,
            m: `Printing invoice for ${row.orderNumber}`,
            s: 'success',
          })
        }
      >
        <PrinterIcon className='h-4 w-4' />
      </button>
      {row.status === 'processing' && (
        <button
          title='Mark as shipped'
          className='text-purple-600 hover:text-purple-900'
          onClick={() =>
            setSnack({
              o: true,
              m: `Order ${row.orderNumber} marked as shipped`,
              s: 'success',
            })
          }
        >
          <TruckIcon className='h-4 w-4' />
        </button>
      )}
    </div>
  );

  const handleView = (order: any) => {
    router.push(`/admin/orders/${order.id}`);
  };

  const breadcrumbs = [{ label: 'Orders', active: true }];

  return (
    <div className='space-y-6'>
      <PageHeader
        title='Orders'
        description='Manage customer orders and fulfillment'
        breadcrumbs={breadcrumbs}
      />

      <ContentLoader isLoading={loading} message='Loading orders...'>
        <DataTable
          columns={columns}
          data={orders}
          loading={loading}
          pagination
          pageSize={10}
          filterable
          searchable
          onView={handleView}
          // renderRowActions={customActions}
          emptyMessage='No orders found.'
        />
      </ContentLoader>

      <Snackbar
        open={snack.o}
        autoHideDuration={4000}
        onClose={() => setSnack(s => ({ ...s, o: false }))}
      >
        <Alert
          onClose={() => setSnack(s => ({ ...s, o: false }))}
          severity={snack.s}
          variant='filled'
          sx={{ width: '100%' }}
        >
          {snack.m}
        </Alert>
      </Snackbar>
    </div>
  );
}

/* ——————————————————————————————————————————————————————————————— */
/* Suspense wrapper                                                   */
/* ——————————————————————————————————————————————————————————————— */
export default function OrdersPage() {
  return (
    <Suspense fallback={<OrdersTableSkeleton />}>
      <OrdersContent />
    </Suspense>
  );
}
