'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import <PERSON>Header, { ActionButton } from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import Form<PERSON>ield, { FormContainer } from '@/components/admin/FormField';
import {
  PlusIcon,
  XMarkIcon,
  EyeIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import { getPages, createPage, deletePage, type Page } from '@/lib/strapi-api';
import {
  getCacheStats,
  clearAllCache,
  cleanupExpiredCache,
} from '@/lib/strapi-cache';

// Use the Page interface from strapi-api
type CMSPage = Page;

// Mock data for CMS pages
const mockPages: CMSPage[] = [
  {
    id: '1',
    title: 'About Us',
    slug: 'about-us',
    content:
      '<h1>About Our Company</h1><p>We are a leading e-commerce platform...</p>',
    excerpt: 'Learn more about our company and mission',
    metaTitle: 'About Us - ONDC Seller Platform',
    metaDescription:
      'Discover our story, mission, and values at ONDC Seller Platform',
    status: 'published',
    template: 'about',
    featured: true,
    publishedAt: '2024-01-10T10:00:00Z',
    viewCount: 1250,
    author: 'Admin User',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content:
      '<h1>Privacy Policy</h1><p>This privacy policy explains how we collect...</p>',
    excerpt: 'Our commitment to protecting your privacy',
    metaTitle: 'Privacy Policy - ONDC Seller Platform',
    metaDescription:
      'Read our privacy policy to understand how we protect your data',
    status: 'published',
    template: 'default',
    featured: false,
    publishedAt: '2024-01-05T14:00:00Z',
    viewCount: 890,
    author: 'Admin User',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-14T15:45:00Z',
  },
  {
    id: '3',
    title: 'Terms of Service',
    slug: 'terms-of-service',
    content:
      '<h1>Terms of Service</h1><p>By using our platform, you agree to...</p>',
    excerpt: 'Terms and conditions for using our platform',
    metaTitle: 'Terms of Service - ONDC Seller Platform',
    metaDescription: 'Read our terms of service before using our platform',
    status: 'published',
    template: 'default',
    featured: false,
    publishedAt: '2024-01-05T14:30:00Z',
    viewCount: 567,
    author: 'Admin User',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-13T12:20:00Z',
  },
  {
    id: '4',
    title: 'Contact Us',
    slug: 'contact-us',
    content: '<h1>Get in Touch</h1><p>We would love to hear from you...</p>',
    excerpt: 'Get in touch with our team',
    metaTitle: 'Contact Us - ONDC Seller Platform',
    metaDescription:
      'Contact our team for support, partnerships, or general inquiries',
    status: 'published',
    template: 'contact',
    featured: true,
    publishedAt: '2024-01-08T09:00:00Z',
    viewCount: 2340,
    author: 'Admin User',
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-12T09:15:00Z',
  },
  {
    id: '5',
    title: 'FAQ',
    slug: 'faq',
    content:
      '<h1>Frequently Asked Questions</h1><p>Find answers to common questions...</p>',
    excerpt: 'Answers to frequently asked questions',
    metaTitle: 'FAQ - ONDC Seller Platform',
    metaDescription:
      'Find answers to frequently asked questions about our platform',
    status: 'draft',
    template: 'default',
    featured: false,
    viewCount: 0,
    author: 'Admin User',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-11T14:30:00Z',
  },
  {
    id: '6',
    title: 'Seller Guidelines',
    slug: 'seller-guidelines',
    content:
      '<h1>Seller Guidelines</h1><p>Guidelines for sellers on our platform...</p>',
    excerpt: 'Important guidelines for sellers',
    metaTitle: 'Seller Guidelines - ONDC Seller Platform',
    metaDescription:
      'Read our comprehensive guidelines for sellers on the platform',
    status: 'archived',
    template: 'default',
    featured: false,
    publishedAt: '2023-12-15T10:00:00Z',
    viewCount: 156,
    author: 'Admin User',
    createdAt: '2023-12-01T00:00:00Z',
    updatedAt: '2024-01-10T11:20:00Z',
  },
];

interface PageFormData {
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  metaTitle: string;
  metaDescription: string;
  status: string;
  template: string;
  featured: boolean;
}

const initialFormData: PageFormData = {
  title: '',
  slug: '',
  content: '',
  excerpt: '',
  metaTitle: '',
  metaDescription: '',
  status: 'draft',
  template: 'default',
  featured: false,
};

export default function CMSPagesPage() {
  const router = useRouter();
  const [pages, setPages] = useState<CMSPage[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState<PageFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);
  const [cacheStats, setCacheStats] = useState<any>(null);

  useEffect(() => {
    fetchPages();
    fetchCacheStats();
  }, []);

  const fetchPages = async () => {
    setLoading(true);
    try {
      const response = await getPages();
      setPages(response.data || []);
    } catch (error) {
      console.error('Error fetching pages:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCacheStats = () => {
    const stats = getCacheStats();
    setCacheStats(stats);
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));

      // Auto-generate slug from title
      if (name === 'title' && value) {
        const slug = value
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        setFormData(prev => ({ ...prev, slug }));
      }

      // Auto-generate meta title from title
      if (name === 'title' && value) {
        const metaTitle = `${value} - ONDC Seller Platform`;
        setFormData(prev => ({ ...prev, metaTitle }));
      }
    }

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) {
      errors.title = 'Page title is required';
    }

    if (!formData.slug.trim()) {
      errors.slug = 'Page slug is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      errors.slug =
        'Slug must contain only lowercase letters, numbers, and hyphens';
    }

    if (!formData.content.trim()) {
      errors.content = 'Page content is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newPage: CMSPage = {
        id: Date.now().toString(),
        title: formData.title,
        slug: formData.slug,
        content: formData.content,
        excerpt: formData.excerpt || undefined,
        metaTitle: formData.metaTitle || undefined,
        metaDescription: formData.metaDescription || undefined,
        status: formData.status as CMSPage['status'],
        template: formData.template as CMSPage['template'],
        featured: formData.featured,
        publishedAt:
          formData.status === 'published'
            ? new Date().toISOString()
            : undefined,
        viewCount: 0,
        author: 'Admin User',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setPages(prev => [newPage, ...prev]);
      setFormData(initialFormData);
      setShowAddForm(false);
    } catch (error) {
      console.error('Error creating page:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (page: CMSPage) => {
    router.push(`/admin/pages/${page.id}/edit`);
  };

  const handleView = (page: CMSPage) => {
    window.open(`/${page.slug}`, '_blank');
  };

  const handleDelete = async (page: CMSPage) => {
    if (window.confirm(`Are you sure you want to delete "${page.title}"?`)) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setPages(prev => prev.filter(p => p.id !== page.id));
      } catch (error) {
        console.error('Error deleting page:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: CMSPage['status']) => {
    const statusConfig = {
      published: { color: 'bg-green-100 text-green-800', label: 'Published' },
      draft: { color: 'bg-yellow-100 text-yellow-800', label: 'Draft' },
      archived: { color: 'bg-gray-100 text-gray-800', label: 'Archived' },
    };

    const config = statusConfig[status];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const getTemplateBadge = (template: CMSPage['template']) => {
    const templateConfig = {
      default: { color: 'bg-blue-100 text-blue-800', label: 'Default' },
      landing: { color: 'bg-purple-100 text-purple-800', label: 'Landing' },
      contact: { color: 'bg-green-100 text-green-800', label: 'Contact' },
      about: { color: 'bg-orange-100 text-orange-800', label: 'About' },
    };

    const config = templateConfig[template];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const columns = [
    {
      key: 'title',
      label: 'Page',
      sortable: true,
      render: (value: string, row: CMSPage) => (
        <div>
          <div className='flex items-center'>
            <div className='text-sm font-medium text-gray-900'>{value}</div>
            {row.featured && (
              <span className='ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800'>
                Featured
              </span>
            )}
          </div>
          <div className='text-sm text-gray-500'>/{row.slug}</div>
        </div>
      ),
    },
    {
      key: 'template',
      label: 'Template',
      sortable: true,
      render: (value: CMSPage['template']) => getTemplateBadge(value),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: CMSPage['status']) => getStatusBadge(value),
    },
    {
      key: 'viewCount',
      label: 'Views',
      sortable: true,
      render: (value: number) => (
        <span className='text-sm font-medium text-gray-900'>
          {value.toLocaleString()}
        </span>
      ),
    },
    {
      key: 'author',
      label: 'Author',
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{value}</span>
      ),
    },
    {
      key: 'publishedAt',
      label: 'Published',
      sortable: true,
      render: (value: string, row: CMSPage) => (
        <span className='text-sm text-gray-500'>
          {value
            ? formatDate(value)
            : row.status === 'draft'
              ? 'Not published'
              : '—'}
        </span>
      ),
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{formatDate(value)}</span>
      ),
    },
  ];

  const breadcrumbs = [
    { label: 'CMS', href: '#' },
    { label: 'Pages', active: true },
  ];

  const customActions = (row: CMSPage) => [
    {
      label: 'View',
      icon: EyeIcon,
      onClick: () => handleView(row),
      disabled: row.status !== 'published',
    },
  ];

  return (
    <div className='space-y-6'>
      <PageHeader
        title='CMS Pages'
        description='Manage your website content and pages'
        breadcrumbs={breadcrumbs}
        actions={
          <ActionButton onClick={() => setShowAddForm(true)} icon={PlusIcon}>
            Add Page
          </ActionButton>
        }
      />

      {showAddForm && (
        <div className='max-w-4xl'>
          <FormContainer
            title='Add New Page'
            description='Create a new content page'
            onSubmit={handleSubmit}
            actions={
              <>
                <ActionButton
                  variant='secondary'
                  onClick={() => {
                    setShowAddForm(false);
                    setFormData(initialFormData);
                    setFormErrors({});
                  }}
                  icon={XMarkIcon}
                >
                  Cancel
                </ActionButton>
                <ActionButton
                  onClick={() => handleSubmit({} as React.FormEvent)}
                  disabled={submitting}
                  icon={PlusIcon}
                >
                  {submitting ? 'Creating...' : 'Create Page'}
                </ActionButton>
              </>
            }
          >
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                label='Page Title'
                name='title'
                value={formData.title}
                onChange={handleInputChange}
                placeholder='Enter page title'
                required
                error={formErrors.title}
                help='The main title of the page'
              />
              <FormField
                label='Page Slug'
                name='slug'
                value={formData.slug}
                onChange={handleInputChange}
                placeholder='page-slug'
                required
                error={formErrors.slug}
                help='URL-friendly version (auto-generated from title)'
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                label='Template'
                name='template'
                type='select'
                value={formData.template}
                onChange={handleInputChange}
                options={[
                  { value: 'default', label: 'Default' },
                  { value: 'landing', label: 'Landing Page' },
                  { value: 'contact', label: 'Contact Page' },
                  { value: 'about', label: 'About Page' },
                ]}
                help='Page template to use'
              />
              <FormField
                label='Status'
                name='status'
                type='select'
                value={formData.status}
                onChange={handleInputChange}
                options={[
                  { value: 'draft', label: 'Draft' },
                  { value: 'published', label: 'Published' },
                  { value: 'archived', label: 'Archived' },
                ]}
              />
            </div>

            <FormField
              label='Page Content'
              name='content'
              type='textarea'
              rows={8}
              value={formData.content}
              onChange={handleInputChange}
              placeholder='Enter page content (HTML supported)'
              required
              error={formErrors.content}
              help='Main content of the page (HTML tags are supported)'
            />

            <FormField
              label='Excerpt'
              name='excerpt'
              type='textarea'
              rows={2}
              value={formData.excerpt}
              onChange={handleInputChange}
              placeholder='Brief description of the page'
              help='Short description used in previews and search results'
            />

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                label='Meta Title'
                name='metaTitle'
                value={formData.metaTitle}
                onChange={handleInputChange}
                placeholder='SEO title for search engines'
                help='Title shown in search engine results (auto-generated from title)'
              />
              <FormField
                label='Meta Description'
                name='metaDescription'
                value={formData.metaDescription}
                onChange={handleInputChange}
                placeholder='SEO description for search engines'
                help='Description shown in search engine results'
              />
            </div>

            <FormField
              label='Featured Page'
              name='featured'
              type='checkbox'
              value={formData.featured}
              onChange={handleInputChange}
              help='Mark this page as featured'
            />
          </FormContainer>
        </div>
      )}

      <DataTable
        columns={columns}
        data={pages}
        loading={loading}
        searchable
        filterable
        pagination
        pageSize={10}
        onEdit={handleEdit}
        onDelete={handleDelete}
        emptyMessage='No pages found. Create your first page to get started.'
      />
    </div>
  );
}
