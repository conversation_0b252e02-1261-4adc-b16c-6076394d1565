'use client';

import React, {
  useState,
  useEffect,
  Suspense,
  useMemo,
  useCallback,
  memo,
} from 'react';
import { useRouter } from 'next/navigation';
import PageHeader from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import {
  PlusIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { ProductsTableSkeleton } from '@/components/skeletons/TableSkeleton';
import { useAdminTenant } from '@/hooks/useAdminTenant';
import { TenantProduct } from '@/lib/api/multi-tenant';
import { Button, Typography, Stack } from '@mui/material';
import { useMedusaBackendProducts } from '@/hooks/useMedusaAdminBackend';
import ConfirmDialog from '@/components/ConfirmDialog';
import { useToast } from '@/components/common/ToastProvider';
import { useOptimizedLoading } from '@/contexts/OptimizedLoadingContext';
import { useProgressiveDataFetch } from '@/hooks/useProgressiveDataFetch';

// Memoized product stats component
const ProductStats = memo(function ProductStats({ 
  stats 
}: { 
  stats: { total: number; published: number; draft: number; outOfStock: number } 
}) {
  return (
    <div className="grid grid-cols-4 gap-4 mb-6">
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
        <div className="text-sm text-gray-500">Total Products</div>
      </div>
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="text-2xl font-bold text-green-600">{stats.published}</div>
        <div className="text-sm text-gray-500">Published</div>
      </div>
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="text-2xl font-bold text-yellow-600">{stats.draft}</div>
        <div className="text-sm text-gray-500">Draft</div>
      </div>
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="text-2xl font-bold text-red-600">{stats.outOfStock}</div>
        <div className="text-sm text-gray-500">Out of Stock</div>
      </div>
    </div>
  );
});

// Memoized table columns
const useProductColumns = () => {
  const formatPrice = useCallback((price: number) =>
    new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price), []);

  const formatDate = useCallback((dateString: string) =>
    new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }), []);

  const getStatusBadge = useCallback((status: string) => {
    const statusConfig = {
      published: { color: 'bg-green-100 text-green-800', label: 'Published' },
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
      'Out of Stock': {
        color: 'bg-red-100 text-red-800',
        label: 'Out of Stock',
      },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  }, []);

  return useMemo(() => [
    {
      key: 'name',
      label: 'Product',
      sortable: true,
      render: (value: string, row: any) => (
        <div>
          <div className='text-sm font-medium text-gray-900'>{value}</div>
          <div className='text-sm text-gray-500'>{row.slug || 'No SKU'}</div>
        </div>
      ),
    },
    {
      key: 'categories',
      label: 'Categories',
      sortable: true,
      render: (value: string[]) => (
        <div className='flex flex-wrap gap-1'>
          {value && value.length > 0 ? (
            value.map((item, index) => (
              <span
                key={index}
                className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
              >
                {item}
              </span>
            ))
          ) : (
            <span className='text-gray-500 text-xs'>-</span>
          )}
        </div>
      ),
    },
    {
      key: 'price',
      label: 'Price',
      sortable: true,
      render: (_: any, row: any) => {
        const price = row.original_price;
        const salePrice = row.sale_price;
        return (
          <div className='space-y-1'>
            {salePrice && salePrice < price ? (
              <>
                <div className='text-sm font-medium text-gray-900'>
                  {formatPrice(salePrice)}
                </div>
                <div className='text-xs text-gray-500 line-through'>
                  {formatPrice(price)}
                </div>
              </>
            ) : (
              <div className='text-sm font-medium text-gray-900'>
                {formatPrice(price)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: 'inventory_quantity',
      label: 'Stock',
      sortable: true,
      render: (value: number) => (
        <span
          className={`text-sm font-medium ${
            value <= 10 ? 'text-red-600' : 'text-gray-900'
          }`}
        >
          {value}
        </span>
      ),
    },
    {
      key: 'collection',
      label: 'Collection',
      sortable: true,
      render: (value: string) => (
        <div>
          <span
            className={
              value
                ? 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
                : 'text-gray-500'
            }
          >
            {value || '-'}
          </span>
        </div>
      ),
    },
    {
      key: 'product_status',
      label: 'Status',
      sortable: true,
      render: (value: string) => getStatusBadge(value),
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{formatDate(value)}</span>
      ),
    },
  ], [formatPrice, formatDate, getStatusBadge]);
};

// Optimized products content component
const OptimizedProductsContent = memo(function OptimizedProductsContent() {
  const toast = useToast();
  const router = useRouter();
  const { selectedTenant } = useAdminTenant();
  const { withLoading } = useOptimizedLoading();
  const columns = useProductColumns();

  const [productStats, setProductStats] = useState({
    total: 0,
    published: 0,
    draft: 0,
    outOfStock: 0,
  });
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    product: TenantProduct | null;
  }>({
    isOpen: false,
    product: null,
  });
  const [isDeleting, setIsDeleting] = useState(false);

  const {
    products: rawProducts,
    deleteProduct,
    fetchProducts: fetchAllProducts,
  } = useMedusaBackendProducts();

  // Progressive data fetching
  const {
    data: products,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    progress,
  } = useProgressiveDataFetch(
    async (offset, limit) => {
      const result = await fetchAllProducts({
        limit,
        offset,
        sort: 'created_at:desc',
      });
      return { data: rawProducts || [], total: rawProducts?.length || 0 };
    },
    `products_${selectedTenant?.id || 'default'}`,
    {
      batchSize: 25,
      maxItems: 500,
      enableCache: true,
      cacheTTL: 3 * 60 * 1000, // 3 minutes
    }
  );

  // Calculate stats when products change
  useEffect(() => {
    if (products.length > 0) {
      const stats = {
        total: products.length,
        published: products.filter(p => p.status === 'published').length,
        draft: products.filter(p => p.status === 'draft').length,
        outOfStock: products.filter(
          p => p.variants?.some((v: any) => (v.inventory_quantity || 0) <= 0)
        ).length,
      };
      setProductStats(stats);
    }
  }, [products]);

  const handleEdit = useCallback((product: TenantProduct) => {
    withLoading(
      async () => {
        router.push(`/admin/products/${product.id}/edit`);
      },
      { message: 'Loading product editor...', type: 'navigation' }
    );
  }, [router, withLoading]);

  const handleDelete = useCallback((product: TenantProduct) => {
    setDeleteConfirmation({
      isOpen: true,
      product: product,
    });
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!deleteConfirmation.product) return;

    setIsDeleting(true);
    try {
      await withLoading(
        async () => {
          await deleteProduct(deleteConfirmation.product!.id.toString());
          toast.success(
            `Product "${
              deleteConfirmation.product!.title || deleteConfirmation.product!.id
            }" deleted successfully.`
          );
          await refresh();
        },
        { message: 'Deleting product...', type: 'form' }
      );
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product. Please try again.');
    } finally {
      setIsDeleting(false);
      setDeleteConfirmation({ isOpen: false, product: null });
    }
  }, [deleteConfirmation.product, deleteProduct, toast, refresh, withLoading]);

  const cancelDelete = useCallback(() => {
    setDeleteConfirmation({ isOpen: false, product: null });
  }, []);

  const handleAddProduct = useCallback(() => {
    withLoading(
      async () => {
        router.push('/admin/products/new');
      },
      { message: 'Loading product creator...', type: 'navigation' }
    );
  }, [router, withLoading]);

  // Transform products for table display
  const transformedProducts = useMemo(() => {
    if (!Array.isArray(products)) return [];

    return products
      .map(product => {
        const prices = product?.metadata?.additional_data?.product_prices;
        const firstPrice =
          Array.isArray(prices) && prices.length > 0 ? prices[0] : {};

        return {
          id: product.id,
          documentId: product.id,
          name: product?.title || 'No Name',
          slug: product?.handle || 'N/A',
          inventory_quantity:
            product?.metadata?.additional_data?.product_quantity || 0,
          original_price: firstPrice?.original_price || 0,
          sale_price: firstPrice?.discounted_price || 0,
          product_status: product?.status || 'Draft',
          collection: product?.collection?.title || false,
          categories: Array.isArray(product?.categories)
            ? product.categories.map((category: any) => category.name)
            : ['Uncategorized'],
          createdAt: product?.created_at || new Date().toISOString(),
          updatedAt: product?.updated_at || new Date().toISOString(),
          _original: product,
        };
      })
      .filter(Boolean)
      .sort(
        (a, b) =>
          new Date(String(b.createdAt)).getTime() -
          new Date(String(a.createdAt)).getTime()
      );
  }, [products]);

  const breadcrumbs = [{ label: 'Products', active: true }];

  const pageTitle = selectedTenant
    ? `Products - ${selectedTenant.name}`
    : 'Products';

  const pageDescription = selectedTenant
    ? `Manage products for ${selectedTenant.name} (${productStats.total} total, ${productStats.published} published)`
    : 'Select a tenant to manage products';

  return (
    <div className='space-y-6'>
      <PageHeader
        title={pageTitle}
        description={pageDescription}
        breadcrumbs={breadcrumbs}
        actions={
          <button
            onClick={handleAddProduct}
            className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            disabled={loading}
          >
            <PlusIcon className='-ml-1 mr-2 h-5 w-5' aria-hidden='true' />
            Add Product
          </button>
        }
      />

      {/* Product Stats */}
      <ProductStats stats={productStats} />

      {/* Progress indicator for loading */}
      {loading && progress > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center">
            <div className="flex-1">
              <div className="text-sm font-medium text-blue-800">
                Loading products... {Math.round(progress)}%
              </div>
              <div className="mt-2 w-full bg-blue-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className='bg-red-50 border border-red-200 rounded-md p-4'>
          <div className='flex'>
            <div className='ml-3'>
              <h3 className='text-sm font-medium text-red-800'>
                Error loading products
              </h3>
              <div className='mt-2 text-sm text-red-700'>
                <p>{error}</p>
              </div>
              <div className='mt-4'>
                <button
                  type='button'
                  className='bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200'
                  onClick={refresh}
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Products Table */}
      <div className="bg-white shadow rounded-lg">
        <DataTable
          columns={columns}
          data={transformedProducts}
          loading={loading && transformedProducts.length === 0}
          searchable
          filterable
          pagination
          pageSize={25}
          onEdit={row => handleEdit(row._original)}
          onDelete={row => handleDelete(row._original)}
          emptyMessage={
            'No products found. Create your first product to get started.'
          }
        />
        
        {/* Load More Button */}
        {hasMore && !loading && (
          <div className="px-6 py-4 border-t border-gray-200">
            <button
              onClick={loadMore}
              className="w-full px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Load More Products
            </button>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteConfirmation.isOpen}
        onClose={!isDeleting ? cancelDelete : undefined}
        header={
          <Stack direction='row' alignItems='center' gap={1}>
            <ExclamationTriangleIcon className='h-5 w-5 text-amber-500' />
            <Typography variant='h6'>Delete Product</Typography>
          </Stack>
        }
        body={
          <Typography>
            Are you sure you want to delete{' '}
            <strong>{deleteConfirmation.product?.title}</strong>? This action
            cannot be undone.
          </Typography>
        }
        actions={
          <>
            <Button onClick={cancelDelete} disabled={isDeleting}>
              Cancel
            </Button>
            <Button
              color='error'
              variant='contained'
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting…' : 'Delete'}
            </Button>
          </>
        }
      />
    </div>
  );
});

// Main optimized products page
const OptimizedProductsPage = memo(function OptimizedProductsPage() {
  return (
    <Suspense fallback={<ProductsTableSkeleton />}>
      <OptimizedProductsContent />
    </Suspense>
  );
});

export default OptimizedProductsPage;
