'use client';

import React, { useEffect, useMemo, useState, useCallback } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Container,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import {
  Inventory as ProductIcon,
  LocalOffer as PriceIcon,
  Image as ImageIcon,
  Settings as SettingsIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Description as DescriptionIcon,
  AttachMoney as AttachMoneyIcon,
  Inventory as InventoryIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import muiTheme from '../../../../../theme/mui-theme';
import { ThemeProvider } from '@mui/material/styles';
import {
  useMedusaAdminProducts,
  useMedusaBackendProducts,
} from '@/hooks/useMedusaAdminBackend';
import { MultiSelectDropdown } from '@/components/MultiSelectDropdown';
import { useDropzone } from 'react-dropzone';
import { useToast } from '@/components/common/ToastProvider';
import { useRouter, useParams } from 'next/navigation';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';
import { RichTextEditor } from '@/components/ui/RichTextEditor';
import { ProductVariantsEditor } from '@/components/ProductVariantsEditor';

interface VariantRow {
  id: string;
  title: string;
  sku: string;
  weight: number | string;
  width: number | string;
  length: number | string;
  height: number | string;
  originalPrice: number | string;
  salePrice: number | string;
  material: string;
}

const EditProduct = () => {
  const toast = useToast();
  const router = useRouter();
  const params = useParams();
  const { showLoading, hideLoading } = useLoadingBackdrop();

  // Main product state
  const [title, setTitle] = useState('');
  const [handle, setHandle] = useState('');
  const [description, setDescription] = useState('');
  const [status, setStatus] = useState<'' | string>('');
  const [inventoryStatus, setInventoryStatus] = useState<
    'in_stock' | 'out_of_stock'
  >('in_stock');
  const [price, setPrice] = useState('');
  const [comparePrice, setComparePrice] = useState('');
  const [quantity, setQuantity] = useState('');
  const [productCategories, setProductCategories] = useState<string[]>([]);
  const [collection, setCollection] = useState<'' | string>('');
  const [productTags, setProductTags] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Images: "upload" mode = files, "url" mode = URLs
  const [imgMode, setImgMode] = useState<'upload' | 'url'>('upload');
  const [imgUrls, setImgUrls] = useState<string[]>([]);
  const [images, setImages] = useState<(File | string)[]>([]);
  const [thumbnailMode, setThumbnailMode] = useState<'upload' | 'url'>(
    'upload'
  );
  const [thumbnailUrl, setThumbnailUrl] = useState<string>('');
  const [thumbnail, setThumbnail] = useState<File | string | null>(null);

  // Rich text fields
  const [overview, setOverview] = useState('');
  const [features, setFeatures] = useState('');
  const [specification, setSpecification] = useState('');

  // Variants
  const [variants, setVariants] = useState<VariantRow[]>([]);

  const {
    currentProduct,
    loadingProduct,
    error: fetchingError,
    fetchProduct: fetchSingleProduct,
    fetchCategories,
    fetchCollections,
    fetchTags,
    categories,
    collections,
    tags,
    loadingCategories,
  } = useMedusaAdminProducts();

  const { updateProduct } = useMedusaBackendProducts();

  // Lookup options
  const categoryOptions = useMemo(
    () => categories.map((a: any) => ({ label: a.name, value: a.id })),
    [categories]
  );
  const collectionptions = useMemo(
    () => collections.map((a: any) => ({ label: a.title, value: a.id })),
    [collections]
  );
  const tagptions = useMemo(
    () => tags.map((a: any) => ({ label: a.value, value: a.id })),
    [tags]
  );

  // Dropzone for images (upload mode)
  const onDropImages = useCallback(
    (acceptedFiles: File[]) => {
      if (images.length + acceptedFiles.length > 20) return;
      setImages(prev => [...prev, ...acceptedFiles]);
    },
    [images]
  );
  const {
    getRootProps: getImagesRootProps,
    getInputProps: getImagesInputProps,
    isDragActive: isImagesDragActive,
  } = useDropzone({
    onDrop: onDropImages,
    accept: { 'image/*': [] },
    multiple: true,
    maxFiles: 20,
    disabled: images.length >= 20 || imgMode !== 'upload',
  });

  // Dropzone for thumbnail (upload mode)
  const onDropThumb = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length) setThumbnail(acceptedFiles[0]);
  }, []);
  const {
    getRootProps: getThumbRootProps,
    getInputProps: getThumbInputProps,
    isDragActive: isThumbDragActive,
  } = useDropzone({
    onDrop: onDropThumb,
    accept: { 'image/*': [] },
    multiple: false,
    maxFiles: 1,
    disabled: thumbnailMode !== 'upload',
  });

  // Previews
  const imagePreviewUrls = useMemo(
    () =>
      imgMode === 'url'
        ? imgUrls
        : images.map(img =>
            typeof img === 'string' ? img : URL.createObjectURL(img)
          ),
    [images, imgUrls, imgMode]
  );
  const thumbnailPreview = useMemo(
    () =>
      thumbnailMode === 'url'
        ? thumbnailUrl
        : thumbnail
          ? typeof thumbnail === 'string'
            ? thumbnail
            : URL.createObjectURL(thumbnail)
          : null,
    [thumbnail, thumbnailMode, thumbnailUrl]
  );

  // Remove handlers
  const handleImageRemove = (idx: number) => {
    if (imgMode === 'upload') {
      setImages(imgs => imgs.filter((_, i) => i !== idx));
    } else {
      setImgUrls(urls => urls.filter((_, i) => i !== idx));
    }
  };
  const handleThumbnailRemove = () => {
    if (thumbnailMode === 'upload') setThumbnail(null);
    else setThumbnailUrl('');
  };

  // Variants handlers
  const handleAddVariant = () =>
    setVariants(v => [
      ...v,
      {
        id: crypto.randomUUID(),
        title: '',
        sku: '',
        weight: 0,
        width: 0,
        length: 0,
        height: 0,
        originalPrice: 0,
        salePrice: 0,
        material: '',
      },
    ]);
  const handleVariantChange = (
    rowId: string,
    field: keyof VariantRow,
    value: string | number
  ) =>
    setVariants(rows =>
      rows.map(r => (r.id === rowId ? { ...r, [field]: value } : r))
    );
  const handleDeleteVariant = (rowId: string) =>
    setVariants(rows => rows.filter(r => r.id !== rowId));

  // Fetch product data on mount
  const fetchProduct = async () => {
    try {
      await fetchSingleProduct(params?.id);
      await fetchCategories();
      await fetchCollections();
      await fetchTags();
      hideLoading();
    } catch (error) {
      toast.error('Failed to fetch product');
    }
  };

  // Sync product data to state on fetch
  useEffect(() => {
    fetchProduct();
  }, []);

  useEffect(() => {
    if (!currentProduct) return;

    // Standard fields
    setTitle(currentProduct.title ?? '');
    setHandle(currentProduct.handle ?? '');
    setDescription(currentProduct.description ?? '');
    setStatus(currentProduct.status);
    setProductCategories(currentProduct.categories.map(c => c.id));
    setCollection(currentProduct.collection?.id ?? '');
    setProductTags(currentProduct.tags.map(t => t.id));
    const prices = currentProduct?.metadata?.additional_data?.product_prices;
    const productPrice =
      Array.isArray(prices) && prices.length > 0 ? prices[0] : {};
    setPrice(productPrice?.original_price ?? 0);
    setComparePrice(productPrice?.sale_price ?? 0);
    setQuantity(
      currentProduct.metadata?.additional_data?.product_quantity ?? ''
    );
    setInventoryStatus(
      currentProduct.metadata?.additional_data?.product_inventory_status ??
        'in_stock'
    );

    // Rich text fields
    setOverview(
      currentProduct.metadata?.additional_data?.product_overview ?? ''
    );
    setFeatures(
      currentProduct.metadata?.additional_data?.product_features ?? ''
    );
    setSpecification(
      currentProduct.metadata?.additional_data?.product_specifications ?? ''
    );

    // Images (mode-detect: upload or url)
    if (
      Array.isArray(currentProduct.images) &&
      currentProduct.images.length > 0 &&
      currentProduct.images.every(img => img && typeof img.url === 'string')
    ) {
      setImgMode('url');
      setImgUrls(currentProduct.images.map(i => i.url));
      setImages([]); // clear file state
    } else {
      setImgMode('upload');
      setImages(currentProduct.images?.map(i => i.url) ?? []);
      setImgUrls([]);
    }

    if (
      currentProduct.thumbnail &&
      typeof currentProduct.thumbnail === 'string'
    ) {
      setThumbnailMode('url');
      setThumbnailUrl(currentProduct.thumbnail);
      setThumbnail(null);
    } else {
      setThumbnailMode('upload');
      setThumbnail(currentProduct.thumbnail ?? null);
      setThumbnailUrl('');
    }

    // Variants
    setVariants(
      currentProduct?.variants?.map(v => ({
        id: v?.id,
        title: v?.title,
        sku: v?.sku,
        weight: v.weight ?? '',
        width: v.width ?? '',
        length: v.length ?? '',
        height: v.height ?? '',
        originalPrice:
          Array.isArray(v?.prices) && v.prices.length > 0
            ? v.prices[0].amount
            : '',
        salePrice: v?.metadata?.sale_price || 0,
        inventoryStatus: v?.metadata?.product_inventory_status || '',
        variantQuantity: v?.metadata?.product_quantity || null,

        material: v.material ?? '',
      })) || []
    );
  }, [currentProduct]);

  // Save handler
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      const payload: Record<string, any> = {};

      if (title !== currentProduct?.title) payload.title = title;
      if (handle !== currentProduct?.handle) payload.handle = handle;
      if (description !== currentProduct?.description)
        payload.description = description;
      if (status !== currentProduct?.status) payload.status = status;

      payload.metadata = {
        additional_data: {
          product_overview: overview,
          product_features: features,
          product_specifications: specification,
          product_prices: [
            {
              sale_price: Number(comparePrice) || 0,
              original_price: Number(price) || 0,
            },
          ],
          product_quantity: Number(quantity) || 0,
          product_inventory_status: inventoryStatus || 'in_stock',
        },
      };

      // categories, collection, tags
      if (
        JSON.stringify(productCategories) !==
        JSON.stringify(currentProduct?.categories.map(c => c.id))
      ) {
        payload.categories = productCategories.map(id => ({ id }));
      }
      if ((collection || null) !== (currentProduct?.collection?.id || null)) {
        payload.collection_id = collection || null;
      }
      if (
        JSON.stringify(productTags) !==
        JSON.stringify(currentProduct?.tags.map(t => t.id))
      ) {
        payload.tags = productTags.map(id => ({ id }));
      }

      // Images
      if (imgMode === 'url') {
        payload.images = imgUrls.map(imageUrl => ({ url: imageUrl }));
      } else {
        payload.images = images;
      }
      if (thumbnailMode === 'url') {
        payload.thumbnail = thumbnailUrl;
      } else {
        payload.thumbnail = thumbnail;
      }

      // Variants
      if (variants.length) {
        payload.variants = variants.map(v => ({
          ...(v.id?.startsWith('variant_') && { id: v.id }),
          title: v.title,
          sku: v.sku,
          material: v.material || null,
          weight: v.weight === '' ? null : v.weight,
          width: v.width === '' ? null : v.width,
          length: v.length === '' ? null : v.length,
          height: v.height === '' ? null : v.height,
          metadata: {
            sale_price: Number(v.salePrice) || 0,
            original_price: Number(v.originalPrice) || 0,
            product_quantity: Number(v.variantQuantity) || 0,
            product_inventory_status: v.inventoryStatus || 'in_stock',
          },
          prices: [
            {
              currency_code: 'inr',
              amount: v.originalPrice === '' ? 0 : v.originalPrice,
            },
          ].filter(Boolean),
        }));
      }

      const res = await updateProduct(params?.id, payload);
      if (res?.product?.id) {
        toast.success('Product updated successfully');
        await fetchProduct();
      }
    } catch (e: any) {
      toast.error('Update failed');
    }
    setIsSubmitting(false);
  };

  if (loadingProduct || loadingCategories) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '50vh',
            }}
          >
            <Typography>Loading product...</Typography>
          </Box>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        <Container maxWidth='lg' sx={{ py: 4 }}>
          {/* HEADER */}
          <Card
            elevation={0}
            sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
          >
            <Stack
              direction='row'
              justifyContent='space-between'
              alignItems='center'
              mb={2}
            >
              <Box>
                <Typography
                  variant='h4'
                  component='h1'
                  fontWeight='bold'
                  color='primary.main'
                >
                  Edit Product
                </Typography>
                <Typography variant='body1' color='text.secondary' mt={1}>
                  Update product information and settings
                </Typography>
              </Box>
              <Stack direction='row' spacing={2}>
                <Button
                  variant='outlined'
                  onClick={() => router.back()}
                  startIcon={<CancelIcon />}
                >
                  Cancel
                </Button>
                <Button
                  variant='contained'
                  type='submit'
                  form='product-edit-form'
                  disabled={isSubmitting}
                  startIcon={<SaveIcon />}
                  sx={{ minWidth: 140 }}
                  onClick={handleSave}
                >
                  {isSubmitting ? 'Updating...' : 'Update Product'}
                </Button>
              </Stack>
            </Stack>
          </Card>

          {/* BASIC INFO */}
          <Grid container spacing={3}>
            <Grid item size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <DescriptionIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Basic Information
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Product name &amp; description
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <TextField
                        label='Name'
                        name='name'
                        fullWidth
                        value={title}
                        onChange={e => setTitle(e.target.value)}
                        required
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <TextField
                        label='Slug'
                        name='sku'
                        fullWidth
                        value={handle}
                        onChange={e => setHandle(e.target.value)}
                        required
                        helperText='URL-friendly unique identifier'
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <TextField
                        label='Description'
                        name='description'
                        fullWidth
                        multiline
                        rows={3}
                        value={description}
                        onChange={e => setDescription(e.target.value)}
                        required
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* PRICING */}
            <Grid item size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <AttachMoneyIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Pricing
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Price &amp; cost
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    <Grid item size={{ xs: 12, md: 3 }}>
                      <TextField
                        label='Original Price(MRP)'
                        name='price'
                        type='number'
                        fullWidth
                        value={price}
                        onChange={e => setPrice(e.target.value)}
                        required
                        InputProps={{
                          startAdornment: (
                            <Typography sx={{ mr: 1 }}>₹</Typography>
                          ),
                        }}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 3 }}>
                      <TextField
                        label='Sale Price'
                        name='comparePrice'
                        type='number'
                        fullWidth
                        value={comparePrice}
                        onChange={e => setComparePrice(e.target.value)}
                        InputProps={{
                          startAdornment: (
                            <Typography sx={{ mr: 1 }}>₹</Typography>
                          ),
                        }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* INVENTORY */}
            <Grid item size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <InventoryIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Inventory
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Manage stock levels
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        label='Quantity'
                        name='quantity'
                        type='number'
                        fullWidth
                        value={quantity}
                        onChange={e => {
                          setQuantity(e.target.value);
                          setInventoryStatus(
                            Number(e.target.value) > 0
                              ? 'in_stock'
                              : 'out_of_stock'
                          );
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormControl fullWidth>
                        <InputLabel id='inv-status-label'>
                          Inventory Status
                        </InputLabel>
                        <Select
                          labelId='inv-status-label'
                          name='inventoryStatus'
                          label='Inventory Status'
                          value={inventoryStatus}
                          onChange={e => setInventoryStatus(e.target.value)}
                        >
                          <MenuItem value='in_stock'>In Stock</MenuItem>
                          <MenuItem value='out_of_stock'>Out of Stock</MenuItem>
                        </Select>
                        <FormHelperText>
                          Auto-updates when quantity changes (editable if
                          needed)
                        </FormHelperText>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* ORGANIZATION */}
            <Grid item size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <CategoryIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Organization
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Categorize and publish product
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <MultiSelectDropdown
                        label='Categories'
                        multiple
                        value={productCategories}
                        options={categoryOptions}
                        onChange={value =>
                          setProductCategories(
                            typeof value === 'string' ? value.split(',') : value
                          )
                        }
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <MultiSelectDropdown
                        label='Collections'
                        value={collection}
                        options={collectionptions}
                        onChange={val => setCollection(val)}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <MultiSelectDropdown
                        label='Tags'
                        multiple
                        value={productTags}
                        options={tagptions}
                        onChange={val =>
                          setProductTags(
                            typeof val === 'string' ? val.split(',') : val
                          )
                        }
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <FormControl fullWidth>
                        <InputLabel id='status-label'>Status</InputLabel>
                        <Select
                          labelId='status-label'
                          value={status}
                          label='Status'
                          onChange={e => setStatus(e.target.value)}
                        >
                          {['draft', 'proposed', 'published', 'rejected'].map(
                            s => (
                              <MenuItem key={s} value={s}>
                                {s}
                              </MenuItem>
                            )
                          )}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* IMAGES UPLOAD/URL */}
            <Grid item size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <ImageIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Product Images
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Upload a thumbnail and up to 20 product images
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3} display={'block'}>
                    {/* Product images uploader or URL */}
                    <Grid item xs={12} md={9}>
                      <Typography fontWeight={500} mb={1}>
                        Product Images
                      </Typography>
                      <Stack direction='row' spacing={2} mb={2}>
                        <Button
                          variant={
                            imgMode === 'upload' ? 'contained' : 'outlined'
                          }
                          onClick={() => setImgMode('upload')}
                          size='small'
                          sx={{ minWidth: 80 }}
                        >
                          Upload
                        </Button>
                        <Button
                          variant={imgMode === 'url' ? 'contained' : 'outlined'}
                          onClick={() => setImgMode('url')}
                          size='small'
                          sx={{ minWidth: 80 }}
                        >
                          Image URL
                        </Button>
                      </Stack>
                      {imgMode === 'upload' ? (
                        <Box
                          {...getImagesRootProps()}
                          sx={{
                            border: '2px dashed',
                            borderColor: isImagesDragActive
                              ? 'primary.main'
                              : 'grey.300',
                            borderRadius: 2,
                            p: 2,
                            textAlign: 'center',
                            mb: 2,
                            cursor:
                              images.length >= 20 ? 'not-allowed' : 'pointer',
                            background:
                              'linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%)',
                            transition: 'border-color 0.2s',
                            opacity: images.length >= 20 ? 0.5 : 1,
                            '&:hover': {
                              borderColor: 'primary.main',
                              bgcolor: 'primary.50',
                            },
                          }}
                        >
                          <input
                            {...getImagesInputProps()}
                            disabled={images.length >= 20}
                          />
                          <Stack
                            direction='row'
                            alignItems='center'
                            justifyContent='center'
                            spacing={1}
                          >
                            <CloudUploadIcon
                              sx={{ fontSize: 28, color: 'primary.main' }}
                            />
                            <Typography fontWeight={500} color='primary.main'>
                              Upload Images
                            </Typography>
                          </Stack>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mt: 0.5 }}
                          >
                            JPEG / PNG / WebP, max 5MB each.{' '}
                            {20 - images.length} left
                          </Typography>
                        </Box>
                      ) : (
                        <Box>
                          <TextField
                            label='Image URLs (comma-separated)'
                            multiline
                            minRows={2}
                            value={imgUrls.join(',')}
                            onChange={e =>
                              setImgUrls(
                                e.target.value
                                  .split(',')
                                  .map(s => s.trim())
                                  .filter(Boolean)
                              )
                            }
                            fullWidth
                            placeholder='https://img1.jpg,https://img2.jpg'
                          />
                          <Stack direction='row' spacing={1} mt={1}>
                            {imgUrls.map((u, i) => (
                              <Box
                                key={u}
                                sx={{
                                  position: 'relative',
                                  width: 64,
                                  height: 64,
                                  border: '1px solid #ccc',
                                  borderRadius: 1,
                                  overflow: 'hidden',
                                  mr: 1,
                                }}
                              >
                                <img
                                  src={u}
                                  alt=''
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                  }}
                                />
                                <IconButton
                                  size='small'
                                  sx={{
                                    position: 'absolute',
                                    top: 0,
                                    right: 0,
                                  }}
                                  onClick={() =>
                                    setImgUrls(urls =>
                                      urls.filter((_, ii) => ii !== i)
                                    )
                                  }
                                >
                                  <DeleteIcon fontSize='small' />
                                </IconButton>
                              </Box>
                            ))}
                          </Stack>
                        </Box>
                      )}
                      {/* Image preview for files */}
                      {imgMode === 'upload' && imagePreviewUrls.length > 0 && (
                        <Grid container spacing={1} sx={{ mt: 1 }}>
                          {imagePreviewUrls.map((url, i) => (
                            <Grid item xs={6} sm={3} md={2} key={url}>
                              <Box
                                sx={{
                                  position: 'relative',
                                  width: '100%',
                                  height: 120,
                                  borderRadius: 2,
                                  overflow: 'hidden',
                                  border: '1.5px solid',
                                  borderColor: 'grey.300',
                                  background: '#f8fafc',
                                }}
                              >
                                <img
                                  src={url}
                                  alt={`img-${i}`}
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'contain',
                                    background: '#fff',
                                  }}
                                />
                                <IconButton
                                  size='small'
                                  onClick={() => handleImageRemove(i)}
                                  sx={{
                                    position: 'absolute',
                                    top: 4,
                                    right: 4,
                                    bgcolor: 'rgba(0,0,0,0.5)',
                                    color: '#fff',
                                    '&:hover': { bgcolor: 'rgba(0,0,0,0.8)' },
                                    zIndex: 2,
                                  }}
                                >
                                  <DeleteIcon fontSize='small' />
                                </IconButton>
                              </Box>
                            </Grid>
                          ))}
                        </Grid>
                      )}
                    </Grid>
                    {/* Thumbnail uploader and preview beside images */}
                    <Grid item xs={12} md={3}>
                      <Typography fontWeight={500} mb={1}>
                        Thumbnail
                      </Typography>
                      <Stack direction='row' spacing={2} mb={2}>
                        <Button
                          variant={
                            thumbnailMode === 'upload'
                              ? 'contained'
                              : 'outlined'
                          }
                          onClick={() => setThumbnailMode('upload')}
                          size='small'
                          sx={{ minWidth: 80 }}
                        >
                          Upload
                        </Button>
                        <Button
                          variant={
                            thumbnailMode === 'url' ? 'contained' : 'outlined'
                          }
                          onClick={() => setThumbnailMode('url')}
                          size='small'
                          sx={{ minWidth: 80 }}
                        >
                          Image URL
                        </Button>
                      </Stack>
                      {thumbnailMode === 'upload' ? (
                        <Box
                          {...getThumbRootProps()}
                          sx={{
                            border: '2px dashed',
                            borderColor: isThumbDragActive
                              ? 'primary.main'
                              : 'grey.300',
                            borderRadius: 2,
                            p: 2,
                            textAlign: 'center',
                            mb: 2,
                            cursor: 'pointer',
                            background:
                              'linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%)',
                            transition: 'border-color 0.2s',
                            '&:hover': {
                              borderColor: 'primary.main',
                              bgcolor: 'primary.50',
                            },
                          }}
                        >
                          <input {...getThumbInputProps()} />
                          <Stack
                            direction='row'
                            alignItems='center'
                            justifyContent='center'
                            spacing={1}
                          >
                            <CloudUploadIcon
                              sx={{ fontSize: 28, color: 'primary.main' }}
                            />
                            <Typography fontWeight={500} color='primary.main'>
                              Upload Thumbnail
                            </Typography>
                          </Stack>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mt: 0.5 }}
                          >
                            JPEG / PNG / WebP, max 5MB
                          </Typography>
                        </Box>
                      ) : (
                        <Box>
                          <TextField
                            label='Thumbnail URL'
                            value={thumbnailUrl}
                            onChange={e => setThumbnailUrl(e.target.value)}
                            fullWidth
                          />
                          {thumbnailUrl && (
                            <Box
                              sx={{
                                mt: 1,
                                position: 'relative',
                                width: 80,
                                height: 80,
                                border: '1px solid #ccc',
                                borderRadius: 1,
                                overflow: 'hidden',
                              }}
                            >
                              <img
                                src={thumbnailUrl}
                                alt='thumb'
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover',
                                }}
                              />
                              <IconButton
                                size='small'
                                sx={{ position: 'absolute', top: 0, right: 0 }}
                                onClick={() => setThumbnailUrl('')}
                              >
                                <DeleteIcon fontSize='small' />
                              </IconButton>
                            </Box>
                          )}
                        </Box>
                      )}
                      {/* Image preview for file */}
                      {thumbnailMode === 'upload' && thumbnailPreview && (
                        <Box
                          sx={{
                            mt: 1,
                            position: 'relative',
                            width: 80,
                            height: 80,
                            border: '1px solid #ccc',
                            borderRadius: 1,
                            overflow: 'hidden',
                          }}
                        >
                          <img
                            src={thumbnailPreview}
                            alt='thumb'
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                          />
                          <IconButton
                            size='small'
                            sx={{ position: 'absolute', top: 0, right: 0 }}
                            onClick={handleThumbnailRemove}
                          >
                            <DeleteIcon fontSize='small' />
                          </IconButton>
                        </Box>
                      )}
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* RICH TEXT: Overview */}
            <Grid item size={12}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' mb={1}>
                    <Typography variant='h6' fontWeight='bold' flexGrow={1}>
                      Product Overview
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 2 }} />
                  <RichTextEditor
                    value={overview}
                    onChange={setOverview}
                    placeholder='Enter product overview...'
                    minHeight={150}
                  />
                </CardContent>
              </Card>
            </Grid>

            {/* RICH TEXT: Product Features */}
            <Grid item size={12}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' mb={1}>
                    <Typography variant='h6' fontWeight='bold' flexGrow={1}>
                      Product Features
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 2 }} />
                  <RichTextEditor
                    value={features}
                    onChange={setFeatures}
                    placeholder='Enter product features...'
                    minHeight={120}
                  />
                </CardContent>
              </Card>
            </Grid>

            {/* RICH TEXT: Product Specification */}
            <Grid item size={12}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' mb={1}>
                    <Typography variant='h6' fontWeight='bold' flexGrow={1}>
                      Product Specification
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 2 }} />
                  <RichTextEditor
                    value={specification}
                    onChange={setSpecification}
                    placeholder='Enter product specifications...'
                    minHeight={120}
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item size={12}>
              <ProductVariantsEditor
                variants={variants}
                onChange={setVariants}
              />
            </Grid>
          </Grid>
        </Container>
      </Box>
    </ThemeProvider>
  );
};

export default EditProduct;
