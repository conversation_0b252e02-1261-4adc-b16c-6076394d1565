'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import {
  <PERSON><PERSON>,
  Stack,
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Paper,
  Divider,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Edit as EditIcon,
  ArrowBack as ArrowBackIcon,
  Inventory as InventoryIcon,
  Category as CategoryIcon,
  LocalOffer as PriceIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../theme/mui-theme';
import { useMedusaAdminProducts } from '@/hooks/useMedusaAdminBackend';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';

interface Product {
  id: string;
  name: string;
  description: string;
  sku: string;
  price: number;
  comparePrice?: number;
  costPrice?: number;
  quantity: number;
  category: string;
  brand?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  images: string[];
  status: 'active' | 'inactive' | 'draft';
  tags: string[];
  seo: {
    title?: string;
    description?: string;
    keywords?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export default function ProductViewPage() {
  const router = useRouter();
  const params = useParams();
  const { showLoading, hideLoading } = useLoadingBackdrop();

  const productId = params.id as string;

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);

  const {
    currentProduct,
    loadingProduct,
    error: fetchingError,
    fetchProduct: fetchSingleProduct,
  } = useMedusaAdminProducts();

  const fetchProduct = async () => {
    try {
      await fetchSingleProduct(productId);
      hideLoading();
    } catch (error) {
      console.error('Error fetching product:', error);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  useEffect(() => {
    fetchProduct();
  }, [params.id]);

  if (loadingProduct) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '50vh',
            }}
          >
            <Typography>Loading product...</Typography>
          </Box>
        </Box>
      </ThemeProvider>
    );
  }

  if (!currentProduct) {
    return (
      <ThemeProvider theme={muiTheme}>
        <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '50vh',
            }}
          >
            <Typography>Product not found</Typography>
          </Box>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        {/* Header */}
        <Paper
          elevation={0}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 2 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
          >
            <Box>
              <Stack direction='row' alignItems='center' spacing={2} mb={1}>
                <Button
                  variant='outlined'
                  startIcon={<ArrowBackIcon />}
                  onClick={() => router.push('/admin/products')}
                >
                  Back to Products
                </Button>
                <Chip
                  label={currentProduct.status.toUpperCase()}
                  color={getStatusColor(currentProduct.status) as any}
                  size='small'
                />
              </Stack>
              <Typography
                variant='h4'
                component='h1'
                fontWeight='bold'
                color='primary.main'
              >
                {currentProduct.title}
              </Typography>
              <Typography variant='body1' color='text.secondary' mt={1}>
                SKU: {currentProduct.handle} • Category:{' '}
                {currentProduct?.categories[0]?.name}
              </Typography>
            </Box>
            <Button
              variant='contained'
              startIcon={<EditIcon />}
              onClick={() =>
                router.push(`/admin/products/${currentProduct.id}/edit`)
              }
            >
              Edit Product
            </Button>
          </Stack>
        </Paper>

        {/* Content */}
        <Grid container spacing={3}>
          {/* Product Images */}
          <Grid size={{ xs: 12, md: 12 }}>
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant='h6' fontWeight='bold' mb={2}>
                  Product Images
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  {currentProduct.images.length > 0 ? (
                    currentProduct.images.map((img, idx) => (
                      <img
                        key={idx}
                        src={img.url || img} // support both string or object
                        alt={`Product image ${idx + 1}`}
                        width={150}
                        height={150}
                        style={{ objectFit: 'cover', borderRadius: 8 }}
                      />
                    ))
                  ) : (
                    <Typography>No images uploaded</Typography>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Product Details */}
          <Grid size={{ xs: 12, md: 12 }}>
            <Stack spacing={3}>
              {/* Basic Information */}
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <InfoIcon color='primary' />
                    <Typography variant='h6' fontWeight='bold'>
                      Basic Information
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography
                        variant='subtitle2'
                        color='text.secondary'
                        gutterBottom
                      >
                        Product Name
                      </Typography>
                      <Typography variant='body1' fontWeight='medium'>
                        {currentProduct.title}
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography
                        variant='subtitle2'
                        color='text.secondary'
                        gutterBottom
                      >
                        SKU
                      </Typography>
                      <Typography variant='body1' fontWeight='medium'>
                        {currentProduct.handle}
                      </Typography>
                    </Grid>
                    <Grid size={12}>
                      <Typography
                        variant='subtitle2'
                        color='text.secondary'
                        gutterBottom
                      >
                        Description
                      </Typography>
                      <Typography variant='body1'>
                        {currentProduct.description}
                      </Typography>
                    </Grid>

                    <Grid size={12}>
                      <Typography
                        variant='subtitle2'
                        color='text.secondary'
                        gutterBottom
                      >
                        Tags
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {currentProduct?.tags.map(tag => (
                          <Chip
                            key={tag.id}
                            label={tag.value}
                            size='small'
                            variant='outlined'
                          />
                        ))}
                      </Box>
                    </Grid>

                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography
                        variant='subtitle2'
                        color='text.secondary'
                        gutterBottom
                      >
                        Categories
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {currentProduct.categories.map(cat => (
                          <Chip key={cat.id} label={cat.name} size='small' />
                        ))}
                      </Box>
                    </Grid>

                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography
                        variant='subtitle2'
                        color='text.secondary'
                        gutterBottom
                      >
                        Collection
                      </Typography>
                      <Typography variant='body1'>
                        {currentProduct.collection?.title || '-'}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <InventoryIcon color='primary' />
                    <Typography variant='h6' fontWeight='bold'>
                      Product Variants & Pricing
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />

                  <TableContainer component={Paper} variant='outlined'>
                    <Table size='small'>
                      <TableHead>
                        <TableRow>
                          <TableCell>Title</TableCell>
                          <TableCell>SKU</TableCell>
                          <TableCell>Material</TableCell>
                          <TableCell>Price</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {currentProduct.variants.map(variant => (
                          <TableRow key={variant.id}>
                            <TableCell>{variant.title}</TableCell>
                            <TableCell>{variant.sku || '-'}</TableCell>
                            <TableCell>{variant.material || '-'}</TableCell>
                            <TableCell>
                              {variant.prices[0]?.amount
                                ? ` ${formatPrice(variant.prices[0].amount)}`
                                : '-'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>

              {/* Pricing & Inventory */}
              {/* <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction="row" alignItems="center" spacing={2} mb={3}>
                    <PriceIcon color="primary" />
                    <Typography variant="h6" fontWeight="bold">
                      Pricing & Inventory
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Selling Price
                      </Typography>
                      <Typography variant="h6" color="primary.main" fontWeight="bold">
                        {formatPrice(currentProduct?.price)}
                      </Typography>
                    </Grid>
                    {product?.comparePrice && (
                      <Grid size={{ xs: 12, md: 4 }}>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Compare Price
                        </Typography>
                        <Typography variant="body1" sx={{ textDecoration: 'line-through' }}>
                          {formatPrice(product.comparePrice)}
                        </Typography>
                      </Grid>
                    )}
                    {product?.costPrice && (
                      <Grid size={{ xs: 12, md: 4 }}>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Cost Price
                        </Typography>
                        <Typography variant="body1">{formatPrice(product.costPrice)}</Typography>
                      </Grid>
                    )}
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Stock Quantity
                      </Typography>
                      <Typography
                        variant="h6"
                        fontWeight="bold"
                        color={product?.quantity <= 10 ? 'error.main' : 'text.primary'}
                      >
                        {product?.quantity} units
                      </Typography>
                      {product?.quantity <= 10 && (
                        <Typography variant="caption" color="error.main">
                          Low stock warning
                        </Typography>
                      )}
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Category
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {currentProduct?.category}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card> */}

              {/* Physical Properties */}
              {(product?.weight || product?.dimensions) && (
                <Card elevation={2} sx={{ borderRadius: 2 }}>
                  <CardContent sx={{ p: 4 }}>
                    <Stack
                      direction='row'
                      alignItems='center'
                      spacing={2}
                      mb={3}
                    >
                      <InventoryIcon color='primary' />
                      <Typography variant='h6' fontWeight='bold'>
                        Physical Properties
                      </Typography>
                    </Stack>
                    <Divider sx={{ mb: 3 }} />

                    <Grid container spacing={3}>
                      {product.weight && (
                        <Grid size={{ xs: 12, md: 6 }}>
                          <Typography
                            variant='subtitle2'
                            color='text.secondary'
                            gutterBottom
                          >
                            Weight
                          </Typography>
                          <Typography variant='body1' fontWeight='medium'>
                            {product.weight}g
                          </Typography>
                        </Grid>
                      )}
                      {product.dimensions && (
                        <Grid size={{ xs: 12, md: 6 }}>
                          <Typography
                            variant='subtitle2'
                            color='text.secondary'
                            gutterBottom
                          >
                            Dimensions (L × W × H)
                          </Typography>
                          <Typography variant='body1' fontWeight='medium'>
                            {product.dimensions.length} ×{' '}
                            {product.dimensions.width} ×{' '}
                            {product.dimensions.height} cm
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>
              )}

              {/* SEO Information */}
              {product?.seo &&
                (product.seo.title ||
                  product.seo.description ||
                  product.seo.keywords) && (
                  <Card elevation={2} sx={{ borderRadius: 2 }}>
                    <CardContent sx={{ p: 4 }}>
                      <Typography variant='h6' fontWeight='bold' mb={3}>
                        SEO Information
                      </Typography>
                      <Divider sx={{ mb: 3 }} />

                      <Grid container spacing={3}>
                        {product.seo.title && (
                          <Grid size={12}>
                            <Typography
                              variant='subtitle2'
                              color='text.secondary'
                              gutterBottom
                            >
                              SEO Title
                            </Typography>
                            <Typography variant='body1'>
                              {product.seo.title}
                            </Typography>
                          </Grid>
                        )}
                        {product.seo.description && (
                          <Grid size={12}>
                            <Typography
                              variant='subtitle2'
                              color='text.secondary'
                              gutterBottom
                            >
                              SEO Description
                            </Typography>
                            <Typography variant='body1'>
                              {product.seo.description}
                            </Typography>
                          </Grid>
                        )}
                        {product.seo.keywords && (
                          <Grid size={12}>
                            <Typography
                              variant='subtitle2'
                              color='text.secondary'
                              gutterBottom
                            >
                              SEO Keywords
                            </Typography>
                            <Typography variant='body1'>
                              {product.seo.keywords}
                            </Typography>
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                )}

              {/* Timestamps */}
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant='h6' fontWeight='bold' mb={3}>
                    Timestamps
                  </Typography>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography
                        variant='subtitle2'
                        color='text.secondary'
                        gutterBottom
                      >
                        Created At
                      </Typography>
                      <Typography variant='body1'>
                        {formatDate(currentProduct.created_at)}
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography
                        variant='subtitle2'
                        color='text.secondary'
                        gutterBottom
                      >
                        Last Updated
                      </Typography>
                      <Typography variant='body1'>
                        {formatDate(currentProduct.updated_at)}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Stack>
          </Grid>
        </Grid>
      </Box>
    </ThemeProvider>
  );
}
