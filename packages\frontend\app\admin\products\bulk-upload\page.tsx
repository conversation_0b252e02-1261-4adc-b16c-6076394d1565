'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import PageHeader from '@/components/admin/PageHeader';
import { BulkUploadFileUpload } from '@/components/admin/BulkUploadFileUpload';
import { BulkUploadTemplate } from '@/components/admin/BulkUploadTemplate';
import { BulkUploadErrorBoundary } from '@/components/admin/BulkUploadErrorBoundary';
import { useBulkImport } from '@/hooks/useBulkImport';
import { useToast } from '@/components/common/ToastProvider';
import {
  ArrowLeftIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

type UploadStep = 'template' | 'upload' | 'validate' | 'import' | 'complete';

const BulkUploadContent = () => {
  const router = useRouter();
  const toast = useToast();
  const [currentStep, setCurrentStep] = useState<UploadStep>('template');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const {
    // Upload state
    isUploading,
    uploadProgress,
    uploadError,

    // Import state
    importStatus,

    // Operations
    uploadFile,
    downloadTemplate,
    reset,
    clearErrors,
  } = useBulkImport();

  const handleFileSelect = useCallback(
    async (file: File) => {
      setSelectedFile(file);
      clearErrors();

      try {
        // Upload and process the file (optimized backend does immediate import)
        await uploadFile(file); // Optimized backend handles immediate processing

        // Skip validation step since optimized backend processes immediately
        setCurrentStep('complete');
      } catch (error) {
        console.error('File upload/import failed:', error);
      }
    },
    [uploadFile, clearErrors]
  );

  const handleFileRemove = useCallback(() => {
    setSelectedFile(null);
    reset();
    setCurrentStep('upload');
  }, [reset]);

  // Navigation handlers
  const handleGoToProducts = useCallback(() => {
    router.push('/admin/products');
  }, [router]);

  const handleStartOver = useCallback(() => {
    setSelectedFile(null);
    reset();
    setCurrentStep('template');
  }, [reset]);

  const steps = [
    {
      id: 'template',
      name: 'Download Template',
      completed: currentStep !== 'template',
    },
    {
      id: 'upload',
      name: 'Upload & Import',
      completed: ['complete'].includes(currentStep),
    },
    {
      id: 'complete',
      name: 'Complete',
      completed: currentStep === 'complete',
    },
  ];

  // Update step based on import status
  React.useEffect(() => {
    if (importStatus?.state === 'COMPLETED') {
      setCurrentStep('complete');
    }
  }, [importStatus]);

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Page Header */}
      <PageHeader
        title='Bulk Product Upload'
        actions={
          <button
            onClick={() => router.back()}
            className='inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            <ArrowLeftIcon className='w-4 h-4 mr-2' />
            Back
          </button>
        }
      />

      {/* Subtitle */}
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4 mb-4'>
        <p className='text-gray-600'>
          Import multiple products at once using Excel files
        </p>
      </div>

      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        {/* Progress Steps */}
        <div className='mb-8'>
          <nav aria-label='Progress'>
            <ol className='flex items-center justify-between sm:justify-start'>
              {steps.map((step, stepIdx) => (
                <li
                  key={step.id}
                  className={cn(
                    stepIdx !== steps.length - 1 ? 'pr-4 sm:pr-8 lg:pr-20' : '',
                    'relative flex-1 sm:flex-none'
                  )}
                >
                  {step.completed ? (
                    <div
                      className='absolute inset-0 flex items-center'
                      aria-hidden='true'
                    >
                      <div className='h-0.5 w-full bg-blue-600' />
                    </div>
                  ) : (
                    <div
                      className='absolute inset-0 flex items-center'
                      aria-hidden='true'
                    >
                      <div className='h-0.5 w-full bg-gray-200' />
                    </div>
                  )}
                  <div
                    className={cn(
                      'relative w-8 h-8 flex items-center justify-center rounded-full border-2',
                      // eslint-disable-next-line no-nested-ternary
                      step.completed
                        ? 'bg-blue-600 border-blue-600'
                        : currentStep === step.id
                          ? 'border-blue-600 bg-white'
                          : 'border-gray-300 bg-white'
                    )}
                  >
                    {step.completed ? (
                      <CheckCircleIcon className='w-5 h-5 text-white' />
                    ) : (
                      <span
                        className={cn(
                          'text-sm font-medium',
                          currentStep === step.id
                            ? 'text-blue-600'
                            : 'text-gray-500'
                        )}
                      >
                        {stepIdx + 1}
                      </span>
                    )}
                  </div>
                  <div className='mt-2'>
                    <span
                      className={cn(
                        'text-sm font-medium',
                        step.completed || currentStep === step.id
                          ? 'text-blue-600'
                          : 'text-gray-500'
                      )}
                    >
                      {step.name}
                    </span>
                  </div>
                </li>
              ))}
            </ol>
          </nav>
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8'>
          {/* Main Content */}
          <div className='lg:col-span-2 space-y-6 min-w-0 overflow-hidden'>
            {/* Step 1: Template Download */}
            {currentStep === 'template' && (
              <div className='space-y-6'>
                <BulkUploadTemplate onDownloadTemplate={downloadTemplate} />

                <div className='flex justify-end'>
                  <button
                    onClick={() => setCurrentStep('upload')}
                    className='inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  >
                    Continue to Upload
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: File Upload & Import */}
            {currentStep === 'upload' && (
              <div className='space-y-6'>
                <BulkUploadFileUpload
                  onFileSelect={handleFileSelect}
                  onFileRemove={handleFileRemove}
                  isUploading={isUploading}
                  uploadProgress={uploadProgress}
                  error={uploadError}
                  selectedFile={selectedFile}
                />

                {/* Import Status */}
                {isUploading && (
                  <div className='p-4 bg-blue-50 border border-blue-200 rounded-lg'>
                    <div className='flex items-center space-x-3'>
                      <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600'></div>
                      <div>
                        <p className='text-sm font-medium text-blue-900'>
                          Processing your file...
                        </p>
                        <p className='text-xs text-blue-700'>
                          Upload progress: {uploadProgress}%
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 3: Import Complete */}
            {currentStep === 'complete' && (
              <div className='space-y-6'>
                {/* Import Results */}
                {importStatus && (
                  <div className='p-6 bg-green-50 border border-green-200 rounded-lg'>
                    <div className='flex items-center space-x-3 mb-4'>
                      <CheckCircleIcon className='w-8 h-8 text-green-500' />
                      <div>
                        <h3 className='text-lg font-medium text-green-900'>
                          Import Completed Successfully!
                        </h3>
                        <p className='text-sm text-green-700'>
                          Your products have been imported and are now available
                          in your store.
                        </p>
                      </div>
                    </div>

                    {/* Import Statistics */}
                    <div className='grid grid-cols-3 gap-4 mb-4'>
                      <div className='text-center p-3 bg-white rounded-lg'>
                        <div className='text-2xl font-bold text-green-600'>
                          {importStatus.progress.total_records}
                        </div>
                        <div className='text-sm text-gray-600'>
                          Total Records
                        </div>
                      </div>
                      <div className='text-center p-3 bg-white rounded-lg'>
                        <div className='text-2xl font-bold text-green-600'>
                          {importStatus.progress.processed_records}
                        </div>
                        <div className='text-sm text-gray-600'>Successful</div>
                      </div>
                      <div className='text-center p-3 bg-white rounded-lg'>
                        <div className='text-2xl font-bold text-red-600'>
                          {importStatus.progress.failed_records}
                        </div>
                        <div className='text-sm text-gray-600'>Failed</div>
                      </div>
                    </div>

                    <div className='flex space-x-3'>
                      <button
                        onClick={handleStartOver}
                        className='inline-flex items-center px-4 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-white hover:bg-green-50'
                      >
                        Import More Products
                      </button>

                      <button
                        onClick={handleGoToProducts}
                        className='inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700'
                      >
                        View Products
                      </button>
                    </div>
                  </div>
                )}

                {/* Error Display */}
                {uploadError && (
                  <div className='p-6 bg-red-50 border border-red-200 rounded-lg'>
                    <div className='flex items-center space-x-3'>
                      <ExclamationTriangleIcon className='w-8 h-8 text-red-500' />
                      <div>
                        <h3 className='text-lg font-medium text-red-900'>
                          Import Failed
                        </h3>
                        <p className='text-sm text-red-700'>{uploadError}</p>
                      </div>
                    </div>

                    <div className='flex space-x-3 mt-4'>
                      <button
                        onClick={handleStartOver}
                        className='inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700'
                      >
                        Try Again
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className='space-y-6 min-w-0'>
            {/* Help Card */}
            <div className='bg-white border border-gray-200 rounded-lg p-4 sm:p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Need Help?
              </h3>
              <div className='space-y-2 text-sm text-gray-600'>
                <p className='break-words'>
                  • Download the template to see the required format
                </p>
                <p className='break-words'>
                  • Ensure all required fields are filled
                </p>
                <p className='break-words'>
                  • Use proper data types for each column
                </p>
                <p className='break-words'>• Keep file size under 10MB</p>
                <p className='break-words'>
                  • Maximum 1000 products per import
                </p>
              </div>
            </div>

            {/* Import Status */}
            {importStatus && (
              <div className='bg-white border border-gray-200 rounded-lg p-4 sm:p-6'>
                <h3 className='text-lg font-medium text-gray-900 mb-4'>
                  Import Summary
                </h3>
                <div className='space-y-3'>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-gray-600 break-words'>
                      Total Records:
                    </span>
                    <span className='text-sm font-medium ml-2 flex-shrink-0'>
                      {importStatus.progress.total_records}
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-gray-600 break-words'>
                      Successful:
                    </span>
                    <span className='text-sm font-medium text-green-600 ml-2 flex-shrink-0'>
                      {importStatus.progress.processed_records}
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-gray-600 break-words'>
                      Failed:
                    </span>
                    <span className='text-sm font-medium text-red-600 ml-2 flex-shrink-0'>
                      {importStatus.progress.failed_records}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const BulkUploadPage = () => (
  <BulkUploadErrorBoundary>
    <BulkUploadContent />
  </BulkUploadErrorBoundary>
);

export default BulkUploadPage;
