'use client';

import React, { useState } from 'react';
import { StarIcon, FireIcon, TagIcon } from '@heroicons/react/24/outline';

export default function DealManagementPage() {
  const [activeTab, setActiveTab] = useState('featured');

  return (
    <div className='space-y-6'>
      {/* Page Header */}
      <div className='md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8'>
        <div className='flex-1 min-w-0'>
          <h2 className='text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate'>
            Deal Management
          </h2>
          <p className='mt-1 text-sm text-gray-500'>
            Manage featured products, top deals, and hot picks for your store
          </p>
        </div>
        <div className='mt-4 flex md:mt-0 md:ml-4'>
          <button className='inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700'>
            Save Changes
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className='px-4 sm:px-6 md:px-8'>
        <div className='border-b border-gray-200'>
          <nav className='-mb-px flex space-x-8'>
            <button
              onClick={() => setActiveTab('featured')}
              className={`${
                activeTab === 'featured'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <StarIcon className='h-5 w-5' />
              <span>Featured Management</span>
              <span className='ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                4
              </span>
            </button>
            <button
              onClick={() => setActiveTab('top-deals')}
              className={`${
                activeTab === 'top-deals'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <TagIcon className='h-5 w-5' />
              <span>Top Deals</span>
              <span className='ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800'>
                6
              </span>
            </button>
            <button
              onClick={() => setActiveTab('hot-picks')}
              className={`${
                activeTab === 'hot-picks'
                  ? 'border-red-500 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <FireIcon className='h-5 w-5' />
              <span>Hot Picks</span>
              <span className='ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800'>
                4
              </span>
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className='px-4 sm:px-6 md:px-8'>
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center space-x-3'>
                <div className='p-2 rounded-lg bg-blue-100'>
                  <StarIcon className='h-5 w-5 text-blue-600' />
                </div>
                <div>
                  <h3 className='text-lg leading-6 font-medium text-gray-900'>
                    {activeTab === 'featured' && 'Featured Management'}
                    {activeTab === 'top-deals' && 'Top Deals'}
                    {activeTab === 'hot-picks' && 'Hot Picks'}
                  </h3>
                  <p className='text-sm text-gray-500'>
                    {activeTab === 'featured' &&
                      'Handpicked products showcased prominently on the homepage'}
                    {activeTab === 'top-deals' &&
                      'Best deals and discounted products for customers'}
                    {activeTab === 'hot-picks' &&
                      'Trending and popular products that customers love'}
                  </p>
                </div>
              </div>
              <div className='text-right'>
                <div className='text-sm font-medium text-gray-900'>
                  {activeTab === 'featured' && '4 / 8'}
                  {activeTab === 'top-deals' && '6 / 6'}
                  {activeTab === 'hot-picks' && '4 / 4'}
                </div>
                <div className='text-xs text-gray-500'>products</div>
              </div>
            </div>
            <p className='text-sm text-gray-500'>
              Complete Deal Management interface with product management
              functionality will be added here.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
