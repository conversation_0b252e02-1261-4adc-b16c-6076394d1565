'use client';

import React from 'react';

/**
 * Simple loading component for products page
 */
export default function ProductsLoading() {
  return (
    <div className='bg-white shadow rounded-lg'>
      <div className='px-6 py-4 border-b border-gray-200'>
        <div className='animate-pulse flex justify-between items-center'>
          <div className='h-6 bg-gray-200 rounded w-32'></div>
          <div className='h-10 bg-gray-200 rounded w-24'></div>
        </div>
      </div>
      <div className='divide-y divide-gray-200'>
        {Array.from({ length: 10 }).map((_, index) => (
          <div key={index} className='px-6 py-4'>
            <div className='animate-pulse flex items-center space-x-4'>
              <div className='h-16 w-16 bg-gray-200 rounded'></div>
              <div className='flex-1 space-y-2'>
                <div className='h-4 bg-gray-200 rounded w-3/4'></div>
                <div className='h-3 bg-gray-200 rounded w-1/2'></div>
                <div className='h-3 bg-gray-200 rounded w-1/4'></div>
              </div>
              <div className='text-right space-y-2'>
                <div className='h-4 bg-gray-200 rounded w-16'></div>
                <div className='h-3 bg-gray-200 rounded w-12'></div>
              </div>
              <div className='h-8 w-8 bg-gray-200 rounded'></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
