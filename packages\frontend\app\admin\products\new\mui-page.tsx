'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Breadcrumbs,
  Link,
  Chip,
  IconButton,
  Divider,
  Alert,
  Snackbar,
  Paper,
  Stack,
  Autocomplete,
  InputAdornment,
} from '@mui/material';
import {
  Add as AddIcon,
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Close as CloseIcon,
  NavigateNext as NavigateNextIcon,
  AttachMoney as AttachMoneyIcon,
  Inventory as InventoryIcon,
  Category as CategoryIcon,
  Description as DescriptionIcon,
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../theme/mui-theme';
import MuiFormField, {
  MuiFormContainer,
} from '../../../../components/admin/MuiFormField';

interface ProductFormData {
  name: string;
  description: string;
  shortDescription: string;
  sku: string;
  price: string;
  comparePrice: string;
  cost: string;
  trackQuantity: boolean;
  quantity: string;
  category: string;
  status: string;
  visibility: string;
  weight: string;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
  seo: {
    title: string;
    description: string;
    keywords: string;
  };
  images: File[];
}

const initialFormData: ProductFormData = {
  name: '',
  description: '',
  shortDescription: '',
  sku: '',
  price: '',
  comparePrice: '',
  cost: '',
  trackQuantity: true,
  quantity: '',
  category: '',
  status: 'draft',
  visibility: 'visible',
  weight: '',
  dimensions: {
    length: '',
    width: '',
    height: '',
  },
  seo: {
    title: '',
    description: '',
    keywords: '',
  },
  images: [],
};

const categories = [
  { value: 'electronics', label: 'Electronics' },
  { value: 'clothing', label: 'Clothing' },
  { value: 'shoes', label: 'Shoes' },
  { value: 'accessories', label: 'Accessories' },
  { value: 'home', label: 'Home & Garden' },
  { value: 'sports', label: 'Sports & Outdoors' },
];

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

const visibilityOptions = [
  { value: 'visible', label: 'Visible' },
  { value: 'hidden', label: 'Hidden' },
  { value: 'catalog-only', label: 'Catalog Only' },
];

export default function MuiNewProductPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<ProductFormData>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => {
        const parentValue = prev[parent as keyof ProductFormData];
        return {
          ...prev,
          [parent]: {
            ...(typeof parentValue === 'object' && parentValue !== null
              ? parentValue
              : {}),
            [child]: value,
          },
        };
      });
    } else {
      setFormData(prev => ({
        ...prev,
        [name]:
          type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
      }));
    }

    // Auto-generate SKU from product name
    if (name === 'name' && value) {
      const sku = value
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setFormData(prev => ({ ...prev, sku: sku.substring(0, 20) }));
    }

    // Auto-generate SEO title from product name
    if (name === 'name' && value) {
      setFormData(prev => ({
        ...prev,
        seo: { ...prev.seo, title: `${value} - ONDC Seller Platform` },
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Validate file types
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const invalidFiles = files.filter(file => !validTypes.includes(file.type));

    if (invalidFiles.length > 0) {
      setSnackbar({
        open: true,
        message: 'Please select only JPEG, PNG, or WebP images',
        severity: 'error',
      });
      return;
    }

    // Validate file sizes (max 5MB each)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const oversizedFiles = files.filter(file => file.size > maxSize);

    if (oversizedFiles.length > 0) {
      setSnackbar({
        open: true,
        message: 'Please select images smaller than 5MB',
        severity: 'error',
      });
      return;
    }

    // Update form data
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...files],
    }));

    // Create preview URLs
    const newPreviewUrls = files.map(file => URL.createObjectURL(file));
    setImagePreviewUrls(prev => [...prev, ...newPreviewUrls]);
  };

  const handleImageRemove = (index: number) => {
    // Revoke the object URL to free memory
    URL.revokeObjectURL(imagePreviewUrls[index]);

    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));

    setImagePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Product description is required';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = 'Price must be a valid positive number';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (formData.trackQuantity && !formData.quantity.trim()) {
      newErrors.quantity = 'Quantity is required when tracking inventory';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      setSnackbar({
        open: true,
        message: 'Please fix the errors before submitting',
        severity: 'error',
      });
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Product data:', formData);
      setSnackbar({
        open: true,
        message: 'Product created successfully!',
        severity: 'success',
      });

      // Redirect to products list after a short delay
      setTimeout(() => {
        router.push('/admin/products');
      }, 1500);
    } catch (error) {
      console.error('Error creating product:', error);
      setSnackbar({
        open: true,
        message: 'Error creating product',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusChip = (status: string) => {
    const colors = {
      draft: 'warning',
      active: 'success',
      inactive: 'error',
    } as const;

    return (
      <Chip
        label={status.charAt(0).toUpperCase() + status.slice(1)}
        color={colors[status as keyof typeof colors] || 'default'}
        size='small'
        variant='outlined'
      />
    );
  };

  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
        {/* Header with Breadcrumbs */}
        <Box sx={{ mb: 4 }}>
          <Breadcrumbs
            separator={<NavigateNextIcon fontSize='small' />}
            sx={{ mb: 2 }}
          >
            <Link
              color='inherit'
              href='/admin/products'
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
              }}
            >
              Products
            </Link>
            <Typography color='text.primary'>New Product</Typography>
          </Breadcrumbs>

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
            }}
          >
            <Box>
              <Typography variant='h4' component='h1' sx={{ mb: 1 }}>
                New Product
              </Typography>
              <Typography variant='body1' color='text.secondary'>
                Create a new product for your store
              </Typography>
            </Box>
            <Stack direction='row' spacing={2}>
              <Button
                variant='outlined'
                startIcon={<ArrowBackIcon />}
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                startIcon={<SaveIcon />}
                onClick={handleSubmit}
                disabled={loading}
                size='large'
              >
                {loading ? 'Creating...' : 'Create Product'}
              </Button>
            </Stack>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Main Content */}
          <Grid size={{ xs: 12, lg: 8 }}>
            <Stack spacing={3}>
              {/* Basic Information */}
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant='h6' component='h2'>
                      Basic Information
                    </Typography>
                  </Box>
                  <Typography
                    variant='body2'
                    color='text.secondary'
                    sx={{ mb: 3 }}
                  >
                    Essential product details and description
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <MuiFormField
                        label='Product Name'
                        name='name'
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder='Enter product name'
                        required
                        error={errors.name}
                        help='This will be displayed to customers'
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <MuiFormField
                        label='SKU'
                        name='sku'
                        value={formData.sku}
                        onChange={handleInputChange}
                        placeholder='Enter SKU'
                        required
                        error={errors.sku}
                        help='Stock Keeping Unit - unique identifier'
                      />
                    </Grid>
                    <Grid size={12}>
                      <MuiFormField
                        label='Short Description'
                        name='shortDescription'
                        type='textarea'
                        rows={2}
                        value={formData.shortDescription}
                        onChange={handleInputChange}
                        placeholder='Brief product description'
                        help='This will appear in product listings'
                      />
                    </Grid>
                    <Grid size={12}>
                      <MuiFormField
                        label='Description'
                        name='description'
                        type='textarea'
                        rows={6}
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder='Detailed product description'
                        required
                        error={errors.description}
                        help='Provide comprehensive product details'
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Pricing */}
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <AttachMoneyIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant='h6' component='h2'>
                      Pricing
                    </Typography>
                  </Box>
                  <Typography
                    variant='body2'
                    color='text.secondary'
                    sx={{ mb: 3 }}
                  >
                    Set product pricing and cost information
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MuiFormField
                        label='Price'
                        name='price'
                        type='number'
                        value={formData.price}
                        onChange={handleInputChange}
                        placeholder='0.00'
                        required
                        error={errors.price}
                        help='Selling price for customers'
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MuiFormField
                        label='Compare at Price'
                        name='comparePrice'
                        type='number'
                        value={formData.comparePrice}
                        onChange={handleInputChange}
                        placeholder='0.00'
                        help='Original price for discount display'
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MuiFormField
                        label='Cost per Item'
                        name='cost'
                        type='number'
                        value={formData.cost}
                        onChange={handleInputChange}
                        placeholder='0.00'
                        help='Cost for profit calculations'
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Inventory */}
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <InventoryIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant='h6' component='h2'>
                      Inventory
                    </Typography>
                  </Box>
                  <Typography
                    variant='body2'
                    color='text.secondary'
                    sx={{ mb: 3 }}
                  >
                    Manage product inventory and stock levels
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid size={12}>
                      <MuiFormField
                        label='Track Quantity'
                        name='trackQuantity'
                        type='checkbox'
                        checked={formData.trackQuantity}
                        onChange={handleInputChange}
                        help='Enable inventory tracking for this product'
                      />
                    </Grid>
                    {formData.trackQuantity && (
                      <Grid size={{ xs: 12, md: 6 }}>
                        <MuiFormField
                          label='Quantity'
                          name='quantity'
                          type='number'
                          value={formData.quantity}
                          onChange={handleInputChange}
                          placeholder='0'
                          error={errors.quantity}
                          help='Available stock quantity'
                        />
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>

              {/* Organization */}
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <CategoryIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant='h6' component='h2'>
                      Organization
                    </Typography>
                  </Box>
                  <Typography
                    variant='body2'
                    color='text.secondary'
                    sx={{ mb: 3 }}
                  >
                    Categorize and organize your product
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MuiFormField
                        label='Category'
                        name='category'
                        type='select'
                        value={formData.category}
                        onChange={handleInputChange}
                        options={categories}
                        required
                        error={errors.category}
                        help='Product category for organization'
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MuiFormField
                        label='Status'
                        name='status'
                        type='select'
                        value={formData.status}
                        onChange={handleInputChange}
                        options={statusOptions}
                        help='Product availability status'
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <MuiFormField
                        label='Visibility'
                        name='visibility'
                        type='select'
                        value={formData.visibility}
                        onChange={handleInputChange}
                        options={visibilityOptions}
                        help='Product visibility settings'
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Product Images */}
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <ImageIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant='h6' component='h2'>
                      Product Images
                    </Typography>
                  </Box>
                  <Typography
                    variant='body2'
                    color='text.secondary'
                    sx={{ mb: 3 }}
                  >
                    Upload high-quality images of your product. First image will
                    be the main product image.
                  </Typography>

                  {/* Image Upload Area */}
                  <Box
                    sx={{
                      border: '2px dashed',
                      borderColor: 'grey.300',
                      borderRadius: 2,
                      p: 3,
                      textAlign: 'center',
                      mb: 3,
                      cursor: 'pointer',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: 'primary.50',
                      },
                    }}
                    onClick={() =>
                      document.getElementById('image-upload')?.click()
                    }
                  >
                    <input
                      id='image-upload'
                      type='file'
                      multiple
                      accept='image/jpeg,image/jpg,image/png,image/webp'
                      onChange={handleImageUpload}
                      style={{ display: 'none' }}
                    />
                    <CloudUploadIcon
                      sx={{ fontSize: 48, color: 'grey.400', mb: 2 }}
                    />
                    <Typography
                      variant='h6'
                      color='text.secondary'
                      gutterBottom
                    >
                      Click to upload images
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      Supports JPEG, PNG, WebP (max 5MB each)
                    </Typography>
                  </Box>

                  {/* Image Previews */}
                  {imagePreviewUrls.length > 0 && (
                    <Box>
                      <Typography variant='subtitle2' sx={{ mb: 2 }}>
                        Uploaded Images ({imagePreviewUrls.length})
                      </Typography>
                      <Grid container spacing={2}>
                        {imagePreviewUrls.map((url, index) => (
                          <Grid size={{ xs: 6, sm: 4, md: 3 }} key={index}>
                            <Box
                              sx={{
                                position: 'relative',
                                aspectRatio: '1',
                                borderRadius: 1,
                                overflow: 'hidden',
                                border: '1px solid',
                                borderColor: 'grey.300',
                              }}
                            >
                              <img
                                src={url}
                                alt={`Product ${index + 1}`}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover',
                                }}
                              />
                              <IconButton
                                size='small'
                                sx={{
                                  position: 'absolute',
                                  top: 4,
                                  right: 4,
                                  bgcolor: 'rgba(0, 0, 0, 0.6)',
                                  color: 'white',
                                  '&:hover': {
                                    bgcolor: 'rgba(0, 0, 0, 0.8)',
                                  },
                                }}
                                onClick={() => handleImageRemove(index)}
                              >
                                <DeleteIcon fontSize='small' />
                              </IconButton>
                              {index === 0 && (
                                <Chip
                                  label='Main'
                                  size='small'
                                  color='primary'
                                  sx={{
                                    position: 'absolute',
                                    bottom: 4,
                                    left: 4,
                                  }}
                                />
                              )}
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Stack>
          </Grid>

          {/* Sidebar */}
          <Grid size={{ xs: 12, lg: 4 }}>
            <Stack spacing={3}>
              {/* Product Status */}
              <Card>
                <CardContent>
                  <Typography variant='h6' component='h2' sx={{ mb: 2 }}>
                    Product Status
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      mb: 2,
                    }}
                  >
                    <Typography variant='body2' color='text.secondary'>
                      Current Status:
                    </Typography>
                    {getStatusChip(formData.status)}
                  </Box>
                  <Typography variant='body2' color='text.secondary'>
                    {formData.status === 'draft' &&
                      'Product is saved as draft and not visible to customers'}
                    {formData.status === 'active' &&
                      'Product is live and available for purchase'}
                    {formData.status === 'inactive' &&
                      'Product is hidden from customers'}
                  </Typography>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardContent>
                  <Typography variant='h6' component='h2' sx={{ mb: 2 }}>
                    Quick Actions
                  </Typography>
                  <Stack spacing={2}>
                    <Button
                      variant='outlined'
                      fullWidth
                      onClick={() =>
                        setFormData(prev => ({ ...prev, status: 'draft' }))
                      }
                    >
                      Save as Draft
                    </Button>
                    <Button
                      variant='contained'
                      fullWidth
                      onClick={() =>
                        setFormData(prev => ({ ...prev, status: 'active' }))
                      }
                    >
                      Publish Product
                    </Button>
                  </Stack>
                </CardContent>
              </Card>

              {/* Product Summary */}
              <Card>
                <CardContent>
                  <Typography variant='h6' component='h2' sx={{ mb: 2 }}>
                    Product Summary
                  </Typography>
                  <Stack spacing={1}>
                    <Box
                      sx={{ display: 'flex', justifyContent: 'space-between' }}
                    >
                      <Typography variant='body2' color='text.secondary'>
                        Name:
                      </Typography>
                      <Typography variant='body2' sx={{ fontWeight: 500 }}>
                        {formData.name || 'Not set'}
                      </Typography>
                    </Box>
                    <Box
                      sx={{ display: 'flex', justifyContent: 'space-between' }}
                    >
                      <Typography variant='body2' color='text.secondary'>
                        SKU:
                      </Typography>
                      <Typography variant='body2' sx={{ fontWeight: 500 }}>
                        {formData.sku || 'Not set'}
                      </Typography>
                    </Box>
                    <Box
                      sx={{ display: 'flex', justifyContent: 'space-between' }}
                    >
                      <Typography variant='body2' color='text.secondary'>
                        Price:
                      </Typography>
                      <Typography variant='body2' sx={{ fontWeight: 500 }}>
                        {formData.price ? `₹${formData.price}` : 'Not set'}
                      </Typography>
                    </Box>
                    <Box
                      sx={{ display: 'flex', justifyContent: 'space-between' }}
                    >
                      <Typography variant='body2' color='text.secondary'>
                        Category:
                      </Typography>
                      <Typography variant='body2' sx={{ fontWeight: 500 }}>
                        {categories.find(cat => cat.value === formData.category)
                          ?.label || 'Not set'}
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Stack>
          </Grid>
        </Grid>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}
