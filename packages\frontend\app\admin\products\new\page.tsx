'use client';

import React, { useEffect, useState, useMemo, Suspense } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  Grid,
  IconButton,
  Stack,
  Typography,
  TextField,
  Snackbar,
  Alert,
  Paper,
  MenuItem,
  Select,
  InputLabel,
  FormControl,
  FormHelperText,
} from '@mui/material';
import {
  Cancel as CancelIcon,
  Save as SaveIcon,
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  Description as DescriptionIcon,
  Inventory as InventoryIcon,
  AttachMoney as AttachMoneyIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import { useRouter } from 'next/navigation';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../../../theme/mui-theme';
import { useToast } from '@/components/common/ToastProvider';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';
import { MultiSelectDropdown } from '@/components/MultiSelectDropdown';
import {
  useMedusaAdminProducts,
  useMedusaBackendProducts,
} from '@/hooks/useMedusaAdminBackend';
import { ProductFormSkeleton } from '@/components/skeletons/FormSkeleton';
import { RichTextEditor } from '@/components/ui/RichTextEditor';

// --- If not imported from shared components, include ProductVariantsEditor inline here!
import {
  ProductVariantsEditor,
  VariantRow,
} from '@/components/ProductVariantsEditor';

// --- Helpers
const slugify = (str: string) =>
  str
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)+/g, '');

const syncInventoryStatus = (qty: string | number) =>
  Number(qty) > 0 ? 'in_stock' : 'out_of_stock';

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'published', label: 'Published' },
];

// --- Main Component
const NewProductContent = () => {
  const toast = useToast();
  const router = useRouter();
  const { showLoading, hideLoading } = useLoadingBackdrop();

  // Snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    severity: 'success',
    message: '',
  });

  // Images
  const [imgMode, setImgMode] = useState<'upload' | 'url'>('upload');
  const [imgUrls, setImgUrls] = useState<string[]>([]);
  const [images, setImages] = useState<(File | string)[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [thumbnailMode, setThumbnailMode] = useState<'upload' | 'url'>(
    'upload'
  );
  const [thumbnailUrl, setThumbnailUrl] = useState<string>('');
  const [thumbnail, setThumbnail] = useState<File | string | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);

  // Look-up
  const [productCategories, setProductCategories] = useState<string[]>([]);
  const [collection, setCollection] = useState<string>('');
  const [productTags, setProductTags] = useState<string[]>([]);

  const {
    fetchCategories,
    fetchCollections,
    fetchTags,
    categories,
    collections,
    tags,
  } = useMedusaAdminProducts();
  const { createProduct } = useMedusaBackendProducts();

  // Richtext fields
  const [overview, setOverview] = useState('');
  const [features, setFeatures] = useState('');
  const [specification, setSpecification] = useState('');

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    sku: '',
    description: '',
    price: '',
    comparePrice: '',
    quantity: '',
    inventoryStatus: 'in_stock',
    status: 'draft',
  });
  const [errors, setErrors] = useState<{ [k: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Variants
  const [variants, setVariants] = useState<VariantRow[]>([]);

  // Lookup options
  const categoryOptions = useMemo(
    () => categories.map(c => ({ label: c.name, value: c.id })),
    [categories]
  );
  const collectionOptions = useMemo(
    () => collections.map(c => ({ label: c.title, value: c.id })),
    [collections]
  );
  const tagOptions = useMemo(
    () => tags.map(t => ({ label: t.value, value: t.id })),
    [tags]
  );

  // Fetch lookups once
  useEffect(() => {
    void (async () => {
      await Promise.all([fetchCategories(), fetchCollections(), fetchTags()]);
      hideLoading();
    })();
  }, [fetchCategories, fetchCollections, fetchTags]);

  // --------- Image Dropzone
  const onDropImages = (acceptedFiles: File[]) => {
    if (acceptedFiles.some(f => f.size > 5 * 1024 * 1024)) {
      setSnackbar({
        open: true,
        severity: 'error',
        message: 'Images must be ≤ 5 MB',
      });
      return;
    }
    setImages(prev => [...prev, ...acceptedFiles]);
    setImagePreviewUrls(prev => [
      ...prev,
      ...acceptedFiles.map(f => URL.createObjectURL(f)),
    ]);
  };
  const {
    getRootProps: getImagesRootProps,
    getInputProps: getImagesInputProps,
    isDragActive: isImagesDragActive,
  } = useDropzone({
    onDrop: onDropImages,
    accept: {
      'image/jpeg': [],
      'image/jpg': [],
      'image/png': [],
      'image/webp': [],
    },
    maxFiles: 20,
    multiple: true,
    disabled: imgMode === 'url',
  });

  // Thumbnail
  const onDropThumbnail = (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file.size > 5 * 1024 * 1024) {
      setSnackbar({
        open: true,
        severity: 'error',
        message: 'Image must be ≤ 5 MB',
      });
      return;
    }
    setThumbnail(file);
    setThumbnailPreview(URL.createObjectURL(file));
  };
  const {
    getRootProps: getThumbRootProps,
    getInputProps: getThumbInputProps,
    isDragActive: isThumbDragActive,
  } = useDropzone({
    onDrop: onDropThumbnail,
    accept: {
      'image/jpeg': [],
      'image/jpg': [],
      'image/png': [],
      'image/webp': [],
    },
    maxFiles: 1,
    multiple: false,
    disabled: thumbnailMode === 'url',
  });

  // Remove preview/URL images
  const handleImageRemove = (idx: number) => {
    if (imgMode === 'upload') {
      setImagePreviewUrls(urls => urls.filter((_, i) => i !== idx));
      setImages(imgs => imgs.filter((_, i) => i !== idx));
    } else {
      setImgUrls(urls => urls.filter((_, i) => i !== idx));
    }
  };
  const handleThumbnailRemove = () => {
    if (thumbnailMode === 'upload') {
      setThumbnail(null);
      setThumbnailPreview(null);
    } else {
      setThumbnailUrl('');
    }
  };

  // ----------- Validation
  const validate = () => {
    const err: { [k: string]: string } = {};
    if (!formData.name) err.name = 'Required';
    if (!formData.sku) err.sku = 'Required';
    if (!formData.description) err.description = 'Required';
    if (!formData.price) err.price = 'Required';
    if (!formData.quantity) err.quantity = 'Required';
    if (!overview) err.overview = 'Product overview required';
    if (!specification) err.specification = 'Product spec required';
    if (
      (imgMode === 'upload' && imagePreviewUrls.length === 0) ||
      (imgMode === 'url' && imgUrls.length === 0)
    )
      err.images = 'At least one image required';
    if (
      (thumbnailMode === 'upload' && !thumbnailPreview) ||
      (thumbnailMode === 'url' && !thumbnailUrl)
    )
      err.thumb = 'Thumbnail image required';
    setErrors(err);
    return Object.keys(err).length === 0;
  };

  // ----------- Submit handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    // if (!validate()) {
    //   toast.error('Please fix validation errors!');
    //   setIsSubmitting(false);
    //   return;
    // }
    showLoading();
    try {
      // Build payload...
      const payload: Record<string, any> = {
        title: formData.name,
        handle: formData.sku,
        description: formData.description,

        status: formData.status,
        categories: productCategories.map(id => ({ id })),
        collection_id: collection,
        tags: productTags.map(id => ({ id })),
        options: [
          {
            title: 'default',
            values: ['default'],
          },
        ],
        metadata: {
          additional_data: {
            // images: imgMode === 'url' ? imgUrls : imagePreviewUrls,
            product_prices: [
              {
                sale_price: Number(formData.comparePrice) || 0,
                original_price: Number(formData.price) || 0,
              },
            ],
            product_quantity: Number(formData.quantity) || 0,
            product_inventory_status: formData.inventoryStatus,
            product_overview: overview,
            product_features: features,
            product_specifications: specification,
          },
        },
      };
      if (variants.length) {
        payload.variants = variants.map(v => ({
          title: v.title,
          sku: v.sku,
          material: v.material || null,
          weight: v.weight === '' ? null : v.weight,
          width: v.width === '' ? null : v.width,
          length: v.length === '' ? null : v.length,
          height: v.height === '' ? null : v.height,
          metadata: {
            sale_price: Number(v.salePrice) || 0,
            original_price: Number(v.originalPrice) || 0,
            product_quantity: Number(v.variantQuantity) || 0,
            product_inventory_status: v.inventoryStatus || 'in_stock',
          },
          prices: [
            {
              currency_code: 'inr',
              amount: v.originalPrice === '' ? 0 : v.originalPrice,
            },
          ].filter(Boolean),
        }));
      }
      if (imgMode === 'url') {
        payload.images = imgUrls.map(imageUrl => ({ url: imageUrl }));
      }
      if (thumbnailMode === 'url') {
        payload.thumbnail = thumbnailUrl;
      }
      console.log('payload::::::::::', payload);
      const response = await createProduct(payload);
      if (response?.product) {
        toast.success('Product created successfully!');
        router.push(`/admin/products/${response?.product?.id}/edit`);
      }
    } catch (error) {
      console.log('error::::::::::', error);
      toast.error('Failed to add new product.');
    }
    setIsSubmitting(false);
    hideLoading();
  };

  // ----------- UI
  return (
    <ThemeProvider theme={muiTheme}>
      <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', p: 3 }}>
        <Paper
          elevation={2}
          sx={{ p: 3, mb: 3, bgcolor: 'white', borderRadius: 4, boxShadow: 4 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
            mb={2}
          >
            <Box>
              <Typography variant='h4' fontWeight='bold' color='primary.main'>
                New Product
              </Typography>
              <Typography color='text.secondary' mt={1}>
                Create a new product for your store
              </Typography>
            </Box>
            <Stack direction='row' spacing={2}>
              <Button
                variant='outlined'
                startIcon={<CancelIcon />}
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                startIcon={<SaveIcon />}
                onClick={handleSubmit}
                disabled={isSubmitting}
                sx={{ minWidth: 140 }}
              >
                {isSubmitting ? 'Creating…' : 'Create Product'}
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* FORM */}
        <Box component='form' onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* BASIC INFO */}
            <Grid item size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <DescriptionIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Basic Information
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid item size={{ xs: 12, md: 3 }}>
                      <TextField
                        label='Name'
                        name='name'
                        fullWidth
                        value={formData.name}
                        onChange={e => {
                          setFormData(f => ({
                            ...f,
                            name: e.target.value,
                            sku: slugify(e.target.value),
                          }));
                        }}
                        required
                        error={!!errors.name}
                        helperText={errors.name}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 3 }}>
                      <TextField
                        label='Slug'
                        name='sku'
                        fullWidth
                        value={formData.sku}
                        onChange={e =>
                          setFormData(f => ({
                            ...f,
                            sku: slugify(e.target.value),
                          }))
                        }
                        required
                        error={!!errors.sku}
                        helperText={errors.sku || 'URL-friendly unique ID'}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 6 }}>
                      <TextField
                        label='Description'
                        name='description'
                        multiline
                        rows={3}
                        fullWidth
                        value={formData.description}
                        onChange={e =>
                          setFormData(f => ({
                            ...f,
                            description: e.target.value,
                          }))
                        }
                        required
                        error={!!errors.description}
                        helperText={errors.description}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* PRICING */}
            <Grid item size={{ xs: 12, md: 6 }}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <AttachMoneyIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Pricing
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    <Grid item size={{ xs: 12, md: 6 }}>
                      <TextField
                        label='Original Price (MRP)'
                        name='price'
                        type='number'
                        fullWidth
                        value={formData.price}
                        onChange={e =>
                          setFormData(f => ({ ...f, price: e.target.value }))
                        }
                        required
                        error={!!errors.price}
                        helperText={errors.price}
                        InputProps={{
                          startAdornment: (
                            <Typography sx={{ mr: 1 }}>₹</Typography>
                          ),
                        }}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 6 }}>
                      <TextField
                        label='Sale Price'
                        name='comparePrice'
                        type='number'
                        fullWidth
                        value={formData.comparePrice}
                        onChange={e =>
                          setFormData(f => ({
                            ...f,
                            comparePrice: e.target.value,
                          }))
                        }
                        InputProps={{
                          startAdornment: (
                            <Typography sx={{ mr: 1 }}>₹</Typography>
                          ),
                        }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* INVENTORY */}
            <Grid item size={{ xs: 12, md: 6 }}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <InventoryIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Inventory
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    <Grid item size={{ xs: 12, md: 6 }}>
                      <TextField
                        label='Quantity'
                        name='quantity'
                        type='number'
                        fullWidth
                        value={formData.quantity}
                        onChange={e =>
                          setFormData(f => ({
                            ...f,
                            quantity: e.target.value,
                            inventoryStatus: syncInventoryStatus(
                              e.target.value
                            ),
                          }))
                        }
                        required
                        error={!!errors.quantity}
                        helperText={errors.quantity}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 6 }}>
                      <FormControl fullWidth>
                        <InputLabel id='inv-status-label'>
                          Inventory Status
                        </InputLabel>
                        <Select
                          labelId='inv-status-label'
                          name='inventoryStatus'
                          label='Inventory Status'
                          value={formData.inventoryStatus}
                          onChange={e =>
                            setFormData(f => ({
                              ...f,
                              inventoryStatus: e.target.value as string,
                            }))
                          }
                          MenuProps={{
                            PaperProps: { style: { zIndex: 1302 } },
                          }}
                        >
                          <MenuItem value='in_stock'>In Stock</MenuItem>
                          <MenuItem value='out_of_stock'>Out of Stock</MenuItem>
                        </Select>
                        <FormHelperText>
                          Auto-updates when quantity changes
                        </FormHelperText>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* ORGANIZATION */}
            <Grid item size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <CategoryIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Organization
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3}>
                    <Grid item size={{ xs: 12, md: 3 }}>
                      <MultiSelectDropdown
                        label='Categories'
                        multiple
                        value={productCategories}
                        options={categoryOptions}
                        onChange={val => setProductCategories(val)}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 3 }}>
                      <MultiSelectDropdown
                        label='Collection'
                        value={collection}
                        options={collectionOptions}
                        onChange={val => setCollection(val)}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 3 }}>
                      <MultiSelectDropdown
                        label='Tags'
                        multiple
                        value={productTags}
                        options={tagOptions}
                        onChange={val => setProductTags(val)}
                      />
                    </Grid>
                    <Grid item size={{ xs: 12, md: 3 }}>
                      <FormControl fullWidth>
                        <InputLabel id='status-label'>Status</InputLabel>
                        <Select
                          labelId='status-label'
                          value={formData.status}
                          label='Status'
                          onChange={e =>
                            setFormData(f => ({
                              ...f,
                              status: e.target.value as string,
                            }))
                          }
                        >
                          {statusOptions.map(o => (
                            <MenuItem key={o.value} value={o.value}>
                              {o.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* IMAGES */}
            {/* IMAGES UPLOAD/URL */}
            <Grid item size={12}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                    <ImageIcon color='primary' />
                    <Box>
                      <Typography variant='h6' fontWeight='bold'>
                        Product Images
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Upload a thumbnail and up to 20 product images
                      </Typography>
                    </Box>
                  </Stack>
                  <Divider sx={{ mb: 3 }} />
                  <Grid container spacing={3} display={'block'}>
                    {/* Product images uploader or URL */}
                    <Grid item xs={12} md={9}>
                      <Typography fontWeight={500} mb={1}>
                        Product Images
                      </Typography>
                      <Stack direction='row' spacing={2} mb={2}>
                        <Button
                          variant={
                            imgMode === 'upload' ? 'contained' : 'outlined'
                          }
                          onClick={() => setImgMode('upload')}
                          size='small'
                          sx={{ minWidth: 80 }}
                        >
                          Upload
                        </Button>
                        <Button
                          variant={imgMode === 'url' ? 'contained' : 'outlined'}
                          onClick={() => setImgMode('url')}
                          size='small'
                          sx={{ minWidth: 80 }}
                        >
                          Image URL
                        </Button>
                      </Stack>
                      {imgMode === 'upload' ? (
                        <Box
                          {...getImagesRootProps()}
                          sx={{
                            border: '2px dashed',
                            borderColor: isImagesDragActive
                              ? 'primary.main'
                              : 'grey.300',
                            borderRadius: 2,
                            p: 2,
                            textAlign: 'center',
                            mb: 2,
                            cursor:
                              images.length >= 20 ? 'not-allowed' : 'pointer',
                            background:
                              'linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%)',
                            transition: 'border-color 0.2s',
                            opacity: images.length >= 20 ? 0.5 : 1,
                            '&:hover': {
                              borderColor: 'primary.main',
                              bgcolor: 'primary.50',
                            },
                          }}
                        >
                          <input
                            {...getImagesInputProps()}
                            disabled={images.length >= 20}
                          />
                          <Stack
                            direction='row'
                            alignItems='center'
                            justifyContent='center'
                            spacing={1}
                          >
                            <CloudUploadIcon
                              sx={{ fontSize: 28, color: 'primary.main' }}
                            />
                            <Typography fontWeight={500} color='primary.main'>
                              Upload Images
                            </Typography>
                          </Stack>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mt: 0.5 }}
                          >
                            JPEG / PNG / WebP, max 5MB each.{' '}
                            {20 - images.length} left
                          </Typography>
                        </Box>
                      ) : (
                        <Box>
                          <TextField
                            label='Image URLs (comma-separated)'
                            multiline
                            minRows={2}
                            value={imgUrls.join(',')}
                            onChange={e =>
                              setImgUrls(
                                e.target.value
                                  .split(',')
                                  .map(s => s.trim())
                                  .filter(Boolean)
                              )
                            }
                            fullWidth
                            placeholder='https://img1.jpg,https://img2.jpg'
                          />
                          <Stack direction='row' spacing={1} mt={1}>
                            {imgUrls.map((u, i) => (
                              <Box
                                key={u}
                                sx={{
                                  position: 'relative',
                                  width: 64,
                                  height: 64,
                                  border: '1px solid #ccc',
                                  borderRadius: 1,
                                  overflow: 'hidden',
                                  mr: 1,
                                }}
                              >
                                <img
                                  src={u}
                                  alt=''
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                  }}
                                />
                                <IconButton
                                  size='small'
                                  sx={{
                                    position: 'absolute',
                                    top: 0,
                                    right: 0,
                                  }}
                                  onClick={() =>
                                    setImgUrls(urls =>
                                      urls.filter((_, ii) => ii !== i)
                                    )
                                  }
                                >
                                  <DeleteIcon fontSize='small' />
                                </IconButton>
                              </Box>
                            ))}
                          </Stack>
                        </Box>
                      )}
                      {/* Image preview for files */}
                      {imgMode === 'upload' && imagePreviewUrls.length > 0 && (
                        <Grid container spacing={1} sx={{ mt: 1 }}>
                          {imagePreviewUrls.map((url, i) => (
                            <Grid item xs={6} sm={3} md={2} key={url}>
                              <Box
                                sx={{
                                  position: 'relative',
                                  width: '100%',
                                  height: 120,
                                  borderRadius: 2,
                                  overflow: 'hidden',
                                  border: '1.5px solid',
                                  borderColor: 'grey.300',
                                  background: '#f8fafc',
                                }}
                              >
                                <img
                                  src={url}
                                  alt={`img-${i}`}
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'contain',
                                    background: '#fff',
                                  }}
                                />
                                <IconButton
                                  size='small'
                                  onClick={() => handleImageRemove(i)}
                                  sx={{
                                    position: 'absolute',
                                    top: 4,
                                    right: 4,
                                    bgcolor: 'rgba(0,0,0,0.5)',
                                    color: '#fff',
                                    '&:hover': { bgcolor: 'rgba(0,0,0,0.8)' },
                                    zIndex: 2,
                                  }}
                                >
                                  <DeleteIcon fontSize='small' />
                                </IconButton>
                              </Box>
                            </Grid>
                          ))}
                        </Grid>
                      )}
                    </Grid>
                    {/* Thumbnail uploader and preview beside images */}
                    <Grid item xs={12} md={3}>
                      <Typography fontWeight={500} mb={1}>
                        Thumbnail
                      </Typography>
                      <Stack direction='row' spacing={2} mb={2}>
                        <Button
                          variant={
                            thumbnailMode === 'upload'
                              ? 'contained'
                              : 'outlined'
                          }
                          onClick={() => setThumbnailMode('upload')}
                          size='small'
                          sx={{ minWidth: 80 }}
                        >
                          Upload
                        </Button>
                        <Button
                          variant={
                            thumbnailMode === 'url' ? 'contained' : 'outlined'
                          }
                          onClick={() => setThumbnailMode('url')}
                          size='small'
                          sx={{ minWidth: 80 }}
                        >
                          Image URL
                        </Button>
                      </Stack>
                      {thumbnailMode === 'upload' ? (
                        <Box
                          {...getThumbRootProps()}
                          sx={{
                            border: '2px dashed',
                            borderColor: isThumbDragActive
                              ? 'primary.main'
                              : 'grey.300',
                            borderRadius: 2,
                            p: 2,
                            textAlign: 'center',
                            mb: 2,
                            cursor: 'pointer',
                            background:
                              'linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%)',
                            transition: 'border-color 0.2s',
                            '&:hover': {
                              borderColor: 'primary.main',
                              bgcolor: 'primary.50',
                            },
                          }}
                        >
                          <input {...getThumbInputProps()} />
                          <Stack
                            direction='row'
                            alignItems='center'
                            justifyContent='center'
                            spacing={1}
                          >
                            <CloudUploadIcon
                              sx={{ fontSize: 28, color: 'primary.main' }}
                            />
                            <Typography fontWeight={500} color='primary.main'>
                              Upload Thumbnail
                            </Typography>
                          </Stack>
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{ mt: 0.5 }}
                          >
                            JPEG / PNG / WebP, max 5MB
                          </Typography>
                        </Box>
                      ) : (
                        <Box>
                          <TextField
                            label='Thumbnail URL'
                            value={thumbnailUrl}
                            onChange={e => setThumbnailUrl(e.target.value)}
                            fullWidth
                          />
                          {thumbnailUrl && (
                            <Box
                              sx={{
                                mt: 1,
                                position: 'relative',
                                width: 80,
                                height: 80,
                                border: '1px solid #ccc',
                                borderRadius: 1,
                                overflow: 'hidden',
                              }}
                            >
                              <img
                                src={thumbnailUrl}
                                alt='thumb'
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover',
                                }}
                              />
                              <IconButton
                                size='small'
                                sx={{ position: 'absolute', top: 0, right: 0 }}
                                onClick={() => setThumbnailUrl('')}
                              >
                                <DeleteIcon fontSize='small' />
                              </IconButton>
                            </Box>
                          )}
                        </Box>
                      )}
                      {/* Image preview for file */}
                      {thumbnailMode === 'upload' && thumbnailPreview && (
                        <Box
                          sx={{
                            mt: 1,
                            position: 'relative',
                            width: 80,
                            height: 80,
                            border: '1px solid #ccc',
                            borderRadius: 1,
                            overflow: 'hidden',
                          }}
                        >
                          <img
                            src={thumbnailPreview}
                            alt='thumb'
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                          />
                          <IconButton
                            size='small'
                            sx={{ position: 'absolute', top: 0, right: 0 }}
                            onClick={handleThumbnailRemove}
                          >
                            <DeleteIcon fontSize='small' />
                          </IconButton>
                        </Box>
                      )}
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* RICH TEXT: Overview */}
            <Grid item size={12}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' mb={1}>
                    <Typography variant='h6' fontWeight='bold' flexGrow={1}>
                      Product Overview
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 2 }} />
                  <RichTextEditor
                    value={overview}
                    onChange={setOverview}
                    placeholder='Enter product overview...'
                    minHeight={150}
                  />
                  {errors.overview && (
                    <FormHelperText error>{errors.overview}</FormHelperText>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* RICH TEXT: Product Features */}
            <Grid item size={12}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' mb={1}>
                    <Typography variant='h6' fontWeight='bold' flexGrow={1}>
                      Product Features
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 2 }} />
                  <RichTextEditor
                    value={features}
                    onChange={setFeatures}
                    placeholder='Enter product features...'
                    minHeight={120}
                  />
                </CardContent>
              </Card>
            </Grid>

            {/* RICH TEXT: Product Specification */}
            <Grid item size={12}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction='row' alignItems='center' mb={1}>
                    <Typography variant='h6' fontWeight='bold' flexGrow={1}>
                      Product Specification
                    </Typography>
                  </Stack>
                  <Divider sx={{ mb: 2 }} />
                  <RichTextEditor
                    value={specification}
                    onChange={setSpecification}
                    placeholder='Enter product specifications...'
                    minHeight={120}
                  />
                  {errors.specification && (
                    <FormHelperText error>
                      {errors.specification}
                    </FormHelperText>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* VARIANTS */}
            <Grid item size={12}>
              <ProductVariantsEditor
                variants={variants}
                onChange={setVariants}
              />
            </Grid>
          </Grid>
        </Box>
        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        >
          <Alert
            onClose={() => setSnackbar(s => ({ ...s, open: false }))}
            severity={snackbar.severity}
            variant='filled'
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
};

const NewProductPage = () => (
  <Suspense fallback={<ProductFormSkeleton />}>
    <NewProductContent />
  </Suspense>
);

export default NewProductPage;
