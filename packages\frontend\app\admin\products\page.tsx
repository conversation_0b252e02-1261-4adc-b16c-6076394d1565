'use client';

import React, {
  useState,
  useEffect,
  Suspense,
  useMemo,
  useCallback,
} from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import BulkUploadDialog from '@/components/admin/BulkUploadDialog';
import {
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  BuildingStorefrontIcon,
  ExclamationTriangleIcon,
  CloudArrowUpIcon,
} from '@heroicons/react/24/outline';
import { AdminTableWrapper } from '@/components/admin/AdminSuspenseWrapper';
import {
  useLoading,
  useAsyncOperation,
  LoadingProvider,
} from '@/contexts/LoadingContext';
import { ProductsTableSkeleton } from '@/components/skeletons/TableSkeleton';
import {
  ContentLoader,
  FastLoadingSkeleton,
} from '@/components/skeletons/SkeletonBase';
import { getProducts } from '@/lib/strapi-api';
import { STRAPI_CONFIG } from '@/config/strapi';
import { useAdminTenant, useAdminTenantListener } from '@/hooks/useAdminTenant';
import { multiTenantAPI, TenantProduct } from '@/lib/api/multi-tenant';
import { Button, Typography, Stack } from '@mui/material';
// import {
//   Warning as WarningIcon,
//   Delete as DeleteIcon,
//   Cancel as CancelIcon,
// } from '@mui/icons-material';
import { useMedusaBackendProducts } from '@/hooks/useMedusaAdminBackend';
import ConfirmDialog from '@/components/ConfirmDialog';
import { useToast } from '@/components/common/ToastProvider';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';

// Types for admin product display (extends TenantProduct)
type AdminProduct = TenantProduct & {
  _original: TenantProduct;
};

// Products content component
const ProductsContent = () => {
  const toast = useToast();
  const router = useRouter();
  const { selectedTenant, tenantId } = useAdminTenant();
  const { showLoading, hideLoading } = useLoadingBackdrop();
  const { startPageTransition, finishPageTransition } = useLoading();

  // Consolidated loading states
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productStats, setProductStats] = useState({
    total: 0,
    published: 0,
    draft: 0,
    outOfStock: 0,
  });
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    product: TenantProduct | null;
  }>({
    isOpen: false,
    product: null,
  });

  // Bulk upload dialog state
  const [isBulkUploadDialogOpen, setIsBulkUploadDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const {
    products,
    loading: fetchingProducts,
    error: fetchingError,
    fetchProducts: fetchAllProducts,
    deleteProduct,
  } = useMedusaBackendProducts();

  // Consolidated loading state
  const isLoading = isInitialLoading || fetchingProducts;

  // Optimized fetch with progressive loading
  const fetchProducts = useCallback(
    async (currentTenantId?: string) => {
      try {
        startPageTransition('Loading products...');
        showLoading();
        // Start with smaller batch for faster initial render
        await fetchAllProducts({
          limit: 50,
          offset: 0,
          sort: 'created_at:desc',
        });

        // Calculate stats from loaded products
        if (products.length > 0) {
          const stats = {
            total: products.length,
            published: products.filter(p => p.status === 'published').length,
            draft: products.filter(p => p.status === 'draft').length,
            outOfStock: products.filter(
              p =>
                p.variants?.some((v: any) => (v.inventory_quantity || 0) <= 0)
            ).length,
          };
          setProductStats(stats);
        }

        finishPageTransition();
      } catch (err) {
        console.error('Failed to fetch tenant products:', err);
        setError(
          `Failed to load products for ${
            selectedTenant?.name || 'selected tenant'
          }. Please try again later.`
        );
        setProductStats({ total: 0, published: 0, draft: 0, outOfStock: 0 });
        finishPageTransition();
        hideLoading();
      } finally {
        setIsInitialLoading(false);
        hideLoading();
      }
    },
    [
      fetchAllProducts,
      products,
      selectedTenant,
      startPageTransition,
      finishPageTransition,
    ]
  );

  // Initial load when component mounts or tenant changes
  useEffect(() => {
    fetchProducts();
  }, []);

  // const handleView = (product: TenantProduct) => {
  //   showLoading();

  //   router.push(`/admin/products/${product.id}`);
  // };

  const handleEdit = (product: TenantProduct) => {
    showLoading();
    router.push(`/admin/products/${product.id}/edit`);
  };

  const handleDelete = (product: TenantProduct) => {
    setDeleteConfirmation({
      isOpen: true,
      product: product,
    });
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation.product) return;

    setIsDeleting(true);
    try {
      // Call delete API with proper string ID
      await deleteProduct(deleteConfirmation.product.id.toString());

      // Show success toast
      toast.success(
        `Product "${
          deleteConfirmation.product.name || deleteConfirmation.product.id
        }" deleted successfully.`
      );

      // Refresh products list
      await fetchProducts();
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product. Please try again.');
    } finally {
      setIsDeleting(false);
      setDeleteConfirmation({ isOpen: false, product: null });
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmation({ isOpen: false, product: null });
  };

  const formatPrice = (price: number) =>
    new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);

  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      published: { color: 'bg-green-100 text-green-800', label: 'Published' },
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
      'Out of Stock': {
        color: 'bg-red-100 text-red-800',
        label: 'Out of Stock',
      },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const [isNavigating, setIsNavigating] = useState(false);
  const handleAddProduct = () => {
    setIsNavigating(true);
    showLoading();

    router.push('/admin/products/new');
  };

  const handleBulkUpload = () => {
    setIsBulkUploadDialogOpen(true);
  };

  const handleBulkUploadClose = () => {
    setIsBulkUploadDialogOpen(false);
  };

  const handleBulkUploadSuccess = () => {
    // Refresh products list after successful upload
    fetchProducts();
  };

  const columns = [
    {
      key: 'name',
      label: 'Product',
      sortable: true,
      render: (value: string, row: any) => (
        <div>
          <div className='text-sm font-medium text-gray-900'>{value}</div>
          <div className='text-sm text-gray-500'>{row.slug || 'No SKU'}</div>
        </div>
      ),
    },

    {
      key: 'categories',
      label: 'categories',
      sortable: true,
      render: (value: string[]) => (
        <div className='flex flex-wrap gap-1'>
          {value && value.length > 0 ? (
            value.map((item, index) => (
              <span
                key={index}
                className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
              >
                {item}
              </span>
            ))
          ) : (
            <span className='text-gray-500 text-xs'>-</span>
          )}
        </div>
      ),
    },
    {
      key: 'price',
      label: 'Price',
      sortable: true,
      render: (_: any, row: any) => {
        const price = row.original_price;
        const salePrice = row.sale_price;
        return (
          <div className='space-y-1'>
            {salePrice && salePrice < price ? (
              <>
                <div className='text-sm font-medium text-gray-900'>
                  {formatPrice(salePrice)}
                </div>
                <div className='text-xs text-gray-500 line-through'>
                  {formatPrice(price)}
                </div>
              </>
            ) : (
              <div className='text-sm font-medium text-gray-900'>
                {formatPrice(price)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: 'inventory_quantity',
      label: 'Stock',
      sortable: true,
      render: (value: number) => (
        <span
          className={`text-sm font-medium ${
            value <= 10 ? 'text-red-600' : 'text-gray-900'
          }`}
        >
          {value}
        </span>
      ),
    },
    {
      key: 'collection',
      label: 'collection',
      sortable: true,
      render: (value: string) => (
        <div>
          <span
            className={
              value
                ? 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
                : 'text-gray-500'
            }
          >
            {value || '-'}
          </span>
        </div>
      ),
    },
    {
      key: 'product_status',
      label: 'Status',
      sortable: true,
      render: (value: string) => getStatusBadge(value),
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{formatDate(value)}</span>
      ),
    },
  ];

  const transformedProducts = useMemo(() => {
    if (!Array.isArray(products)) return [];

    return (
      products
        .map(product => {
          const prices = product?.metadata?.additional_data?.product_prices;
          const firstPrice =
            Array.isArray(prices) && prices.length > 0 ? prices[0] : {};

          return {
            id: product.id,
            documentId: product.id,
            name: product?.title || 'No Name',
            slug: product?.handle || 'N/A',
            inventory_quantity:
              product?.metadata?.additional_data?.product_quantity || 0,
            original_price: firstPrice?.original_price || 0,
            sale_price: firstPrice?.discounted_price || 0,
            product_status: product?.status || 'Draft',
            collection: product?.collection?.title || false,
            categories: Array.isArray(product?.categories)
              ? product.categories.map((category: any) => category.name)
              : ['Uncategorized'],
            createdAt: product?.created_at || new Date().toISOString(),
            updatedAt: product?.updated_at || new Date().toISOString(),
            _original: product,
          };
        })
        .filter(Boolean)
        // .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        .sort(
          (a, b) =>
            new Date(String(b.createdAt)).getTime() -
            new Date(String(a.createdAt)).getTime()
        )
    );
  }, [products]);

  const breadcrumbs = [{ label: 'Products', active: true }];

  // Tenant-aware page title and description
  const pageTitle = selectedTenant
    ? `Products - ${selectedTenant.name}`
    : 'Products';

  const pageDescription = selectedTenant
    ? `Manage products for ${selectedTenant.name} (${productStats.total} total, ${productStats.published} published)`
    : 'Select a tenant to manage products';

  return (
    <div className='space-y-6'>
      <PageHeader
        title={pageTitle}
        description={pageDescription}
        breadcrumbs={breadcrumbs}
        actions={
          <div className='flex space-x-3'>
            <button
              onClick={handleBulkUpload}
              className='inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 relative'
              disabled={isNavigating}
            >
              <CloudArrowUpIcon
                className='-ml-1 mr-2 h-5 w-5'
                aria-hidden='true'
              />
              {'Bulk Upload'}
            </button>
            <button
              onClick={handleAddProduct}
              className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 relative'
              disabled={isNavigating}
            >
              <PlusIcon className='-ml-1 mr-2 h-5 w-5' aria-hidden='true' />
              {'Add Product'}
            </button>
          </div>
        }
      />

      {/* Error State */}
      {error && (
        <div className='bg-red-50 border border-red-200 rounded-md p-4'>
          <div className='flex'>
            <div className='ml-3'>
              <h3 className='text-sm font-medium text-red-800'>
                Error loading products
              </h3>
              <div className='mt-2 text-sm text-red-700'>
                <p>{error}</p>
              </div>
              <div className='mt-4'>
                <button
                  type='button'
                  className='bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200'
                  onClick={() => fetchProducts()}
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Products Table */}
      <ContentLoader isLoading={isLoading} message={`Loading products...`}>
        <DataTable
          columns={columns}
          data={transformedProducts}
          loading={isLoading}
          searchable
          filterable
          pagination
          pageSize={10}
          // onView={row => handleView(row._original)}
          onEdit={row => handleEdit(row._original)}
          onDelete={row => handleDelete(row._original)}
          emptyMessage={
            'No products found. Create your first product to get started.'
          }
        />
      </ContentLoader>

      <ConfirmDialog
        open={deleteConfirmation.isOpen}
        onClose={!isDeleting ? cancelDelete : undefined}
        header={
          <Stack direction='row' alignItems='center' gap={1}>
            <ExclamationTriangleIcon className='h-5 w-5 text-amber-500' />
            <Typography variant='h6'>Delete Product</Typography>
          </Stack>
        }
        body={
          <Typography>
            Are you sure you want to delete{' '}
            <strong>{deleteConfirmation.product?.title}</strong>? This action
            cannot be undone.
          </Typography>
        }
        actions={
          <>
            <Button onClick={cancelDelete} disabled={isDeleting}>
              Cancel
            </Button>
            <Button
              color='error'
              variant='contained'
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting…' : 'Delete'}
            </Button>
          </>
        }
      />

      {/* Bulk Upload Dialog */}
      <BulkUploadDialog
        isOpen={isBulkUploadDialogOpen}
        onClose={handleBulkUploadClose}
        onUploadSuccess={handleBulkUploadSuccess}
      />
    </div>
  );
};

// Optimized products page with immediate loading indicators
const ProductsPage = () => {
  return (
    <LoadingProvider>
      <Suspense fallback={<ProductsTableSkeleton />}>
        <ProductsContent />
      </Suspense>
    </LoadingProvider>
  );
};

export default ProductsPage;
