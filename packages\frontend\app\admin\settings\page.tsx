'use client';

import React, { useState, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormControlLabel,
  Checkbox,
  Button,
  Stack,
  Divider,
  Alert,
  Snackbar,
  CircularProgress,
  Grid,
  Paper,
  Chip,
} from '@mui/material';
import {
  Save as SaveIcon,
  Check as CheckIcon,
  Settings as SettingsIcon,
  Store as StoreIcon,
  Language as LanguageIcon,
  LocalShipping as ShippingIcon,
  Email as EmailIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import PageHeader, { ActionButton } from '@/components/admin/PageHeader';
import { useSettingsFormValidation } from '@/hooks/useFormValidation';
import { FIELD_VALIDATION_RULES } from '@/lib/validation';

// Enhanced TypeScript interfaces for settings
interface StoreSettings {
  // General Settings
  storeName: string;
  storeDescription: string;
  storeEmail: string;
  storePhone: string;
  storeAddress: string;
  storeCity: string;
  storeState: string;
  storeCountry: string;
  storeZipCode: string;

  // Currency & Localization
  currency: string;
  timezone: string;
  dateFormat: string;

  // Shipping Settings
  freeShippingThreshold: string;
  shippingRate: string;
  shippingZones: string[];

  // Email Settings
  emailNotifications: boolean;
  orderConfirmationEmail: boolean;
  shippingNotificationEmail: boolean;

  // Security Settings
  twoFactorAuth: boolean;
  sessionTimeout: string;
  passwordPolicy: string;
}

interface SettingsFormErrors {
  [key: string]: string;
}

interface SettingsValidationRules {
  [key: string]: any;
}

const initialSettings: StoreSettings = {
  storeName: 'ONDC Seller Store',
  storeDescription: 'Your trusted ONDC marketplace seller',
  storeEmail: '<EMAIL>',
  storePhone: '+91 98765 43210',
  storeAddress: '123 Business Street',
  storeCity: 'Mumbai',
  storeState: 'Maharashtra',
  storeCountry: 'India',
  storeZipCode: '400001',
  currency: 'INR',
  timezone: 'Asia/Kolkata',
  dateFormat: 'DD/MM/YYYY',
  freeShippingThreshold: '50000',
  shippingRate: '5000',
  shippingZones: ['Mumbai', 'Delhi', 'Bangalore'],
  emailNotifications: true,
  orderConfirmationEmail: true,
  shippingNotificationEmail: true,
  twoFactorAuth: false,
  sessionTimeout: '30',
  passwordPolicy: 'medium',
};

const currencyOptions = [
  { value: 'INR', label: 'Indian Rupee (₹)' },
  { value: 'USD', label: 'US Dollar ($)' },
  { value: 'EUR', label: 'Euro (€)' },
  { value: 'GBP', label: 'British Pound (£)' },
];

const timezoneOptions = [
  { value: 'Asia/Kolkata', label: 'Asia/Kolkata (IST)' },
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'America/New_York (EST)' },
  { value: 'Europe/London', label: 'Europe/London (GMT)' },
];

const dateFormatOptions = [
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
];

const passwordPolicyOptions = [
  { value: 'low', label: 'Low (6+ characters)' },
  { value: 'medium', label: 'Medium (8+ chars, mixed case)' },
  { value: 'high', label: 'High (12+ chars, symbols)' },
];

export default function SettingsPage() {
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [submitError, setSubmitError] = useState<string>('');

  // Enhanced form validation with comprehensive error handling
  const {
    data: formData,
    errors,
    touched,
    isValid,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldError,
    setErrors,
    resetForm,
    validateField,
    validateForm,
    clearErrors,
  } = useSettingsFormValidation({
    initialData: initialSettings,
    onSubmit: async (data: Record<string, any>) => {
      try {
        setSubmitError('');
        console.log('Saving settings:', data);

        // Simulate API call with proper error handling
        await new Promise((resolve, reject) => {
          setTimeout(() => {
            // Simulate random API failures for testing
            if (Math.random() > 0.8) {
              reject(new Error('Network error: Unable to save settings'));
            } else {
              resolve(data);
            }
          }, 1500);
        });

        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 5000);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to save settings';
        setSubmitError(errorMessage);
        console.error('Settings save error:', error);
        throw error; // Re-throw to be handled by the form validation hook
      }
    },
    onError: (formErrors: SettingsFormErrors) => {
      console.error('Settings form validation errors:', formErrors);
    },
  });

  // Handle Select component changes (Material-UI specific)
  const handleSelectChange = useCallback(
    (name: string) => (event: any) => {
      handleChange({
        target: {
          name,
          value: event.target.value,
        },
      } as React.ChangeEvent<HTMLInputElement>);
    },
    [handleChange]
  );

  // Handle checkbox changes
  const handleCheckboxChange = useCallback(
    (name: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
      const syntheticEvent = {
        target: {
          name,
          value: event.target.checked,
          type: 'checkbox',
          checked: event.target.checked,
        },
        preventDefault: () => {},
        stopPropagation: () => {},
      } as any;
      handleChange(syntheticEvent);
    },
    [handleChange]
  );

  const breadcrumbs = [{ label: 'Settings', active: true }];

  return (
    <Box sx={{ p: 3 }}>
      <PageHeader
        title='Settings'
        description='Configure your store settings and preferences'
        breadcrumbs={breadcrumbs}
        actions={
          <Button
            variant='contained'
            startIcon={
              isSubmitting ? (
                <CircularProgress size={20} color='inherit' />
              ) : showSuccessMessage ? (
                <CheckIcon />
              ) : (
                <SaveIcon />
              )
            }
            onClick={handleSubmit}
            disabled={isSubmitting || !isValid}
            color={showSuccessMessage ? 'success' : 'primary'}
            sx={{ minWidth: 140 }}
          >
            {isSubmitting
              ? 'Saving...'
              : showSuccessMessage
                ? 'Saved!'
                : 'Save Settings'}
          </Button>
        }
      />

      {/* Success Message */}
      <Snackbar
        open={showSuccessMessage}
        autoHideDuration={5000}
        onClose={() => setShowSuccessMessage(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setShowSuccessMessage(false)}
          severity='success'
          sx={{ width: '100%' }}
        >
          Settings saved successfully!
        </Alert>
      </Snackbar>

      {/* Error Message */}
      {submitError && (
        <Alert
          severity='error'
          sx={{ mb: 3 }}
          onClose={() => setSubmitError('')}
        >
          {submitError}
        </Alert>
      )}

      {/* Form Validation Errors */}
      {Object.keys(errors).length > 0 && (
        <Alert severity='warning' sx={{ mb: 3 }}>
          <Typography variant='body2' fontWeight='medium'>
            Please fix the following errors:
          </Typography>
          <Box component='ul' sx={{ mt: 1, pl: 2 }}>
            {Object.entries(errors).map(([field, error]) => (
              <Typography component='li' key={field} variant='body2'>
                {error}
              </Typography>
            ))}
          </Box>
        </Alert>
      )}

      <Box component='form' onSubmit={handleSubmit} sx={{ maxWidth: 1200 }}>
        <Stack spacing={4}>
          {/* General Settings */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 4 }}>
              <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                <StoreIcon color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    General Settings
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Basic store information and contact details
                  </Typography>
                </Box>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Stack spacing={3}>
                <Typography
                  variant='subtitle1'
                  fontWeight='medium'
                  color='text.primary'
                >
                  Store Information
                </Typography>

                <Box
                  sx={{
                    display: 'flex',
                    gap: 3,
                    flexDirection: { xs: 'column', md: 'row' },
                  }}
                >
                  <Box sx={{ flex: 1 }}>
                    <TextField
                      fullWidth
                      label='Store Name'
                      name='storeName'
                      value={formData.storeName || ''}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      required
                      error={Boolean(errors.storeName && touched.storeName)}
                      helperText={
                        (errors.storeName &&
                          touched.storeName &&
                          errors.storeName) ||
                        'The name of your store as it appears to customers'
                      }
                      disabled={isSubmitting}
                      variant='outlined'
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <TextField
                      fullWidth
                      label='Store Email'
                      name='storeEmail'
                      type='email'
                      value={formData.storeEmail || ''}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      required
                      error={Boolean(errors.storeEmail && touched.storeEmail)}
                      helperText={
                        (errors.storeEmail &&
                          touched.storeEmail &&
                          errors.storeEmail) ||
                        'Primary contact email for your store'
                      }
                      disabled={isSubmitting}
                      variant='outlined'
                    />
                  </Box>
                </Box>

                <TextField
                  fullWidth
                  label='Store Description'
                  name='storeDescription'
                  value={formData.storeDescription || ''}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  multiline
                  rows={3}
                  error={Boolean(
                    errors.storeDescription && touched.storeDescription
                  )}
                  helperText={
                    (errors.storeDescription &&
                      touched.storeDescription &&
                      errors.storeDescription) ||
                    'Brief description of your store and what you sell'
                  }
                  disabled={isSubmitting}
                  variant='outlined'
                />

                <Box
                  sx={{
                    display: 'flex',
                    gap: 3,
                    flexDirection: { xs: 'column', md: 'row' },
                  }}
                >
                  <Box sx={{ flex: 1 }}>
                    <TextField
                      fullWidth
                      label='Phone Number'
                      name='storePhone'
                      value={formData.storePhone || ''}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(errors.storePhone && touched.storePhone)}
                      helperText={
                        (errors.storePhone &&
                          touched.storePhone &&
                          errors.storePhone) ||
                        'Contact phone number for customer support'
                      }
                      disabled={isSubmitting}
                      variant='outlined'
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <TextField
                      fullWidth
                      label='Address'
                      name='storeAddress'
                      value={formData.storeAddress || ''}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(
                        errors.storeAddress && touched.storeAddress
                      )}
                      helperText={
                        (errors.storeAddress &&
                          touched.storeAddress &&
                          errors.storeAddress) ||
                        'Physical address of your store'
                      }
                      disabled={isSubmitting}
                      variant='outlined'
                    />
                  </Box>
                </Box>

                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: {
                      xs: '1fr',
                      sm: '1fr 1fr',
                      md: '1fr 1fr 1fr 1fr',
                    },
                    gap: 3,
                  }}
                >
                  <TextField
                    fullWidth
                    label='City'
                    name='storeCity'
                    value={formData.storeCity || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(errors.storeCity && touched.storeCity)}
                    helperText={
                      errors.storeCity && touched.storeCity && errors.storeCity
                    }
                    disabled={isSubmitting}
                    variant='outlined'
                  />
                  <TextField
                    fullWidth
                    label='State'
                    name='storeState'
                    value={formData.storeState || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(errors.storeState && touched.storeState)}
                    helperText={
                      errors.storeState &&
                      touched.storeState &&
                      errors.storeState
                    }
                    disabled={isSubmitting}
                    variant='outlined'
                  />
                  <TextField
                    fullWidth
                    label='Country'
                    name='storeCountry'
                    value={formData.storeCountry || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(errors.storeCountry && touched.storeCountry)}
                    helperText={
                      errors.storeCountry &&
                      touched.storeCountry &&
                      errors.storeCountry
                    }
                    disabled={isSubmitting}
                    variant='outlined'
                  />
                  <TextField
                    fullWidth
                    label='ZIP Code'
                    name='storeZipCode'
                    value={formData.storeZipCode || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(errors.storeZipCode && touched.storeZipCode)}
                    helperText={
                      errors.storeZipCode &&
                      touched.storeZipCode &&
                      errors.storeZipCode
                    }
                    disabled={isSubmitting}
                    variant='outlined'
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>

          {/* Currency & Localization */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 4 }}>
              <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                <LanguageIcon color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    Currency & Localization
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Configure currency, timezone, and date formats
                  </Typography>
                </Box>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', md: '1fr 1fr 1fr' },
                  gap: 3,
                }}
              >
                <FormControl fullWidth disabled={isSubmitting}>
                  <InputLabel>Currency</InputLabel>
                  <Select
                    name='currency'
                    value={formData.currency || ''}
                    onChange={handleSelectChange('currency')}
                    onBlur={handleBlur}
                    label='Currency'
                    error={Boolean(errors.currency && touched.currency)}
                  >
                    {currencyOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.currency && touched.currency && (
                    <Typography
                      variant='caption'
                      color='error'
                      sx={{ mt: 0.5, ml: 1.5 }}
                    >
                      {errors.currency}
                    </Typography>
                  )}
                </FormControl>
                <FormControl fullWidth disabled={isSubmitting}>
                  <InputLabel>Timezone</InputLabel>
                  <Select
                    name='timezone'
                    value={formData.timezone || ''}
                    onChange={handleSelectChange('timezone')}
                    onBlur={handleBlur}
                    label='Timezone'
                    error={Boolean(errors.timezone && touched.timezone)}
                  >
                    {timezoneOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.timezone && touched.timezone && (
                    <Typography
                      variant='caption'
                      color='error'
                      sx={{ mt: 0.5, ml: 1.5 }}
                    >
                      {errors.timezone}
                    </Typography>
                  )}
                </FormControl>
                <FormControl fullWidth disabled={isSubmitting}>
                  <InputLabel>Date Format</InputLabel>
                  <Select
                    name='dateFormat'
                    value={formData.dateFormat || ''}
                    onChange={handleSelectChange('dateFormat')}
                    onBlur={handleBlur}
                    label='Date Format'
                    error={Boolean(errors.dateFormat && touched.dateFormat)}
                  >
                    {dateFormatOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.dateFormat && touched.dateFormat && (
                    <Typography
                      variant='caption'
                      color='error'
                      sx={{ mt: 0.5, ml: 1.5 }}
                    >
                      {errors.dateFormat}
                    </Typography>
                  )}
                </FormControl>
              </Box>
            </CardContent>
          </Card>

          {/* Shipping Settings */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 4 }}>
              <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                <ShippingIcon color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    Shipping Settings
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Configure shipping rates and policies
                  </Typography>
                </Box>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Box
                sx={{
                  display: 'flex',
                  gap: 3,
                  flexDirection: { xs: 'column', md: 'row' },
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <TextField
                    fullWidth
                    label='Free Shipping Threshold'
                    name='freeShippingThreshold'
                    type='number'
                    value={formData.freeShippingThreshold || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(
                      errors.freeShippingThreshold &&
                        touched.freeShippingThreshold
                    )}
                    helperText={
                      (errors.freeShippingThreshold &&
                        touched.freeShippingThreshold &&
                        errors.freeShippingThreshold) ||
                      'Minimum order amount for free shipping (in paise)'
                    }
                    disabled={isSubmitting}
                    variant='outlined'
                    InputProps={{
                      startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    }}
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <TextField
                    fullWidth
                    label='Standard Shipping Rate'
                    name='shippingRate'
                    type='number'
                    value={formData.shippingRate || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(errors.shippingRate && touched.shippingRate)}
                    helperText={
                      (errors.shippingRate &&
                        touched.shippingRate &&
                        errors.shippingRate) ||
                      'Default shipping cost (in paise)'
                    }
                    disabled={isSubmitting}
                    variant='outlined'
                    InputProps={{
                      startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    }}
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>

          {/* Email Settings */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 4 }}>
              <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                <EmailIcon color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    Email Notifications
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Configure email notification preferences
                  </Typography>
                </Box>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Stack spacing={2}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name='emailNotifications'
                      checked={Boolean(formData.emailNotifications)}
                      onChange={handleCheckboxChange('emailNotifications')}
                      disabled={isSubmitting}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant='body1'>
                        Enable Email Notifications
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Allow the system to send email notifications
                      </Typography>
                    </Box>
                  }
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      name='orderConfirmationEmail'
                      checked={Boolean(formData.orderConfirmationEmail)}
                      onChange={handleCheckboxChange('orderConfirmationEmail')}
                      disabled={isSubmitting}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant='body1'>
                        Send Order Confirmation Emails
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Automatically send confirmation emails when orders are
                        placed
                      </Typography>
                    </Box>
                  }
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      name='shippingNotificationEmail'
                      checked={Boolean(formData.shippingNotificationEmail)}
                      onChange={handleCheckboxChange(
                        'shippingNotificationEmail'
                      )}
                      disabled={isSubmitting}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant='body1'>
                        Send Shipping Notification Emails
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Notify customers when their orders are shipped
                      </Typography>
                    </Box>
                  }
                />
              </Stack>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 4 }}>
              <Stack direction='row' alignItems='center' spacing={2} mb={3}>
                <SecurityIcon color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    Security Settings
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Configure security and authentication settings
                  </Typography>
                </Box>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Stack spacing={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name='twoFactorAuth'
                      checked={Boolean(formData.twoFactorAuth)}
                      onChange={handleCheckboxChange('twoFactorAuth')}
                      disabled={isSubmitting}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant='body1'>
                        Enable Two-Factor Authentication
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        Require additional verification for admin login
                      </Typography>
                    </Box>
                  }
                />

                <Box
                  sx={{
                    display: 'flex',
                    gap: 3,
                    flexDirection: { xs: 'column', md: 'row' },
                  }}
                >
                  <Box sx={{ flex: 1 }}>
                    <TextField
                      fullWidth
                      label='Session Timeout (minutes)'
                      name='sessionTimeout'
                      type='number'
                      value={formData.sessionTimeout || ''}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(
                        errors.sessionTimeout && touched.sessionTimeout
                      )}
                      helperText={
                        (errors.sessionTimeout &&
                          touched.sessionTimeout &&
                          errors.sessionTimeout) ||
                        'Auto-logout after inactivity'
                      }
                      disabled={isSubmitting}
                      variant='outlined'
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <FormControl fullWidth disabled={isSubmitting}>
                      <InputLabel>Password Policy</InputLabel>
                      <Select
                        name='passwordPolicy'
                        value={formData.passwordPolicy || ''}
                        onChange={handleSelectChange('passwordPolicy')}
                        onBlur={handleBlur}
                        label='Password Policy'
                        error={Boolean(
                          errors.passwordPolicy && touched.passwordPolicy
                        )}
                      >
                        {passwordPolicyOptions.map(option => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.passwordPolicy && touched.passwordPolicy && (
                        <Typography
                          variant='caption'
                          color='error'
                          sx={{ mt: 0.5, ml: 1.5 }}
                        >
                          {errors.passwordPolicy}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Box>
    </Box>
  );
}
