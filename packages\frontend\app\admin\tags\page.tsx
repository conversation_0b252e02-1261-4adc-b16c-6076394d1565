'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Button,
  TextField,
  Typography,
  Stack,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  PlusIcon,
  PencilSquareIcon,
  TrashIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

import PageHeader from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import ConfirmDialog from '@/components/ConfirmDialog';
import { useMedusaBackendTags } from '@/hooks/useMedusaAdminBackend';
import { useToast } from '@/components/common/ToastProvider';

/* ------------------------------------------------------------------ */
/* ---------------------- local helpers ----------------------------- */
const fmt = (iso?: string) =>
  iso
    ? new Date(iso).toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
    : '—';

/* ------------------------------------------------------------------ */
/* --------------------------- component ---------------------------- */
export default function TagsPage() {
  const router = useRouter();
  const toast = useToast();

  const { tags, loading, fetchTags, createTag, updateTag, deleteTag } =
    useMedusaBackendTags(); // adjust path if needed

  /* ───────────────────────── fetch on mount ─────────────────────── */
  useEffect(() => {
    fetchTags();
  }, []);

  const rows = useMemo(
    () =>
      tags
        ?.map(t => ({
          id: t.id,
          name: t.value,
          createdAt: t.created_at,
          updatedAt: t.updated_at,
        }))
        .sort(
          (a, b) =>
            new Date(String(b.createdAt)).getTime() -
            new Date(String(a.createdAt)).getTime()
        ),
    [tags]
  );

  /* ----------------------------------------------------------------
     Snackbar
     ---------------------------------------------------------------- */
  const [snack, setSnack] = useState<{
    open: boolean;
    msg: string;
    sev: 'success' | 'error';
  }>({
    open: false,
    msg: '',
    sev: 'success',
  });

  /* ----------------------------------------------------------------
     Add / Edit dialog state
     ---------------------------------------------------------------- */
  type Mode = 'add' | 'edit';
  const [aeOpen, setAeOpen] = useState(false);
  const [aeMode, setAeMode] = useState<Mode>('add');
  const [aeId, setAeId] = useState<string | null>(null);
  const [aeValue, setAeValue] = useState('');
  const [aeError, setAeError] = useState('');
  const [aeLoading, setAeLoading] = useState(false);

  const openAdd = () => {
    setAeMode('add');
    setAeId(null);
    setAeValue('');
    setAeError('');
    setAeOpen(true);
  };
  const openEdit = (row: any) => {
    setAeMode('edit');
    setAeId(row.id);
    setAeValue(row.name);
    setAeError('');
    setAeOpen(true);
  };
  const closeAE = () => !aeLoading && setAeOpen(false);

  const saveAE = async () => {
    if (!aeValue.trim()) {
      setAeError('Tag name is required');
      return;
    }
    setAeLoading(true);
    try {
      if (aeMode === 'add') {
        await createTag({ value: aeValue });
        toast.success('Tag created successfully.');
      } else if (aeId) {
        await updateTag(aeId, { value: aeValue });
        toast.success('Tag updated successfully.');
      }
      await fetchTags();
      closeAE();
    } catch (err) {
      console.error(err);
      toast.error('Something went wrong!');
    } finally {
      setAeLoading(false);
    }
  };

  /* ----------------------------------------------------------------
     Delete dialog state
     ---------------------------------------------------------------- */
  const [delOpen, setDelOpen] = useState(false);
  const [delTarget, setDelTarget] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [delLoading, setDelLoading] = useState(false);

  const openDelete = (row: any) => {
    setDelTarget({ id: row.id, name: row.name });
    setDelOpen(true);
  };
  const closeDelete = () => !delLoading && setDelOpen(false);

  const confirmDelete = async () => {
    if (!delTarget) return;
    setDelLoading(true);
    try {
      await deleteTag(delTarget.id);
      await fetchTags();
      setSnack({ open: true, msg: 'Tag deleted', sev: 'success' });
    } catch (err) {
      console.error(err);
      setSnack({ open: true, msg: 'Delete failed', sev: 'error' });
    } finally {
      setDelLoading(false);
      closeDelete();
    }
  };

  /* ----------------------------------------------------------------
     DataTable column definitions (add Edit/Delete icons)
     ---------------------------------------------------------------- */
  const columns = [
    {
      key: 'name',
      label: 'Tag',
      sortable: true,
      render: (val: string) => (
        <span className='text-sm font-medium text-gray-900'>{val}</span>
      ),
    },
    {
      key: 'createdAt',
      label: 'Created',
      render: (v: string) => (
        <span className='text-sm text-gray-500'>{fmt(v)}</span>
      ),
    },
    {
      key: 'updatedAt',
      label: 'Updated',
      render: (v: string) => (
        <span className='text-sm text-gray-500'>{fmt(v)}</span>
      ),
    },
  ];

  /* ----------------------------------------------------------------
     Render
     ---------------------------------------------------------------- */
  return (
    <div className='space-y-6'>
      <PageHeader
        title='Tags'
        breadcrumbs={[{ label: 'Tags', active: true }]}
        actions={
          <Button
            variant='contained'
            startIcon={<PlusIcon className='h-5 w-5' />}
            onClick={openAdd}
          >
            Add Tag
          </Button>
        }
      />

      <DataTable
        columns={columns}
        data={rows}
        loading={loading}
        pageSize={10}
        searchable
        filterable
        pagination
        onEdit={openEdit}
        onDelete={openDelete}
        emptyMessage='No tags yet — create one to get started.'
      />

      {/* ================= Add/Edit dialog ================= */}
      <ConfirmDialog
        open={aeOpen}
        onClose={closeAE}
        header={
          <Typography>{aeMode === 'add' ? 'New tag' : 'Edit tag'}</Typography>
        }
        body={
          <TextField
            fullWidth
            label='Tag name'
            value={aeValue}
            onChange={e => {
              setAeValue(e.target.value);
              if (aeError) setAeError('');
            }}
            error={!!aeError}
            helperText={aeError}
            disabled={aeLoading}
          />
        }
        actions={
          <>
            <Button onClick={closeAE} disabled={aeLoading}>
              Cancel
            </Button>
            <Button
              variant='contained'
              onClick={saveAE}
              disabled={aeLoading}
              startIcon={
                aeMode === 'add' ? (
                  <PlusIcon className='h-4 w-4' />
                ) : (
                  <PencilSquareIcon className='h-4 w-4' />
                )
              }
            >
              {aeLoading ? 'Saving…' : aeMode === 'add' ? 'Create' : 'Save'}
            </Button>
          </>
        }
      />

      {/* ================= Delete dialog =================== */}
      <ConfirmDialog
        open={delOpen}
        onClose={closeDelete}
        header={
          <Stack direction='row' gap={1} alignItems='center'>
            <ExclamationTriangleIcon className='h-5 w-5 text-amber-500' />
            <Typography variant='h6'>Delete tag</Typography>
          </Stack>
        }
        body={
          <Typography>
            Are you sure you want to delete <strong>{delTarget?.name}</strong>?
            This action cannot be undone.
          </Typography>
        }
        actions={
          <>
            <Button onClick={closeDelete} disabled={delLoading}>
              Cancel
            </Button>
            <Button
              color='error'
              variant='contained'
              onClick={confirmDelete}
              disabled={delLoading}
            >
              {delLoading ? 'Deleting…' : 'Delete'}
            </Button>
          </>
        }
      />

      {/* ================= Snackbar ======================= */}
      <Snackbar
        open={snack.open}
        autoHideDuration={5000}
        onClose={() => setSnack(s => ({ ...s, open: false }))}
      >
        <Alert
          onClose={() => setSnack(s => ({ ...s, open: false }))}
          severity={snack.sev}
          variant='filled'
          sx={{ width: '100%' }}
        >
          {snack.msg}
        </Alert>
      </Snackbar>
    </div>
  );
}
