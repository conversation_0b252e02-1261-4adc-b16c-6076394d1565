import { type ComponentType } from 'react';

export interface StatCard {
  name: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: ComponentType<any>;
}

export interface RevenueTrendData {
  bucket: string;
  gross: number;
  net: number;
  orders: number;
}

export interface CustomerSplitData {
  [key: string]: number;
}

export interface RefundRateData {
  [key: string]: number;
}

export interface TopProductData {
  id: string;
  title: string;
  sales: number;
  revenue: number;
}

export interface RecentOrderData {
  id: string;
  customer: string;
  email: string;
  total: number;
  status: string;
  date: string;
}

export interface DashboardContentProps {
  stats: StatCard[];
  revenueTrend: RevenueTrendData[];
  customerSplit: CustomerSplitData;
  refundRate: RefundRateData;
  topProducts: TopProductData[];
  recentOrders: RecentOrderData[];
}
