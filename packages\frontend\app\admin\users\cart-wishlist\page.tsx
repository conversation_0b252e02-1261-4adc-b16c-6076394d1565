'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  ShoppingCartIcon,
  HeartIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

interface CartItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  price: number;
  quantity: number;
  addedAt: string;
  userId: string;
  userName: string;
  userEmail: string;
}

interface WishlistItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  price: number;
  addedAt: string;
  userId: string;
  userName: string;
  userEmail: string;
}

interface AbandonedCart {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  items: CartItem[];
  totalValue: number;
  lastActivity: string;
  daysSinceAbandoned: number;
}

interface CartAnalytics {
  totalCarts: number;
  totalValue: number;
  averageCartValue: number;
  abandonmentRate: string;
  conversionRate: string;
  topProducts: Array<{
    productId: string;
    productName: string;
    timesAdded: number;
    revenue: number;
  }>;
}

export default function CartWishlistManagementPage() {
  const [activeTab, setActiveTab] = useState<
    'carts' | 'wishlists' | 'abandoned' | 'analytics'
  >('carts');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('all');
  const [isLoading, setIsLoading] = useState(true);

  // Mock data
  const [cartItems] = useState<CartItem[]>([
    {
      id: 'cart-1',
      productId: 'prod-1',
      productName: 'Nike Air Max 270',
      productImage: '/images/products/placeholder.svg',
      price: 12999,
      quantity: 2,
      addedAt: '2024-01-15T10:30:00Z',
      userId: 'user-1',
      userName: 'John Doe',
      userEmail: '<EMAIL>',
    },
    {
      id: 'cart-2',
      productId: 'prod-2',
      productName: 'Adidas Ultraboost 22',
      productImage: '/images/products/placeholder.svg',
      price: 15999,
      quantity: 1,
      addedAt: '2024-01-15T09:15:00Z',
      userId: 'user-2',
      userName: 'Jane Smith',
      userEmail: '<EMAIL>',
    },
    {
      id: 'cart-3',
      productId: 'prod-3',
      productName: 'Puma RS-X',
      productImage: '/images/products/placeholder.svg',
      price: 8999,
      quantity: 1,
      addedAt: '2024-01-14T16:45:00Z',
      userId: 'user-3',
      userName: 'Mike Johnson',
      userEmail: '<EMAIL>',
    },
  ]);

  const [wishlistItems] = useState<WishlistItem[]>([
    {
      id: 'wish-1',
      productId: 'prod-4',
      productName: 'New Balance 990v5',
      productImage: '/images/products/placeholder.svg',
      price: 18999,
      addedAt: '2024-01-14T14:20:00Z',
      userId: 'user-1',
      userName: 'John Doe',
      userEmail: '<EMAIL>',
    },
    {
      id: 'wish-2',
      productId: 'prod-5',
      productName: 'Converse Chuck Taylor',
      productImage: '/images/products/placeholder.svg',
      price: 5999,
      addedAt: '2024-01-13T11:30:00Z',
      userId: 'user-4',
      userName: 'Sarah Wilson',
      userEmail: '<EMAIL>',
    },
  ]);

  const [abandonedCarts] = useState<AbandonedCart[]>([
    {
      id: 'abandoned-1',
      userId: 'user-5',
      userName: 'David Brown',
      userEmail: '<EMAIL>',
      items: [
        {
          id: 'cart-4',
          productId: 'prod-6',
          productName: 'Vans Old Skool',
          productImage: '/images/products/placeholder.svg',
          price: 6999,
          quantity: 2,
          addedAt: '2024-01-10T15:30:00Z',
          userId: 'user-5',
          userName: 'David Brown',
          userEmail: '<EMAIL>',
        },
      ],
      totalValue: 13998,
      lastActivity: '2024-01-10T15:30:00Z',
      daysSinceAbandoned: 5,
    },
  ]);

  const [analytics] = useState<CartAnalytics>({
    totalCarts: 156,
    totalValue: 2456789,
    averageCartValue: 15748,
    abandonmentRate: '68.5%',
    conversionRate: '31.5%',
    topProducts: [
      {
        productId: 'prod-1',
        productName: 'Nike Air Max 270',
        timesAdded: 45,
        revenue: 584955,
      },
      {
        productId: 'prod-2',
        productName: 'Adidas Ultraboost 22',
        timesAdded: 38,
        revenue: 607962,
      },
      {
        productId: 'prod-3',
        productName: 'Puma RS-X',
        timesAdded: 32,
        revenue: 287968,
      },
    ],
  });

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const filteredCartItems = cartItems.filter(
    item =>
      item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.userEmail.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredWishlistItems = wishlistItems.filter(
    item =>
      item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.userEmail.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAbandonedCarts = abandonedCarts.filter(
    cart =>
      cart.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cart.userEmail.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleExportData = () => {
    const data = {
      carts: cartItems,
      wishlists: wishlistItems,
      abandonedCarts: abandonedCarts,
      analytics: analytics,
      exportDate: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cart-wishlist-data-${
      new Date().toISOString().split('T')[0]
    }.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className='space-y-6 px-4 sm:px-6 md:px-8'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
          <div className='h-10 bg-gray-200 rounded w-full mb-6'></div>
          <div className='space-y-4'>
            {[...Array(5)].map((_, i) => (
              <div key={i} className='h-16 bg-gray-200 rounded'></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Page Header */}
      <div className='md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8'>
        <div className='flex-1 min-w-0'>
          <h2 className='text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate'>
            Cart & Wishlist Management
          </h2>
          <p className='mt-1 text-sm text-gray-500'>
            Monitor user shopping behavior and cart analytics
          </p>
        </div>
        <div className='mt-4 flex space-x-3 md:mt-0 md:ml-4'>
          <button
            onClick={handleExportData}
            className='inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            <ArrowDownTrayIcon className='-ml-1 mr-2 h-5 w-5' />
            Export Data
          </button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className='px-4 sm:px-6 md:px-8'>
        <div className='flex flex-col sm:flex-row gap-4'>
          <div className='flex-1 relative'>
            <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
              <MagnifyingGlassIcon className='h-5 w-5 text-gray-400' />
            </div>
            <input
              type='text'
              placeholder='Search by product, user name, or email...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500'
            />
          </div>
          <select
            value={filterBy}
            onChange={e => setFilterBy(e.target.value)}
            className='inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            <option value='all'>All Users</option>
            <option value='recent'>Recent Activity</option>
            <option value='high-value'>High Value</option>
            <option value='frequent'>Frequent Shoppers</option>
          </select>
        </div>
      </div>

      {/* Tabs */}
      <div className='px-4 sm:px-6 md:px-8'>
        <div className='border-b border-gray-200'>
          <nav className='-mb-px flex space-x-8'>
            {[
              {
                id: 'carts',
                name: 'Active Carts',
                icon: ShoppingCartIcon,
                count: cartItems.length,
              },
              {
                id: 'wishlists',
                name: 'Wishlists',
                icon: HeartIcon,
                count: wishlistItems.length,
              },
              {
                id: 'abandoned',
                name: 'Abandoned Carts',
                icon: ExclamationTriangleIcon,
                count: abandonedCarts.length,
              },
              {
                id: 'analytics',
                name: 'Analytics',
                icon: ChartBarIcon,
                count: null,
              },
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                <tab.icon className='h-5 w-5' />
                <span>{tab.name}</span>
                {tab.count !== null && (
                  <span
                    className={`${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-600'
                    } inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium`}
                  >
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className='px-4 sm:px-6 md:px-8'>
        {activeTab === 'carts' && (
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <h3 className='text-lg leading-6 font-medium text-gray-900 mb-4'>
                Active Carts
              </h3>
              <div className='overflow-x-auto'>
                <table className='min-w-full divide-y divide-gray-200'>
                  <thead className='bg-gray-50'>
                    <tr>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Product
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        User
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Quantity
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Value
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Added
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className='bg-white divide-y divide-gray-200'>
                    {filteredCartItems.map(item => (
                      <tr key={item.id} className='hover:bg-gray-50'>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          <div className='flex items-center'>
                            <div className='flex-shrink-0 h-12 w-12'>
                              <Image
                                src={item.productImage}
                                alt={item.productName}
                                width={48}
                                height={48}
                                className='h-12 w-12 rounded-lg object-cover'
                                onError={e => {
                                  const target = e.target as HTMLImageElement;
                                  target.src =
                                    '/images/products/placeholder.svg';
                                }}
                              />
                            </div>
                            <div className='ml-4'>
                              <div className='text-sm font-medium text-gray-900'>
                                {item.productName}
                              </div>
                              <div className='text-sm text-gray-500'>
                                ID: {item.productId}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          <div className='text-sm font-medium text-gray-900'>
                            {item.userName}
                          </div>
                          <div className='text-sm text-gray-500'>
                            {item.userEmail}
                          </div>
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                          {item.quantity}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                          ₹{(item.price * item.quantity).toLocaleString()}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                          {new Date(item.addedAt).toLocaleDateString()}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm font-medium'>
                          <div className='flex space-x-2'>
                            <button className='text-blue-600 hover:text-blue-900'>
                              <EyeIcon className='h-5 w-5' />
                            </button>
                            <button className='text-red-600 hover:text-red-900'>
                              <TrashIcon className='h-5 w-5' />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'wishlists' && (
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <h3 className='text-lg leading-6 font-medium text-gray-900 mb-4'>
                User Wishlists
              </h3>
              <div className='overflow-x-auto'>
                <table className='min-w-full divide-y divide-gray-200'>
                  <thead className='bg-gray-50'>
                    <tr>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Product
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        User
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Price
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Added
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className='bg-white divide-y divide-gray-200'>
                    {filteredWishlistItems.map(item => (
                      <tr key={item.id} className='hover:bg-gray-50'>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          <div className='flex items-center'>
                            <div className='flex-shrink-0 h-12 w-12'>
                              <Image
                                src={item.productImage}
                                alt={item.productName}
                                width={48}
                                height={48}
                                className='h-12 w-12 rounded-lg object-cover'
                                onError={e => {
                                  const target = e.target as HTMLImageElement;
                                  target.src =
                                    '/images/products/placeholder.svg';
                                }}
                              />
                            </div>
                            <div className='ml-4'>
                              <div className='text-sm font-medium text-gray-900'>
                                {item.productName}
                              </div>
                              <div className='text-sm text-gray-500'>
                                ID: {item.productId}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap'>
                          <div className='text-sm font-medium text-gray-900'>
                            {item.userName}
                          </div>
                          <div className='text-sm text-gray-500'>
                            {item.userEmail}
                          </div>
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                          ₹{item.price.toLocaleString()}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                          {new Date(item.addedAt).toLocaleDateString()}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm font-medium'>
                          <div className='flex space-x-2'>
                            <button className='text-blue-600 hover:text-blue-900'>
                              <EyeIcon className='h-5 w-5' />
                            </button>
                            <button className='text-red-600 hover:text-red-900'>
                              <HeartSolidIcon className='h-5 w-5' />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'abandoned' && (
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <h3 className='text-lg leading-6 font-medium text-gray-900 mb-4'>
                Abandoned Carts
              </h3>
              <div className='space-y-4'>
                {filteredAbandonedCarts.map(cart => (
                  <div
                    key={cart.id}
                    className='border rounded-lg p-4 hover:bg-gray-50'
                  >
                    <div className='flex items-center justify-between mb-3'>
                      <div>
                        <h4 className='text-sm font-medium text-gray-900'>
                          {cart.userName}
                        </h4>
                        <p className='text-sm text-gray-500'>
                          {cart.userEmail}
                        </p>
                      </div>
                      <div className='text-right'>
                        <div className='text-sm font-medium text-gray-900'>
                          ₹{cart.totalValue.toLocaleString()}
                        </div>
                        <div className='text-sm text-red-600'>
                          {cart.daysSinceAbandoned} days ago
                        </div>
                      </div>
                    </div>
                    <div className='space-y-2'>
                      {cart.items.map(item => (
                        <div
                          key={item.id}
                          className='flex items-center space-x-3 text-sm'
                        >
                          <Image
                            src={item.productImage}
                            alt={item.productName}
                            width={32}
                            height={32}
                            className='h-8 w-8 rounded object-cover'
                            onError={e => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/images/products/placeholder.svg';
                            }}
                          />
                          <span className='flex-1'>{item.productName}</span>
                          <span className='text-gray-500'>
                            Qty: {item.quantity}
                          </span>
                          <span className='font-medium'>
                            ₹{(item.price * item.quantity).toLocaleString()}
                          </span>
                        </div>
                      ))}
                    </div>
                    <div className='mt-3 flex justify-end space-x-2'>
                      <button className='px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200'>
                        Send Reminder
                      </button>
                      <button className='px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200'>
                        Offer Discount
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className='space-y-6'>
            {/* Analytics Overview */}
            <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4'>
              <div className='bg-white overflow-hidden shadow rounded-lg'>
                <div className='p-5'>
                  <div className='flex items-center'>
                    <div className='flex-shrink-0'>
                      <ShoppingCartIcon className='h-6 w-6 text-gray-400' />
                    </div>
                    <div className='ml-5 w-0 flex-1'>
                      <dl>
                        <dt className='text-sm font-medium text-gray-500 truncate'>
                          Total Active Carts
                        </dt>
                        <dd className='text-2xl font-semibold text-gray-900'>
                          {analytics.totalCarts}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className='bg-white overflow-hidden shadow rounded-lg'>
                <div className='p-5'>
                  <div className='flex items-center'>
                    <div className='flex-shrink-0'>
                      <CurrencyDollarIcon className='h-6 w-6 text-gray-400' />
                    </div>
                    <div className='ml-5 w-0 flex-1'>
                      <dl>
                        <dt className='text-sm font-medium text-gray-500 truncate'>
                          Total Cart Value
                        </dt>
                        <dd className='text-2xl font-semibold text-gray-900'>
                          ₹{analytics.totalValue.toLocaleString()}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className='bg-white overflow-hidden shadow rounded-lg'>
                <div className='p-5'>
                  <div className='flex items-center'>
                    <div className='flex-shrink-0'>
                      <ChartBarIcon className='h-6 w-6 text-gray-400' />
                    </div>
                    <div className='ml-5 w-0 flex-1'>
                      <dl>
                        <dt className='text-sm font-medium text-gray-500 truncate'>
                          Avg Cart Value
                        </dt>
                        <dd className='text-2xl font-semibold text-gray-900'>
                          ₹{analytics.averageCartValue.toLocaleString()}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className='bg-white overflow-hidden shadow rounded-lg'>
                <div className='p-5'>
                  <div className='flex items-center'>
                    <div className='flex-shrink-0'>
                      <ExclamationTriangleIcon className='h-6 w-6 text-gray-400' />
                    </div>
                    <div className='ml-5 w-0 flex-1'>
                      <dl>
                        <dt className='text-sm font-medium text-gray-500 truncate'>
                          Abandonment Rate
                        </dt>
                        <dd className='text-2xl font-semibold text-red-600'>
                          {analytics.abandonmentRate}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Top Products in Carts */}
            <div className='bg-white shadow rounded-lg'>
              <div className='px-4 py-5 sm:p-6'>
                <h3 className='text-lg leading-6 font-medium text-gray-900 mb-4'>
                  Top Products in Carts
                </h3>
                <div className='overflow-x-auto'>
                  <table className='min-w-full divide-y divide-gray-200'>
                    <thead className='bg-gray-50'>
                      <tr>
                        <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                          Product
                        </th>
                        <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                          Times Added
                        </th>
                        <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                          Revenue Potential
                        </th>
                      </tr>
                    </thead>
                    <tbody className='bg-white divide-y divide-gray-200'>
                      {analytics.topProducts.map((product, index) => (
                        <tr
                          key={product.productId}
                          className='hover:bg-gray-50'
                        >
                          <td className='px-6 py-4 whitespace-nowrap'>
                            <div className='text-sm font-medium text-gray-900'>
                              {product.productName}
                            </div>
                            <div className='text-sm text-gray-500'>
                              ID: {product.productId}
                            </div>
                          </td>
                          <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                            {product.timesAdded}
                          </td>
                          <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                            ₹{product.revenue.toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
