'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import <PERSON>Header, { ActionButton } from '@/components/admin/PageHeader';
import DataTable from '@/components/admin/DataTable';
import FormField, { FormContainer } from '@/components/admin/FormField';
import {
  PlusIcon,
  XMarkIcon,
  EyeIcon,
  PuzzlePieceIcon,
} from '@heroicons/react/24/outline';

interface Widget {
  id: string;
  name: string;
  type:
    | 'banner'
    | 'text'
    | 'image'
    | 'video'
    | 'html'
    | 'product_list'
    | 'category_list';
  title?: string;
  content: string;
  settings: Record<string, any>;
  position: 'header' | 'footer' | 'sidebar' | 'content' | 'homepage';
  sortOrder: number;
  status: 'active' | 'inactive';
  pages: string[];
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
}

// Mock data for widgets
const mockWidgets: Widget[] = [
  {
    id: '1',
    name: 'Homepage Banner',
    type: 'banner',
    title: 'Welcome to ONDC Seller Platform',
    content: 'Join thousands of sellers and grow your business with us',
    settings: {
      backgroundColor: '#3B82F6',
      textColor: '#FFFFFF',
      buttonText: 'Get Started',
      buttonLink: '/auth/register',
      imageUrl: '/images/banners/homepage.jpg',
    },
    position: 'homepage',
    sortOrder: 1,
    status: 'active',
    pages: ['homepage'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    name: 'Footer Contact Info',
    type: 'text',
    title: 'Contact Information',
    content:
      'Email: <EMAIL>\nPhone: +91 12345 67890\nAddress: 123 Business Street, Mumbai, India',
    settings: {
      fontSize: '14px',
      textAlign: 'left',
    },
    position: 'footer',
    sortOrder: 1,
    status: 'active',
    pages: ['all'],
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-14T15:45:00Z',
  },
  {
    id: '3',
    name: 'Featured Products',
    type: 'product_list',
    title: 'Featured Products',
    content: 'Display featured products on homepage',
    settings: {
      limit: 8,
      layout: 'grid',
      showPrice: true,
      showRating: true,
      filterBy: 'featured',
    },
    position: 'homepage',
    sortOrder: 2,
    status: 'active',
    pages: ['homepage'],
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-13T12:20:00Z',
  },
  {
    id: '4',
    name: 'Promotional Video',
    type: 'video',
    title: 'How to Sell on ONDC',
    content: 'Learn how to start selling on our platform',
    settings: {
      videoUrl: 'https://www.youtube.com/watch?v=example',
      autoplay: false,
      controls: true,
      width: '100%',
      height: '400px',
    },
    position: 'content',
    sortOrder: 1,
    status: 'inactive',
    pages: ['about-us'],
    startDate: '2024-01-15T00:00:00Z',
    endDate: '2024-02-15T00:00:00Z',
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-12T09:15:00Z',
  },
  {
    id: '5',
    name: 'Category Navigation',
    type: 'category_list',
    title: 'Shop by Category',
    content: 'Browse products by category',
    settings: {
      layout: 'horizontal',
      showImages: true,
      showCount: true,
      limit: 6,
    },
    position: 'header',
    sortOrder: 1,
    status: 'active',
    pages: ['all'],
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-11T14:30:00Z',
  },
  {
    id: '6',
    name: 'Custom HTML Block',
    type: 'html',
    title: 'Newsletter Signup',
    content:
      '<div class="newsletter"><h3>Subscribe to our newsletter</h3><form><input type="email" placeholder="Enter your email"><button>Subscribe</button></form></div>',
    settings: {
      customCSS:
        '.newsletter { background: #f8f9fa; padding: 20px; border-radius: 8px; }',
    },
    position: 'sidebar',
    sortOrder: 1,
    status: 'active',
    pages: ['blog', 'news'],
    createdAt: '2024-01-06T00:00:00Z',
    updatedAt: '2024-01-10T11:20:00Z',
  },
];

interface WidgetFormData {
  name: string;
  type: string;
  title: string;
  content: string;
  position: string;
  sortOrder: string;
  status: string;
  pages: string;
  startDate: string;
  endDate: string;
}

const initialFormData: WidgetFormData = {
  name: '',
  type: 'text',
  title: '',
  content: '',
  position: 'content',
  sortOrder: '1',
  status: 'active',
  pages: '',
  startDate: '',
  endDate: '',
};

export default function WidgetsPage() {
  const router = useRouter();
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState<WidgetFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    // Simulate API call
    const fetchWidgets = async () => {
      setLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setWidgets(mockWidgets);
      } catch (error) {
        console.error('Error fetching widgets:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWidgets();
  }, []);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Widget name is required';
    }

    if (!formData.content.trim()) {
      errors.content = 'Widget content is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newWidget: Widget = {
        id: Date.now().toString(),
        name: formData.name,
        type: formData.type as Widget['type'],
        title: formData.title || undefined,
        content: formData.content,
        settings: {},
        position: formData.position as Widget['position'],
        sortOrder: parseInt(formData.sortOrder) || 1,
        status: formData.status as Widget['status'],
        pages: formData.pages
          ? formData.pages.split(',').map(p => p.trim())
          : ['all'],
        startDate: formData.startDate || undefined,
        endDate: formData.endDate || undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setWidgets(prev => [newWidget, ...prev]);
      setFormData(initialFormData);
      setShowAddForm(false);
    } catch (error) {
      console.error('Error creating widget:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (widget: Widget) => {
    router.push(`/admin/widgets/${widget.id}/edit`);
  };

  const handlePreview = (widget: Widget) => {
    // Open preview modal or new window
    console.log('Preview widget:', widget);
  };

  const handleDelete = async (widget: Widget) => {
    if (window.confirm(`Are you sure you want to delete "${widget.name}"?`)) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setWidgets(prev => prev.filter(w => w.id !== widget.id));
      } catch (error) {
        console.error('Error deleting widget:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: Widget['status']) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      inactive: { color: 'bg-red-100 text-red-800', label: 'Inactive' },
    };

    const config = statusConfig[status];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const getTypeBadge = (type: Widget['type']) => {
    const typeConfig = {
      banner: { color: 'bg-purple-100 text-purple-800', label: 'Banner' },
      text: { color: 'bg-blue-100 text-blue-800', label: 'Text' },
      image: { color: 'bg-green-100 text-green-800', label: 'Image' },
      video: { color: 'bg-red-100 text-red-800', label: 'Video' },
      html: { color: 'bg-yellow-100 text-yellow-800', label: 'HTML' },
      product_list: {
        color: 'bg-indigo-100 text-indigo-800',
        label: 'Product List',
      },
      category_list: {
        color: 'bg-pink-100 text-pink-800',
        label: 'Category List',
      },
    };

    const config = typeConfig[type];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const getPositionBadge = (position: Widget['position']) => {
    const positionConfig = {
      header: { color: 'bg-blue-100 text-blue-800', label: 'Header' },
      footer: { color: 'bg-gray-100 text-gray-800', label: 'Footer' },
      sidebar: { color: 'bg-green-100 text-green-800', label: 'Sidebar' },
      content: { color: 'bg-yellow-100 text-yellow-800', label: 'Content' },
      homepage: { color: 'bg-purple-100 text-purple-800', label: 'Homepage' },
    };

    const config = positionConfig[position];
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const columns = [
    {
      key: 'name',
      label: 'Widget',
      sortable: true,
      render: (value: string, row: Widget) => (
        <div>
          <div className='text-sm font-medium text-gray-900'>{value}</div>
          {row.title && (
            <div className='text-sm text-gray-500'>{row.title}</div>
          )}
        </div>
      ),
    },
    {
      key: 'type',
      label: 'Type',
      sortable: true,
      render: (value: Widget['type']) => getTypeBadge(value),
    },
    {
      key: 'position',
      label: 'Position',
      sortable: true,
      render: (value: Widget['position']) => getPositionBadge(value),
    },
    {
      key: 'pages',
      label: 'Pages',
      render: (value: string[]) => {
        if (value.includes('all')) {
          return <span className='text-sm text-gray-500'>All pages</span>;
        }

        const displayPages = value.slice(0, 2);
        const remainingCount = value.length - 2;

        return (
          <div className='text-sm text-gray-500'>
            {displayPages.join(', ')}
            {remainingCount > 0 && (
              <span className='text-gray-400'> +{remainingCount} more</span>
            )}
          </div>
        );
      },
    },
    {
      key: 'sortOrder',
      label: 'Order',
      sortable: true,
      render: (value: number) => (
        <span className='text-sm font-medium text-gray-900'>{value}</span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: Widget['status']) => getStatusBadge(value),
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      render: (value: string) => (
        <span className='text-sm text-gray-500'>{formatDate(value)}</span>
      ),
    },
  ];

  const breadcrumbs = [
    { label: 'CMS', href: '#' },
    { label: 'Widgets', active: true },
  ];

  const customActions = (row: Widget) => [
    {
      label: 'Preview',
      icon: EyeIcon,
      onClick: () => handlePreview(row),
    },
  ];

  return (
    <div className='space-y-6'>
      <PageHeader
        title='CMS Widgets'
        description='Manage reusable content widgets and components'
        breadcrumbs={breadcrumbs}
        actions={
          <ActionButton onClick={() => setShowAddForm(true)} icon={PlusIcon}>
            Add Widget
          </ActionButton>
        }
      />

      {showAddForm && (
        <div className='max-w-4xl'>
          <FormContainer
            title='Add New Widget'
            description='Create a new content widget'
            onSubmit={handleSubmit}
            actions={
              <>
                <ActionButton
                  variant='secondary'
                  onClick={() => {
                    setShowAddForm(false);
                    setFormData(initialFormData);
                    setFormErrors({});
                  }}
                  icon={XMarkIcon}
                >
                  Cancel
                </ActionButton>
                <ActionButton
                  onClick={() => handleSubmit({} as React.FormEvent)}
                  disabled={submitting}
                  icon={PlusIcon}
                >
                  {submitting ? 'Creating...' : 'Create Widget'}
                </ActionButton>
              </>
            }
          >
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                label='Widget Name'
                name='name'
                value={formData.name}
                onChange={handleInputChange}
                placeholder='Enter widget name'
                required
                error={formErrors.name}
                help='Internal name for the widget'
              />
              <FormField
                label='Widget Type'
                name='type'
                type='select'
                value={formData.type}
                onChange={handleInputChange}
                options={[
                  { value: 'text', label: 'Text Block' },
                  { value: 'banner', label: 'Banner' },
                  { value: 'image', label: 'Image' },
                  { value: 'video', label: 'Video' },
                  { value: 'html', label: 'Custom HTML' },
                  { value: 'product_list', label: 'Product List' },
                  { value: 'category_list', label: 'Category List' },
                ]}
                help='Type of content widget'
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                label='Position'
                name='position'
                type='select'
                value={formData.position}
                onChange={handleInputChange}
                options={[
                  { value: 'header', label: 'Header' },
                  { value: 'footer', label: 'Footer' },
                  { value: 'sidebar', label: 'Sidebar' },
                  { value: 'content', label: 'Content Area' },
                  { value: 'homepage', label: 'Homepage' },
                ]}
                help='Where to display the widget'
              />
              <FormField
                label='Sort Order'
                name='sortOrder'
                type='number'
                value={formData.sortOrder}
                onChange={handleInputChange}
                placeholder='1'
                help='Display order (lower numbers appear first)'
              />
            </div>

            <FormField
              label='Widget Title'
              name='title'
              value={formData.title}
              onChange={handleInputChange}
              placeholder='Enter widget title (optional)'
              help='Display title for the widget'
            />

            <FormField
              label='Widget Content'
              name='content'
              type='textarea'
              rows={6}
              value={formData.content}
              onChange={handleInputChange}
              placeholder='Enter widget content'
              required
              error={formErrors.content}
              help='Main content of the widget (HTML supported for HTML type)'
            />

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                label='Pages'
                name='pages'
                value={formData.pages}
                onChange={handleInputChange}
                placeholder='homepage, about-us, contact (or leave empty for all pages)'
                help='Comma-separated list of page slugs (empty = all pages)'
              />
              <FormField
                label='Status'
                name='status'
                type='select'
                value={formData.status}
                onChange={handleInputChange}
                options={[
                  { value: 'active', label: 'Active' },
                  { value: 'inactive', label: 'Inactive' },
                ]}
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                label='Start Date'
                name='startDate'
                type='text'
                value={formData.startDate}
                onChange={handleInputChange}
                help='When to start showing the widget (optional)'
              />
              <FormField
                label='End Date'
                name='endDate'
                type='text'
                value={formData.endDate}
                onChange={handleInputChange}
                help='When to stop showing the widget (optional)'
              />
            </div>
          </FormContainer>
        </div>
      )}

      <DataTable
        columns={columns}
        data={widgets}
        loading={loading}
        searchable
        filterable
        pagination
        pageSize={10}
        onEdit={handleEdit}
        onDelete={handleDelete}
        emptyMessage='No widgets found. Create your first widget to get started.'
      />
    </div>
  );
}
