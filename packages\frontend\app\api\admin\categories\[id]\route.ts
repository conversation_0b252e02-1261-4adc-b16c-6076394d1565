// import { NextRequest, NextResponse } from 'next/server';

// // Import the same mock data (in real app, this would come from database)
// const MOCK_ADMIN_CATEGORIES = [
//   {
//     id: '1',
//     name: 'Electronics',
//     slug: 'electronics',
//     description: 'Electronic devices and gadgets',
//     image: '/images/categories/electronics.jpg',
//     status: 'active',
//     parentId: null,
//     sortOrder: 1,
//     productCount: 156,
//     metaTitle: 'Electronics - ONDC Seller',
//     metaDescription: 'Browse our wide selection of electronic devices',
//     createdAt: '2024-01-01T00:00:00Z',
//     updatedAt: '2024-01-15T10:30:00Z',
//   },
//   {
//     id: '2',
//     name: 'Clothing',
//     slug: 'clothing',
//     description: 'Fashion and apparel for all ages',
//     image: '/images/categories/clothing.jpg',
//     status: 'active',
//     parentId: null,
//     sortOrder: 2,
//     productCount: 234,
//     metaTitle: 'Clothing & Fashion - ONDC Seller',
//     metaDescription: 'Discover the latest fashion trends and clothing',
//     createdAt: '2024-01-02T00:00:00Z',
//     updatedAt: '2024-01-14T15:45:00Z',
//   },
//   {
//     id: '3',
//     name: 'Home & Garden',
//     slug: 'home-garden',
//     description: 'Home improvement and garden supplies',
//     image: '/images/categories/home-garden.jpg',
//     status: 'active',
//     parentId: null,
//     sortOrder: 3,
//     productCount: 89,
//     metaTitle: 'Home & Garden - ONDC Seller',
//     metaDescription: 'Everything for your home and garden needs',
//     createdAt: '2024-01-03T00:00:00Z',
//     updatedAt: '2024-01-13T12:20:00Z',
//   },
//   {
//     id: '4',
//     name: 'Sports & Outdoors',
//     slug: 'sports-outdoors',
//     description: 'Sports equipment and outdoor gear',
//     image: '/images/categories/sports.jpg',
//     status: 'active',
//     parentId: null,
//     sortOrder: 4,
//     productCount: 167,
//     metaTitle: 'Sports & Outdoors - ONDC Seller',
//     metaDescription: 'Sports equipment and outdoor adventure gear',
//     createdAt: '2024-01-04T00:00:00Z',
//     updatedAt: '2024-01-12T09:15:00Z',
//   },
//   {
//     id: '5',
//     name: 'Books',
//     slug: 'books',
//     description: 'Books and educational materials',
//     image: '/images/categories/books.jpg',
//     status: 'inactive',
//     parentId: null,
//     sortOrder: 5,
//     productCount: 45,
//     metaTitle: 'Books - ONDC Seller',
//     metaDescription: 'Wide selection of books and educational materials',
//     createdAt: '2024-01-05T00:00:00Z',
//     updatedAt: '2024-01-11T14:30:00Z',
//   },
//   {
//     id: '6',
//     name: 'Smartphones',
//     slug: 'smartphones',
//     description: 'Latest smartphones and accessories',
//     image: '/images/categories/smartphones.jpg',
//     status: 'active',
//     parentId: '1', // Child of Electronics
//     sortOrder: 1,
//     productCount: 78,
//     metaTitle: 'Smartphones - ONDC Seller',
//     metaDescription: 'Latest smartphones from top brands',
//     createdAt: '2024-01-06T00:00:00Z',
//     updatedAt: '2024-01-10T16:45:00Z',
//   },
// ];

// // GET /api/admin/categories/[id] - Get specific category
// export async function GET(
//   request: NextRequest,
//   { params }: { params: { id: string } }
// ) {
//   try {
//     const { id } = params;
//     console.log('[Admin Category API] GET request for ID:', id);

//     const category = MOCK_ADMIN_CATEGORIES.find(c => c.id === id);

//     if (!category) {
//       return NextResponse.json(
//         { error: 'Category not found' },
//         { status: 404 }
//       );
//     }

//     console.log('[Admin Category API] Found category:', category.name);

//     return NextResponse.json({ category });
//   } catch (error) {
//     console.error('[Admin Category API] Error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to fetch category',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }

// // PUT /api/admin/categories/[id] - Update specific category
// export async function PUT(
//   request: NextRequest,
//   { params }: { params: { id: string } }
// ) {
//   try {
//     const { id } = params;
//     const body = await request.json();
//     console.log('[Admin Category API] PUT request for ID:', id, body);

//     const categoryIndex = MOCK_ADMIN_CATEGORIES.findIndex(c => c.id === id);

//     if (categoryIndex === -1) {
//       return NextResponse.json(
//         { error: 'Category not found' },
//         { status: 404 }
//       );
//     }

//     // Check if slug is being changed and if it conflicts with another category
//     if (body.slug && body.slug !== MOCK_ADMIN_CATEGORIES[categoryIndex].slug) {
//       const existingCategory = MOCK_ADMIN_CATEGORIES.find(
//         c => c.slug === body.slug && c.id !== id
//       );
//       if (existingCategory) {
//         return NextResponse.json(
//           { error: 'Category with this slug already exists' },
//           { status: 409 }
//         );
//       }
//     }

//     // Update category
//     const updatedCategory = {
//       ...MOCK_ADMIN_CATEGORIES[categoryIndex],
//       ...body,
//       id, // Ensure ID doesn't change
//       updatedAt: new Date().toISOString(),
//     };

//     MOCK_ADMIN_CATEGORIES[categoryIndex] = updatedCategory;

//     console.log('[Admin Category API] Updated category:', updatedCategory.name);

//     return NextResponse.json({
//       message: 'Category updated successfully',
//       category: updatedCategory,
//     });
//   } catch (error) {
//     console.error('[Admin Category API] Update error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to update category',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }

// // DELETE /api/admin/categories/[id] - Delete specific category
// export async function DELETE(
//   request: NextRequest,
//   { params }: { params: { id: string } }
// ) {
//   try {
//     const { id } = params;
//     console.log('[Admin Category API] DELETE request for ID:', id);

//     const categoryIndex = MOCK_ADMIN_CATEGORIES.findIndex(c => c.id === id);

//     if (categoryIndex === -1) {
//       return NextResponse.json(
//         { error: 'Category not found' },
//         { status: 404 }
//       );
//     }

//     // Check if category has child categories
//     const hasChildren = MOCK_ADMIN_CATEGORIES.some(c => c.parentId === id);
//     if (hasChildren) {
//       return NextResponse.json(
//         {
//           error:
//             'Cannot delete category with child categories. Please delete or reassign child categories first.',
//         },
//         { status: 400 }
//       );
//     }

//     const deletedCategory = MOCK_ADMIN_CATEGORIES[categoryIndex];
//     MOCK_ADMIN_CATEGORIES.splice(categoryIndex, 1);

//     console.log('[Admin Category API] Deleted category:', deletedCategory.name);

//     return NextResponse.json({
//       message: 'Category deleted successfully',
//       category: deletedCategory,
//     });
//   } catch (error) {
//     console.error('[Admin Category API] Delete error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to delete category',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }
