// import { NextRequest, NextResponse } from 'next/server';

// // Mock admin collections data
// const MOCK_ADMIN_COLLECTIONS = [
//   {
//     id: 'COLL-001',
//     name: 'Summer Sale 2024',
//     slug: 'summer-sale-2024',
//     description: 'Hot deals for the summer season',
//     image: '/images/collections/summer-sale.jpg',
//     status: 'active',
//     type: 'manual', // manual, automatic
//     sortOrder: 1,
//     productCount: 25,
//     conditions: {
//       rules: [
//         { field: 'tags', operator: 'contains', value: 'summer' },
//         { field: 'category', operator: 'equals', value: 'clothing' },
//       ],
//       match: 'any', // any, all
//     },
//     products: ['1', '2', '3'], // Product IDs
//     seoTitle: 'Summer Sale 2024 - Best Deals',
//     seoDescription: 'Discover amazing summer deals on clothing and accessories',
//     isVisible: true,
//     isFeatured: true,
//     startDate: '2024-06-01T00:00:00Z',
//     endDate: '2024-08-31T23:59:59Z',
//     createdAt: '2024-01-10T10:00:00Z',
//     updatedAt: '2024-01-15T14:30:00Z',
//   },
//   {
//     id: 'COLL-002',
//     name: 'New Arrivals',
//     slug: 'new-arrivals',
//     description: 'Latest products added to our store',
//     image: '/images/collections/new-arrivals.jpg',
//     status: 'active',
//     type: 'automatic',
//     sortOrder: 2,
//     productCount: 18,
//     conditions: {
//       rules: [
//         { field: 'created_at', operator: 'greater_than', value: '2024-01-01' },
//       ],
//       match: 'all',
//     },
//     products: [],
//     seoTitle: 'New Arrivals - Latest Products',
//     seoDescription: 'Check out our newest products and latest additions',
//     isVisible: true,
//     isFeatured: true,
//     startDate: null,
//     endDate: null,
//     createdAt: '2024-01-05T09:00:00Z',
//     updatedAt: '2024-01-14T16:20:00Z',
//   },
//   {
//     id: 'COLL-003',
//     name: 'Best Sellers',
//     slug: 'best-sellers',
//     description: 'Our most popular products',
//     image: '/images/collections/best-sellers.jpg',
//     status: 'active',
//     type: 'automatic',
//     sortOrder: 3,
//     productCount: 12,
//     conditions: {
//       rules: [{ field: 'sales_count', operator: 'greater_than', value: '50' }],
//       match: 'all',
//     },
//     products: [],
//     seoTitle: 'Best Sellers - Most Popular Products',
//     seoDescription: 'Shop our best-selling products loved by customers',
//     isVisible: true,
//     isFeatured: false,
//     startDate: null,
//     endDate: null,
//     createdAt: '2024-01-03T11:30:00Z',
//     updatedAt: '2024-01-12T13:45:00Z',
//   },
//   {
//     id: 'COLL-004',
//     name: 'Winter Clearance',
//     slug: 'winter-clearance',
//     description: 'End of season winter clearance sale',
//     image: '/images/collections/winter-clearance.jpg',
//     status: 'inactive',
//     type: 'manual',
//     sortOrder: 4,
//     productCount: 8,
//     conditions: {
//       rules: [],
//       match: 'all',
//     },
//     products: ['4', '5'],
//     seoTitle: 'Winter Clearance - Final Sale',
//     seoDescription: 'Last chance to grab winter items at discounted prices',
//     isVisible: false,
//     isFeatured: false,
//     startDate: '2024-02-01T00:00:00Z',
//     endDate: '2024-02-29T23:59:59Z',
//     createdAt: '2024-01-01T08:00:00Z',
//     updatedAt: '2024-01-10T12:15:00Z',
//   },
// ];

// // GET /api/admin/collections - List all collections with admin features
// export async function GET(request: NextRequest) {
//   try {
//     const { searchParams } = new URL(request.url);
//     const page = parseInt(searchParams.get('page') || '1');
//     const limit = parseInt(searchParams.get('limit') || '10');
//     const search = searchParams.get('search') || '';
//     const status = searchParams.get('status') || '';
//     const type = searchParams.get('type') || '';
//     const isFeatured = searchParams.get('isFeatured') || '';
//     const sortBy = searchParams.get('sortBy') || 'sortOrder';
//     const sortOrder = searchParams.get('sortOrder') || 'asc';

//     console.log('[Admin Collections API] GET request:', {
//       page,
//       limit,
//       search,
//       status,
//       type,
//       isFeatured,
//       sortBy,
//       sortOrder,
//     });

//     // Filter collections based on search criteria
//     let filteredCollections = [...MOCK_ADMIN_COLLECTIONS];

//     // Apply search filter
//     if (search) {
//       filteredCollections = filteredCollections.filter(
//         collection =>
//           collection.name.toLowerCase().includes(search.toLowerCase()) ||
//           collection.description.toLowerCase().includes(search.toLowerCase()) ||
//           collection.slug.toLowerCase().includes(search.toLowerCase())
//       );
//     }

//     // Apply status filter
//     if (status) {
//       filteredCollections = filteredCollections.filter(
//         collection => collection.status === status
//       );
//     }

//     // Apply type filter
//     if (type) {
//       filteredCollections = filteredCollections.filter(
//         collection => collection.type === type
//       );
//     }

//     // Apply featured filter
//     if (isFeatured) {
//       const featured = isFeatured === 'true';
//       filteredCollections = filteredCollections.filter(
//         collection => collection.isFeatured === featured
//       );
//     }

//     // Apply sorting
//     filteredCollections.sort((a, b) => {
//       let aValue = a[sortBy as keyof typeof a];
//       let bValue = b[sortBy as keyof typeof b];

//       if (typeof aValue === 'string') {
//         aValue = aValue.toLowerCase();
//         bValue = (bValue as string).toLowerCase();
//       }

//       if (sortOrder === 'desc') {
//         return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
//       } else {
//         return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
//       }
//     });

//     // Apply pagination
//     const offset = (page - 1) * limit;
//     const paginatedCollections = filteredCollections.slice(
//       offset,
//       offset + limit
//     );

//     const response = {
//       collections: paginatedCollections,
//       pagination: {
//         page,
//         limit,
//         total: filteredCollections.length,
//         totalPages: Math.ceil(filteredCollections.length / limit),
//         hasNext: offset + limit < filteredCollections.length,
//         hasPrev: page > 1,
//       },
//       filters: {
//         search,
//         status,
//         type,
//         isFeatured,
//         sortBy,
//         sortOrder,
//       },
//     };

//     console.log('[Admin Collections API] Returning:', {
//       count: paginatedCollections.length,
//       total: filteredCollections.length,
//       page,
//     });

//     return NextResponse.json(response);
//   } catch (error) {
//     console.error('[Admin Collections API] Error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to fetch collections',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }

// // POST /api/admin/collections - Create new collection
// export async function POST(request: NextRequest) {
//   try {
//     const body = await request.json();
//     console.log('[Admin Collections API] POST request:', body);

//     // Validate required fields
//     const requiredFields = ['name', 'slug'];
//     for (const field of requiredFields) {
//       if (!body[field]) {
//         return NextResponse.json(
//           { error: `Missing required field: ${field}` },
//           { status: 400 }
//         );
//       }
//     }

//     // Check if slug already exists
//     const existingCollection = MOCK_ADMIN_COLLECTIONS.find(
//       c => c.slug === body.slug
//     );
//     if (existingCollection) {
//       return NextResponse.json(
//         { error: 'Collection with this slug already exists' },
//         { status: 409 }
//       );
//     }

//     // Create new collection
//     const newCollection = {
//       id: `COLL-${String(MOCK_ADMIN_COLLECTIONS.length + 1).padStart(3, '0')}`,
//       name: body.name,
//       slug: body.slug,
//       description: body.description || '',
//       image: body.image || '/images/collections/placeholder.jpg',
//       status: body.status || 'active',
//       type: body.type || 'manual',
//       sortOrder: body.sortOrder || MOCK_ADMIN_COLLECTIONS.length + 1,
//       productCount: 0,
//       conditions: body.conditions || { rules: [], match: 'all' },
//       products: body.products || [],
//       seoTitle: body.seoTitle || body.name,
//       seoDescription: body.seoDescription || body.description || '',
//       isVisible: body.isVisible !== undefined ? body.isVisible : true,
//       isFeatured: body.isFeatured || false,
//       startDate: body.startDate || null,
//       endDate: body.endDate || null,
//       createdAt: new Date().toISOString(),
//       updatedAt: new Date().toISOString(),
//     };

//     // Add to mock data (in real app, this would be saved to database)
//     MOCK_ADMIN_COLLECTIONS.push(newCollection);

//     console.log(
//       '[Admin Collections API] Created collection:',
//       newCollection.id
//     );

//     return NextResponse.json(
//       { message: 'Collection created successfully', collection: newCollection },
//       { status: 201 }
//     );
//   } catch (error) {
//     console.error('[Admin Collections API] Create error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to create collection',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }
