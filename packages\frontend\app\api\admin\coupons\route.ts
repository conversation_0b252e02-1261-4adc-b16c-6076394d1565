// import { NextRequest, NextResponse } from 'next/server';

// // Mock admin coupons data
// const MOCK_ADMIN_COUPONS = [
//   {
//     id: 'COUP-001',
//     code: 'SUMMER20',
//     name: 'Summer Sale 20% Off',
//     description: '20% discount on all summer items',
//     type: 'percentage', // percentage, fixed_amount, free_shipping
//     value: 20,
//     status: 'active',
//     usageLimit: 1000,
//     usageCount: 245,
//     usageLimitPerCustomer: 1,
//     minimumOrderAmount: 1000,
//     maximumDiscountAmount: 5000,
//     applicableProducts: [], // Empty means all products
//     applicableCategories: ['2'], // Clothing category
//     excludedProducts: [],
//     excludedCategories: [],
//     startDate: '2024-06-01T00:00:00Z',
//     endDate: '2024-08-31T23:59:59Z',
//     isFirstOrderOnly: false,
//     isPublic: true,
//     createdAt: '2024-05-15T10:00:00Z',
//     updatedAt: '2024-01-15T14:30:00Z',
//   },
//   {
//     id: 'COUP-002',
//     code: 'WELCOME10',
//     name: 'Welcome Discount',
//     description: '₹500 off on first order above ₹2000',
//     type: 'fixed_amount',
//     value: 500,
//     status: 'active',
//     usageLimit: null, // Unlimited
//     usageCount: 89,
//     usageLimitPerCustomer: 1,
//     minimumOrderAmount: 2000,
//     maximumDiscountAmount: null,
//     applicableProducts: [],
//     applicableCategories: [],
//     excludedProducts: [],
//     excludedCategories: [],
//     startDate: '2024-01-01T00:00:00Z',
//     endDate: null, // No end date
//     isFirstOrderOnly: true,
//     isPublic: true,
//     createdAt: '2024-01-01T08:00:00Z',
//     updatedAt: '2024-01-10T12:15:00Z',
//   },
//   {
//     id: 'COUP-003',
//     code: 'FREESHIP',
//     name: 'Free Shipping',
//     description: 'Free shipping on orders above ₹1500',
//     type: 'free_shipping',
//     value: 0,
//     status: 'active',
//     usageLimit: null,
//     usageCount: 156,
//     usageLimitPerCustomer: null, // No limit per customer
//     minimumOrderAmount: 1500,
//     maximumDiscountAmount: null,
//     applicableProducts: [],
//     applicableCategories: [],
//     excludedProducts: [],
//     excludedCategories: [],
//     startDate: '2024-01-01T00:00:00Z',
//     endDate: null,
//     isFirstOrderOnly: false,
//     isPublic: true,
//     createdAt: '2024-01-01T09:30:00Z',
//     updatedAt: '2024-01-08T16:45:00Z',
//   },
//   {
//     id: 'COUP-004',
//     code: 'VIP15',
//     name: 'VIP Customer Discount',
//     description: '15% discount for VIP customers',
//     type: 'percentage',
//     value: 15,
//     status: 'active',
//     usageLimit: 100,
//     usageCount: 23,
//     usageLimitPerCustomer: 5,
//     minimumOrderAmount: 0,
//     maximumDiscountAmount: 10000,
//     applicableProducts: [],
//     applicableCategories: [],
//     excludedProducts: [],
//     excludedCategories: [],
//     startDate: '2024-01-01T00:00:00Z',
//     endDate: '2024-12-31T23:59:59Z',
//     isFirstOrderOnly: false,
//     isPublic: false, // Private coupon
//     createdAt: '2024-01-01T11:00:00Z',
//     updatedAt: '2024-01-05T14:20:00Z',
//   },
//   {
//     id: 'COUP-005',
//     code: 'EXPIRED50',
//     name: 'Expired Mega Sale',
//     description: '50% off everything - expired',
//     type: 'percentage',
//     value: 50,
//     status: 'expired',
//     usageLimit: 500,
//     usageCount: 467,
//     usageLimitPerCustomer: 1,
//     minimumOrderAmount: 500,
//     maximumDiscountAmount: 15000,
//     applicableProducts: [],
//     applicableCategories: [],
//     excludedProducts: [],
//     excludedCategories: [],
//     startDate: '2023-12-01T00:00:00Z',
//     endDate: '2023-12-31T23:59:59Z',
//     isFirstOrderOnly: false,
//     isPublic: true,
//     createdAt: '2023-11-15T10:00:00Z',
//     updatedAt: '2024-01-01T00:00:00Z',
//   },
// ];

// // GET /api/admin/coupons - List all coupons with admin features
// export async function GET(request: NextRequest) {
//   try {
//     const { searchParams } = new URL(request.url);
//     const page = parseInt(searchParams.get('page') || '1');
//     const limit = parseInt(searchParams.get('limit') || '10');
//     const search = searchParams.get('search') || '';
//     const status = searchParams.get('status') || '';
//     const type = searchParams.get('type') || '';
//     const isPublic = searchParams.get('isPublic') || '';
//     const sortBy = searchParams.get('sortBy') || 'createdAt';
//     const sortOrder = searchParams.get('sortOrder') || 'desc';

//     console.log('[Admin Coupons API] GET request:', {
//       page,
//       limit,
//       search,
//       status,
//       type,
//       isPublic,
//       sortBy,
//       sortOrder,
//     });

//     // Filter coupons based on search criteria
//     let filteredCoupons = [...MOCK_ADMIN_COUPONS];

//     // Apply search filter
//     if (search) {
//       filteredCoupons = filteredCoupons.filter(
//         coupon =>
//           coupon.code.toLowerCase().includes(search.toLowerCase()) ||
//           coupon.name.toLowerCase().includes(search.toLowerCase()) ||
//           coupon.description.toLowerCase().includes(search.toLowerCase())
//       );
//     }

//     // Apply status filter
//     if (status) {
//       filteredCoupons = filteredCoupons.filter(
//         coupon => coupon.status === status
//       );
//     }

//     // Apply type filter
//     if (type) {
//       filteredCoupons = filteredCoupons.filter(coupon => coupon.type === type);
//     }

//     // Apply public filter
//     if (isPublic) {
//       const publicFilter = isPublic === 'true';
//       filteredCoupons = filteredCoupons.filter(
//         coupon => coupon.isPublic === publicFilter
//       );
//     }

//     // Apply sorting
//     filteredCoupons.sort((a, b) => {
//       let aValue = a[sortBy as keyof typeof a];
//       let bValue = b[sortBy as keyof typeof b];

//       if (typeof aValue === 'string') {
//         aValue = aValue.toLowerCase();
//         bValue = (bValue as string).toLowerCase();
//       }

//       if (sortOrder === 'desc') {
//         return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
//       } else {
//         return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
//       }
//     });

//     // Apply pagination
//     const offset = (page - 1) * limit;
//     const paginatedCoupons = filteredCoupons.slice(offset, offset + limit);

//     const response = {
//       coupons: paginatedCoupons,
//       pagination: {
//         page,
//         limit,
//         total: filteredCoupons.length,
//         totalPages: Math.ceil(filteredCoupons.length / limit),
//         hasNext: offset + limit < filteredCoupons.length,
//         hasPrev: page > 1,
//       },
//       filters: {
//         search,
//         status,
//         type,
//         isPublic,
//         sortBy,
//         sortOrder,
//       },
//     };

//     console.log('[Admin Coupons API] Returning:', {
//       count: paginatedCoupons.length,
//       total: filteredCoupons.length,
//       page,
//     });

//     return NextResponse.json(response);
//   } catch (error) {
//     console.error('[Admin Coupons API] Error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to fetch coupons',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }

// // POST /api/admin/coupons - Create new coupon
// export async function POST(request: NextRequest) {
//   try {
//     const body = await request.json();
//     console.log('[Admin Coupons API] POST request:', body);

//     // Validate required fields
//     const requiredFields = ['code', 'name', 'type', 'value'];
//     for (const field of requiredFields) {
//       if (!body[field]) {
//         return NextResponse.json(
//           { error: `Missing required field: ${field}` },
//           { status: 400 }
//         );
//       }
//     }

//     // Check if code already exists
//     const existingCoupon = MOCK_ADMIN_COUPONS.find(c => c.code === body.code);
//     if (existingCoupon) {
//       return NextResponse.json(
//         { error: 'Coupon with this code already exists' },
//         { status: 409 }
//       );
//     }

//     // Validate coupon type and value
//     if (body.type === 'percentage' && (body.value < 0 || body.value > 100)) {
//       return NextResponse.json(
//         { error: 'Percentage value must be between 0 and 100' },
//         { status: 400 }
//       );
//     }

//     if (body.type === 'fixed_amount' && body.value < 0) {
//       return NextResponse.json(
//         { error: 'Fixed amount value must be positive' },
//         { status: 400 }
//       );
//     }

//     // Create new coupon
//     const newCoupon = {
//       id: `COUP-${String(MOCK_ADMIN_COUPONS.length + 1).padStart(3, '0')}`,
//       code: body.code.toUpperCase(),
//       name: body.name,
//       description: body.description || '',
//       type: body.type,
//       value: body.value,
//       status: body.status || 'active',
//       usageLimit: body.usageLimit || null,
//       usageCount: 0,
//       usageLimitPerCustomer: body.usageLimitPerCustomer || null,
//       minimumOrderAmount: body.minimumOrderAmount || 0,
//       maximumDiscountAmount: body.maximumDiscountAmount || null,
//       applicableProducts: body.applicableProducts || [],
//       applicableCategories: body.applicableCategories || [],
//       excludedProducts: body.excludedProducts || [],
//       excludedCategories: body.excludedCategories || [],
//       startDate: body.startDate || new Date().toISOString(),
//       endDate: body.endDate || null,
//       isFirstOrderOnly: body.isFirstOrderOnly || false,
//       isPublic: body.isPublic !== undefined ? body.isPublic : true,
//       createdAt: new Date().toISOString(),
//       updatedAt: new Date().toISOString(),
//     };

//     // Add to mock data (in real app, this would be saved to database)
//     MOCK_ADMIN_COUPONS.push(newCoupon);

//     console.log('[Admin Coupons API] Created coupon:', newCoupon.id);

//     return NextResponse.json(
//       { message: 'Coupon created successfully', coupon: newCoupon },
//       { status: 201 }
//     );
//   } catch (error) {
//     console.error('[Admin Coupons API] Create error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to create coupon',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }
