// import { NextRequest, NextResponse } from 'next/server';

// // Mock orders data for customer
// const MOCK_CUSTOMER_ORDERS = [
//   {
//     id: 'order_01HQZJQZJQZJQZJQZJQZJQZJ',
//     orderNumber: 'ORD-001',
//     customer: {
//       id: 'CUST-001',
//       name: '<PERSON>',
//       email: '<EMAIL>',
//     },
//     status: 'completed',
//     paymentStatus: 'paid',
//     total: 2599,
//     items: 2,
//     shippingAddress: {
//       city: 'Mumbai',
//       state: 'Maharashtra',
//       country: 'India',
//     },
//     createdAt: '2024-01-15T10:30:00Z',
//     updatedAt: '2024-01-15T14:30:00Z',
//   },
//   {
//     id: 'order_02HQZJQZJQZJQZJQZJQZJQZJ',
//     orderNumber: 'ORD-002',
//     customer: {
//       id: 'CUST-001',
//       name: '<PERSON>',
//       email: '<EMAIL>',
//     },
//     status: 'processing',
//     paymentStatus: 'paid',
//     total: 1299,
//     items: 1,
//     shippingAddress: {
//       city: 'Mumbai',
//       state: 'Maharashtra',
//       country: 'India',
//     },
//     createdAt: '2024-01-10T09:15:00Z',
//     updatedAt: '2024-01-10T09:15:00Z',
//   },
//   {
//     id: 'order_03HQZJQZJQZJQZJQZJQZJQZJ',
//     orderNumber: 'ORD-003',
//     customer: {
//       id: 'CUST-001',
//       name: 'John Doe',
//       email: '<EMAIL>',
//     },
//     status: 'shipped',
//     paymentStatus: 'paid',
//     total: 4599,
//     items: 3,
//     shippingAddress: {
//       city: 'Mumbai',
//       state: 'Maharashtra',
//       country: 'India',
//     },
//     createdAt: '2024-01-05T16:20:00Z',
//     updatedAt: '2024-01-06T10:00:00Z',
//   },
// ];

// // GET /api/admin/customers/[id]/orders - Get customer orders
// export async function GET(
//   request: NextRequest,
//   { params }: { params: { id: string } }
// ) {
//   try {
//     const { id } = params;
//     const { searchParams } = new URL(request.url);
//     const limit = parseInt(searchParams.get('limit') || '10');
//     const offset = parseInt(searchParams.get('offset') || '0');
//     const status = searchParams.get('status') || '';

//     console.log('[Admin Customer Orders API] GET request for customer:', id, {
//       limit,
//       offset,
//       status,
//     });

//     // Filter orders by customer ID and status
//     let customerOrders = MOCK_CUSTOMER_ORDERS.filter(
//       order => order.customer.id === id
//     );

//     if (status) {
//       customerOrders = customerOrders.filter(order => order.status === status);
//     }

//     // Apply pagination
//     const startIndex = offset;
//     const endIndex = startIndex + limit;
//     const paginatedOrders = customerOrders.slice(startIndex, endIndex);

//     console.log(
//       '[Admin Customer Orders API] Found orders:',
//       paginatedOrders.length
//     );

//     return NextResponse.json({
//       orders: paginatedOrders,
//       count: paginatedOrders.length,
//       total: customerOrders.length,
//       customer_id: id,
//       pagination: {
//         page: Math.floor(offset / limit) + 1,
//         limit,
//         offset,
//         hasMore: endIndex < customerOrders.length,
//       },
//     });
//   } catch (error) {
//     console.error('[Admin Customer Orders API] Error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to fetch customer orders',
//         details: error instanceof Error ? error.message : 'Unknown error',
//         customer_id: params.id,
//       },
//       { status: 500 }
//     );
//   }
// }
