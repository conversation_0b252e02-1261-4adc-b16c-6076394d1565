// import { NextRequest, NextResponse } from 'next/server';

// // Import the same mock data (in real app, this would come from database)
// const MOCK_ADMIN_CUSTOMERS = [
//   {
//     id: 'CUST-001',
//     firstName: '<PERSON>',
//     lastName: 'Doe',
//     email: '<EMAIL>',
//     phone: '+91 9876543210',
//     status: 'active',
//     emailVerified: true,
//     phoneVerified: true,
//     totalOrders: 3,
//     totalSpent: 45999,
//     averageOrderValue: 15333,
//     lastOrderDate: '2024-01-15T10:30:00Z',
//     dateOfBirth: '1990-05-15',
//     gender: 'male',
//     addresses: [
//       {
//         id: 'ADDR-001',
//         type: 'shipping',
//         name: '<PERSON>',
//         address1: '123 Main Street',
//         address2: 'Apt 4B',
//         city: 'Mumbai',
//         state: 'Maharashtra',
//         postalCode: '400001',
//         country: 'India',
//         phone: '+91 9876543210',
//         isDefault: true,
//       },
//     ],
//     tags: ['vip', 'repeat-customer'],
//     notes: 'Prefers express delivery',
//     marketingOptIn: true,
//     createdAt: '2023-12-01T08:00:00Z',
//     updatedAt: '2024-01-15T14:45:00Z',
//   },
//   // Add other customers here (truncated for brevity)
// ];

// // GET /api/admin/customers/[id] - Get specific customer
// export async function GET(
//   request: NextRequest,
//   { params }: { params: { id: string } }
// ) {
//   try {
//     const { id } = params;
//     console.log('[Admin Customer API] GET request for ID:', id);

//     const customer = MOCK_ADMIN_CUSTOMERS.find(c => c.id === id);

//     if (!customer) {
//       return NextResponse.json(
//         { error: 'Customer not found' },
//         { status: 404 }
//       );
//     }

//     console.log('[Admin Customer API] Found customer:', customer.email);

//     return NextResponse.json({ customer });
//   } catch (error) {
//     console.error('[Admin Customer API] Error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to fetch customer',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }

// // PUT /api/admin/customers/[id] - Update specific customer
// export async function PUT(
//   request: NextRequest,
//   { params }: { params: { id: string } }
// ) {
//   try {
//     const { id } = params;
//     const body = await request.json();
//     console.log('[Admin Customer API] PUT request for ID:', id, body);

//     const customerIndex = MOCK_ADMIN_CUSTOMERS.findIndex(c => c.id === id);

//     if (customerIndex === -1) {
//       return NextResponse.json(
//         { error: 'Customer not found' },
//         { status: 404 }
//       );
//     }

//     // Check if email is being changed and if it conflicts with another customer
//     if (
//       body.email &&
//       body.email !== MOCK_ADMIN_CUSTOMERS[customerIndex].email
//     ) {
//       const existingCustomer = MOCK_ADMIN_CUSTOMERS.find(
//         c => c.email === body.email && c.id !== id
//       );
//       if (existingCustomer) {
//         return NextResponse.json(
//           { error: 'Customer with this email already exists' },
//           { status: 409 }
//         );
//       }
//     }

//     // Update customer
//     const updatedCustomer = {
//       ...MOCK_ADMIN_CUSTOMERS[customerIndex],
//       ...body,
//       id, // Ensure ID doesn't change
//       updatedAt: new Date().toISOString(),
//     };

//     MOCK_ADMIN_CUSTOMERS[customerIndex] = updatedCustomer;

//     console.log(
//       '[Admin Customer API] Updated customer:',
//       updatedCustomer.email
//     );

//     return NextResponse.json({
//       message: 'Customer updated successfully',
//       customer: updatedCustomer,
//     });
//   } catch (error) {
//     console.error('[Admin Customer API] Update error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to update customer',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }

// // DELETE /api/admin/customers/[id] - Delete specific customer (admin only)
// export async function DELETE(
//   request: NextRequest,
//   { params }: { params: { id: string } }
// ) {
//   try {
//     const { id } = params;
//     console.log('[Admin Customer API] DELETE request for ID:', id);

//     const customerIndex = MOCK_ADMIN_CUSTOMERS.findIndex(c => c.id === id);

//     if (customerIndex === -1) {
//       return NextResponse.json(
//         { error: 'Customer not found' },
//         { status: 404 }
//       );
//     }

//     // Check if customer has orders (in real app, you might want to prevent deletion)
//     const customer = MOCK_ADMIN_CUSTOMERS[customerIndex];
//     if (customer.totalOrders > 0) {
//       return NextResponse.json(
//         {
//           error:
//             'Cannot delete customer with existing orders. Consider deactivating the account instead.',
//           suggestion:
//             'Use PUT request to set status to "inactive" or "suspended"',
//         },
//         { status: 400 }
//       );
//     }

//     const deletedCustomer = MOCK_ADMIN_CUSTOMERS[customerIndex];
//     MOCK_ADMIN_CUSTOMERS.splice(customerIndex, 1);

//     console.log(
//       '[Admin Customer API] Deleted customer:',
//       deletedCustomer.email
//     );

//     return NextResponse.json({
//       message: 'Customer deleted successfully',
//       customer: deletedCustomer,
//     });
//   } catch (error) {
//     console.error('[Admin Customer API] Delete error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to delete customer',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }
