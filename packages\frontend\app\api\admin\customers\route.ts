// import { NextRequest, NextResponse } from 'next/server';

// // Mock admin customers data
// const MOCK_ADMIN_CUSTOMERS = [
//   {
//     id: 'CUST-001',
//     firstName: '<PERSON>',
//     lastName: 'Doe',
//     email: '<EMAIL>',
//     phone: '+91 9876543210',
//     status: 'active',
//     emailVerified: true,
//     phoneVerified: true,
//     totalOrders: 3,
//     totalSpent: 45999,
//     averageOrderValue: 15333,
//     lastOrderDate: '2024-01-15T10:30:00Z',
//     dateOfBirth: '1990-05-15',
//     gender: 'male',
//     addresses: [
//       {
//         id: 'ADDR-001',
//         type: 'shipping',
//         name: '<PERSON>',
//         address1: '123 Main Street',
//         address2: 'Apt 4B',
//         city: 'Mumbai',
//         state: 'Maharashtra',
//         postalCode: '400001',
//         country: 'India',
//         phone: '+91 9876543210',
//         isDefault: true,
//       },
//     ],
//     tags: ['vip', 'repeat-customer'],
//     notes: 'Prefers express delivery',
//     marketingOptIn: true,
//     createdAt: '2023-12-01T08:00:00Z',
//     updatedAt: '2024-01-15T14:45:00Z',
//   },
//   {
//     id: 'CUST-002',
//     firstName: 'Jane',
//     lastName: 'Smith',
//     email: '<EMAIL>',
//     phone: '+91 9876543211',
//     status: 'active',
//     emailVerified: true,
//     phoneVerified: false,
//     totalOrders: 5,
//     totalSpent: 78999,
//     averageOrderValue: 15800,
//     lastOrderDate: '2024-01-14T15:20:00Z',
//     dateOfBirth: '1985-08-22',
//     gender: 'female',
//     addresses: [
//       {
//         id: 'ADDR-002',
//         type: 'shipping',
//         name: 'Jane Smith',
//         address1: '456 Oak Avenue',
//         address2: '',
//         city: 'Delhi',
//         state: 'Delhi',
//         postalCode: '110001',
//         country: 'India',
//         phone: '+91 9876543211',
//         isDefault: true,
//       },
//     ],
//     tags: ['premium', 'early-adopter'],
//     notes: 'Interested in new product launches',
//     marketingOptIn: true,
//     createdAt: '2023-11-15T10:30:00Z',
//     updatedAt: '2024-01-14T16:20:00Z',
//   },
//   {
//     id: 'CUST-003',
//     firstName: 'Mike',
//     lastName: 'Johnson',
//     email: '<EMAIL>',
//     phone: '+91 9876543212',
//     status: 'active',
//     emailVerified: true,
//     phoneVerified: true,
//     totalOrders: 2,
//     totalSpent: 18999,
//     averageOrderValue: 9500,
//     lastOrderDate: '2024-01-13T11:15:00Z',
//     dateOfBirth: '1992-03-10',
//     gender: 'male',
//     addresses: [
//       {
//         id: 'ADDR-003',
//         type: 'shipping',
//         name: 'Mike Johnson',
//         address1: '789 Pine Road',
//         address2: 'Building C',
//         city: 'Bangalore',
//         state: 'Karnataka',
//         postalCode: '560001',
//         country: 'India',
//         phone: '+91 9876543212',
//         isDefault: true,
//       },
//     ],
//     tags: ['tech-enthusiast'],
//     notes: 'Prefers weekend delivery',
//     marketingOptIn: false,
//     createdAt: '2023-10-20T12:45:00Z',
//     updatedAt: '2024-01-13T18:30:00Z',
//   },
//   {
//     id: 'CUST-004',
//     firstName: 'Sarah',
//     lastName: 'Wilson',
//     email: '<EMAIL>',
//     phone: '+91 **********',
//     status: 'inactive',
//     emailVerified: true,
//     phoneVerified: true,
//     totalOrders: 1,
//     totalSpent: 17999,
//     averageOrderValue: 17999,
//     lastOrderDate: '2024-01-12T09:45:00Z',
//     dateOfBirth: '1988-11-05',
//     gender: 'female',
//     addresses: [
//       {
//         id: 'ADDR-004',
//         type: 'shipping',
//         name: 'Sarah Wilson',
//         address1: '321 Elm Street',
//         address2: '',
//         city: 'Chennai',
//         state: 'Tamil Nadu',
//         postalCode: '600001',
//         country: 'India',
//         phone: '+91 **********',
//         isDefault: true,
//       },
//     ],
//     tags: ['one-time-buyer'],
//     notes: 'Requested account deactivation',
//     marketingOptIn: false,
//     createdAt: '2024-01-10T07:30:00Z',
//     updatedAt: '2024-01-12T15:20:00Z',
//   },
//   {
//     id: 'CUST-005',
//     firstName: 'David',
//     lastName: 'Brown',
//     email: '<EMAIL>',
//     phone: '+91 **********',
//     status: 'suspended',
//     emailVerified: false,
//     phoneVerified: false,
//     totalOrders: 0,
//     totalSpent: 0,
//     averageOrderValue: 0,
//     lastOrderDate: null,
//     dateOfBirth: '1995-07-18',
//     gender: 'male',
//     addresses: [],
//     tags: ['suspicious-activity'],
//     notes: 'Account suspended due to fraudulent activity',
//     marketingOptIn: false,
//     createdAt: '2024-01-08T14:20:00Z',
//     updatedAt: '2024-01-09T09:15:00Z',
//   },
// ];

// // GET /api/admin/customers - List all customers with admin features
// export async function GET(request: NextRequest) {
//   try {
//     const { searchParams } = new URL(request.url);
//     const page = parseInt(searchParams.get('page') || '1');
//     const limit = parseInt(searchParams.get('limit') || '10');
//     const search = searchParams.get('search') || '';
//     const status = searchParams.get('status') || '';
//     const emailVerified = searchParams.get('emailVerified') || '';
//     const sortBy = searchParams.get('sortBy') || 'createdAt';
//     const sortOrder = searchParams.get('sortOrder') || 'desc';

//     console.log('[Admin Customers API] GET request:', {
//       page,
//       limit,
//       search,
//       status,
//       emailVerified,
//       sortBy,
//       sortOrder,
//     });

//     // Filter customers based on search criteria
//     let filteredCustomers = [...MOCK_ADMIN_CUSTOMERS];

//     // Apply search filter
//     if (search) {
//       filteredCustomers = filteredCustomers.filter(
//         customer =>
//           customer.firstName.toLowerCase().includes(search.toLowerCase()) ||
//           customer.lastName.toLowerCase().includes(search.toLowerCase()) ||
//           customer.email.toLowerCase().includes(search.toLowerCase()) ||
//           customer.phone.includes(search) ||
//           customer.id.toLowerCase().includes(search.toLowerCase())
//       );
//     }

//     // Apply status filter
//     if (status) {
//       filteredCustomers = filteredCustomers.filter(
//         customer => customer.status === status
//       );
//     }

//     // Apply email verification filter
//     if (emailVerified) {
//       const isVerified = emailVerified === 'true';
//       filteredCustomers = filteredCustomers.filter(
//         customer => customer.emailVerified === isVerified
//       );
//     }

//     // Apply sorting
//     filteredCustomers.sort((a, b) => {
//       let aValue = a[sortBy as keyof typeof a];
//       let bValue = b[sortBy as keyof typeof b];

//       if (typeof aValue === 'string') {
//         aValue = aValue.toLowerCase();
//         bValue = (bValue as string).toLowerCase();
//       }

//       if (sortOrder === 'desc') {
//         return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
//       } else {
//         return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
//       }
//     });

//     // Apply pagination
//     const offset = (page - 1) * limit;
//     const paginatedCustomers = filteredCustomers.slice(offset, offset + limit);

//     const response = {
//       customers: paginatedCustomers,
//       pagination: {
//         page,
//         limit,
//         total: filteredCustomers.length,
//         totalPages: Math.ceil(filteredCustomers.length / limit),
//         hasNext: offset + limit < filteredCustomers.length,
//         hasPrev: page > 1,
//       },
//       filters: {
//         search,
//         status,
//         emailVerified,
//         sortBy,
//         sortOrder,
//       },
//     };

//     console.log('[Admin Customers API] Returning:', {
//       count: paginatedCustomers.length,
//       total: filteredCustomers.length,
//       page,
//     });

//     return NextResponse.json(response);
//   } catch (error) {
//     console.error('[Admin Customers API] Error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to fetch customers',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }

// // POST /api/admin/customers - Create new customer (admin only)
// export async function POST(request: NextRequest) {
//   try {
//     const body = await request.json();
//     console.log('[Admin Customers API] POST request:', body);

//     // Validate required fields
//     const requiredFields = ['firstName', 'lastName', 'email'];
//     for (const field of requiredFields) {
//       if (!body[field]) {
//         return NextResponse.json(
//           { error: `Missing required field: ${field}` },
//           { status: 400 }
//         );
//       }
//     }

//     // Check if email already exists
//     const existingCustomer = MOCK_ADMIN_CUSTOMERS.find(
//       c => c.email === body.email
//     );
//     if (existingCustomer) {
//       return NextResponse.json(
//         { error: 'Customer with this email already exists' },
//         { status: 409 }
//       );
//     }

//     // Create new customer
//     const newCustomer = {
//       id: `CUST-${String(MOCK_ADMIN_CUSTOMERS.length + 1).padStart(3, '0')}`,
//       firstName: body.firstName,
//       lastName: body.lastName,
//       email: body.email,
//       phone: body.phone || '',
//       status: body.status || 'active',
//       emailVerified: body.emailVerified || false,
//       phoneVerified: body.phoneVerified || false,
//       totalOrders: 0,
//       totalSpent: 0,
//       averageOrderValue: 0,
//       lastOrderDate: null,
//       dateOfBirth: body.dateOfBirth || null,
//       gender: body.gender || null,
//       addresses: body.addresses || [],
//       tags: body.tags || [],
//       notes: body.notes || '',
//       marketingOptIn: body.marketingOptIn || false,
//       createdAt: new Date().toISOString(),
//       updatedAt: new Date().toISOString(),
//     };

//     // Add to mock data (in real app, this would be saved to database)
//     MOCK_ADMIN_CUSTOMERS.push(newCustomer);

//     console.log('[Admin Customers API] Created customer:', newCustomer.id);

//     return NextResponse.json(
//       { message: 'Customer created successfully', customer: newCustomer },
//       { status: 201 }
//     );
//   } catch (error) {
//     console.error('[Admin Customers API] Create error:', error);
//     return NextResponse.json(
//       {
//         error: 'Failed to create customer',
//         details: error instanceof Error ? error.message : 'Unknown error',
//       },
//       { status: 500 }
//     );
//   }
// }
