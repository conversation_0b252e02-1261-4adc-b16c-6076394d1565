// import { NextRequest, NextResponse } from 'next/server';
// import { getProducts } from '@/lib/strapi-api';

// // GET /api/admin/products/all - Get all products from Strapi CMS for admin selection
// export async function GET(request: NextRequest) {
//   try {
//     const { searchParams } = new URL(request.url);

//     // Parse query parameters
//     const search = searchParams.get('search') || '';
//     const featured = searchParams.get('featured');
//     const limit = searchParams.get('limit')
//       ? parseInt(searchParams.get('limit')!)
//       : 100;

//     console.log(
//       '🚀 Admin Products All API: Fetching all products from Strapi CMS...'
//     );
//     console.log('📊 Parameters:', { search, featured, limit });

//     // Fetch all products from Strapi CMS
//     const response = await getProducts({
//       pageSize: limit,
//       populate: 'categories,images',
//     });

//     console.log('✅ Admin Products All API: Successfully fetched products');
//     console.log('📊 Products count:', response.data?.length || 0);

//     // Transform products to match admin interface format
//     const products = response.data || [];
//     let transformedProducts = products.map(product => ({
//       id: product.id.toString(),
//       name: product.name,
//       image:
//         product.images?.[0]?.url ||
//         `https://via.placeholder.com/300x300/3B82F6/FFFFFF?text=${encodeURIComponent(
//           product.name
//         )}`,
//       price: product.sale_price || product.price || 0,
//       originalPrice: product.sale_price ? product.price : undefined,
//       discount:
//         product.sale_price && product.price
//           ? Math.round(
//               ((product.price - product.sale_price) / product.price) * 100
//             )
//           : undefined,
//       category: product.categories?.[0]?.name || 'Uncategorized',
//       rating: 4.5, // Default rating - could be enhanced with actual ratings
//       reviews: Math.floor(Math.random() * 500) + 50, // Mock reviews for now
//       inStock: product.inventory_quantity > 0,
//       featured: product.featured || false,
//       topSelling: false, // Could be enhanced with sales data
//       hotDeal: product.sale_price ? true : false,
//       sku: product.sku,
//       status: product.product_status,
//       inventory_quantity: product.inventory_quantity,
//       description: product.description,
//       short_description: product.short_description,
//       createdAt: product.createdAt,
//       updatedAt: product.updatedAt,
//     }));

//     // Filter by search term if provided
//     if (search) {
//       transformedProducts = transformedProducts.filter(
//         product =>
//           product.name.toLowerCase().includes(search.toLowerCase()) ||
//           product.category.toLowerCase().includes(search.toLowerCase()) ||
//           product.sku?.toLowerCase().includes(search.toLowerCase())
//       );
//     }

//     // Filter by featured status if provided
//     if (featured !== null) {
//       const isFeatured = featured === 'true';
//       transformedProducts = transformedProducts.filter(
//         product => product.featured === isFeatured
//       );
//     }

//     return NextResponse.json({
//       success: true,
//       products: transformedProducts,
//       count: transformedProducts.length,
//       total: response.meta?.pagination?.total || transformedProducts.length,
//       timestamp: new Date().toISOString(),
//     });
//   } catch (error) {
//     console.error('❌ Admin Products All API: Error fetching products:', error);

//     return NextResponse.json(
//       {
//         success: false,
//         error: error instanceof Error ? error.message : 'Unknown error',
//         products: [],
//         count: 0,
//         timestamp: new Date().toISOString(),
//       },
//       { status: 500 }
//     );
//   }
// }
