/**
 * Authentication Login API Route
 *
 * Handles login for different authentication providers
 * Supports development mode, Keycloak, and OneSSO
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthConfig, getPermissionsForRole } from '@/lib/auth/config';
import jwt from 'jsonwebtoken';

// CORS headers for authentication endpoints
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tenant-ID',
  'Access-Control-Max-Age': '86400',
};

// Helper function to create JSON response with CORS headers
function createCorsResponse(data: any, options: { status?: number } = {}) {
  return NextResponse.json(data, {
    status: options.status || 200,
    headers: corsHeaders,
  });
}

// Development mode mock users
const DEV_USERS = [
  {
    id: '1',
    email: 'demo',
    name: 'Demo Admin',
    role: 'admin' as const,
    avatar: '/images/avatars/admin.svg',
    permissions: ['*'],
    isActive: true,
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Demo Seller',
    role: 'seller' as const,
    avatar: '/images/avatars/seller.svg',
    permissions: getPermissionsForRole('seller'),
    isActive: true,
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin' as const,
    avatar: '/images/avatars/admin.svg',
    permissions: ['*'],
    isActive: true,
  },
];

// Handle preflight OPTIONS requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  });
}

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    const config = getAuthConfig();

    console.log('🔐 Login attempt:', { email, provider: config.provider });

    // Validate input
    if (!email || !password) {
      return createCorsResponse(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    switch (config.provider) {
      case 'development':
        return handleDevelopmentLogin(email, password, config);

      case 'keycloak':
        return handleKeycloakLogin(email, password, config);

      case 'onesso':
        return handleOneSSOLogin(email, password, config);

      default:
        return createCorsResponse(
          { error: 'Invalid authentication provider' },
          { status: 500 }
        );
    }
  } catch (error) {
    console.error('❌ Login API error:', error);
    return createCorsResponse(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Handle development mode login
 */
function handleDevelopmentLogin(email: string, password: string, config: any) {
  console.log('🔧 Development mode login attempt');

  // Define password mapping for development users
  const DEV_PASSWORDS: Record<string, string> = {
    demo: 'demo',
    '<EMAIL>': 'demo',
    '<EMAIL>': 'supersecret',
  };

  // Check if user exists and password matches
  const user = DEV_USERS.find(u => u.email === email);
  if (user && DEV_PASSWORDS[email] === password) {
    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      config.jwt.secret,
      { expiresIn: config.jwt.refreshExpiresIn }
    );

    console.log('✅ Development login successful:', user.email);

    return createCorsResponse({
      data: {
        user: {
          ...user,
          lastLogin: new Date(),
          token,
        },
        token,
        refresh_token: refreshToken,
      },
    });
  }

  console.log('❌ Development login failed: Invalid credentials');
  return createCorsResponse({ error: 'Invalid credentials' }, { status: 401 });
}

/**
 * Handle Keycloak login (redirect-based)
 */
function handleKeycloakLogin(email: string, password: string, config: any) {
  console.log('🔐 Keycloak login - redirecting to Keycloak');

  // For Keycloak, we typically redirect to the Keycloak login page
  // This endpoint would be used for direct API authentication if supported
  const keycloakLoginUrl = `${config.keycloak.url}/realms/${config.keycloak.realm}/protocol/openid-connect/auth`;

  return createCorsResponse({
    redirect: true,
    url: keycloakLoginUrl,
    message: 'Redirect to Keycloak for authentication',
  });
}

/**
 * Handle OneSSO login (redirect-based)
 */
function handleOneSSOLogin(email: string, password: string, config: any) {
  console.log('🔐 OneSSO login - redirecting to OneSSO');

  // For OneSSO, we redirect to the OneSSO authorization endpoint
  const params = new URLSearchParams({
    client_id: config.onesso.clientId,
    redirect_uri: config.onesso.redirectUri,
    response_type: config.onesso.responseType,
    scope: config.onesso.scope,
    state: generateState(),
  });

  const onessoLoginUrl = `${
    config.onesso.url
  }/oauth/authorize?${params.toString()}`;

  return createCorsResponse({
    redirect: true,
    url: onessoLoginUrl,
    message: 'Redirect to OneSSO for authentication',
  });
}

/**
 * Generate state parameter for OAuth
 */
function generateState(): string {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}
