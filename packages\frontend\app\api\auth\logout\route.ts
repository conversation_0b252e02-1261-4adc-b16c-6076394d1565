/**
 * Authentication Logout API Route
 *
 * Handles logout for different authentication providers
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthConfig, getAuthUrls } from '@/lib/auth/config';

// CORS headers for authentication endpoints
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tenant-ID',
  'Access-Control-Max-Age': '86400',
};

// Helper function to create JSON response with CORS headers
function createCorsResponse(data: any, options: { status?: number } = {}) {
  return NextResponse.json(data, {
    status: options.status || 200,
    headers: corsHeaders,
  });
}

// Handle preflight OPTIONS requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  });
}

export async function POST(request: NextRequest) {
  try {
    const config = getAuthConfig();

    console.log('🔓 Logout request:', { provider: config.provider });

    switch (config.provider) {
      case 'development':
        return handleDevelopmentLogout();

      case 'keycloak':
        return handleKeycloakLogout(config);

      case 'onesso':
        return handleOneSSOLogout(config);

      default:
        return createCorsResponse(
          { error: 'Invalid authentication provider' },
          { status: 500 }
        );
    }
  } catch (error) {
    console.error('❌ Logout API error:', error);
    return createCorsResponse(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Handle development mode logout
 */
function handleDevelopmentLogout() {
  console.log('🔧 Development mode logout');

  return createCorsResponse({
    data: {
      message: 'Logged out successfully',
      redirect: '/auth/login',
    },
  });
}

/**
 * Handle Keycloak logout
 */
function handleKeycloakLogout(config: any) {
  console.log('🔐 Keycloak logout');

  const urls = getAuthUrls();
  const logoutUrl = `${urls.keycloak.logout}?redirect_uri=${encodeURIComponent(
    window.location.origin
  )}`;

  return createCorsResponse({
    data: {
      message: 'Redirecting to Keycloak logout',
      redirect: logoutUrl,
      external: true,
    },
  });
}

/**
 * Handle OneSSO logout
 */
function handleOneSSOLogout(config: any) {
  console.log('🔐 OneSSO logout');

  const urls = getAuthUrls();
  const logoutUrl = `${urls.onesso.logout}?redirect_uri=${encodeURIComponent(
    window.location.origin
  )}`;

  return createCorsResponse({
    data: {
      message: 'Redirecting to OneSSO logout',
      redirect: logoutUrl,
      external: true,
    },
  });
}
