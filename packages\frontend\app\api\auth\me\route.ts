/**
 * Get Current User API Route
 *
 * Returns the current authenticated user's information
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthConfig } from '@/lib/auth/config';
import jwt from 'jsonwebtoken';

// CORS headers for authentication endpoints
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tenant-ID',
  'Access-Control-Max-Age': '86400',
};

// Helper function to create JSON response with CORS headers
function createCorsResponse(data: any, options: { status?: number } = {}) {
  return NextResponse.json(data, {
    status: options.status || 200,
    headers: corsHeaders,
  });
}

// Handle preflight OPTIONS requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  });
}

export async function GET(request: NextRequest) {
  try {
    const config = getAuthConfig();
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return createCorsResponse(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);

    try {
      // Verify JWT token
      const decoded = jwt.verify(token, config.jwt.secret) as any;

      // Mock user data for development mode
      const user = {
        id: decoded.userId,
        email: decoded.email,
        name: decoded.email === 'demo' ? 'Demo Admin' : 'Demo User',
        role: decoded.role || 'customer',
        avatar: '/images/avatars/default.svg',
        permissions: decoded.role === 'admin' ? ['*'] : ['products:view'],
        lastLogin: new Date(),
        isActive: true,
      };

      console.log('✅ User profile retrieved:', user.email);

      return createCorsResponse({
        data: user,
      });
    } catch (jwtError) {
      console.error('❌ Invalid token:', jwtError);
      return createCorsResponse(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('❌ Get user API error:', error);
    return createCorsResponse(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
