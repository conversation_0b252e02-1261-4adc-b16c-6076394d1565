import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  const filename = params.filename;
  
  try {
    // If requesting demo.jpg, serve demo.svg instead
    if (filename === 'demo.jpg') {
      const svgPath = join(process.cwd(), 'public', 'images', 'avatars', 'demo.svg');
      const svgContent = await readFile(svgPath, 'utf-8');
      
      return new NextResponse(svgContent, {
        headers: {
          'Content-Type': 'image/svg+xml',
          'Cache-Control': 'public, max-age=31536000',
        },
      });
    }
    
    // For other files, try to serve them directly
    const filePath = join(process.cwd(), 'public', 'images', 'avatars', filename);
    const fileContent = await readFile(filePath);
    
    // Determine content type based on file extension
    const contentType = filename.endsWith('.svg') ? 'image/svg+xml' : 
                       filename.endsWith('.png') ? 'image/png' : 
                       filename.endsWith('.jpg') || filename.endsWith('.jpeg') ? 'image/jpeg' : 
                       'application/octet-stream';
    
    return new NextResponse(fileContent, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000',
      },
    });
  } catch (error) {
    // If file not found, return a default avatar SVG
    const defaultSvg = `
      <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="50" cy="50" r="50" fill="#6B7280"/>
        <circle cx="50" cy="35" r="15" fill="#FFFFFF"/>
        <path d="M20 85C20 70 32 60 50 60C68 60 80 70 80 85" fill="#FFFFFF"/>
      </svg>
    `;
    
    return new NextResponse(defaultSvg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=31536000',
      },
    });
  }
}
