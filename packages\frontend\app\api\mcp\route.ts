import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import {
  getTenantIdFromNextRequest,
  addTenantToResponse,
} from '../../../lib/server-tenant';

// Mock MCP server URL (using the mock backend)
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:9000';

// Use mock backend for now
const MOCK_BACKEND_URL = 'http://localhost:9000';

// Mock data for MCP testing
const MOCK_MCP_DATA = {
  sync_operations: new Map(),
  reservations: new Map(),
  webhooks: new Map(),
  tools: [
    {
      name: 'medusa_store_list_products',
      description: 'List products from Medusa store',
      category: 'store',
      parameters: {
        type: 'object',
        properties: {
          limit: { type: 'integer', default: 10 },
          offset: { type: 'integer', default: 0 },
          expand: {
            type: 'string',
            description: 'Comma-separated list of relations to expand',
          },
        },
      },
      examples: [{ limit: 10, offset: 0, expand: 'variants,images' }],
    },
    {
      name: 'medusa_admin_create_product',
      description: 'Create a new product in Medusa',
      category: 'admin',
      parameters: {
        type: 'object',
        required: ['title', 'handle'],
        properties: {
          title: { type: 'string' },
          handle: { type: 'string' },
          description: { type: 'string' },
          status: { type: 'string', enum: ['draft', 'published'] },
        },
      },
    },
  ],
};

/**
 * Handle MCP API requests - Legacy endpoint for backward compatibility
 * @param req The request object
 * @returns The response object
 */
export async function POST(req: NextRequest) {
  try {
    // Get the request body
    const body = await req.json();
    const { tool, params, context } = body;

    // Get tenant ID from middleware (falls back to default if not present)
    const tenantId = getTenantIdFromNextRequest(req);

    console.log(`[MCP API] Legacy POST request: ${tool}`, { params, tenantId });

    // Use the mock backend directly instead of MCP
    let response;

    try {
      if (tool === 'getProducts') {
        // Extract parameters
        const { limit = 10, offset = 0, category } = params;

        // Build query parameters
        let url = `${MOCK_BACKEND_URL}/store/products?limit=${limit}&offset=${offset}`;

        // Add category filter if provided (in a real app, this would be handled by the backend)
        if (category) {
          url += `&category=${encodeURIComponent(category)}`;
          console.log(`Filtering products by category: ${category}`);
        }

        // Call the mock backend directly for products
        response = await axios.get(url, {
          headers: {
            'Content-Type': 'application/json',
            'x-tenant-id': tenantId,
          },
        });

        // Create the response with the products data
        const productsResponse = NextResponse.json(response.data);

        // Add tenant ID to the response
        return addTenantToResponse(productsResponse, tenantId);
      } else if (tool === 'getProduct') {
        // Call the mock backend directly for a single product
        const productId = params.id;
        response = await axios.get(
          `${MOCK_BACKEND_URL}/store/products/${productId}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-tenant-id': tenantId,
            },
          }
        );

        // Create the response with the product data
        const productResponse = NextResponse.json(response.data);

        // Add tenant ID to the response
        return addTenantToResponse(productResponse, tenantId);
      } else {
        // For other tools, use the original MCP approach (which will likely fail for now)
        console.log('Using fallback MCP approach for tool:', tool);
        response = await axios.post(`${MCP_SERVER_URL}/rpc`, {
          jsonrpc: '2.0',
          method: 'mcp.tool',
          params: {
            name: tool,
            input: params,
            context: {
              ...context,
              headers: {
                ...context?.headers,
                'x-tenant-id': tenantId,
              },
            },
          },
          id: Date.now(),
        });

        // Check for errors
        if (response.data.error) {
          console.error('MCP API error:', response.data.error);

          // Create the error response
          const errorResponse = NextResponse.json(
            { error: response.data.error.message || 'Error calling MCP tool' },
            { status: 500 }
          );

          // Add tenant ID to the error response
          return addTenantToResponse(errorResponse, tenantId);
        }

        // Create the response with the result data
        const resultResponse = NextResponse.json(response.data.result);

        // Add tenant ID to the response
        return addTenantToResponse(resultResponse, tenantId);
      }
    } catch (innerError) {
      console.error(`Error calling ${tool}:`, innerError);

      // If we're in development mode, return mock data for testing
      if (process.env.NODE_ENV === 'development' && tool === 'getProducts') {
        console.log('Returning mock product data for development');

        // Create the mock data response
        const mockResponse = NextResponse.json({
          products: [
            {
              id: 'mock_prod_1',
              title: 'Mock Product 1',
              description: 'This is a mock product for testing',
              thumbnail: 'https://picsum.photos/200',
              handle: 'mock-product-1',
              status: 'published',
              variants: [
                {
                  id: 'mock_variant_1',
                  title: 'Default Variant',
                  prices: [{ amount: 1000, currency_code: 'inr' }],
                  inventory_quantity: 10,
                },
              ],
              tenant_id: tenantId,
            },
            {
              id: 'mock_prod_2',
              title: 'Mock Product 2',
              description: 'Another mock product for testing',
              thumbnail: 'https://picsum.photos/200?random=2',
              handle: 'mock-product-2',
              status: 'published',
              variants: [
                {
                  id: 'mock_variant_2',
                  title: 'Default Variant',
                  prices: [{ amount: 2000, currency_code: 'inr' }],
                  inventory_quantity: 5,
                },
              ],
              tenant_id: tenantId,
            },
          ],
          count: 2,
          offset: 0,
          limit: 10,
        });

        // Add tenant ID to the response
        return addTenantToResponse(mockResponse, tenantId);
      }

      throw innerError;
    }
  } catch (error) {
    console.error('MCP API error:', error);

    // Create a more detailed error response
    const errorResponse = NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        path: req.url,
        details: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    );

    // Add tenant ID to the error response
    return addTenantToResponse(errorResponse, getTenantIdFromNextRequest(req));
  }
}
