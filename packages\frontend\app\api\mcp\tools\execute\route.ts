import { NextRequest, NextResponse } from 'next/server';
import { getTenantIdFromNextRequest } from '../../../../../lib/server-tenant';

/**
 * MCP Tools Execute Endpoint
 * POST /api/mcp/tools/execute - Execute an MCP tool
 */
export async function POST(request: NextRequest) {
  try {
    const tenantId = getTenantIdFromNextRequest(request);
    const body = await request.json();
    const { tool_name, parameters, context } = body;

    console.log(
      '[MCP Tools Execute] Executing tool:',
      tool_name,
      'for tenant:',
      tenantId
    );
    console.log('[MCP Tools Execute] Parameters:', parameters);

    // Validate MCP token
    const mcpToken = request.headers.get('X-MCP-Token');
    if (!mcpToken) {
      return NextResponse.json(
        { error: 'MCP token required', error_code: 'MISSING_MCP_TOKEN' },
        { status: 401 }
      );
    }

    const executionId = `exec_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    const startTime = Date.now();

    // Mock tool execution based on tool name
    let result;
    let status = 'success';

    switch (tool_name) {
      case 'medusa_store_list_products':
        result = {
          products: [
            {
              id: 'prod_mcp_001',
              title: 'MCP Test Product 1',
              handle: 'mcp-test-product-1',
              status: 'published',
              created_at: new Date().toISOString(),
              variants: [
                {
                  id: 'var_mcp_001',
                  title: 'Default Variant',
                  sku: 'MCP-001',
                  inventory_quantity: 100,
                },
              ],
            },
            {
              id: 'prod_mcp_002',
              title: 'MCP Test Product 2',
              handle: 'mcp-test-product-2',
              status: 'published',
              created_at: new Date().toISOString(),
              variants: [
                {
                  id: 'var_mcp_002',
                  title: 'Default Variant',
                  sku: 'MCP-002',
                  inventory_quantity: 50,
                },
              ],
            },
          ],
          count: 2,
          limit: parameters?.limit || 10,
          offset: parameters?.offset || 0,
        };
        break;

      case 'medusa_admin_create_product':
        result = {
          product: {
            id: `prod_${Date.now()}`,
            title: parameters?.title || 'New Product',
            handle: parameters?.handle || 'new-product',
            description: parameters?.description || '',
            status: parameters?.status || 'draft',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        };
        break;

      case 'medusa_admin_update_inventory':
        result = {
          inventory_item: {
            variant_id: parameters?.variant_id,
            location_id: parameters?.location_id || 'loc_default',
            stocked_quantity: parameters?.quantity || 0,
            reserved_quantity: 0,
            incoming_quantity: 0,
            updated_at: new Date().toISOString(),
          },
        };
        break;

      case 'ondc_sync_products':
        result = {
          sync_id: `sync_${Date.now()}`,
          direction: parameters?.direction || 'bidirectional',
          dry_run: parameters?.dry_run || false,
          products_affected: Math.floor(Math.random() * 10) + 1,
          status: 'in_progress',
          started_at: new Date().toISOString(),
          estimated_completion: new Date(Date.now() + 300000).toISOString(), // 5 minutes
        };
        break;

      case 'webhook_register':
        result = {
          webhook: {
            id: `webhook_${Date.now()}`,
            url: parameters?.url,
            events: parameters?.events || [],
            secret: parameters?.secret ? '***masked***' : null,
            status: 'active',
            created_at: new Date().toISOString(),
          },
        };
        break;

      default:
        status = 'error';
        result = {
          error: 'Unknown tool',
          error_code: 'TOOL_NOT_FOUND',
          available_tools: [
            'medusa_store_list_products',
            'medusa_admin_create_product',
            'medusa_admin_update_inventory',
            'ondc_sync_products',
            'webhook_register',
          ],
        };
        break;
    }

    const executionTime = Date.now() - startTime;

    const response = {
      tool_name,
      execution_id: executionId,
      status,
      result,
      execution_time: executionTime,
      timestamp: new Date().toISOString(),
      tenant_id: tenantId,
      context: context || {},
    };

    console.log('[MCP Tools Execute] Tool execution completed:', {
      tool_name,
      execution_id: executionId,
      status,
      execution_time: executionTime,
    });

    const statusCode = status === 'success' ? 200 : 400;
    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    console.error('[MCP Tools Execute] Error:', error);

    return NextResponse.json(
      {
        error: 'Tool execution failed',
        tool_name: 'unknown',
        error_code: 'EXECUTION_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
