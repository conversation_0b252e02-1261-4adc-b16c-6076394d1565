import { NextRequest, NextResponse } from 'next/server';
import { getTenantIdFromNextRequest } from '../../../../../lib/server-tenant';

/**
 * MCP Tools List Endpoint
 * GET /api/mcp/tools/list - List available MCP tools
 */
export async function GET(request: NextRequest) {
  try {
    const tenantId = getTenantIdFromNextRequest(request);
    console.log('[MCP Tools List] Request for tenant:', tenantId);

    const tools = [
      {
        name: 'medusa_store_list_products',
        description:
          'List products from Medusa store with filtering and pagination',
        category: 'store',
        parameters: {
          type: 'object',
          properties: {
            limit: {
              type: 'integer',
              default: 10,
              minimum: 1,
              maximum: 100,
              description: 'Number of products to return',
            },
            offset: {
              type: 'integer',
              default: 0,
              minimum: 0,
              description: 'Number of products to skip',
            },
            expand: {
              type: 'string',
              description:
                'Comma-separated list of relations to expand (variants,images,categories)',
            },
            category_id: {
              type: 'string',
              description: 'Filter by category ID',
            },
          },
        },
        examples: [
          { limit: 10, offset: 0, expand: 'variants,images' },
          { limit: 5, category_id: 'cat_electronics', expand: 'variants' },
        ],
      },
      {
        name: 'medusa_admin_create_product',
        description: 'Create a new product in Medusa admin',
        category: 'admin',
        parameters: {
          type: 'object',
          required: ['title', 'handle'],
          properties: {
            title: { type: 'string', description: 'Product title' },
            handle: {
              type: 'string',
              description: 'URL-friendly product handle',
            },
            description: { type: 'string', description: 'Product description' },
            status: {
              type: 'string',
              enum: ['draft', 'published'],
              default: 'draft',
              description: 'Product status',
            },
            variants: {
              type: 'array',
              description: 'Product variants',
              items: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  sku: { type: 'string' },
                  prices: { type: 'array' },
                },
              },
            },
          },
        },
        examples: [
          {
            title: 'Premium Headphones',
            handle: 'premium-headphones',
            description: 'High-quality wireless headphones',
            status: 'published',
          },
        ],
      },
      {
        name: 'medusa_admin_update_inventory',
        description: 'Update product inventory levels',
        category: 'admin',
        parameters: {
          type: 'object',
          required: ['variant_id', 'quantity'],
          properties: {
            variant_id: { type: 'string', description: 'Product variant ID' },
            quantity: {
              type: 'integer',
              minimum: 0,
              description: 'New inventory quantity',
            },
            location_id: {
              type: 'string',
              description: 'Inventory location ID',
            },
          },
        },
        examples: [
          {
            variant_id: 'var_123',
            quantity: 100,
            location_id: 'loc_warehouse',
          },
        ],
      },
      {
        name: 'ondc_sync_products',
        description: 'Synchronize products between ONDC and Medusa',
        category: 'sync',
        parameters: {
          type: 'object',
          properties: {
            direction: {
              type: 'string',
              enum: ['ondc_to_medusa', 'medusa_to_ondc', 'bidirectional'],
              default: 'bidirectional',
              description: 'Sync direction',
            },
            product_ids: {
              type: 'array',
              items: { type: 'string' },
              description: 'Specific product IDs to sync (optional)',
            },
            dry_run: {
              type: 'boolean',
              default: false,
              description: 'Preview changes without applying them',
            },
          },
        },
        examples: [
          { direction: 'ondc_to_medusa', dry_run: true },
          { direction: 'bidirectional', product_ids: ['prod_123', 'prod_456'] },
        ],
      },
      {
        name: 'webhook_register',
        description: 'Register a webhook for real-time notifications',
        category: 'utility',
        parameters: {
          type: 'object',
          required: ['url', 'events'],
          properties: {
            url: {
              type: 'string',
              format: 'uri',
              description: 'Webhook endpoint URL',
            },
            events: {
              type: 'array',
              items: {
                type: 'string',
                enum: [
                  'product.created',
                  'product.updated',
                  'product.deleted',
                  'order.created',
                  'order.updated',
                  'order.fulfilled',
                  'inventory.updated',
                  'customer.created',
                ],
              },
              description: 'Events to subscribe to',
            },
            secret: {
              type: 'string',
              description: 'Webhook secret for verification',
            },
          },
        },
        examples: [
          {
            url: 'https://example.com/webhook',
            events: ['product.created', 'order.updated'],
            secret: 'webhook_secret_123',
          },
        ],
      },
    ];

    const response = {
      tools,
      total_count: tools.length,
      tenant_id: tenantId,
      timestamp: new Date().toISOString(),
    };

    console.log('[MCP Tools List] Returning', tools.length, 'tools');

    return NextResponse.json(response);
  } catch (error) {
    console.error('[MCP Tools List] Error:', error);

    return NextResponse.json(
      {
        error: 'Failed to list MCP tools',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
