import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

/**
 * ONDC Search API Route
 *
 * Handles ONDC product search requests from BAPs (Buyer App Platforms)
 * Integrated from ondc-seller-app package
 */

interface ONDCSearchRequest {
  context: {
    domain: string;
    country: string;
    city: string;
    action: string;
    core_version: string;
    bap_id: string;
    bap_uri: string;
    bpp_id: string;
    bpp_uri: string;
    transaction_id: string;
    message_id: string;
    timestamp: string;
    key?: string;
    ttl?: string;
  };
  message: {
    intent?: {
      item?: {
        descriptor?: {
          name?: string;
        };
      };
      category?: {
        id?: string;
      };
      fulfillment?: {
        type?: string;
      };
      payment?: {
        '@ondc/org/buyer_app_finder_fee_type'?: string;
        '@ondc/org/buyer_app_finder_fee_amount'?: string;
      };
    };
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: ONDCSearchRequest = await request.json();
    const headersList = headers();
    const tenantId = headersList.get('x-tenant-id') || 'default';

    console.log('[ONDC Search] Processing search request:', {
      transactionId: body.context.transaction_id,
      messageId: body.context.message_id,
      tenantId,
    });

    // Extract search criteria
    const searchCriteria = {
      query: body.message.intent?.item?.descriptor?.name || '',
      category: body.message.intent?.category?.id || '',
      fulfillmentType: body.message.intent?.fulfillment?.type || '',
    };

    // In development mode, return mock response
    if (process.env.NODE_ENV === 'development') {
      const mockResponse = {
        context: {
          ...body.context,
          action: 'on_search',
          timestamp: new Date().toISOString(),
        },
        message: {
          catalog: {
            'bpp/descriptor': {
              name: 'ONDC Seller Platform',
              symbol: 'OSP',
              short_desc: 'Multi-tenant ONDC seller platform',
              long_desc:
                'Comprehensive ONDC seller platform with multi-tenant support',
              images: [],
            },
            'bpp/providers': [
              {
                id: tenantId,
                descriptor: {
                  name: `Seller ${tenantId}`,
                  symbol: tenantId.toUpperCase(),
                  short_desc: `Products from ${tenantId}`,
                  long_desc: `Quality products and services from ${tenantId}`,
                  images: [],
                },
                categories: [
                  {
                    id: 'electronics',
                    descriptor: {
                      name: 'Electronics',
                    },
                  },
                  {
                    id: 'fashion',
                    descriptor: {
                      name: 'Fashion',
                    },
                  },
                ],
                items: [
                  {
                    id: 'item_1',
                    descriptor: {
                      name: 'Sample Product 1',
                      short_desc: 'High-quality sample product',
                      long_desc: 'Detailed description of sample product 1',
                      images: [],
                    },
                    price: {
                      currency: 'INR',
                      value: '999.00',
                    },
                    category_id: 'electronics',
                    fulfillment_id: 'fulfillment_1',
                    location_id: 'location_1',
                    '@ondc/org/available_on_cod': false,
                    '@ondc/org/cancellable': true,
                    '@ondc/org/returnable': true,
                    '@ondc/org/seller_pickup_return': false,
                    '@ondc/org/time_to_ship': 'PT24H',
                    '@ondc/org/contact_details_consumer_care':
                      'Support Team,<EMAIL>,1800-123-4567',
                  },
                ],
                fulfillments: [
                  {
                    id: 'fulfillment_1',
                    type: 'Delivery',
                    contact: {
                      phone: '1800-123-4567',
                      email: '<EMAIL>',
                    },
                  },
                ],
                locations: [
                  {
                    id: 'location_1',
                    gps: '12.9716,77.5946',
                    address: {
                      locality: 'Sample Locality',
                      street: 'Sample Street',
                      city: 'Bangalore',
                      area_code: '560001',
                      state: 'Karnataka',
                    },
                  },
                ],
              },
            ],
          },
        },
      };

      return NextResponse.json(mockResponse);
    }

    // In production, integrate with actual ONDC service
    // This would call the consolidated ONDC service
    const response = await fetch(`${process.env.ONDC_SERVICE_URL}/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId,
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`ONDC service error: ${response.statusText}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('[ONDC Search] Error processing search request:', error);

    return NextResponse.json(
      {
        context: {
          action: 'on_search',
          timestamp: new Date().toISOString(),
        },
        error: {
          type: 'INTERNAL_ERROR',
          code: '500',
          message: 'Internal server error',
        },
      },
      { status: 500 }
    );
  }
}
