import { NextResponse } from 'next/server';
import { getProductsByCategory } from '@/lib/strapi-api';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const pageSize = parseInt(searchParams.get('pageSize') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    console.log('🚀 Products by Category API: Fetching products');
    console.log('📊 Parameters:', {
      categoryId,
      subcategoryId,
      pageSize,
      page,
    });

    let response;

    if (subcategoryId) {
      // Fetch products from specific subcategory
      console.log('🎯 Fetching products from subcategory:', subcategoryId);
      response = await getProductsByCategory(subcategoryId, { pageSize, page });
    } else if (categoryId) {
      // Fetch products from parent category (all subcategories)
      console.log('🎯 Fetching products from parent category:', categoryId);
      console.log('🔍 Including subcategories: true');
      response = await getProductsByCategory(parseInt(categoryId), {
        pageSize,
        page,
        includeSubcategories: true,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Category ID or Subcategory ID is required',
          data: [],
        },
        { status: 400 }
      );
    }

    console.log('✅ Products by Category API: Successfully fetched products');
    console.log('📊 Products count:', response.data?.length || 0);

    return NextResponse.json({
      success: true,
      data: response.data || [],
      meta: response.meta || {},
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error(
      '❌ Products by Category API: Error fetching products:',
      error
    );

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        data: [],
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
