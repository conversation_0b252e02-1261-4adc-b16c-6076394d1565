import { NextRequest, NextResponse } from 'next/server';

const BACKEND_BASE_URL = 'http://localhost:9000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Get headers from the original request
    const tenantId =
      request.headers.get('x-tenant-id') || 'tenant-electronics-001';
    const publishableApiKey =
      request.headers.get('x-publishable-api-key') ||
      'pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0';

    console.log('[Proxy] Product details request:', {
      id,
      tenantId,
      timestamp: new Date().toISOString(),
    });

    // Make request to backend
    const backendUrl = `${BACKEND_BASE_URL}/store/test-products/${id}`;
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId,
        'x-publishable-api-key': publishableApiKey,
      },
    });

    if (!response.ok) {
      console.error('[Proxy] Backend error:', {
        status: response.status,
        statusText: response.statusText,
        url: backendUrl,
      });

      return NextResponse.json(
        {
          error: 'Product not found',
          product_id: id,
          tenant_id: tenantId,
          status: response.status,
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    console.log('[Proxy] Success:', {
      productId: data.product?.id,
      tenantId: data.tenant_id,
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error('[Proxy] Error:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch product details',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
