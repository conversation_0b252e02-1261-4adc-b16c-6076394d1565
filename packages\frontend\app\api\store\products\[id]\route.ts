import { NextRequest, NextResponse } from 'next/server';
// TODO: Replace with real Medusa API integration

// Individual product API route using shared MSW data structure
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const tenantId = request.headers.get('x-tenant-id') || 'default';

    console.log('[API Store Product Detail] Fetching product:', {
      id,
      tenantId,
      timestamp: new Date().toISOString(),
    });

    // TODO: Replace with real Medusa API call to get product by ID
    let product = null;

    if (!product) {
      console.log('[API Store Product Detail] Product not found:', {
        id,
        tenantId,
      });
      return NextResponse.json(
        {
          message: `Product not found for tenant: ${tenantId}`,
          code: 'not_found',
        },
        { status: 404 }
      );
    }

    console.log('[API Store Product Detail] Product found:', {
      id: product.id,
      title: product.title,
      tenantId: tenantId,
    });

    // Return the product in the expected format
    return NextResponse.json({
      product: {
        ...product,
        // Ensure images are in the correct format
        images: product.images.map(img =>
          typeof img === 'string' ? img : img.src || ''
        ),
      },
    });
  } catch (error) {
    console.error('[API Store Product Detail] Error:', error);
    return NextResponse.json(
      {
        message: 'Internal server error',
        code: 'internal_error',
      },
      { status: 500 }
    );
  }
}
