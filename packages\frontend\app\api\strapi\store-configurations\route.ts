import { NextRequest, NextResponse } from 'next/server';

const STRAPI_BASE_URL = process.env.STRAPI_BASE_URL || 'http://localhost:1337';
const STRAPI_API_TOKEN =
  process.env.STRAPI_API_TOKEN ||
  'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Handle preflight OPTIONS requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  });
}

// GET - Fetch store configurations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');

    let url = `${STRAPI_BASE_URL}/api/store-configurations`;

    if (userId) {
      url += `?filters[user_id][$eq]=${userId}`;
    }

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Strapi API error: ${response.status}`);
    }

    const data = await response.json();

    return NextResponse.json(data, {
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('❌ Failed to fetch store configurations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch store configurations' },
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}

// POST - Create store configuration
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Processing store configuration creation request...');

    const contentType = request.headers.get('content-type') || '';
    let data: any;
    let logoFile: File | null = null;

    if (contentType.includes('application/json')) {
      // Handle JSON request
      console.log('📝 Processing JSON request...');
      const body = await request.json();
      data = body.data || body;
    } else if (contentType.includes('multipart/form-data')) {
      // Handle FormData request (with file upload)
      console.log('📝 Processing FormData request...');
      const formData = await request.formData();
      const dataString = formData.get('data') as string;

      if (!dataString) {
        console.error('❌ Missing data field in FormData request');
        return NextResponse.json(
          { error: 'Missing data field' },
          {
            status: 400,
            headers: corsHeaders,
          }
        );
      }

      data = JSON.parse(dataString);
      logoFile = formData.get('files.store_logo') as File;
    } else {
      console.error('❌ Unsupported content type:', contentType);
      return NextResponse.json(
        {
          error:
            'Unsupported content type. Use application/json or multipart/form-data',
        },
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    console.log('📊 Parsed store configuration data:', data);

    // Generate store_handle from store_name if not provided
    if (!data.store_handle && data.store_name) {
      data.store_handle = data.store_name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
        .trim();
      console.log('🔗 Generated store_handle:', data.store_handle);
    }

    // Create the store configuration entry
    console.log(
      '🚀 Making request to Strapi:',
      `${STRAPI_BASE_URL}/api/store-configurations`
    );
    console.log('🔑 Using token:', STRAPI_API_TOKEN.substring(0, 20) + '...');
    console.log('📤 Request body:', JSON.stringify({ data }, null, 2));

    const response = await fetch(
      `${STRAPI_BASE_URL}/api/store-configurations`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data }),
      }
    );

    console.log('📡 Strapi response status:', response.status);
    console.log(
      '📡 Strapi response headers:',
      Object.fromEntries(response.headers.entries())
    );

    if (!response.ok) {
      const errorData = await response.text();
      console.error('❌ Strapi API error:', errorData);
      throw new Error(`Strapi API error: ${response.status} - ${errorData}`);
    }

    const result = await response.json();
    console.log('✅ Store configuration created successfully:', result);

    // Handle file upload if present
    if (logoFile && result.data?.id) {
      const fileFormData = new FormData();
      fileFormData.append('files', logoFile);
      fileFormData.append(
        'ref',
        'api::store-configuration.store-configuration'
      );
      fileFormData.append('refId', result.data.id.toString());
      fileFormData.append('field', 'store_logo');

      const uploadResponse = await fetch(`${STRAPI_BASE_URL}/api/upload`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${STRAPI_API_TOKEN}`,
        },
        body: fileFormData,
      });

      if (!uploadResponse.ok) {
        console.error(
          'Failed to upload logo, but store configuration was created'
        );
      }
    }

    return NextResponse.json(result, {
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('❌ Failed to create store configuration:', error);
    return NextResponse.json(
      { error: 'Failed to create store configuration' },
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}

// PUT - Update store configuration
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { user_id, ...updateData } = body;

    if (!user_id) {
      return NextResponse.json(
        { error: 'Missing user_id' },
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    // First, find the store configuration by user_id
    const findResponse = await fetch(
      `${STRAPI_BASE_URL}/api/store-configurations?filters[user_id][$eq]=${user_id}`,
      {
        headers: {
          Authorization: `Bearer ${STRAPI_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!findResponse.ok) {
      throw new Error(
        `Failed to find store configuration: ${findResponse.status}`
      );
    }

    const findResult = await findResponse.json();

    if (!findResult.data || findResult.data.length === 0) {
      return NextResponse.json(
        { error: 'Store configuration not found' },
        {
          status: 404,
          headers: corsHeaders,
        }
      );
    }

    const storeConfigId = findResult.data[0].id;

    // Update the store configuration
    const updateResponse = await fetch(
      `${STRAPI_BASE_URL}/api/store-configurations/${storeConfigId}`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${STRAPI_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: updateData }),
      }
    );

    if (!updateResponse.ok) {
      throw new Error(
        `Failed to update store configuration: ${updateResponse.status}`
      );
    }

    const result = await updateResponse.json();

    return NextResponse.json(result, {
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('❌ Failed to update store configuration:', error);
    return NextResponse.json(
      { error: 'Failed to update store configuration' },
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}

// DELETE - Delete store configuration
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing user_id parameter' },
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    // First, find the store configuration by user_id
    const findResponse = await fetch(
      `${STRAPI_BASE_URL}/api/store-configurations?filters[user_id][$eq]=${userId}`,
      {
        headers: {
          Authorization: `Bearer ${STRAPI_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!findResponse.ok) {
      throw new Error(
        `Failed to find store configuration: ${findResponse.status}`
      );
    }

    const findResult = await findResponse.json();

    if (!findResult.data || findResult.data.length === 0) {
      return NextResponse.json(
        { error: 'Store configuration not found' },
        {
          status: 404,
          headers: corsHeaders,
        }
      );
    }

    const storeConfigId = findResult.data[0].id;

    // Delete the store configuration
    const deleteResponse = await fetch(
      `${STRAPI_BASE_URL}/api/store-configurations/${storeConfigId}`,
      {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${STRAPI_API_TOKEN}`,
        },
      }
    );

    if (!deleteResponse.ok) {
      throw new Error(
        `Failed to delete store configuration: ${deleteResponse.status}`
      );
    }

    return NextResponse.json(
      { message: 'Store configuration deleted successfully' },
      {
        headers: corsHeaders,
      }
    );
  } catch (error) {
    console.error('❌ Failed to delete store configuration:', error);
    return NextResponse.json(
      { error: 'Failed to delete store configuration' },
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}
