/**
 * Tenants API Route
 *
 * Handles CRUD operations for tenants in the ONDC Seller Platform
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/client';

// GET /api/tenants - Get all tenants or a specific tenant
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');
    const domain = searchParams.get('domain');

    console.log('🚀 Tenants API: Fetching tenants...', { slug, domain });

    if (slug) {
      // Get tenant by slug
      const tenant = await db.getTenantBySlug(slug);

      if (!tenant) {
        return NextResponse.json(
          { error: 'Tenant not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ tenant });
    }

    if (domain) {
      // Get tenant by domain
      const tenant = await db.getTenantByDomain(domain);

      if (!tenant) {
        return NextResponse.json(
          { error: 'Tenant not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ tenant });
    }

    // Get all tenants
    const tenants = await db.getTenants({ status: 'active', limit: 50 });

    console.log('✅ Tenants API: Successfully fetched tenants');
    console.log('📊 Tenants count:', tenants?.length || 0);

    return NextResponse.json({ tenants });
  } catch (error) {
    console.error('Tenants API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/tenants - Create a new tenant
export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerComponent();
    const body = await request.json();

    const { name, slug, domain, settings = {}, ondc_config = {} } = body;

    // Validate required fields
    if (!name || !slug) {
      return NextResponse.json(
        { error: 'Name and slug are required' },
        { status: 400 }
      );
    }

    // Check if slug already exists
    const { data: existingTenant } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingTenant) {
      return NextResponse.json(
        { error: 'Tenant with this slug already exists' },
        { status: 409 }
      );
    }

    // Create new tenant
    const { data: tenant, error } = await supabase
      .from('tenants')
      .insert({
        name,
        slug,
        domain,
        settings,
        ondc_config,
        status: 'active',
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating tenant:', error);
      return NextResponse.json(
        { error: 'Failed to create tenant' },
        { status: 500 }
      );
    }

    return NextResponse.json({ tenant }, { status: 201 });
  } catch (error) {
    console.error('Tenants POST API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/tenants - Update a tenant
export async function PUT(request: NextRequest) {
  try {
    const supabase = createSupabaseServerComponent();
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('id');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const { name, slug, domain, settings, ondc_config, status } = body;

    // Update tenant
    const { data: tenant, error } = await supabase
      .from('tenants')
      .update({
        ...(name && { name }),
        ...(slug && { slug }),
        ...(domain !== undefined && { domain }),
        ...(settings && { settings }),
        ...(ondc_config && { ondc_config }),
        ...(status && { status }),
      })
      .eq('id', tenantId)
      .select()
      .single();

    if (error) {
      console.error('Error updating tenant:', error);
      return NextResponse.json(
        { error: 'Failed to update tenant' },
        { status: 500 }
      );
    }

    return NextResponse.json({ tenant });
  } catch (error) {
    console.error('Tenants PUT API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/tenants - Delete a tenant
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createSupabaseServerComponent();
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('id');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    // Soft delete by setting status to inactive
    const { data: tenant, error } = await supabase
      .from('tenants')
      .update({ status: 'inactive' })
      .eq('id', tenantId)
      .select()
      .single();

    if (error) {
      console.error('Error deleting tenant:', error);
      return NextResponse.json(
        { error: 'Failed to delete tenant' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Tenant deleted successfully',
      tenant,
    });
  } catch (error) {
    console.error('Tenants DELETE API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
