'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';
import { useAuth } from '@/contexts/AuthContext';
import { authService, OneSSOAuthClient, getAuthConfig } from '@/lib/auth/keycloak';

/**
 * Authentication Callback Page
 * 
 * Handles OAuth callbacks from Keycloak/OneSSO and completes the authentication flow
 */
export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // Handle OAuth errors
        if (error) {
          setError(errorDescription || error);
          setLoading(false);
          return;
        }

        // Handle missing authorization code
        if (!code) {
          setError('Authorization code not found');
          setLoading(false);
          return;
        }

        const config = getAuthConfig();

        // Handle OneSSO callback
        if (config.provider === 'onesso' && config.onesso) {
          const onessoClient = new OneSSOAuthClient(config.onesso);
          
          try {
            // Exchange code for tokens
            const tokens = await onessoClient.exchangeCodeForTokens(code, state || '');
            
            // Get user info
            const user = await onessoClient.getUserInfo(tokens.access_token);
            
            if (user) {
              // Store tokens securely (you might want to use httpOnly cookies)
              localStorage.setItem('access_token', tokens.access_token);
              if (tokens.refresh_token) {
                localStorage.setItem('refresh_token', tokens.refresh_token);
              }
              
              // Update auth context
              // Note: You'll need to modify the AuthContext to handle external user data
              console.log('OneSSO authentication successful:', user);
              
              // Redirect to dashboard
              router.push('/admin');
            } else {
              setError('Failed to get user information');
            }
          } catch (err) {
            console.error('OneSSO callback error:', err);
            setError('Authentication failed');
          }
        }
        
        // Handle Keycloak callback
        else if (config.provider === 'keycloak') {
          // Keycloak handles the callback automatically through its JavaScript adapter
          // This page might not be needed for Keycloak, but we'll handle it gracefully
          const user = authService.getCurrentUser();
          if (user) {
            console.log('Keycloak authentication successful:', user);
            router.push('/admin');
          } else {
            setError('Keycloak authentication failed');
          }
        }
        
        // Handle unknown provider
        else {
          setError('Unknown authentication provider');
        }

      } catch (err) {
        console.error('Authentication callback error:', err);
        setError('An unexpected error occurred during authentication');
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [searchParams, router, login]);

  // Auto-redirect to login on error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        router.push('/auth/login');
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [error, router]);

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="text.secondary">
          Completing authentication...
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please wait while we verify your credentials
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
          px: 3,
        }}
      >
        <Alert severity="error" sx={{ maxWidth: 500 }}>
          <Typography variant="h6" gutterBottom>
            Authentication Failed
          </Typography>
          <Typography variant="body2">
            {error}
          </Typography>
        </Alert>
        <Typography variant="body2" color="text.secondary" textAlign="center">
          You will be redirected to the login page in a few seconds...
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        gap: 2,
      }}
    >
      <CircularProgress size={60} />
      <Typography variant="h6" color="text.secondary">
        Redirecting...
      </Typography>
    </Box>
  );
}
