'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Box,
  Button,
  Alert,
  Typography,
  Paper,
  Container,
  Divider,
  Stack,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { useAuth } from '@/contexts/AuthContext';
import Mu<PERSON><PERSON><PERSON><PERSON>ield from '@/components/admin/MuiFormField';
import { FormErrorBoundary } from '@/components/admin/ErrorBoundary';
import { useFormValidation } from '@/hooks/useFormValidation';
import { FIELD_VALIDATION_RULES } from '@/lib/validation';
import {
  useMedusaAdminProducts,
  useMedusaBackendLogin,
} from '@/hooks/useMedusaAdminBackend';

const LoginPage = () => {
  const [rememberMe, setRememberMe] = useState(false);
  const { login } = useAuth();
  // const { adminLogin, token } = useMedusaBackendLogin();
  const router = useRouter();

  // Form validation with specific rules for login
  const {
    data,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldError,
    clearErrors,
  } = useFormValidation({
    initialData: {
      email: '',
      password: '',
    },
    validationRules: {
      email:
        process.env.NODE_ENV === 'development'
          ? { required: true } // Allow any format in development
          : FIELD_VALIDATION_RULES.email, // Strict email validation in production
      password: { required: true, minLength: 4 }, // Reduced for demo credentials
    },
    onSubmit: async formData => {
      try {
        // Login and get onboarding status
        const loginResult = await login({
          email: formData.email,
          password: formData.password,
        });
        const admin_token = localStorage.getItem('ondc_auth_token');
        console.log({ admin_token });

        // Redirect based on onboarding status
        if (admin_token) {
          console.log('🔍 Login successful, checking onboarding status:', {
            shouldRedirectToOnboarding: loginResult.shouldRedirectToOnboarding,
            currentStep: loginResult.currentStep,
            redirectPath: loginResult.redirectPath,
          });

          if (loginResult.shouldRedirectToOnboarding) {
            console.log(
              '📋 Redirecting to onboarding at step:',
              loginResult.currentStep
            );
            router.push(loginResult.redirectPath);
          } else {
            console.log(
              '✅ Onboarding completed, redirecting to admin dashboard'
            );
            router.push(loginResult.redirectPath);
          }
        } else {
          console.log('customer');
          router.push('/');
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Login failed';
        setFieldError('submit', errorMessage);
        throw err; // Re-throw to prevent form submission success
      }
    },
    onError: formErrors => {
      console.error('Login form validation errors:', formErrors);
    },
  });

  // const handleDemoLogin = async (role: 'admin' | 'seller' | 'customer') => {
  //   const demoCredentials = {
  //     admin: { email: '<EMAIL>', password: 'supersecret' },
  //     seller: { email: '<EMAIL>', password: '123456' },
  //     customer: { email: '<EMAIL>', password: '123456' },
  //   };

  //   const { email: demoEmail, password: demoPassword } = demoCredentials[role];

  //   clearErrors();

  //   try {
  //     // await login(demoEmail, demoPassword);
  //     await login(demoEmail, demoPassword);

  //     if (role === 'admin') {
  //       router.push('/admin');
  //     } else {
  //       router.push('/');
  //     }
  //   } catch (err) {
  //     const errorMessage = err instanceof Error ? err.message : 'Login failed';
  //     setFieldError('submit', errorMessage);
  //   }
  // };

  return (
    <FormErrorBoundary>
      <Container className='!p-0'>
        <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
          <Typography
            variant='h4'
            component='h1'
            align='center'
            className='text-4xl font-extrabold pb-2'
          >
            OneStore
          </Typography>
          <Typography variant='h5' component='h1' align='center' gutterBottom>
            Sign In
          </Typography>

          <Box component='form' onSubmit={handleSubmit} sx={{ mt: 3 }}>
            {errors.submit && (
              <Alert severity='error' sx={{ mb: 2 }}>
                {errors.submit}
              </Alert>
            )}

            <Stack spacing={3}>
              <MuiFormField
                label={
                  process.env.NODE_ENV === 'development'
                    ? 'Username/Email'
                    : 'Email Address'
                }
                name='email'
                type={process.env.NODE_ENV === 'development' ? 'text' : 'email'}
                value={data.email}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder={
                  process.env.NODE_ENV === 'development'
                    ? 'Enter username or email'
                    : 'Enter your email'
                }
                required
                error={errors.email}
                disabled={isSubmitting}
                validationRules={
                  process.env.NODE_ENV === 'development'
                    ? { required: true }
                    : FIELD_VALIDATION_RULES.email
                }
              />

              <MuiFormField
                label='Password'
                name='password'
                type='password'
                value={data.password}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder='Enter your password'
                required
                error={errors.password}
                disabled={isSubmitting}
                validationRules={{ required: true, minLength: 6 }}
              />

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={rememberMe}
                      onChange={e => setRememberMe(e.target.checked)}
                      name='rememberMe'
                      color='primary'
                    />
                  }
                  label='Remember me'
                />

                <Link
                  href='/auth/forgot-password'
                  style={{ textDecoration: 'none' }}
                >
                  <Typography
                    variant='body2'
                    color='primary'
                    sx={{ cursor: 'pointer' }}
                  >
                    Forgot password?
                  </Typography>
                </Link>
              </Box>

              <Button
                type='submit'
                fullWidth
                variant='contained'
                size='large'
                disabled={isSubmitting}
                sx={{ mt: 3, mb: 2 }}
              >
                {isSubmitting ? 'Signing in...' : 'SIGN IN'}
              </Button>
            </Stack>

            {/* {process.env.NODE_ENV === 'development' && (
                <>
                  <Divider sx={{ my: 3 }}>
                    <Typography variant='body2' color='text.secondary'>
                      Development Mode
                    </Typography>
                  </Divider>

                  <Paper
                    variant='outlined'
                    sx={{
                      p: 2,
                      bgcolor: 'primary.50',
                      borderColor: 'primary.200',
                    }}
                  >
                    <Typography
                      variant='subtitle2'
                      color='primary.main'
                      gutterBottom
                    >
                      Development Credentials:
                    </Typography>
                    <Typography
                      variant='body2'
                      color='primary.dark'
                      sx={{ fontFamily: 'monospace' }}
                    >
                      <strong>Username:</strong> demo
                      <br />
                      <strong>Password:</strong> demo
                    </Typography>
                    <Typography
                      variant='caption'
                      color='text.secondary'
                      sx={{ mt: 1, display: 'block' }}
                    >
                      Use these credentials for development and testing
                    </Typography>
                  </Paper>
                </>
              )} */}

            <Box sx={{ textAlign: 'center', mt: 3 }}>
              <Typography variant='body2' color='text.secondary'>
                Don't have an account?{' '}
                <Link href='/auth/register' style={{ textDecoration: 'none' }}>
                  <Typography
                    component='span'
                    variant='body2'
                    color='primary'
                    sx={{ cursor: 'pointer' }}
                  >
                    Sign up
                  </Typography>
                </Link>
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Container>
    </FormErrorBoundary>
  );
};

export default LoginPage;
