'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Box,
  Button,
  Alert,
  Typography,
  Paper,
  Container,
  Divider,
  Stack,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { useAuth } from '@/contexts/AuthContext';
import Mu<PERSON><PERSON><PERSON><PERSON>ield from '@/components/admin/MuiFormField';
import { FormErrorBoundary } from '@/components/admin/ErrorBoundary';
import { useFormValidation } from '@/hooks/useFormValidation';
import { FIELD_VALIDATION_RULES } from '@/lib/validation';
import { useToast } from '@/components/common/ToastProvider';

const RegisterPage = () => {
  const toast = useToast();
  const [acceptTerms, setAcceptTerms] = useState(false);
  const { registerTenant } = useAuth();
  const router = useRouter();

  // Form validation with specific rules for registration
  const {
    data,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldError,
    clearErrors,
  } = useFormValidation({
    initialData: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    },
    validationRules: {
      firstName: { required: true, minLength: 2 },
      lastName: { required: true, minLength: 2 },
      email: FIELD_VALIDATION_RULES.email,
      phone: { required: true, pattern: /^[+]?[\d\s\-\(\)]{10,}$/ },
      password: { required: true, minLength: 8 },
      confirmPassword: { required: true, minLength: 8 },
    },
    onSubmit: async formData => {
      try {
        if (!acceptTerms) {
          setFieldError('submit', 'Please accept the terms and conditions');
          return toast.error('Please accept the terms and conditions');
        }

        if (formData.password !== formData.confirmPassword) {
          setFieldError('confirmPassword', 'Passwords do not match');
          return toast.error('Passwords do not match');
        }

        // Register user
        const response = await registerTenant({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          password: formData.password,
        });

        console.log('🎉 Registration and auto-login response:', response);

        // Check if auto-login was successful
        if (response.autoLoginSuccess && response.isNewUser) {
          // Redirect new users to onboarding
          toast.success(
            'Account created successfully! Welcome to your onboarding journey.'
          );
          router.push('/onboarding');
        } else if (response.autoLoginSuccess) {
          // Existing user logic (shouldn't happen in registration, but just in case)
          toast.success('Account created and logged in successfully!');
          router.push('/admin');
        } else {
          // Auto-login failed, redirect to login page
          toast.success('Account created successfully! Please log in.');
          router.push('/auth/login?registered=true');
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Registration failed';
        setFieldError('submit', errorMessage);
        toast.error(`Error: ${err}`);
        // throw err; // Re-throw to prevent form submission success
      }
    },
    onError: formErrors => {
      console.error('Registration form validation errors:', formErrors);
    },
  });

  return (
    <FormErrorBoundary>
      <Container className='!min-w-[550px] !p-0'>
        <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
          <Typography variant='h4' component='h1' align='center' gutterBottom>
            Create Account
          </Typography>
          <Typography
            variant='body2'
            align='center'
            color='text.secondary'
            sx={{ mb: 3 }}
          >
            Join our One Store and start your journey
          </Typography>

          <Box component='form' onSubmit={handleSubmit} sx={{ mt: 3 }}>
            {errors.submit && (
              <Alert severity='error' sx={{ mb: 2 }}>
                {errors.submit}
              </Alert>
            )}

            <Stack className='w-full'>
              <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                <MuiFormField
                  label='First Name'
                  name='firstName'
                  type='text'
                  value={data.firstName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  placeholder='Enter your first name'
                  required
                  error={errors.firstName}
                  disabled={isSubmitting}
                  validationRules={{ required: true, minLength: 2 }}
                  fullWidth={true}
                />
                <MuiFormField
                  label='Last Name'
                  name='lastName'
                  type='text'
                  value={data.lastName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  placeholder='Enter your last name'
                  required
                  error={errors.lastName}
                  disabled={isSubmitting}
                  validationRules={{ required: true, minLength: 2 }}
                  fullWidth={true}
                />
              </Box>

              <MuiFormField
                label='Email Address'
                name='email'
                type='email'
                value={data.email}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder='Enter your email'
                required
                error={errors.email}
                disabled={isSubmitting}
                validationRules={FIELD_VALIDATION_RULES.email}
              />

              <MuiFormField
                label='Phone Number'
                name='phone'
                type='tel'
                value={data.phone}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder='Enter your phone number'
                required
                error={errors.phone}
                disabled={isSubmitting}
                validationRules={{
                  required: true,
                  pattern: /^[+]?[\d\s\-\(\)]{10,}$/,
                }}
              />

              <MuiFormField
                label='Password'
                name='password'
                type='password'
                value={data.password}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder='Create a password'
                required
                error={errors.password}
                disabled={isSubmitting}
                validationRules={{ required: true, minLength: 8 }}
                helperText='Password must be at least 8 characters long'
              />

              <MuiFormField
                label='Confirm Password'
                name='confirmPassword'
                type='password'
                value={data.confirmPassword}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder='Confirm your password'
                required
                error={errors.confirmPassword}
                disabled={isSubmitting}
                validationRules={{ required: true, minLength: 8 }}
              />

              <FormControlLabel
                control={
                  <Checkbox
                    checked={acceptTerms}
                    onChange={e => setAcceptTerms(e.target.checked)}
                    name='acceptTerms'
                    color='primary'
                  />
                }
                label={
                  <Typography variant='body2'>
                    I agree to the{' '}
                    <Link href='/terms' style={{ color: 'inherit' }}>
                      Terms of Service
                    </Link>{' '}
                    and{' '}
                    <Link href='/privacy' style={{ color: 'inherit' }}>
                      Privacy Policy
                    </Link>
                  </Typography>
                }
              />

              <Button
                type='submit'
                fullWidth
                variant='contained'
                size='large'
                disabled={isSubmitting || !acceptTerms}
                sx={{ mt: 3, mb: 2 }}
              >
                {isSubmitting ? 'Creating Account...' : 'CREATE ACCOUNT'}
              </Button>
            </Stack>

            <Box sx={{ textAlign: 'center', mt: 3 }}>
              <Typography variant='body2' color='text.secondary'>
                Already have an account?{' '}
                <Link href='/auth/login' style={{ textDecoration: 'none' }}>
                  <Typography
                    component='span'
                    variant='body2'
                    color='primary'
                    sx={{ cursor: 'pointer' }}
                  >
                    Sign in
                  </Typography>
                </Link>
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Container>
    </FormErrorBoundary>
  );
};
export default RegisterPage;
