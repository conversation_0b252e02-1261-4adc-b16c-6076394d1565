'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useParams } from 'next/navigation';
import ProductListingPage, {
  Product,
  ProductListingConfig,
} from '@/components/common/ProductListingPage';
import { useCategoryStore } from '@/stores/categoriesStore';
import { useMedusaBackendProducts } from '@/hooks/useMedusaBackendProducts';
import { MedusaProduct } from '@/lib/medusa-backend-api';
// TODO: Replace with real API calls
// import { getProductsBySubcategory } from '@/lib/medusa-backend-api';

// Define subcategories for each category
const categorySubcategories: Record<
  string,
  Array<{ id: string; name: string }>
> = {
  furniture: [
    { id: 'sofas-couches', name: '<PERSON><PERSON><PERSON> & Couches' },
    { id: 'chairs-recliners', name: 'Chairs & Recliners' },
    { id: 'tables-desks', name: 'Tables & Desks' },
    { id: 'beds-mattresses', name: 'Beds & Mattresses' },
    { id: 'wardrobes-storage', name: 'Wardrobes & Storage' },
  ],
  electronics: [
    { id: 'audio-equipment', name: 'Audio Equipment' },
    { id: 'wearables', name: 'Wearables' },
    { id: 'cameras-photography', name: 'Cameras & Photography' },
    { id: 'computer-accessories', name: 'Computer Accessories' },
    { id: 'mobile-accessories', name: 'Mobile Accessories' },
    { id: 'gaming', name: 'Gaming' },
  ],
  fashion: [
    { id: 'mens-clothing', name: "Men's Clothing" },
    { id: 'womens-clothing', name: "Women's Clothing" },
    { id: 'shoes', name: 'Shoes' },
    { id: 'accessories', name: 'Accessories' },
    { id: 'bags', name: 'Bags' },
  ],
};

const SubcategoryDetailPage = () => {
  const params = useParams();
  const categoryName = params.slug as string;
  const subcategoryName = params.subcategoryName as string;

  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const selectedSubCategoryId = useCategoryStore(
    state => state.selectedSubCategoryId
  );
  const setSelectedProductId = useCategoryStore(
    state => state.setSelectedProductId
  );

  const { loading, error, getProductsByCategory, fetchProduct } =
    useMedusaBackendProducts();

  const productData = useMemo(() => {
    console.log('products:::::::::', products);
    if (products.length < 1) return [];
    const convertedProducts: Product[] = products.map(
      (apiProduct: MedusaProduct, index: number) => {
        const prices = apiProduct?.metadata?.additional_data?.product_prices;
        const firstPrice =
          Array.isArray(prices) && prices.length > 0 ? prices[0] : {};
        return {
          id: apiProduct.id, // Use original product ID instead of converting to number
          name: apiProduct.title,
          slug:
            apiProduct.handle ||
            apiProduct.title.toLowerCase().replace(/\s+/g, '-'),
          originalPrice: firstPrice.original_price || 2299, // Default price since API doesn't provide pricing
          salePrice: firstPrice.sale_price || 1399, // Default sale price
          // discount: 33, // Default discount
          image:
            apiProduct.thumbnail ||
            `https://picsum.photos/400/400?random=${apiProduct.id}`,
          category: 'Products',
          subcategory: 'General',
          tenantId: 'default',
          rating: 4.5, // Default rating
          reviewCount: Math.floor(Math.random() * 500) + 50, // Random review count
          badge: apiProduct.status === 'published' ? 'Available' : 'Draft',
          brand: 'API Brand',
          inStock: apiProduct.status === 'published',
          tags: apiProduct.tags?.map(tag => tag.value) || [],
        };
      }
    );
    console.log('convertedProducts::::::::::', convertedProducts);
    return convertedProducts;
  }, [products]);

  const handleViewProduct = async (id: string) => {
    setSelectedProductId(id);
    await fetchProduct(id);
  };

  const fetchCategoryProducts = async () => {
    const response = await getProductsByCategory(selectedSubCategoryId);
    console.log('response:::::', response);
    setProducts(response);
  };

  // Get display names
  const categoryDisplayName = categoryName
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
  const subcategoryInfo = categorySubcategories[categoryName]?.find(
    sub => sub.id === subcategoryName
  );
  const subcategoryDisplayName =
    subcategoryInfo?.name ||
    subcategoryName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Configuration for ProductListingPage
  const config: ProductListingConfig = {
    title: subcategoryDisplayName,
    description: `Discover our collection of ${subcategoryDisplayName.toLowerCase()} products`,
    showSubcategoryFilter: false, // This is the key difference - no subcategory filter
    showFilters: true,
    showPriceComparison: true,
    showSavingsAmount: true,

    showSort: true,
    showViewToggle: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: categoryDisplayName, href: `/categories/${categoryName}` },
      {
        label: subcategoryDisplayName,
        href: `/categories/${categoryName}/${subcategoryName}`,
      },
    ],
  };

  useEffect(() => {
    if (selectedSubCategoryId !== '') fetchCategoryProducts();
  }, [selectedSubCategoryId]);

  if (!categoryName || !subcategoryName) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold text-gray-900 mb-4'>
            Page Not Found
          </h1>
          <p className='text-gray-600'>
            Please select a valid category and subcategory.
          </p>
        </div>
      </div>
    );
  }
  console.log('productData::::::::::', productData);
  return (
    <ProductListingPage
      products={productData}
      isLoading={loading}
      config={config}
      setProductId={setSelectedProductId}
      handleViewProduct={handleViewProduct}
    />
  );
};

export default SubcategoryDetailPage;
