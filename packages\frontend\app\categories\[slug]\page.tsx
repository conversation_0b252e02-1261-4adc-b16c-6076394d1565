// 'use client';

// import React, { useState, useEffect } from 'react';
// import { useParams } from 'next/navigation';
// import { cn } from '@/lib/utils';
// import ProductGrid from '@/components/ui/ProductGrid';
// import FilterSidebar from '@/components/ui/FilterSidebar';
// import Button from '@/components/ui/Button';
// import { useInfiniteCategoryProducts } from '@/hooks/useInfiniteProducts';
// import { useMedusaBackendProducts } from '@/hooks/useMedusaBackendProducts';
// import { useCategoryStore } from '@/stores/categoriesStore';

// interface CategoryPageProps {
//   params: {
//     slug: string;
//   };
// }

// const CategoryPage: React.FC<CategoryPageProps> = () => {
//   const params = useParams();
//   const slug = params?.slug as string;

//   const [filters, setFilters] = useState<Record<string, string[]>>({});
//   const [sortBy, setSortBy] = useState('created_at');
//   const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
//   const [showFilters, setShowFilters] = useState(false);
//   const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
//   const [filteredData, setFilteredData] = useState([]);

//   // Use the infinite products hook with category filtering
//   const { products, loading, error, hasMore, loadMore, refresh, totalCount } =
//     useInfiniteCategoryProducts(slug, {
//       limit: 12,
//       filters,
//       sortBy,
//       sortOrder,
//     });
//   const selectedCategoryId = useCategoryStore(
//     state => state.selectedCategoryId
//   );

//   const { getProductsByCategory } = useMedusaBackendProducts();

//   // Refresh products when filters or sorting changes
//   useEffect(() => {
//     refresh();
//   }, [filters, sortBy, sortOrder, refresh]);

//   const handleFiltersChange = (newFilters: Record<string, string[]>) => {
//     setFilters(newFilters);
//   };

//   const handleSortChange = (value: string) => {
//     const [field, order] = value.split(':');
//     setSortBy(field);
//     setSortOrder(order as 'asc' | 'desc');
//   };

//   const handleQuickAdd = (productId: string) => {
//     console.log('Quick add product:', productId);
//     // Implement quick add to cart logic
//   };

//   const handleWishlistToggle = (productId: string) => {
//     console.log('Toggle wishlist for product:', productId);
//     // Implement wishlist toggle logic
//   };

//   const getCategoryTitle = (slug: string) => {
//     return slug
//       .split('-')
//       .map(word => word.charAt(0).toUpperCase() + word.slice(1))
//       .join(' ');
//   };

//   const fetchAllCategoriesProducts = async () => {
//     const response = await getProductsByCategory(selectedCategoryId, 1000);
//     console.log(':::::::response:::::::::::', response);
//     const product = response.map(prductData => ({
//       id: prductData.id,
//       title: prductData.title,
//       description: prductData.description,
//       price: prductData.variants[0].metadata.sale_price,
//       originalPrice: prductData.variants[0].metadata.original_price,
//       currency: 'inr',
//       image: prductData.thumbnail,
//       images: prductData.images,

//       category: prductData.categories?.[0]?.name || 'Products',
//       subcategory: prductData.categories?.[1]?.name || 'General',

//       rating: 4.5,
//       reviewCount: Math.floor(Math.random() * 500) + 50,
//       brand: prductData.type?.value || 'Brand',

//       inStock: true,
//       isNew: false,
//       isFeatured: false,
//       tags: prductData.tags,
//     }));
//     setFilteredData(product);
//   };

//   const sortOptions = [
//     { value: 'created_at:desc', label: 'Newest First' },
//     { value: 'created_at:asc', label: 'Oldest First' },
//     { value: 'title:asc', label: 'Name A-Z' },
//     { value: 'title:desc', label: 'Name Z-A' },
//     { value: 'price:asc', label: 'Price Low to High' },
//     { value: 'price:desc', label: 'Price High to Low' },
//   ];

//   const getActiveFiltersCount = () => {
//     return Object.values(filters).reduce(
//       (count, values) => count + values.length,
//       0
//     );
//   };

//   useEffect(() => {
//     if (!selectedCategoryId) return;
//     fetchAllCategoriesProducts();
//   }, [selectedCategoryId]);

//   console.log('products::::::::::', products);

//   return (
//     <div className='min-h-screen bg-gray-50'>
//       {/* Page Header */}
//       <div className='bg-white border-b'>
//         <div className='container mx-auto px-4 py-6'>
//           <div className='flex items-center justify-between'>
//             <div>
//               <h1 className='text-2xl md:text-3xl font-bold text-gray-900 mb-2'>
//                 {getCategoryTitle(slug)}
//               </h1>
//               <p className='text-gray-600'>
//                 {loading
//                   ? 'Loading products...'
//                   : `${totalCount} products found`}
//               </p>
//             </div>

//             {/* Mobile Filter Toggle */}
//             <div className='lg:hidden'>
//               <Button
//                 variant='outline'
//                 onClick={() => setShowFilters(true)}
//                 className='relative'
//               >
//                 <svg
//                   className='w-4 h-4 mr-2'
//                   fill='none'
//                   stroke='currentColor'
//                   viewBox='0 0 24 24'
//                 >
//                   <path
//                     strokeLinecap='round'
//                     strokeLinejoin='round'
//                     strokeWidth={2}
//                     d='M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z'
//                   />
//                 </svg>
//                 Filters
//                 {getActiveFiltersCount() > 0 && (
//                   <span className='absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center'>
//                     {getActiveFiltersCount()}
//                   </span>
//                 )}
//               </Button>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Toolbar */}
//       <div className='bg-white border-b'>
//         <div className='container mx-auto px-4 py-4'>
//           <div className='flex items-center justify-between'>
//             {/* Sort Dropdown */}
//             <div className='flex items-center space-x-4'>
//               <label
//                 htmlFor='sort'
//                 className='text-sm font-medium text-gray-700'
//               >
//                 Sort by:
//               </label>
//               <select
//                 id='sort'
//                 value={`${sortBy}:${sortOrder}`}
//                 onChange={e => handleSortChange(e.target.value)}
//                 className='border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500'
//               >
//                 {sortOptions.map(option => (
//                   <option key={option.value} value={option.value}>
//                     {option.label}
//                   </option>
//                 ))}
//               </select>
//             </div>

//             {/* View Mode Toggle */}
//             <div className='flex items-center space-x-2'>
//               <span className='text-sm text-gray-700'>View:</span>
//               <div className='flex border border-gray-300 rounded-md'>
//                 <button
//                   onClick={() => setViewMode('grid')}
//                   className={cn(
//                     'px-3 py-2 text-sm font-medium transition-colors',
//                     viewMode === 'grid'
//                       ? 'bg-blue-500 text-white'
//                       : 'text-gray-700 hover:bg-gray-50'
//                   )}
//                 >
//                   <svg
//                     className='w-4 h-4'
//                     fill='none'
//                     stroke='currentColor'
//                     viewBox='0 0 24 24'
//                   >
//                     <path
//                       strokeLinecap='round'
//                       strokeLinejoin='round'
//                       strokeWidth={2}
//                       d='M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z'
//                     />
//                   </svg>
//                 </button>
//                 <button
//                   onClick={() => setViewMode('list')}
//                   className={cn(
//                     'px-3 py-2 text-sm font-medium transition-colors',
//                     viewMode === 'list'
//                       ? 'bg-blue-500 text-white'
//                       : 'text-gray-700 hover:bg-gray-50'
//                   )}
//                 >
//                   <svg
//                     className='w-4 h-4'
//                     fill='none'
//                     stroke='currentColor'
//                     viewBox='0 0 24 24'
//                   >
//                     <path
//                       strokeLinecap='round'
//                       strokeLinejoin='round'
//                       strokeWidth={2}
//                       d='M4 6h16M4 10h16M4 14h16M4 18h16'
//                     />
//                   </svg>
//                 </button>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Main Content */}
//       <div className='container mx-auto px-4 py-6'>
//         <div className='flex gap-6'>
//           {/* Filter Sidebar */}
//           <FilterSidebar
//             isOpen={showFilters}
//             onClose={() => setShowFilters(false)}
//             filters={filters}
//             onFiltersChange={handleFiltersChange}
//             showMobileOverlay={true}
//           />

//           {/* Product Grid */}
//           <div className='flex-1'>
//             <ProductGrid
//               products={filteredData}
//               loading={loading}
//               error={error}
//               hasMore={hasMore}
//               onLoadMore={loadMore}
//               onQuickAdd={handleQuickAdd}
//               onWishlistToggle={handleWishlistToggle}
//               columns={{
//                 mobile: 1,
//                 tablet: 2,
//                 desktop: viewMode === 'grid' ? 3 : 1,
//                 wide: viewMode === 'grid' ? 4 : 1,
//               }}
//               variant={viewMode === 'list' ? 'detailed' : 'default'}
//               infiniteScroll={true}
//               loadMoreThreshold={200}
//               emptyStateTitle='No products found in this category'
//               emptyStateDescription='Try adjusting your filters or check back later for new products.'
//               emptyStateAction={
//                 <Button variant='outline' onClick={() => setFilters({})}>
//                   Clear All Filters
//                 </Button>
//               }
//             />
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default CategoryPage;

'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import ProductListingPage, {
  Product,
  ProductListingConfig,
} from '@/components/common/ProductListingPage';
import { useMedusaBackendProducts } from '@/hooks/useMedusaBackendProducts';
import { useCategoryStore } from '@/stores/categoriesStore';
import { MedusaProduct } from '@/lib/medusa-backend-api';
// TODO: Replace with real API calls
// import { getProductsByCategory } from '@/lib/medusa-backend-api';

export default function CategoryDetailPage() {
  console.log('category page render');
  const params = useParams();
  const searchParams = useSearchParams();
  const categorySlug = params.slug as string;
  const subcategory = searchParams.get('subcategory');

  // const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSubcategory, setActiveSubcategory] = useState<string>('');

  const {
    products,
    loading,

    getProductsByCategory,
  } = useMedusaBackendProducts();
  const selectedCategoryId = useCategoryStore(
    state => state.selectedCategoryId
  );
  const getCategoryById = useCategoryStore(state => state.getCategoryById);

  const categoryData = useMemo(() => {
    if (selectedCategoryId == '') return;
    return getCategoryById(selectedCategoryId);
  }, [selectedCategoryId]);

  useEffect(() => {
    if (categorySlug) {
      setActiveSubcategory(subcategory || '');
    }
    if (selectedCategoryId !== '') getProductsByCategory(selectedCategoryId);
  }, [categorySlug, subcategory, selectedCategoryId]);

  const handleSubcategoryChange = (subcategoryId: string) => {
    if (subcategoryId === 'all') {
      setActiveSubcategory('');
      if (categorySlug) {
        getProductsByCategory(selectedCategoryId);
      }
    } else {
      setActiveSubcategory(subcategoryId);
      if (categorySlug) {
        getProductsByCategory(subcategoryId);
      }
    }
  };

  if (!categorySlug) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold text-gray-900 mb-4'>
            Category Not Found
          </h1>
          <p className='text-gray-600'>Please select a valid category.</p>
        </div>
      </div>
    );
  }

  const categoryName = categorySlug
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());

  const allSubcategoryFilters = useMemo(() => {
    if (categoryData?.subcategories.length < 1) return [];
    return categoryData?.subcategories?.map(data => ({
      id: data.id,
      name: data.name,
    }));
  }, [categoryData?.subcategories]);

  const productData = useMemo(() => {
    if (products.length < 1) return [];
    const convertedProducts: Product[] = products.map(
      (apiProduct: MedusaProduct, index: number) => {
        // Safely access variant data
        const firstVariant = apiProduct.variants?.[0];
        const prices = firstVariant?.metadata?.additional_data?.product_prices;
        const firstPrice =
          Array.isArray(prices) && prices.length > 0 ? prices[0] : {};

        return {
          id: apiProduct.id, // Use original product ID instead of converting to number
          name: apiProduct.title,
          slug:
            apiProduct.handle ||
            apiProduct.title.toLowerCase().replace(/\s+/g, '-'),
          originalPrice: firstPrice.original_price || 2299, // Default price since API doesn't provide pricing
          salePrice: firstPrice.sale_price || 1399, // Default sale price
          // discount: 33, // Default discount
          image:
            apiProduct.thumbnail ||
            `https://picsum.photos/400/400?random=${apiProduct.id}`,
          category: 'Products',
          subcategory: 'General',
          tenantId: 'default',
          rating: 4.5, // Default rating
          reviewCount: Math.floor(Math.random() * 500) + 50, // Random review count
          badge: apiProduct.status === 'published' ? 'Available' : 'Draft',
          brand: 'API Brand',
          inStock: apiProduct.status === 'published',
          tags: apiProduct.tags?.map(tag => tag.value) || [],
        };
      }
    );
    return convertedProducts;
  }, [products]);

  // Get subcategory display name
  const subcategoryDisplayName = activeSubcategory
    ? allSubcategoryFilters.find(sub => sub.id === activeSubcategory)?.name
    : null;

  // Format category name for display
  const categoryDisplayName =
    categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

  const config: ProductListingConfig = {
    title:
      activeSubcategory && activeSubcategory !== 'all'
        ? subcategoryDisplayName || activeSubcategory
        : categoryDisplayName,
    subtitle:
      activeSubcategory && activeSubcategory !== 'all'
        ? `Discover our collection of ${
            subcategoryDisplayName?.toLowerCase() || 'products'
          }`
        : `Discover amazing ${categoryName.toLowerCase()} products`,
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: true,
    buttonText: 'Add to Cart',
    buttonStyle: 'primary',
    breadcrumbs:
      activeSubcategory && activeSubcategory !== 'all'
        ? [
            { label: 'Home', href: '/' },
            { label: categoryDisplayName, href: `/categories/${categorySlug}` },
            { label: subcategoryDisplayName || activeSubcategory },
          ]
        : [{ label: 'Home', href: '/' }, { label: categoryDisplayName }],
    subcategoryFilters: allSubcategoryFilters, // Always show subcategory filters on main category page
    activeSubcategory: activeSubcategory,
    showSubcategoryFilter: true, // Always show subcategory filters on category page
  };

  console.log({ productData });
  return (
    <ProductListingPage
      products={productData}
      config={config}
      isLoading={loading}
      onSubcategoryChange={handleSubcategoryChange}
    />
  );
}
