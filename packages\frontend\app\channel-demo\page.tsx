'use client';

import React from 'react';
import { ChannelSwitcher, ChannelIndicator } from '@/components/ChannelSwitcher';
import { ProductList } from '@/components/ProductList';
import { useChannel } from '@/contexts/ChannelContext';

export default function ChannelDemoPage() {
  const { currentChannel, isLoading } = useChannel();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                ONDC Multi-Channel Store
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <ChannelIndicator />
              <ChannelSwitcher variant="dropdown" showDescription />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Channel Selection Section */}
        <section className="mb-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Choose Your Shopping Experience
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Switch between different sales channels to see how products are filtered 
              and displayed based on the selected channel's inventory and branding.
            </p>
          </div>
          
          {/* Channel Cards */}
          <ChannelSwitcher variant="cards" showDescription className="mb-8" />
          
          {/* Channel Tabs */}
          <ChannelSwitcher variant="tabs" className="mb-8" />
        </section>

        {/* Current Channel Info */}
        <section className="mb-8">
          <div 
            className="bg-white rounded-lg shadow-sm p-6 border-l-4"
            style={{ borderLeftColor: currentChannel.theme?.primaryColor }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {currentChannel.logo && (
                  <img 
                    src={currentChannel.logo} 
                    alt={currentChannel.name}
                    className="w-12 h-12 mr-4 rounded-lg"
                  />
                )}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {currentChannel.name}
                  </h3>
                  <p className="text-gray-600">{currentChannel.description}</p>
                  <div className="mt-2 flex items-center text-sm text-gray-500">
                    <span className="mr-4">
                      <strong>Channel ID:</strong> {currentChannel.id}
                    </span>
                    <span className="mr-4">
                      <strong>API Key:</strong> {currentChannel.publishableKey.substring(0, 15)}...
                    </span>
                    {currentChannel.domain && (
                      <span>
                        <strong>Domain:</strong> {currentChannel.domain}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: currentChannel.theme?.primaryColor }}
              />
            </div>
          </div>
        </section>

        {/* Products Section */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Products from {currentChannel.name}
            </h2>
            <div className="text-sm text-gray-500">
              Filtered by sales channel via publishable API key
            </div>
          </div>
          
          <ProductList limit={12} />
        </section>

        {/* Technical Details */}
        <section className="mt-12 bg-gray-100 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            🔧 Technical Implementation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">How Channel Switching Works:</h4>
              <ul className="text-gray-600 space-y-1">
                <li>• Each channel has a unique publishable API key</li>
                <li>• Products are automatically filtered by sales channel</li>
                <li>• Channel selection is persisted in localStorage</li>
                <li>• Theme colors change based on selected channel</li>
                <li>• URL parameters can override channel selection</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Current Channel Details:</h4>
              <div className="text-gray-600 space-y-1">
                <div><strong>Name:</strong> {currentChannel.name}</div>
                <div><strong>ID:</strong> {currentChannel.id}</div>
                <div><strong>API Key:</strong> {currentChannel.publishableKey.substring(0, 20)}...</div>
                <div><strong>Primary Color:</strong> {currentChannel.theme?.primaryColor}</div>
                <div><strong>Category:</strong> {currentChannel.metadata?.category}</div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
