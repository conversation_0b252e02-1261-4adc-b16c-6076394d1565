'use client';

import React from 'react';

interface ChartCardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
}

export default function ChartCard({
  title,
  description,
  children,
  className = '',
  actions,
}: ChartCardProps) {
  return (
    <div
      className={`bg-card text-card-foreground rounded-lg border shadow-sm ${className}`}
    >
      <div className='flex flex-col space-y-1.5 p-6'>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0'>
          <div className='min-w-0 flex-1'>
            <h3
              className='text-2xl font-semibold leading-none tracking-tight truncate'
              title={title}
            >
              {title}
            </h3>
            {description && (
              <p
                className='text-sm text-muted-foreground mt-1 line-clamp-2'
                title={description}
              >
                {description}
              </p>
            )}
          </div>
          {actions && (
            <div className='flex items-center space-x-2 flex-shrink-0'>
              {actions}
            </div>
          )}
        </div>
      </div>
      <div className='px-6 pb-6'>
        <div className='h-80 w-full overflow-hidden'>
          <div className='w-full h-full'>{children}</div>
        </div>
      </div>
    </div>
  );
}

// Simple line chart component using SVG
interface LineChartProps {
  data: Array<{ label: string; value: number }>;
  color?: string;
  height?: number;
}

export function SimpleLineChart({
  data,
  color = '#3B82F6',
  height = 320,
}: LineChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className='flex items-center justify-center h-full text-muted-foreground'>
        No data available
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue || 1;

  // Use responsive dimensions with proper padding for better label spacing
  const padding = { left: 50, right: 50, top: 30, bottom: 60 };
  const chartWidth = 300; // Adjusted width to fit better in container
  const chartHeight = height - padding.top - padding.bottom;
  const totalWidth = chartWidth + padding.left + padding.right;
  const totalHeight = height;

  const points = data
    .map((item, index) => {
      const x =
        padding.left +
        (data.length === 1
          ? chartWidth / 2
          : (index / (data.length - 1)) * chartWidth);
      const y =
        padding.top +
        chartHeight -
        ((item.value - minValue) / range) * chartHeight;
      return `${x},${y}`;
    })
    .join(' ');

  return (
    <div className='w-full h-full min-h-[320px]'>
      <svg
        viewBox={`0 0 ${totalWidth} ${totalHeight}`}
        className='w-full h-full'
        preserveAspectRatio='xMidYMid meet'
        style={{ maxHeight: '320px' }}
      >
        {/* Chart background */}
        <rect
          x={padding.left}
          y={padding.top}
          width={chartWidth}
          height={chartHeight}
          fill='#fafafa'
          stroke='#e5e7eb'
          strokeWidth='0.5'
        />

        {/* Line */}
        <polyline fill='none' stroke={color} strokeWidth='2' points={points} />

        {/* Data points */}
        {data.map((item, index) => {
          const x =
            padding.left +
            (data.length === 1
              ? chartWidth / 2
              : (index / (data.length - 1)) * chartWidth);
          const y =
            padding.top +
            chartHeight -
            ((item.value - minValue) / range) * chartHeight;
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r='4'
              fill={color}
              className='hover:opacity-80 transition-opacity'
              style={{ cursor: 'pointer' }}
            >
              <title>{`${item.label}: ${item.value.toLocaleString()}`}</title>
            </circle>
          );
        })}

        {/* X-axis labels with better spacing */}
        {data.map((item, index) => {
          const x =
            padding.left +
            (data.length === 1
              ? chartWidth / 2
              : (index / (data.length - 1)) * chartWidth);
          return (
            <text
              key={index}
              x={x}
              y={totalHeight - 15}
              textAnchor='middle'
              className='text-xs font-medium'
              fill='#6B7280'
              fontSize='12'
            >
              {item.label}
            </text>
          );
        })}
      </svg>
    </div>
  );
}

// Simple bar chart component
interface BarChartProps {
  data: Array<{ label: string; value: number; color?: string }>;
  height?: number;
}

export function SimpleBarChart({ data, height = 320 }: BarChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className='flex items-center justify-center h-full text-gray-500'>
        No data available
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));

  // Use responsive dimensions with proper padding for rotated labels
  const padding = { left: 40, right: 40, top: 30, bottom: 100 }; // Increased bottom padding for rotated text
  const chartWidth = 400; // Increased width for better spacing
  const chartHeight = height - padding.top - padding.bottom;
  const totalWidth = chartWidth + padding.left + padding.right;
  const totalHeight = height;

  // Calculate bar dimensions with better spacing
  const availableWidth = chartWidth - (data.length + 1) * 10; // 10px spacing between bars
  const barWidth = Math.max(availableWidth / data.length, 20); // Minimum 20px bar width
  const barSpacing = 10;

  return (
    <div className='w-full h-full min-h-[320px]'>
      <svg
        viewBox={`0 0 ${totalWidth} ${totalHeight}`}
        className='w-full h-full'
        preserveAspectRatio='xMidYMid meet'
        style={{ maxHeight: '320px' }}
      >
        {data.map((item, index) => {
          const barHeight = Math.max((item.value / maxValue) * chartHeight, 2); // Minimum height
          const x = padding.left + barSpacing + index * (barWidth + barSpacing);
          const y = padding.top + chartHeight - barHeight;

          return (
            <g key={index}>
              <rect
                x={x}
                y={y}
                width={barWidth}
                height={barHeight}
                fill={item.color || '#3B82F6'}
                className='hover:opacity-80 transition-opacity'
                style={{ cursor: 'pointer' }}
              >
                <title>{`${item.label}: ${item.value}`}</title>
              </rect>

              {/* Value label on top of bar */}
              <text
                x={x + barWidth / 2}
                y={y - 8}
                textAnchor='middle'
                className='text-xs font-medium'
                fill='#111827'
                fontSize='11'
              >
                {item.value}
              </text>

              {/* Product name label at bottom - with rotation for better fit */}
              <text
                x={x + barWidth / 2}
                y={totalHeight - 30}
                textAnchor='middle'
                className='text-xs'
                fill='#6B7280'
                fontSize='11'
                transform={`rotate(-45, ${x + barWidth / 2}, ${
                  totalHeight - 30
                })`}
              >
                {item.label.length > 12
                  ? item.label.substring(0, 12) + '...'
                  : item.label}
              </text>
            </g>
          );
        })}
      </svg>
    </div>
  );
}

// Donut chart component
interface DonutChartProps {
  data: Array<{ label: string; value: number; color: string }>;
  size?: number;
}

export function SimpleDonutChart({ data, size = 240 }: DonutChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className='flex items-center justify-center h-full text-gray-500'>
        No data available
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);
  const center = size / 2;
  const radius = size / 2 - 30; // Adequate margin to prevent clipping
  const innerRadius = radius * 0.6;

  let cumulativePercentage = 0;

  return (
    <div className='flex items-center justify-center h-full w-full'>
      <div className='flex flex-col lg:flex-row items-center justify-center space-y-6 lg:space-y-0 lg:space-x-8 w-full'>
        {/* Chart positioned first on mobile, second on desktop */}
        <div className='flex-shrink-0'>
          <svg
            width={size}
            height={size}
            className='transform -rotate-90'
            viewBox={`0 0 ${size} ${size}`}
            style={{ overflow: 'visible' }}
          >
            {data.map((item, index) => {
              const percentage = (item.value / total) * 100;
              const strokeDasharray = `${percentage} ${100 - percentage}`;
              const strokeDashoffset = -cumulativePercentage;

              cumulativePercentage += percentage;

              return (
                <circle
                  key={index}
                  cx={center}
                  cy={center}
                  r={radius}
                  fill='transparent'
                  stroke={item.color}
                  strokeWidth={radius - innerRadius}
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  className='transition-all duration-300 hover:opacity-80'
                  style={{ cursor: 'pointer' }}
                >
                  <title>{`${item.label}: ${item.value} (${percentage.toFixed(
                    1
                  )}%)`}</title>
                </circle>
              );
            })}
          </svg>
        </div>

        {/* Legend positioned second */}
        <div className='flex min-w-0'>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
            {data.map((item, index) => {
              const percentage = ((item.value / total) * 100).toFixed(1);
              return (
                <div
                  key={index}
                  className='flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors'
                >
                  <div
                    className='w-4 h-4 rounded-full flex-shrink-0'
                    style={{ backgroundColor: item.color }}
                  />
                  <div className='flex-1 min-w-0'>
                    <div
                      className='text-sm font-medium text-gray-900 truncate'
                      title={item.label}
                    >
                      {item.label}
                    </div>
                    <div className='text-xs text-gray-500'>
                      {item.value} ({percentage}%)
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
