import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import MetricCard, {
  RevenueCard,
  OrdersCard,
  CustomersCard,
  ConversionCard,
  ONDCNetworkCard,
  InventoryCard,
} from '../MetricCard';

describe('MetricCard', () => {
  it('renders basic metric card with title and value', () => {
    render(<MetricCard title='Test Metric' value='1,234' />);

    expect(screen.getByText('Test Metric')).toBeInTheDocument();
    expect(screen.getByText('1,234')).toBeInTheDocument();
  });

  it('has standardized card dimensions (h-64)', () => {
    const { container } = render(<MetricCard title='Test' value='100' />);

    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('h-64');
  });

  it('displays change indicator with correct styling', () => {
    render(
      <MetricCard
        title='Test Metric'
        value='1,234'
        change={{ value: '+12.5%', type: 'increase' }}
      />
    );

    const changeElement = screen.getByText('+12.5%');
    expect(changeElement).toBeInTheDocument();
    expect(changeElement).toHaveClass('text-green-600');
  });

  it('displays decrease change with red styling', () => {
    render(
      <MetricCard
        title='Test Metric'
        value='1,234'
        change={{ value: '-5.2%', type: 'decrease' }}
      />
    );

    const changeElement = screen.getByText('-5.2%');
    expect(changeElement).toHaveClass('text-red-600');
  });

  it('implements text truncation with tooltip for long titles', () => {
    render(
      <MetricCard
        title='Very Long Metric Title That Should Be Truncated'
        value='1,234'
      />
    );

    const titleElement = screen.getByText(
      'Very Long Metric Title That Should Be Truncated'
    );
    expect(titleElement).toHaveClass('truncate');
    expect(titleElement).toHaveAttribute(
      'title',
      'Very Long Metric Title That Should Be Truncated'
    );
  });

  it('uses flexbox layout for consistent card structure', () => {
    const { container } = render(<MetricCard title='Test' value='100' />);

    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('flex', 'flex-col');
  });

  it('displays description when provided', () => {
    render(
      <MetricCard
        title='Test Metric'
        value='1,234'
        description='This is a test description'
      />
    );

    expect(screen.getByText('This is a test description')).toBeInTheDocument();
  });

  it('renders icon when provided', () => {
    const testIcon = <svg data-testid='test-icon' />;
    render(<MetricCard title='Test Metric' value='1,234' icon={testIcon} />);

    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });
});

describe('Specific Metric Cards', () => {
  it('renders RevenueCard with correct props', () => {
    render(
      <RevenueCard
        revenue='₹1,24,500'
        change={{ value: '+12.5%', type: 'increase' }}
      />
    );

    expect(screen.getByText('Total Revenue')).toBeInTheDocument();
    expect(screen.getByText('₹1,24,500')).toBeInTheDocument();
    expect(
      screen.getByText('Revenue from ONDC network sales')
    ).toBeInTheDocument();
  });

  it('renders OrdersCard with correct props', () => {
    render(
      <OrdersCard orders={1234} change={{ value: '+8.2%', type: 'increase' }} />
    );

    expect(screen.getByText('Total Orders')).toBeInTheDocument();
    expect(screen.getByText('1234')).toBeInTheDocument();
    expect(screen.getByText('Orders received this month')).toBeInTheDocument();
  });

  it('renders CustomersCard with correct props', () => {
    render(
      <CustomersCard
        customers={856}
        change={{ value: '+15.3%', type: 'increase' }}
      />
    );

    expect(screen.getByText('Active Customers')).toBeInTheDocument();
    expect(screen.getByText('856')).toBeInTheDocument();
    expect(
      screen.getByText('Customers who made purchases')
    ).toBeInTheDocument();
  });

  it('renders ConversionCard with correct props', () => {
    render(
      <ConversionCard
        rate='4.2%'
        change={{ value: '+0.8%', type: 'increase' }}
      />
    );

    expect(screen.getByText('Conversion Rate')).toBeInTheDocument();
    expect(screen.getByText('4.2%')).toBeInTheDocument();
    expect(
      screen.getByText('Visitors to customers conversion')
    ).toBeInTheDocument();
  });

  it('renders ONDCNetworkCard with correct props', () => {
    render(
      <ONDCNetworkCard
        connections={12}
        change={{ value: '+2', type: 'increase' }}
      />
    );

    expect(screen.getByText('ONDC Network Reach')).toBeInTheDocument();
    expect(screen.getByText('12')).toBeInTheDocument();
    expect(screen.getByText('Connected buyer apps')).toBeInTheDocument();
  });

  it('renders InventoryCard with correct props', () => {
    render(
      <InventoryCard
        products={245}
        change={{ value: '+18', type: 'increase' }}
      />
    );

    expect(screen.getByText('Active Products')).toBeInTheDocument();
    expect(screen.getByText('245')).toBeInTheDocument();
    expect(screen.getByText('Products listed on ONDC')).toBeInTheDocument();
  });

  it('all specific cards have standardized dimensions', () => {
    const cards = [
      <RevenueCard revenue='₹1,000' />,
      <OrdersCard orders={100} />,
      <CustomersCard customers={50} />,
      <ConversionCard rate='5%' />,
      <ONDCNetworkCard connections={10} />,
      <InventoryCard products={200} />,
    ];

    cards.forEach((card, index) => {
      const { container } = render(card);
      const cardElement = container.firstChild as HTMLElement;
      expect(cardElement).toHaveClass('h-64');
    });
  });
});
