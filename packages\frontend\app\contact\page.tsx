import React from 'react';
import Breadcrumbs from '@/components/Breadcrumbs';
import PageHero from '@/components/ui/PageHero';
import ContentCard from '@/components/ui/ContentCard';
import StyledContent from '@/components/ui/StyledContent';

import { getPageBySlug, type Page } from '@/lib/strapi-api';

// Server-side function to fetch page content
async function getContactPageContent(): Promise<Page | null> {
  try {
    console.log('Server: Fetching contact page content from Strapi...');
    const content = await getPageBySlug('contact');
    console.log('Server: Fetched content:', content?.title);
    return content;
  } catch (error) {
    console.error('Server: Error fetching contact page:', error);
    return null;
  }
}

export default async function ContactPage() {
  // Fetch content on the server side
  const pageContent = await getContactPageContent();
  // Fallback content if Strapi content is not available
  const fallbackContent: Page = {
    id: 2,
    title: 'Contact Us',
    slug: 'contact',
    content: `<h2>Get in Touch</h2>
<p>Have questions about the ONDC Seller Platform? We're here to help!</p>
<h3>Contact Information</h3>
<p><strong>Email:</strong> <EMAIL></p>
<p><strong>Phone:</strong> +91 1234567890</p>
<p><strong>Address:</strong><br>123 Commerce Street<br>Tech Park, Bangalore<br>Karnataka, India</p>
<h3>Business Hours</h3>
<p>Monday - Friday: 9:00 AM - 6:00 PM IST<br>Saturday: 10:00 AM - 2:00 PM IST<br>Sunday: Closed</p>`,
    excerpt: 'Contact ONDC Seller Platform for support',
    metaTitle: 'Contact Us - ONDC Seller Platform',
    metaDescription: 'Get in touch with ONDC Seller Platform support team.',
    status: 'published',
    template: 'contact',
    featured: false,
    author: 'Admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // Use Strapi content if available, otherwise use fallback
  const displayContent = pageContent || fallbackContent;

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Hero Section */}
      <PageHero
        title={displayContent.title}
        description={
          displayContent.excerpt ||
          "Have questions about the ONDC Seller Platform? We're here to help! Get in touch with our support team."
        }
        icon={
          <svg
            className='w-12 h-12 text-white'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
            />
          </svg>
        }
        gradient='blue'
      />

      {/* Main Content */}
      <div className='px-4 sm:px-6 lg:px-8 py-12'>
        {/* Breadcrumbs */}
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'Contact Us', href: '/contact', active: true },
          ]}
        />

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
          {/* Contact Information */}
          <div className='space-y-6'>
            <ContentCard variant='elevated' padding='xl'>
              <StyledContent content={displayContent.content} />
            </ContentCard>

            {/* Quick Contact Cards */}
            <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
              <ContentCard
                variant='bordered'
                padding='md'
                className='text-center bg-gradient-to-br from-blue-50 to-blue-100'
              >
                <div className='w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3'>
                  <svg
                    className='w-6 h-6 text-white'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z'
                    />
                  </svg>
                </div>
                <h3 className='font-semibold text-gray-900 mb-1'>
                  Phone Support
                </h3>
                <p className='text-sm text-gray-600 mb-2'>
                  Mon-Fri 9AM-6PM IST
                </p>
                <a
                  href='tel:+911234567890'
                  className='text-blue-600 hover:text-blue-800 font-medium'
                >
                  +91 1234567890
                </a>
              </ContentCard>

              <ContentCard
                variant='bordered'
                padding='md'
                className='text-center bg-gradient-to-br from-green-50 to-green-100'
              >
                <div className='w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3'>
                  <svg
                    className='w-6 h-6 text-white'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
                    />
                  </svg>
                </div>
                <h3 className='font-semibold text-gray-900 mb-1'>Live Chat</h3>
                <p className='text-sm text-gray-600 mb-2'>Available 24/7</p>
                <button className='text-green-600 hover:text-green-800 font-medium'>
                  Start Chat
                </button>
              </ContentCard>
            </div>
          </div>

          {/* Contact Form */}
          <div>
            <ContentCard variant='elevated' padding='xl'>
              <div className='mb-6'>
                <h2 className='text-2xl font-bold text-gray-900 mb-2'>
                  Send us a Message
                </h2>
                <p className='text-gray-600'>
                  Fill out the form below and we'll get back to you within 24
                  hours.
                </p>
              </div>
              {/* Simple Contact Form */}
              <div className='space-y-6'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700 mb-2'>
                      Full Name *
                    </label>
                    <input
                      type='text'
                      className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200'
                      placeholder='Enter your full name'
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700 mb-2'>
                      Email Address *
                    </label>
                    <input
                      type='email'
                      className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200'
                      placeholder='Enter your email address'
                    />
                  </div>
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Subject *
                  </label>
                  <input
                    type='text'
                    className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200'
                    placeholder='What is this regarding?'
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Message *
                  </label>
                  <textarea
                    rows={6}
                    className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-vertical'
                    placeholder='Please describe your inquiry in detail...'
                  />
                </div>

                <div className='flex items-center justify-between pt-4'>
                  <p className='text-sm text-gray-600'>* Required fields</p>
                  <button
                    type='submit'
                    className='px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 flex items-center space-x-2'
                  >
                    <span>Send Message</span>
                    <svg
                      className='w-5 h-5'
                      fill='none'
                      stroke='currentColor'
                      viewBox='0 0 24 24'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M12 19l9 2-9-18-9 18 9-2zm0 0v-8'
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </ContentCard>
          </div>
        </div>
      </div>
    </div>
  );
}
