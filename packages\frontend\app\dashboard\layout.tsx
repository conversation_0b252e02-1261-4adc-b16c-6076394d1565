import React from 'react';
import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'ONDC Seller Dashboard',
  description: 'Manage your ONDC seller operations and analytics',
};

export default function DashboardLayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  // This layout provides dashboard-specific styling without duplicating HTML structure
  return (
    <div
      className='dashboard-root min-h-screen bg-gray-50'
      suppressHydrationWarning
    >
      {children}
    </div>
  );
}
