'use client';

import React, { useState, useEffect, Suspense, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTenant } from '@/contexts/TenantContext';
import ProductListingPage, {
  Product,
  ProductListingConfig,
} from '@/components/common/ProductListingPage';
import { useMedusaBackendProducts } from '@/hooks/useMedusaBackendProducts';
import { MedusaProduct } from '@/lib/medusa-backend-api';
import { useCategoryStore } from '@/stores/categoriesStore';
import { useCollectionStore } from '@/stores/collectionStore';

const DealsPageContent = () => {
  const searchParams = useSearchParams();
  const dealType = searchParams?.get('type') || 'top-deals';
  const { tenantId } = useTenant();

  const [productData, setProductData] = useState<Product[]>([]);
  const [error, setError] = useState<string | null>(null);

  const {
    products,
    loading,
    error: apiError,
    fetchProduct,
    fetchProducts,
  } = useMedusaBackendProducts();

  const setSelectedProductId = useCategoryStore(
    state => state.setSelectedProductId
  );

  const collectionData = useCollectionStore(state => state.collectionData);

  // Transform Medusa products to Product format
  const transformMedusaProductsToProducts = useCallback(
    (medusaProducts: MedusaProduct[]): Product[] =>
      medusaProducts.map((medusaProduct: MedusaProduct) => {
        // Get the first variant and its first price
        const firstVariant = medusaProduct.variants?.[0];
        const firstPrice = firstVariant?.metadata;

        // Calculate prices (fallback to default values if not available)
        const originalPrice = firstPrice?.original_price || 2299;
        const salePrice = firstPrice?.sale_price;
        const discount = Math.round(
          ((originalPrice - salePrice) / originalPrice) * 100
        );

        return {
          id: medusaProduct.id,
          name: medusaProduct.title,
          slug:
            medusaProduct.handle ||
            medusaProduct.title.toLowerCase().replace(/\s+/g, '-'),
          originalPrice,
          salePrice,
          discount,
          image:
            medusaProduct.thumbnail ||
            `https://picsum.photos/400/400?random=${medusaProduct.id}`,
          category: medusaProduct.categories?.[0]?.name || 'Products',
          subcategory: medusaProduct.categories?.[1]?.name || 'General',
          tenantId: tenantId || 'default',
          rating: 4.5,
          reviewCount: Math.floor(Math.random() * 500) + 50,
          badge: medusaProduct.status === 'published' ? 'Available' : 'Draft',
          brand: medusaProduct.type?.value || 'Brand',
          inStock:
            medusaProduct.status === 'published' &&
            (firstVariant?.metadata?.product_quantity || 0) > 0,
          tags: medusaProduct.tags?.map(tag => tag.value) || [],
          savings: originalPrice - salePrice,
        };
      }),
    [tenantId]
  );

  // Fetch products based on deal type
  const fetchDealsProducts = async () => {
    try {
      setError(null);
      const resp = await fetchProducts({
        collection_id: [collectionData[0]?.collection_id],
      });
      console.log('resp::::::::', resp);
      // return resp;
    } catch (err) {
      setError('Failed to load products');
    }
  };

  // Load products on component mount and when deal type changes
  useEffect(() => {
    if (!collectionData[0]?.collection_id) return;
    fetchDealsProducts();
  }, [collectionData[0]?.collection_id]);

  // Transform products when they change
  useEffect(() => {
    if (products && products.length > 0) {
      const transformedProducts = transformMedusaProductsToProducts(products);
      setProductData(transformedProducts);
    } else {
      setProductData([]);
    }
  }, [products]);

  const getPageConfig = (): ProductListingConfig => {
    const baseConfig = {
      showDiscountBadge: true,
      showSaleBadge: true,
      showPriceComparison: true,
      showSavingsAmount: true,
      buttonText: 'View Product',
      buttonStyle: 'primary' as const,
    };

    // Configurations based on deal type
    switch (dealType) {
      case 'featured-products':
        return {
          ...baseConfig,
          title: 'Featured Products',
          subtitle: 'Handpicked products just for you',
          breadcrumbs: [
            { label: 'Home', href: '/' },
            { label: 'Featured Products' },
          ],
        };
      case 'hot-picks':
        return {
          ...baseConfig,
          title: 'Hot Picks',
          subtitle: 'Trending products everyone loves',
          breadcrumbs: [{ label: 'Home', href: '/' }, { label: 'Hot Picks' }],
        };
      case 'top-deals':
      default:
        return {
          ...baseConfig,
          title: 'Top Deals',
          subtitle: 'Best offers with up to 70% off',
          breadcrumbs: [{ label: 'Home', href: '/' }, { label: 'Top Deals' }],
        };
    }
  };

  const handleViewProduct = async (id: string) => {
    try {
      setSelectedProductId(id);
      await fetchProduct(id);
    } catch (err) {
      setError('Failed to load product details');
    }
  };

  // Show error state if there's an error
  if (error || apiError) {
    console.error({ error, apiError });
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>
            Something went wrong
          </h2>
          <p className='text-gray-600'>{error || apiError}</p>
        </div>
      </div>
    );
  }
  console.log('::::::::productData:::::::::::', productData);
  return (
    <ProductListingPage
      products={productData}
      config={getPageConfig()}
      isLoading={loading}
      setProductId={setSelectedProductId}
      handleViewProduct={handleViewProduct}
    />
  );
};

// Loading component
const DealsPageLoading = () => (
  <div className='flex items-center justify-center min-h-[400px]'>
    <div className='text-center'>
      <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
      <p className='text-gray-600'>Loading deals...</p>
    </div>
  </div>
);

const DealsPage = () => (
  <Suspense fallback={<DealsPageLoading />}>
    <DealsPageContent />
  </Suspense>
);

export default DealsPage;
