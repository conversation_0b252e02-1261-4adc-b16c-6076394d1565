'use client';
import { useCategoryStore } from '@/stores/categoriesStore';
import MedusaProductDetailPage from '../../../../components/products/MedusaProductDetailPage';
import { MedusaCartProvider } from '@/hooks/useMedusaCart';
import { ToastProvider } from '@/components/common/ToastProvider';
import { useCollectionStore } from '@/stores/collectionStore';

export default function CollectionProductPage() {
  const selectedProductId = useCategoryStore(state => state.selectedProductId);
  const selectedSubCategoryId = useCategoryStore(
    state => state.selectedSubCategoryId
  );
  const selectedCategoryId = useCategoryStore(
    state => state.selectedCategoryId
  );
  const collectionData = useCollectionStore(state => state.collectionData);
  console.log({ collectionData });

  // const productId = selectedProductId;

  console.log('selectedProductId:::::::::', selectedProductId);
  return (
    <MedusaCartProvider>
      <ToastProvider>
        <MedusaProductDetailPage
          productId={selectedProductId}
          categoryId={''}
          subCategoryId={''}
          collectionData={collectionData}
        />
      </ToastProvider>
    </MedusaCartProvider>
  );
}
