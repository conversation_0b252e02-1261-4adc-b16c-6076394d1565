'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Breadcrumbs,
  Link,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  NavigateNext as NavigateNextIcon,
  Inventory as InventoryIcon,
  AttachMoney as AttachMoneyIcon,
  Category as CategoryIcon,
  Image as ImageIcon,
} from '@mui/icons-material';

// Mock Products Data
const MOCK_PRODUCTS = [
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    description:
      'High-quality wireless headphones with active noise cancellation, 30-hour battery life, and premium sound quality. Perfect for music lovers and professionals who demand the best audio experience.',
    short_description:
      'High-quality wireless headphones with active noise cancellation',
    price: 8999,
    sale_price: 7999,
    sku: 'WH-001',
    inventory_quantity: 45,
    product_status: 'Published',
    featured: true,
    weight: 250,
    brand: 'TechStore',
    category: 'Electronics',
    tags: 'electronics,audio,wireless,noise-cancellation',
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    ],
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:00:00.000Z',
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    description:
      'Advanced fitness tracking watch with heart rate monitoring, GPS, and 7-day battery life. Track your workouts, monitor your health, and stay connected.',
    short_description:
      'Advanced fitness tracking watch with heart rate monitoring',
    price: 12999,
    sale_price: undefined,
    sku: 'SW-002',
    inventory_quantity: 32,
    product_status: 'Published',
    featured: false,
    weight: 45,
    brand: 'TechStore',
    category: 'Electronics',
    tags: 'electronics,fitness,smartwatch,health',
    images: [
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
    ],
    createdAt: '2025-01-02T00:00:00.000Z',
    updatedAt: '2025-01-02T00:00:00.000Z',
  },
  {
    id: 3,
    name: 'Wireless Gaming Mouse',
    description:
      'High-precision wireless gaming mouse with RGB lighting and customizable buttons. Perfect for competitive gaming with ultra-low latency.',
    short_description: 'High-precision wireless gaming mouse with RGB lighting',
    price: 4999,
    sale_price: 3999,
    sku: 'GM-003',
    inventory_quantity: 0,
    product_status: 'Out of Stock',
    featured: false,
    weight: 85,
    brand: 'TechStore',
    category: 'Electronics',
    tags: 'electronics,gaming,mouse,wireless',
    images: [
      'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
    ],
    createdAt: '2025-01-03T00:00:00.000Z',
    updatedAt: '2025-01-03T00:00:00.000Z',
  },
];

export default function DemoProductViewPage() {
  const router = useRouter();
  const params = useParams();
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));

        // Find product by ID
        const productId = parseInt(params.id as string);
        const foundProduct = MOCK_PRODUCTS.find(p => p.id === productId);

        if (foundProduct) {
          setProduct(foundProduct);
        } else {
          console.error('Product not found:', productId);
          // Set a default product or handle error
          setProduct(MOCK_PRODUCTS[0]);
        }
      } catch (error) {
        console.error('Error fetching product:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [params.id]);

  const handleBack = () => {
    router.push('/demo-admin-products');
  };

  const handleEdit = () => {
    router.push(`/demo-admin-products/${params.id}/edit`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Published':
        return 'success';
      case 'Draft':
        return 'warning';
      case 'Out of Stock':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Container maxWidth='lg' sx={{ py: 4 }}>
        <Typography>Loading product...</Typography>
      </Container>
    );
  }

  if (!product) {
    return (
      <Container maxWidth='lg' sx={{ py: 4 }}>
        <Typography>Product not found</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth='lg' sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize='small' />}
          sx={{ mb: 2 }}
        >
          <Link color='inherit' href='/' underline='hover'>
            Home
          </Link>
          <Link color='inherit' href='/demo-admin-products' underline='hover'>
            Demo Admin
          </Link>
          <Link color='inherit' href='/demo-admin-products' underline='hover'>
            Products
          </Link>
          <Typography color='text.primary'>{product.name}</Typography>
        </Breadcrumbs>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 3,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton onClick={handleBack}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant='h4' component='h1' fontWeight='bold'>
              {product.name}
            </Typography>
            <Chip
              label={product.product_status}
              color={getStatusColor(product.product_status) as any}
              size='medium'
            />
            {product.featured && (
              <Chip label='Featured' color='primary' size='medium' />
            )}
          </Box>
          <Button
            variant='contained'
            startIcon={<EditIcon />}
            onClick={handleEdit}
          >
            Edit Product
          </Button>
        </Box>
      </Box>

      <Grid container spacing={4}>
        {/* Main Content */}
        <Grid size={{ xs: 12, md: 8 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Product Images */}
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <ImageIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant='h6'>Product Images</Typography>
                </Box>
                <Grid container spacing={2}>
                  {product.images.map((image: string, index: number) => (
                    <Grid size={{ xs: 6, sm: 4, md: 3 }} key={index}>
                      <Box
                        sx={{
                          position: 'relative',
                          aspectRatio: '1',
                          borderRadius: 1,
                          overflow: 'hidden',
                          border: '1px solid',
                          borderColor: 'grey.300',
                        }}
                      >
                        <img
                          src={image}
                          alt={`${product.name} ${index + 1}`}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                          }}
                        />
                        {index === 0 && (
                          <Chip
                            label='Main'
                            size='small'
                            color='primary'
                            sx={{
                              position: 'absolute',
                              bottom: 4,
                              left: 4,
                            }}
                          />
                        )}
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>

            {/* Product Information */}
            <Card>
              <CardContent>
                <Typography variant='h6' gutterBottom>
                  Product Information
                </Typography>
                <Typography variant='body1' paragraph>
                  <strong>Short Description:</strong>
                </Typography>
                <Typography variant='body2' color='text.secondary' paragraph>
                  {product.short_description}
                </Typography>
                <Typography variant='body1' paragraph>
                  <strong>Full Description:</strong>
                </Typography>
                <Typography variant='body2' color='text.secondary' paragraph>
                  {product.description}
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Typography variant='body1' paragraph>
                  <strong>Tags:</strong>
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {product.tags.split(',').map((tag: string, index: number) => (
                    <Chip
                      key={index}
                      label={tag.trim()}
                      size='small'
                      variant='outlined'
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Box>
        </Grid>

        {/* Sidebar */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Pricing */}
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AttachMoneyIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant='h6'>Pricing</Typography>
                </Box>
                <TableContainer>
                  <Table size='small'>
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <strong>Regular Price</strong>
                        </TableCell>
                        <TableCell align='right'>
                          {formatPrice(product.price)}
                        </TableCell>
                      </TableRow>
                      {product.sale_price && (
                        <TableRow>
                          <TableCell>
                            <strong>Sale Price</strong>
                          </TableCell>
                          <TableCell
                            align='right'
                            sx={{ color: 'success.main', fontWeight: 'bold' }}
                          >
                            {formatPrice(product.sale_price)}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            {/* Inventory */}
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <InventoryIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant='h6'>Inventory & Details</Typography>
                </Box>
                <TableContainer>
                  <Table size='small'>
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <strong>SKU</strong>
                        </TableCell>
                        <TableCell align='right'>{product.sku}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <strong>Stock Quantity</strong>
                        </TableCell>
                        <TableCell align='right'>
                          <Chip
                            label={product.inventory_quantity}
                            color={
                              product.inventory_quantity > 0
                                ? 'success'
                                : 'error'
                            }
                            size='small'
                          />
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <strong>Weight</strong>
                        </TableCell>
                        <TableCell align='right'>{product.weight}g</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <strong>Brand</strong>
                        </TableCell>
                        <TableCell align='right'>{product.brand}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            {/* Organization */}
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <CategoryIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant='h6'>Organization</Typography>
                </Box>
                <TableContainer>
                  <Table size='small'>
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <strong>Category</strong>
                        </TableCell>
                        <TableCell align='right'>{product.category}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <strong>Created</strong>
                        </TableCell>
                        <TableCell align='right'>
                          {formatDate(product.createdAt)}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <strong>Updated</strong>
                        </TableCell>
                        <TableCell align='right'>
                          {formatDate(product.updatedAt)}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Box>
        </Grid>
      </Grid>
    </Container>
  );
}
