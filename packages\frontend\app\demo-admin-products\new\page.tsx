'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  Alert,
  Breadcrumbs,
  Link,
  Divider,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  NavigateNext as NavigateNextIcon,
  Image as ImageIcon,
} from '@mui/icons-material';

interface ProductFormData {
  name: string;
  description: string;
  short_description: string;
  sku: string;
  price: string;
  sale_price: string;
  inventory_quantity: string;
  weight: string;
  category: string;
  brand: string;
  product_status: string;
  featured: boolean;
  tags: string;
  images: File[];
}

const initialFormData: ProductFormData = {
  name: '',
  description: '',
  short_description: '',
  sku: '',
  price: '',
  sale_price: '',
  inventory_quantity: '',
  weight: '',
  category: '',
  brand: '',
  product_status: 'Draft',
  featured: false,
  tags: '',
  images: [],
};

export default function DemoAddProductPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<ProductFormData>(initialFormData);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSelectChange = (name: string) => (e: any) => {
    setFormData(prev => ({
      ...prev,
      [name]: e.target.value,
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Validate file types
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const invalidFiles = files.filter(file => !validTypes.includes(file.type));

    if (invalidFiles.length > 0) {
      alert('Please select only JPEG, PNG, or WebP images');
      return;
    }

    // Validate file sizes (max 5MB each)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const oversizedFiles = files.filter(file => file.size > maxSize);

    if (oversizedFiles.length > 0) {
      alert('Please select images smaller than 5MB');
      return;
    }

    // Update form data
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...files],
    }));

    // Create preview URLs
    const newPreviewUrls = files.map(file => URL.createObjectURL(file));
    setImagePreviewUrls(prev => [...prev, ...newPreviewUrls]);
  };

  const handleImageRemove = (index: number) => {
    // Revoke the object URL to free memory
    URL.revokeObjectURL(imagePreviewUrls[index]);

    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));

    setImagePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Product name is required';
    if (!formData.description.trim())
      newErrors.description = 'Description is required';
    if (!formData.sku.trim()) newErrors.sku = 'SKU is required';
    if (!formData.price.trim()) newErrors.price = 'Price is required';
    if (!formData.inventory_quantity.trim())
      newErrors.inventory_quantity = 'Inventory quantity is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Product data:', formData);
      console.log('Images:', formData.images);

      alert('Product created successfully!');
      router.push('/demo-admin-products');
    } catch (error) {
      console.error('Error creating product:', error);
      alert('Failed to create product. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.push('/demo-admin-products');
  };

  return (
    <Container maxWidth='lg' sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize='small' />}
          sx={{ mb: 2 }}
        >
          <Link color='inherit' href='/' underline='hover'>
            Home
          </Link>
          <Link color='inherit' href='/demo-admin-products' underline='hover'>
            Demo Admin
          </Link>
          <Link color='inherit' href='/demo-admin-products' underline='hover'>
            Products
          </Link>
          <Typography color='text.primary'>Add New Product</Typography>
        </Breadcrumbs>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
          <IconButton onClick={handleBack}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant='h4' component='h1' fontWeight='bold'>
            Add New Product
          </Typography>
        </Box>

        <Alert severity='info' sx={{ mb: 3 }}>
          This is a demo form. In the real admin panel, this would save to your
          actual product database.
        </Alert>
      </Box>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={4}>
          {/* Main Content */}
          <Grid item xs={12} md={8}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              {/* Basic Information */}
              <Card>
                <CardContent>
                  <Typography variant='h6' gutterBottom>
                    Basic Information
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label='Product Name'
                        name='name'
                        value={formData.name}
                        onChange={handleInputChange}
                        error={!!errors.name}
                        helperText={errors.name}
                        required
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label='Short Description'
                        name='short_description'
                        value={formData.short_description}
                        onChange={handleInputChange}
                        multiline
                        rows={2}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label='Description'
                        name='description'
                        value={formData.description}
                        onChange={handleInputChange}
                        error={!!errors.description}
                        helperText={errors.description}
                        multiline
                        rows={4}
                        required
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='SKU'
                        name='sku'
                        value={formData.sku}
                        onChange={handleInputChange}
                        error={!!errors.sku}
                        helperText={errors.sku}
                        required
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Brand'
                        name='brand'
                        value={formData.brand}
                        onChange={handleInputChange}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Pricing & Inventory */}
              <Card>
                <CardContent>
                  <Typography variant='h6' gutterBottom>
                    Pricing & Inventory
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Price (in paise)'
                        name='price'
                        type='number'
                        value={formData.price}
                        onChange={handleInputChange}
                        error={!!errors.price}
                        helperText={
                          errors.price ||
                          'Enter price in paise (e.g., 9999 for ₹99.99)'
                        }
                        required
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Sale Price (in paise)'
                        name='sale_price'
                        type='number'
                        value={formData.sale_price}
                        onChange={handleInputChange}
                        helperText='Optional sale price'
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Inventory Quantity'
                        name='inventory_quantity'
                        type='number'
                        value={formData.inventory_quantity}
                        onChange={handleInputChange}
                        error={!!errors.inventory_quantity}
                        helperText={errors.inventory_quantity}
                        required
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Weight (grams)'
                        name='weight'
                        type='number'
                        value={formData.weight}
                        onChange={handleInputChange}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Product Images */}
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <ImageIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant='h6'>Product Images</Typography>
                  </Box>
                  <Typography
                    variant='body2'
                    color='text.secondary'
                    sx={{ mb: 3 }}
                  >
                    Upload high-quality images of your product. First image will
                    be the main product image.
                  </Typography>

                  {/* Image Upload Area */}
                  <Box
                    sx={{
                      border: '2px dashed',
                      borderColor: 'grey.300',
                      borderRadius: 2,
                      p: 3,
                      textAlign: 'center',
                      mb: 3,
                      cursor: 'pointer',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: 'primary.50',
                      },
                    }}
                    onClick={() =>
                      document.getElementById('image-upload')?.click()
                    }
                  >
                    <input
                      id='image-upload'
                      type='file'
                      multiple
                      accept='image/jpeg,image/jpg,image/png,image/webp'
                      onChange={handleImageUpload}
                      style={{ display: 'none' }}
                    />
                    <CloudUploadIcon
                      sx={{ fontSize: 48, color: 'grey.400', mb: 2 }}
                    />
                    <Typography
                      variant='h6'
                      color='text.secondary'
                      gutterBottom
                    >
                      Click to upload images
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      Supports JPEG, PNG, WebP (max 5MB each)
                    </Typography>
                  </Box>

                  {/* Image Previews */}
                  {imagePreviewUrls.length > 0 && (
                    <Box>
                      <Typography variant='subtitle2' sx={{ mb: 2 }}>
                        Uploaded Images ({imagePreviewUrls.length})
                      </Typography>
                      <Grid container spacing={2}>
                        {imagePreviewUrls.map((url, index) => (
                          <Grid item xs={6} sm={4} md={3} key={index}>
                            <Box
                              sx={{
                                position: 'relative',
                                aspectRatio: '1',
                                borderRadius: 1,
                                overflow: 'hidden',
                                border: '1px solid',
                                borderColor: 'grey.300',
                              }}
                            >
                              <img
                                src={url}
                                alt={`Product ${index + 1}`}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover',
                                }}
                              />
                              <IconButton
                                size='small'
                                sx={{
                                  position: 'absolute',
                                  top: 4,
                                  right: 4,
                                  bgcolor: 'rgba(0, 0, 0, 0.6)',
                                  color: 'white',
                                  '&:hover': {
                                    bgcolor: 'rgba(0, 0, 0, 0.8)',
                                  },
                                }}
                                onClick={() => handleImageRemove(index)}
                              >
                                <DeleteIcon fontSize='small' />
                              </IconButton>
                              {index === 0 && (
                                <Chip
                                  label='Main'
                                  size='small'
                                  color='primary'
                                  sx={{
                                    position: 'absolute',
                                    bottom: 4,
                                    left: 4,
                                  }}
                                />
                              )}
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Box>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              {/* Status & Visibility */}
              <Card>
                <CardContent>
                  <Typography variant='h6' gutterBottom>
                    Status & Visibility
                  </Typography>
                  <Box
                    sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
                  >
                    <FormControl fullWidth>
                      <InputLabel>Product Status</InputLabel>
                      <Select
                        value={formData.product_status}
                        label='Product Status'
                        onChange={handleSelectChange('product_status')}
                      >
                        <MenuItem value='Draft'>Draft</MenuItem>
                        <MenuItem value='Published'>Published</MenuItem>
                        <MenuItem value='Out of Stock'>Out of Stock</MenuItem>
                      </Select>
                    </FormControl>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.featured}
                          onChange={handleInputChange}
                          name='featured'
                        />
                      }
                      label='Featured Product'
                    />
                  </Box>
                </CardContent>
              </Card>

              {/* Organization */}
              <Card>
                <CardContent>
                  <Typography variant='h6' gutterBottom>
                    Organization
                  </Typography>
                  <Box
                    sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
                  >
                    <FormControl fullWidth>
                      <InputLabel>Category</InputLabel>
                      <Select
                        value={formData.category}
                        label='Category'
                        onChange={handleSelectChange('category')}
                      >
                        <MenuItem value='Electronics'>Electronics</MenuItem>
                        <MenuItem value='Clothing'>Clothing</MenuItem>
                        <MenuItem value='Home & Garden'>Home & Garden</MenuItem>
                        <MenuItem value='Sports'>Sports</MenuItem>
                        <MenuItem value='Books'>Books</MenuItem>
                      </Select>
                    </FormControl>
                    <TextField
                      fullWidth
                      label='Tags'
                      name='tags'
                      value={formData.tags}
                      onChange={handleInputChange}
                      helperText='Comma-separated tags'
                      placeholder='electronics, wireless, premium'
                    />
                  </Box>
                </CardContent>
              </Card>

              {/* Actions */}
              <Card>
                <CardContent>
                  <Typography variant='h6' gutterBottom>
                    Actions
                  </Typography>
                  <Box
                    sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
                  >
                    <Button
                      type='submit'
                      variant='contained'
                      size='large'
                      startIcon={<SaveIcon />}
                      disabled={isSubmitting}
                      fullWidth
                    >
                      {isSubmitting ? 'Creating...' : 'Create Product'}
                    </Button>
                    <Button
                      variant='outlined'
                      size='large'
                      onClick={handleBack}
                      disabled={isSubmitting}
                      fullWidth
                    >
                      Cancel
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
}
