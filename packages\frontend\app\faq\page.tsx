import React from 'react';
import Breadcrumbs from '@/components/Breadcrumbs';
import PageHero from '@/components/ui/PageHero';
import ContentCard from '@/components/ui/ContentCard';
import StyledContent from '@/components/ui/StyledContent';

import { getPageBySlug, type Page } from '@/lib/strapi-api';

// Server-side function to fetch page content
async function getFAQPageContent(): Promise<Page | null> {
  try {
    console.log('Server: Fetching FAQ page content from Strapi...');
    const content = await getPageBySlug('faq');
    console.log('Server: Fetched FAQ content:', content?.title);
    return content;
  } catch (error) {
    console.error('Server: Error fetching FAQ page:', error);
    return null;
  }
}

const FAQPage = async () => {
  // Fetch content on the server side
  const pageContent = await getFAQPageContent();

  // Fallback content if Strapi content is not available
  const fallbackContent: Page = {
    id: 3,
    title: 'Frequently Asked Questions',
    slug: 'faq',
    content: `<h2>Frequently Asked Questions</h2>
<p>Find answers to common questions about ONDC Marketplace</p>

<h3>General</h3>
<h4>What is ONDC Seller?</h4>
<p>ONDC Seller is a comprehensive e-commerce platform built on the Open Network for Digital Commerce, enabling sellers to reach customers across India through a unified digital marketplace.</p>

<h4>How do I create a seller account?</h4>
<p>You can create a seller account by clicking the 'Sign Up' button in the header and filling out the registration form with your business information and required documents.</p>

<h4>Is it free to use ONDC Seller?</h4>
<p>ONDC Seller offers competitive pricing for sellers with transparent fee structures. Contact our sales team for detailed pricing information based on your business needs.</p>

<h3>Orders & Shipping</h3>
<h4>How can I track my order?</h4>
<p>You can track your order by visiting the 'My Orders' section in your account or using the 'Track Order' link in the header with your order number.</p>

<h4>What are the shipping charges?</h4>
<p>Shipping charges vary by seller and location. We offer free shipping on orders over ₹500 from participating sellers.</p>

<h4>How long does delivery take?</h4>
<p>Delivery times vary by location and seller. Most orders are delivered within 2-7 business days.</p>

<h3>Returns & Refunds</h3>
<h4>What is your return policy?</h4>
<p>We offer a 7-day return policy for most items. Products must be in original condition with tags and packaging intact.</p>

<h4>How do I return an item?</h4>
<p>You can initiate a return from your 'My Orders' section. Select the item you want to return and follow the instructions.</p>

<h4>When will I receive my refund?</h4>
<p>Refunds are processed within 5-7 business days after we receive and verify the returned item.</p>

<h3>Payments</h3>
<h4>What payment methods do you accept?</h4>
<p>We accept all major credit/debit cards, UPI, net banking, and digital wallets like Paytm, PhonePe, and Google Pay.</p>

<h4>Is my payment information secure?</h4>
<p>Yes, we use industry-standard encryption and security measures to protect your payment information.</p>

<h4>Can I pay cash on delivery?</h4>
<p>Cash on delivery is available for select products and locations. You'll see this option during checkout if available.</p>`,
    excerpt:
      'Find answers to frequently asked questions about ONDC Seller Platform',
    metaTitle: 'FAQ - ONDC Seller Platform',
    metaDescription:
      'Get answers to frequently asked questions about ONDC Seller Platform.',
    status: 'published',
    template: 'default',
    featured: false,
    author: 'Admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // Use Strapi content if available, otherwise use fallback
  const displayContent = pageContent || fallbackContent;

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Hero Section */}
      <PageHero
        title={displayContent.title}
        description={
          displayContent.excerpt ||
          'Find answers to frequently asked questions about ONDC Seller Platform'
        }
        icon={
          <svg
            className='w-12 h-12 text-white'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
            />
          </svg>
        }
        gradient='purple'
      />

      {/* Main Content */}
      <div className='px-4 sm:px-6 lg:px-8 py-12'>
        {/* Breadcrumbs */}
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'FAQ', href: '/faq', active: true },
          ]}
        />

        <div className='max-w-4xl mx-auto space-y-8'>
          {/* Main Content */}
          <ContentCard variant='elevated' padding='xl'>
            <StyledContent content={displayContent.content} />
          </ContentCard>

          {/* Contact Section */}
          <ContentCard
            variant='bordered'
            padding='lg'
            className='mt-8 bg-gradient-to-r from-blue-50 to-purple-50'
          >
            <div className='text-center'>
              <div className='w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <svg
                  className='w-8 h-8 text-blue-600'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z'
                  />
                </svg>
              </div>
              <h3 className='text-2xl font-bold text-gray-900 mb-4'>
                Still have questions?
              </h3>
              <p className='text-gray-600 mb-8'>
                Can't find the answer you're looking for? Our customer support
                team is here to help.
              </p>
              <div className='flex flex-col sm:flex-row gap-4 justify-center'>
                <a
                  href='/contact'
                  className='inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5'
                >
                  <svg
                    className='w-5 h-5 mr-2'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
                    />
                  </svg>
                  Contact Support
                </a>
                <a
                  href='mailto:<EMAIL>'
                  className='inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-white hover:shadow-md transition-all duration-200'
                >
                  <svg
                    className='w-5 h-5 mr-2'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
                    />
                  </svg>
                  Email Us
                </a>
              </div>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>
  );
};

export default FAQPage;
