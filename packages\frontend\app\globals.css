@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities for navigation */
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }
}

:root {
  /* shadcn/ui design tokens */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;

  /* Legacy brand colors for compatibility */
  --color-primary: 59, 130, 246; /* blue-500 */
  --color-primary-dark: 37, 99, 235; /* blue-600 */
  --color-primary-light: 96, 165, 250; /* blue-400 */

  /* Secondary colors */
  --color-secondary: 79, 70, 229; /* indigo-600 */
  --color-secondary-light: 129, 140, 248; /* indigo-400 */

  /* Accent colors */
  --color-accent: 245, 158, 11; /* amber-500 */

  /* Table/Grid Gradient Design System */
  --table-header-gradient-start: 221.2 83.2% 75.3%;
  --table-header-gradient-end: 221.2 83.2% 68.3%;
  --table-row-even-gradient-start: 210 40% 98%;
  --table-row-even-gradient-end: 210 40% 96%;
  --table-row-hover-gradient-start: 221.2 83.2% 97%;
  --table-row-hover-gradient-end: 221.2 83.2% 95%;
  --table-selected-gradient-start: 221.2 83.2% 95%;
  --table-selected-gradient-end: 221.2 83.2% 92%;
  --table-border-gradient-start: 214.3 31.8% 91.4%;
  --table-border-gradient-end: 214.3 31.8% 87.4%;

  /* Neutral colors */
  --color-background: 249, 250, 251; /* gray-50 */
  --color-surface: 255, 255, 255; /* white */
  --color-text: 31, 41, 55; /* gray-800 */
  --color-text-light: 107, 114, 128; /* gray-500 */

  /* Status colors */
  --color-success: 16, 185, 129; /* green-500 */
  --color-error: 239, 68, 68; /* red-500 */
  --color-warning: 245, 158, 11; /* amber-500 */
  --color-info: 59, 130, 246; /* blue-500 */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

/* Custom component styles */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-300 transform hover:scale-105 active:scale-95;
  }

  .btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white shadow-md hover:shadow-lg;
  }

  .btn-outline {
    @apply border border-blue-500 text-blue-500 hover:bg-blue-50 hover:border-blue-600;
  }

  .btn-gradient {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl;
  }

  .btn-animated {
    @apply relative overflow-hidden before:absolute before:inset-0 before:bg-white before:opacity-0 before:transition-opacity before:duration-300 hover:before:opacity-20;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .card-interactive {
    @apply card cursor-pointer transform hover:scale-105;
  }

  .card-glow {
    @apply card hover:shadow-blue-500/25 hover:shadow-2xl;
  }

  .badge {
    @apply px-2 py-1 text-xs font-semibold rounded-full transition-all duration-200;
  }

  .badge-success {
    @apply bg-green-100 text-green-800 hover:bg-green-200;
  }

  .badge-error {
    @apply bg-red-100 text-red-800 hover:bg-red-200;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 hover:bg-yellow-200;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800 hover:bg-blue-200;
  }

  .dropdown {
    @apply relative inline-block;
  }

  .dropdown-content {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 opacity-0 invisible transform scale-95 transition-all duration-200;
  }

  .dropdown:hover .dropdown-content {
    @apply opacity-100 visible scale-100;
  }

  .input-animated {
    @apply transition-all duration-300 border-2 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:scale-105;
  }

  .skeleton {
    @apply bg-gray-200 animate-pulse rounded;
  }

  .skeleton-text {
    @apply skeleton h-4 w-full mb-2;
  }

  .skeleton-avatar {
    @apply skeleton h-12 w-12 rounded-full;
  }

  .loading-shimmer {
    @apply relative overflow-hidden bg-gray-200;
  }

  .loading-shimmer::after {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-shimmer;
    content: '';
  }

  .scroll-reveal {
    @apply opacity-0 translate-y-8 transition-all duration-600;
  }

  .scroll-reveal.revealed {
    @apply opacity-100 translate-y-0;
  }

  .floating-element {
    @apply animate-float;
  }

  .glow-effect {
    @apply animate-glow;
  }

  .progress-bar {
    @apply bg-gray-200 rounded-full overflow-hidden;
  }

  .progress-fill {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 h-full transition-all duration-500 ease-out;
  }

  .stats-card {
    @apply card p-6 text-center transform hover:scale-105 transition-all duration-300;
  }

  /* Table/Grid Utility Classes */
  .table-header-gradient {
    /* No background styling - clean minimal headers */
    background: none;
  }

  .table-row-even-gradient {
    background: linear-gradient(
      135deg,
      hsl(var(--table-row-even-gradient-start)) 0%,
      hsl(var(--table-row-even-gradient-end)) 100%
    );
  }

  .table-row-hover-gradient {
    background: linear-gradient(
      135deg,
      hsl(var(--table-row-hover-gradient-start)) 0%,
      hsl(var(--table-row-hover-gradient-end)) 100%
    );
  }

  .table-selected-gradient {
    background: linear-gradient(
      135deg,
      hsl(var(--table-selected-gradient-start)) 0%,
      hsl(var(--table-selected-gradient-end)) 100%
    );
  }

  .table-border-gradient {
    background: linear-gradient(
      135deg,
      hsl(var(--table-border-gradient-start)) 0%,
      hsl(var(--table-border-gradient-end)) 100%
    );
  }

  /* Accessibility enhancements for table headers */
  .table-header-gradient {
    /* Clean minimal styling with bold text */
    color: #374151; /* gray-700 */
    font-weight: bold;
    text-shadow: none;
  }

  .table-row-even-gradient {
    /* Subtle gradient that maintains text readability */
    color: inherit;
  }

  .table-row-hover-gradient {
    /* Hover state with good contrast */
    color: inherit;
  }

  /* Focus states for accessibility */
  .table-header-gradient th:focus,
  .table-row-even-gradient:focus,
  .table-row-hover-gradient:focus {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Remove default button focus outline and add custom focus styles */
  button:focus,
  button:focus-visible {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Custom focus styles for buttons */
  button:focus-visible {
    outline: 2px solid hsl(var(--ring)) !important;
    outline-offset: 2px !important;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .table-header-gradient {
      background: hsl(var(--primary)) !important;
    }

    .table-row-even-gradient {
      background: hsl(var(--muted)) !important;
    }

    .table-row-hover-gradient {
      background: hsl(var(--accent)) !important;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .table-header-gradient,
    .table-row-even-gradient,
    .table-row-hover-gradient,
    .table-selected-gradient,
    .table-border-gradient {
      transition: none !important;
    }
  }

  .stats-number {
    @apply text-3xl font-bold text-blue-600 animate-counter;
  }

  .nav-link {
    @apply relative px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300;
  }

  .nav-link::after {
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300;
    content: '';
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  .nav-link.active::after {
    @apply w-full;
  }

  /* Line clamp utilities for consistent text truncation */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Product card specific styles */
  .product-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200;
    height: 24rem; /* h-96 equivalent */
  }

  .product-card-image {
    @apply relative w-full overflow-hidden;
    height: 12rem; /* h-48 equivalent */
  }

  .product-card-content {
    @apply p-4 flex-1 flex flex-col;
  }

  .product-card-title {
    @apply text-lg font-semibold text-gray-900 line-clamp-2;
    height: 3.5rem; /* h-14 equivalent */
    line-height: 1.75rem; /* leading-7 equivalent */
  }

  .product-card-description {
    @apply text-sm text-gray-600 line-clamp-2;
    height: 2.5rem; /* h-10 equivalent */
    line-height: 1.25rem; /* leading-5 equivalent */
  }
}

body {
  color: rgb(var(--color-text));
  background: rgb(var(--color-background));
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Custom animations for loading states */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

@keyframes counter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Page transition animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading state animations */
.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-counter {
  animation: counter 0.6s ease-out;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.5s ease-out;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
.swiper-slide {
  width: 300px !important;
}
