'use client';

import React, { useState } from 'react';
import Link from 'next/link';

const HelpPage = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const helpCategories = [
    {
      title: 'Getting Started',
      icon: '🚀',
      description: 'Learn the basics of using ONDC Marketplace',
      articles: [
        { title: 'How to create an account', link: '/faq#account' },
        { title: 'Setting up your profile', link: '/faq#profile' },
        { title: 'Understanding ONDC', link: '/faq#ondc' },
        { title: 'First-time buyer guide', link: '/faq#buyer-guide' },
      ],
    },
    {
      title: 'Orders & Payments',
      icon: '💳',
      description: 'Everything about placing orders and payments',
      articles: [
        { title: 'How to place an order', link: '/faq#place-order' },
        { title: 'Payment methods', link: '/faq#payments' },
        { title: 'Order tracking', link: '/track-order' },
        { title: 'Order cancellation', link: '/faq#cancel-order' },
      ],
    },
    {
      title: 'Shipping & Delivery',
      icon: '🚚',
      description: 'Information about shipping and delivery',
      articles: [
        { title: 'Delivery timeframes', link: '/faq#delivery' },
        { title: 'Shipping charges', link: '/faq#shipping' },
        { title: 'Delivery tracking', link: '/track-order' },
        { title: 'Failed delivery', link: '/faq#failed-delivery' },
      ],
    },
    {
      title: 'Returns & Refunds',
      icon: '↩️',
      description: 'Return policy and refund process',
      articles: [
        { title: 'Return policy', link: '/returns' },
        { title: 'How to return an item', link: '/returns#process' },
        { title: 'Refund timeline', link: '/returns#refunds' },
        { title: 'Exchange process', link: '/faq#exchange' },
      ],
    },
    {
      title: 'Account & Security',
      icon: '🔒',
      description: 'Manage your account and security settings',
      articles: [
        { title: 'Account settings', link: '/settings' },
        { title: 'Password reset', link: '/faq#password' },
        { title: 'Privacy settings', link: '/privacy' },
        { title: 'Security tips', link: '/faq#security' },
      ],
    },
    {
      title: 'Technical Support',
      icon: '🛠️',
      description: 'Technical issues and troubleshooting',
      articles: [
        { title: 'Website not loading', link: '/faq#technical' },
        { title: 'Payment failures', link: '/faq#payment-issues' },
        { title: 'App troubleshooting', link: '/faq#app-issues' },
        { title: 'Browser compatibility', link: '/faq#browser' },
      ],
    },
  ];

  const quickActions = [
    {
      title: 'Track Your Order',
      description: 'Enter your order number to track delivery status',
      icon: '📦',
      link: '/track-order',
      color: 'from-blue-500 to-blue-600',
    },
    {
      title: 'Return an Item',
      description: 'Start the return process for your purchase',
      icon: '↩️',
      link: '/returns',
      color: 'from-purple-500 to-purple-600',
    },
    {
      title: 'Contact Support',
      description: 'Get in touch with our customer service team',
      icon: '💬',
      link: '/contact',
      color: 'from-green-500 to-green-600',
    },
    {
      title: 'View FAQ',
      description: 'Find answers to frequently asked questions',
      icon: '❓',
      link: '/faq',
      color: 'from-orange-500 to-orange-600',
    },
  ];

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Hero Section */}
      <div className='bg-gradient-to-r from-blue-500 to-purple-600 text-white'>
        <div className='w-full px-4 py-16'>
          <div className='text-center'>
            <h1 className='text-4xl md:text-5xl font-bold mb-4'>
              How can we help you?
            </h1>
            <p className='text-xl text-blue-100 max-w-2xl mx-auto mb-8'>
              Find answers, get support, and learn how to make the most of ONDC
              Marketplace
            </p>

            {/* Search Bar */}
            <div className='max-w-2xl mx-auto'>
              <div className='relative'>
                <input
                  type='text'
                  placeholder='Search for help articles...'
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className='w-full px-6 py-4 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/20 text-lg'
                />
                <button className='absolute right-2 top-2 p-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200'>
                  <svg
                    className='w-6 h-6'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className='w-full px-4 py-16'>
        <div className='max-w-6xl mx-auto'>
          <h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
            Quick Actions
          </h2>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
            {quickActions.map((action, index) => (
              <Link
                key={index}
                href={action.link}
                className='group bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105'
              >
                <div
                  className={`w-12 h-12 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center text-white text-xl mb-4 group-hover:scale-110 transition-transform duration-200`}
                >
                  {action.icon}
                </div>
                <h3 className='text-lg font-semibold text-gray-900 mb-2'>
                  {action.title}
                </h3>
                <p className='text-gray-600 text-sm'>{action.description}</p>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Help Categories */}
      <div className='bg-white'>
        <div className='w-full px-4 py-16'>
          <div className='max-w-6xl mx-auto'>
            <h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
              Browse Help Topics
            </h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
              {helpCategories.map((category, index) => (
                <div key={index} className='bg-gray-50 p-6 rounded-lg'>
                  <div className='flex items-center mb-4'>
                    <span className='text-2xl mr-3'>{category.icon}</span>
                    <h3 className='text-xl font-semibold text-gray-900'>
                      {category.title}
                    </h3>
                  </div>
                  <p className='text-gray-600 mb-4'>{category.description}</p>
                  <ul className='space-y-2'>
                    {category.articles.map((article, articleIndex) => (
                      <li key={articleIndex}>
                        <Link
                          href={article.link}
                          className='text-blue-600 hover:text-purple-600 transition-colors duration-200 text-sm'
                        >
                          {article.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Contact Support */}
      <div className='bg-gradient-to-r from-blue-500 to-purple-600 text-white'>
        <div className='w-full px-4 py-16'>
          <div className='max-w-4xl mx-auto text-center'>
            <h2 className='text-3xl font-bold mb-4'>Still need help?</h2>
            <p className='text-xl text-blue-100 mb-8'>
              Our customer support team is available 24/7 to assist you
            </p>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className='bg-white/10 backdrop-blur-sm p-6 rounded-lg'>
                <div className='text-3xl mb-4'>📧</div>
                <h3 className='text-lg font-semibold mb-2'>Email Support</h3>
                <p className='text-blue-100 mb-4'>
                  Get detailed help via email
                </p>
                <a
                  href='mailto:<EMAIL>'
                  className='inline-block bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors duration-200'
                >
                  Send Email
                </a>
              </div>
              <div className='bg-white/10 backdrop-blur-sm p-6 rounded-lg'>
                <div className='text-3xl mb-4'>💬</div>
                <h3 className='text-lg font-semibold mb-2'>Live Chat</h3>
                <p className='text-blue-100 mb-4'>Chat with our support team</p>
                <button className='inline-block bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors duration-200'>
                  Start Chat
                </button>
              </div>
              <div className='bg-white/10 backdrop-blur-sm p-6 rounded-lg'>
                <div className='text-3xl mb-4'>📞</div>
                <h3 className='text-lg font-semibold mb-2'>Phone Support</h3>
                <p className='text-blue-100 mb-4'>
                  Speak directly with our team
                </p>
                <a
                  href='tel:+15551234567'
                  className='inline-block bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors duration-200'
                >
                  Call Now
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpPage;
