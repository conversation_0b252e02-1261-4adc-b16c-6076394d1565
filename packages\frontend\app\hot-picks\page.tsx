'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useTenant } from '@/contexts/TenantContext';
import ProductListingPage, {
  Product,
} from '@/components/common/ProductListingPage';
import {
  PAGE_CONFIGS,
  getProductImageUrl,
} from '@/utils/productListingHelpers';

// Tenant-specific Hot Picks Products
const hotPicksProductsByTenant: { [key: string]: any[] } = {
  // TechHub Electronics - Trending tech products
  '20': [
    {
      id: 201,
      name: 'AirPods Pro 2nd Generation',
      slug: 'airpods-pro-2nd-generation',
      originalPrice: 24900,
      salePrice: 19900,
      discount: 20,
      rating: 4.8,
      reviews: 3456,
      badge: 'Hot',
    },
    {
      id: 202,
      name: 'Tesla Model Y Wireless Charger',
      slug: 'tesla-model-y-wireless-charger',
      originalPrice: 12999,
      salePrice: 8999,
      discount: 31,
      rating: 4.6,
      reviews: 892,
      badge: 'Hot',
    },
    {
      id: 203,
      name: 'Meta Quest 3 VR Headset',
      slug: 'meta-quest-3-vr-headset',
      originalPrice: 49999,
      salePrice: 42999,
      discount: 14,
      rating: 4.7,
      reviews: 1234,
      badge: 'Hot',
    },
    {
      id: 204,
      name: 'Steam Deck OLED',
      slug: 'steam-deck-oled',
      originalPrice: 54999,
      salePrice: 49999,
      discount: 9,
      rating: 4.8,
      reviews: 567,
      badge: 'Hot',
    },
    {
      id: 205,
      name: 'DJI Mini 4 Pro Drone',
      slug: 'dji-mini-4-pro-drone',
      originalPrice: 89999,
      salePrice: 79999,
      discount: 11,
      rating: 4.9,
      reviews: 345,
      badge: 'Hot',
    },
    {
      id: 206,
      name: 'Framework Laptop 16',
      slug: 'framework-laptop-16',
      originalPrice: 179999,
      salePrice: 159999,
      discount: 11,
      rating: 4.6,
      reviews: 234,
      badge: 'Hot',
    },
  ],
  // StyleHub Fashion-3 - Trending fashion items
  '19': [
    {
      id: 207,
      name: 'Balenciaga Triple S Sneakers',
      slug: 'balenciaga-triple-s-sneakers',
      originalPrice: 89900,
      salePrice: 74900,
      discount: 17,
      rating: 4.5,
      reviews: 456,
      badge: 'Hot',
    },
    {
      id: 208,
      name: 'Off-White Diagonal Hoodie',
      slug: 'off-white-diagonal-hoodie',
      originalPrice: 59900,
      salePrice: 49900,
      discount: 17,
      rating: 4.7,
      reviews: 789,
      badge: 'Hot',
    },
    {
      id: 209,
      name: 'Supreme Box Logo Tee',
      slug: 'supreme-box-logo-tee',
      originalPrice: 19900,
      salePrice: 16900,
      discount: 15,
      rating: 4.6,
      reviews: 1234,
      badge: 'Hot',
    },
    {
      id: 210,
      name: 'Stone Island Cargo Pants',
      slug: 'stone-island-cargo-pants',
      originalPrice: 39900,
      salePrice: 32900,
      discount: 18,
      rating: 4.8,
      reviews: 345,
      badge: 'Hot',
    },
    {
      id: 211,
      name: 'Fear of God Essentials Hoodie',
      slug: 'fear-of-god-essentials-hoodie',
      originalPrice: 29900,
      salePrice: 24900,
      discount: 17,
      rating: 4.7,
      reviews: 567,
      badge: 'Hot',
    },
    {
      id: 212,
      name: 'Yeezy Boost 350 V2',
      slug: 'yeezy-boost-350-v2',
      originalPrice: 24900,
      salePrice: 19900,
      discount: 20,
      rating: 4.6,
      reviews: 892,
      badge: 'Hot',
    },
  ],
  // HomeHub Essentials - Trending home products
  '21': [
    {
      id: 213,
      name: 'Peloton Bike+',
      slug: 'peloton-bike-plus',
      originalPrice: 249900,
      salePrice: 199900,
      discount: 20,
      rating: 4.8,
      reviews: 1567,
      badge: 'Hot',
    },
    {
      id: 214,
      name: 'Theragun PRO Massage Gun',
      slug: 'theragun-pro-massage-gun',
      originalPrice: 59900,
      salePrice: 49900,
      discount: 17,
      rating: 4.7,
      reviews: 892,
      badge: 'Hot',
    },
    {
      id: 215,
      name: 'Ooni Koda 16 Pizza Oven',
      slug: 'ooni-koda-16-pizza-oven',
      originalPrice: 59900,
      salePrice: 49900,
      discount: 17,
      rating: 4.9,
      reviews: 456,
      badge: 'Hot',
    },
    {
      id: 216,
      name: 'Hydrow Rower',
      slug: 'hydrow-rower',
      originalPrice: 229900,
      salePrice: 189900,
      discount: 17,
      rating: 4.6,
      reviews: 234,
      badge: 'Hot',
    },
    {
      id: 217,
      name: 'Molekule Air Pro Air Purifier',
      slug: 'molekule-air-pro-purifier',
      originalPrice: 89900,
      salePrice: 74900,
      discount: 17,
      rating: 4.5,
      reviews: 345,
      badge: 'Hot',
    },
    {
      id: 218,
      name: 'Purple Mattress Hybrid Premier',
      slug: 'purple-mattress-hybrid-premier',
      originalPrice: 179900,
      salePrice: 149900,
      discount: 17,
      rating: 4.7,
      reviews: 678,
      badge: 'Hot',
    },
  ],
};

// Get hot picks products for current tenant or fallback to default
function getHotPicksForTenant(tenantId: string | null): any[] {
  if (!tenantId) return hotPicksProductsByTenant['20']; // Default to TechHub
  return hotPicksProductsByTenant[tenantId] || hotPicksProductsByTenant['20'];
}

// Convert raw product data to Product interface
function convertToProducts(rawProducts: any[]): Product[] {
  return rawProducts.map(product => ({
    id: product.id,
    name: product.name,
    slug: product.slug,
    originalPrice: product.originalPrice,
    salePrice: product.salePrice,
    discount: product.discount,
    image: getProductImageUrl(product.name, product.id),
    category:
      product.name.toLowerCase().includes('airpods') ||
      product.name.toLowerCase().includes('tesla') ||
      product.name.toLowerCase().includes('meta') ||
      product.name.toLowerCase().includes('steam') ||
      product.name.toLowerCase().includes('dji') ||
      product.name.toLowerCase().includes('framework') ||
      product.name.toLowerCase().includes('molekule')
        ? 'Electronics'
        : product.name.toLowerCase().includes('balenciaga') ||
            product.name.toLowerCase().includes('off-white') ||
            product.name.toLowerCase().includes('supreme') ||
            product.name.toLowerCase().includes('stone island') ||
            product.name.toLowerCase().includes('fear of god') ||
            product.name.toLowerCase().includes('yeezy')
          ? 'Fashion'
          : 'Home & Garden',
    rating: product.rating,
    reviewCount: product.reviews,
    badge: product.badge,
    brand: product.name.includes('AirPods')
      ? 'Apple'
      : product.name.includes('Tesla')
        ? 'Tesla'
        : product.name.includes('Meta')
          ? 'Meta'
          : product.name.includes('Steam')
            ? 'Valve'
            : product.name.includes('DJI')
              ? 'DJI'
              : product.name.includes('Framework')
                ? 'Framework'
                : product.name.includes('Balenciaga')
                  ? 'Balenciaga'
                  : product.name.includes('Off-White')
                    ? 'Off-White'
                    : product.name.includes('Supreme')
                      ? 'Supreme'
                      : product.name.includes('Stone Island')
                        ? 'Stone Island'
                        : product.name.includes('Fear of God')
                          ? 'Fear of God'
                          : product.name.includes('Yeezy')
                            ? 'Yeezy'
                            : product.name.includes('Peloton')
                              ? 'Peloton'
                              : product.name.includes('Theragun')
                                ? 'Therabody'
                                : product.name.includes('Ooni')
                                  ? 'Ooni'
                                  : product.name.includes('Hydrow')
                                    ? 'Hydrow'
                                    : product.name.includes('Molekule')
                                      ? 'Molekule'
                                      : product.name.includes('Purple')
                                        ? 'Purple'
                                        : 'Trending Brand',
    inStock: true,
  }));
}

function HotPicksPageContent() {
  const { tenantId } = useTenant();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load tenant-specific hot picks
    setIsLoading(true);
    const tenantProducts = getHotPicksForTenant(tenantId);
    const convertedProducts = convertToProducts(tenantProducts);

    setTimeout(() => {
      setProducts(convertedProducts);
      setIsLoading(false);
    }, 500);
  }, [tenantId]);

  return (
    <ProductListingPage
      products={products}
      config={PAGE_CONFIGS['hot-picks']}
      isLoading={isLoading}
    />
  );
}

export default function HotPicksPage() {
  return (
    <Suspense
      fallback={
        <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4'></div>
            <p className='text-gray-600'>Loading hot picks...</p>
          </div>
        </div>
      }
    >
      <HotPicksPageContent />
    </Suspense>
  );
}
