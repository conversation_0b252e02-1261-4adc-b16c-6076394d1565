import React from 'react';
import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '../contexts/AuthContext';
import { CartProvider } from '../contexts/CartContext';
import { TenantProvider } from '../contexts/TenantContext';
// MSW removed - using only real backend APIs
import LayoutWrapper from '../components/LayoutWrapper';
import { ToastProvider } from '../components/common/ToastProvider';

import { MedusaCartProvider } from '../hooks/useMedusaCart';
import { SWRProvider } from '../contexts/SWRProvider';
import { StrapiProvider } from '../contexts/StrapiProvider';
import {
  ChannelProvider,
  ChannelThemeProvider,
} from '../contexts/ChannelContext';
import { CentralizedLoadingProvider } from '../contexts/CentralizedLoadingContext';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'ONDC Seller Platform - Your Gateway to Digital Commerce',
    template: '%s | ONDC Seller Platform',
  },
  description:
    "Join the Open Network for Digital Commerce (ONDC) ecosystem. Discover products, manage your business, and connect with customers across India's digital marketplace.",
  keywords: [
    'ONDC',
    'Open Network Digital Commerce',
    'e-commerce',
    'digital marketplace',
    'online shopping',
    'seller platform',
    'India commerce',
    'digital payments',
    'online business',
  ],
  authors: [{ name: 'ONDC Seller Platform Team' }],
  creator: 'ONDC Seller Platform',
  publisher: 'ONDC Seller Platform',
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001'
  ),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_IN',
    url: '/',
    title: 'ONDC Seller Platform - Your Gateway to Digital Commerce',
    description:
      "Join the Open Network for Digital Commerce (ONDC) ecosystem. Discover products, manage your business, and connect with customers across India's digital marketplace.",
    siteName: 'ONDC Seller Platform',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'ONDC Seller Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ONDC Seller Platform - Your Gateway to Digital Commerce',
    description:
      'Join the Open Network for Digital Commerce (ONDC) ecosystem. Discover products, manage your business, and connect with customers.',
    images: ['/og-image.jpg'],
    creator: '@ondcplatform',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

const RootLayout = ({ children }: { children: React.ReactNode }) => (
  <html lang='en'>
    <body className={inter.className}>
      <SWRProvider>
        <StrapiProvider>
          <TenantProvider>
            <AuthProvider>
              <CartProvider>
                <MedusaCartProvider>
                  <ToastProvider>
                    <CentralizedLoadingProvider>
                      <ChannelProvider>
                        <ChannelThemeProvider>
                          <LayoutWrapper>{children}</LayoutWrapper>
                        </ChannelThemeProvider>
                      </ChannelProvider>
                    </CentralizedLoadingProvider>
                  </ToastProvider>
                </MedusaCartProvider>
              </CartProvider>
            </AuthProvider>
          </TenantProvider>
        </StrapiProvider>
      </SWRProvider>
    </body>
  </html>
);

export default RootLayout;
