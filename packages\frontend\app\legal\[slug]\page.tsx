import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Metadata } from 'next';

// Define the legal pages available
const legalPages = {
  'terms-of-service': {
    title: 'Terms of Service',
    description: 'Terms of Service for the ONDC Seller Platform',
    lastUpdated: '2024-01-15',
  },
  'privacy-policy': {
    title: 'Privacy Policy', 
    description: 'Privacy Policy for the ONDC Seller Platform',
    lastUpdated: '2024-01-15',
  },
  'cookie-policy': {
    title: 'Cookie Policy',
    description: 'Cookie Policy for the ONDC Seller Platform',
    lastUpdated: '2024-01-15',
  },
  'refund-policy': {
    title: 'Refund Policy',
    description: 'Refund and Return Policy for the ONDC Seller Platform',
    lastUpdated: '2024-01-15',
  },
} as const;

type LegalPageSlug = keyof typeof legalPages;

interface Props {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const page = legalPages[params.slug as LegalPageSlug];
  
  if (!page) {
    return {
      title: 'Page Not Found',
    };
  }

  return {
    title: `${page.title} | ONDC Seller Platform`,
    description: page.description,
  };
}

export async function generateStaticParams() {
  return Object.keys(legalPages).map((slug) => ({
    slug,
  }));
}

export default function LegalPage({ params }: Props) {
  const page = legalPages[params.slug as LegalPageSlug];

  if (!page) {
    notFound();
  }

  // For now, redirect to the appropriate static pages
  // In a real implementation, you would load MDX content here
  if (params.slug === 'terms-of-service') {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{page.title}</h1>
              <p className="text-gray-600">Last updated: {page.lastUpdated}</p>
            </div>

            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 mb-6">
                This page would typically load MDX content for the {page.title}. 
                For now, please visit our dedicated <Link href="/terms" className="text-blue-600 hover:text-blue-800">Terms of Service</Link> page.
              </p>
            </div>

            <div className="mt-8 pt-8 border-t border-gray-200">
              <Link
                href="/"
                className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (params.slug === 'privacy-policy') {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{page.title}</h1>
              <p className="text-gray-600">Last updated: {page.lastUpdated}</p>
            </div>

            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 mb-6">
                This page would typically load MDX content for the {page.title}. 
                For now, please visit our dedicated <Link href="/privacy" className="text-blue-600 hover:text-blue-800">Privacy Policy</Link> page.
              </p>
            </div>

            <div className="mt-8 pt-8 border-t border-gray-200">
              <Link
                href="/"
                className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Generic template for other legal pages
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{page.title}</h1>
            <p className="text-gray-600">Last updated: {page.lastUpdated}</p>
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-gray-700 mb-6">
              This page would typically load MDX content for the {page.title}. 
              The content management system integration would load the appropriate legal document here.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">MDX Integration Ready</h3>
              <p className="text-blue-800">
                This page is configured to support MDX content loading. Legal documents can be managed 
                through the content management system and rendered dynamically.
              </p>
            </div>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Available Legal Pages</h2>
              <ul className="space-y-2">
                {Object.entries(legalPages).map(([slug, pageInfo]) => (
                  <li key={slug}>
                    <Link 
                      href={`/legal/${slug}`}
                      className="text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      {pageInfo.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </section>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-200">
            <Link
              href="/"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
