import { createMcpHandler } from '@vercel/mcp-adapter';
import { z } from 'zod';

// Create the MCP handler
const handler = createMcpHandler(
  server => {
    // Define available tools
    server.tool(
      'getUserData',
      'Retrieve user data from the application',
      {
        userId: z.string().describe('User ID to fetch'),
      },
      async ({ userId }) => ({
        content: [
          {
            type: 'text',
            text: `User data for ID: ${userId}`,
          },
        ],
      })
    );
  },
  {
    serverInfo: {
      name: 'My Next.js MCP Server',
      version: '1.0.0',
    },
  },
  {
    basePath: '/mcp', // This should match the path where [transports] is located
  }
);

// Export the handler for both GET and POST requests
export { handler as GET, handler as POST };
