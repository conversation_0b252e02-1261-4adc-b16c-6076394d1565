import { createStoreConfiguration } from '@/lib/api/store-config';
import OnboardingWizard from '../../components/onboarding/OnboardingWizard';
import { useToast } from '@/components/common/ToastProvider';
import { useOnboardingStore } from '@/stores/onboardingStore';

const OnboardingPage = () => {
  // const toast = useToast();
  // const {
  //   currentStep,
  //   storeData,
  //   productData,
  //   bulkUploadFile,
  //   isLoading,
  //   error,
  //   completedSteps,
  //   setCurrentStep,
  //   setBulkUploadFile,
  //   completeOnboarding,
  //   setError,
  // } = useOnboardingStore();

  // console.log(':::store config::::::::::', currentStep, storeData);
  const handleSaveStoreConfig = async (payload: any) => {
    await createStoreConfiguration(payload);
    // toast.success('Store configuration saved successfully!');
  };

  return <OnboardingWizard />;
};

export default OnboardingPage;
