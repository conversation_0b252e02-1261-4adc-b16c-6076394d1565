'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { medusaAPI } from '@/lib/medusa-backend-api';
import {
  CheckCircleIcon,
  TruckIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';

interface OrderItem {
  id: string;
  title: string;
  quantity: number;
  unit_price: number;
  total: number;
}

interface Order {
  id: string;
  display_id?: string;
  status: string;
  currency_code: string;
  email: string;
  total: number;
  subtotal: number;
  tax_total?: number;
  shipping_total?: number;
  items: OrderItem[];
  shipping_address?: any;
  billing_address?: any;
  created_at: string;
  metadata?: any;
}

export default function OrderDetailPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const orderId = params.id as string;
  const isSuccess = searchParams.get('success') === 'true';

  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);
        setError(null);

        // Try to get order from the simple orders endpoint
        const response = await medusaAPI.getOrders({ email: '<EMAIL>' });
        console.log({ response });
        if (response.success && response.orders) {
          const foundOrder = response.orders.find(o => o.id === orderId);
          if (foundOrder) {
            setOrder(foundOrder);
          } else {
            setError('Order not found');
          }
        } else {
          setError('Failed to fetch order');
        }
      } catch (err) {
        console.error('Error fetching order:', err);
        setError('Failed to fetch order details');
      } finally {
        setLoading(false);
      }
    };

    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto'></div>
          <p className='mt-4 text-gray-600'>Loading order details...</p>
        </div>
      </div>
    );
  }
  console.log({ order, error });
  if (error || !order) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold text-gray-900 mb-4'>
            Order Not Found
          </h2>
          <p className='text-gray-600 mb-6'>
            {error || 'The order you are looking for does not exist.'}
          </p>
          <button
            onClick={() => window.history.back()}
            className='bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700'
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        {/* Action Buttons */}
        <div className='my-6 flex space-x-4 justify-end'>
          <Link
            href={'/orders'}
            className='px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50'
          >
            Go to Orders
          </Link>
          {/* <button
            onClick={() => window.print()}
            className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Print Order
          </button> */}
        </div>
        {/* Success Banner */}
        {isSuccess && (
          <div className='bg-green-50 border border-green-200 rounded-lg p-6 mb-8'>
            <div className='flex items-center'>
              <CheckCircleIcon className='w-8 h-8 text-green-500 mr-4' />
              <div>
                <h2 className='text-xl font-semibold text-green-800'>
                  Order Placed Successfully!
                </h2>
                <p className='text-green-700 mt-1'>
                  Thank you for your order. We'll send you a confirmation email
                  shortly.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Order Header */}
        <div className='bg-white rounded-lg shadow-sm p-6 mb-6'>
          <div className='flex justify-between items-start'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900'>
                Order #{order.display_id || order.id}
              </h1>
              <p className='text-gray-600 mt-1'>
                Placed on {formatDate(order.created_at)}
              </p>
              <p className='text-gray-600'>Email: {order.email}</p>
            </div>
            <div className='text-right'>
              <div className='flex items-center text-sm text-gray-600 mb-2'>
                <ClockIcon className='w-4 h-4 mr-1' />
                Status:{' '}
                <span className='ml-1 font-medium capitalize'>
                  {order?.status ? order.status : 'Order placed'}
                </span>
              </div>
              {/* <div className="text-2xl font-bold text-gray-900">{formatPrice(order.total)}</div> */}
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className='bg-white rounded-lg shadow-sm p-6 mb-6'>
          <h2 className='text-xl font-semibold text-gray-900 mb-4'>
            Order Items
          </h2>
          <div className='space-y-4'>
            {order.items.map(item => (
              <div
                key={item.id}
                className='flex items-center justify-between border-b border-gray-200 pb-4 last:border-0'
              >
                <div className='flex items-center space-x-4'>
                  <div className='w-16 h-16 bg-gray-200 rounded-md flex-shrink-0'></div>
                  <div>
                    <h3 className='font-medium text-gray-900'>{item.title}</h3>
                    <p className='text-sm text-gray-600'>
                      Quantity: {item.quantity}
                    </p>
                    <p className='text-sm text-gray-600'>
                      Unit Price: {formatPrice(item.unit_price)}
                    </p>
                  </div>
                </div>
                <div className='text-right'>
                  <p className='font-medium text-gray-900'>
                    {formatPrice(item.unit_price * item.quantity)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Summary */}
        <div className='bg-white rounded-lg shadow-sm p-6 mb-6'>
          <h2 className='text-xl font-semibold text-gray-900 mb-4'>
            Order Summary
          </h2>
          <div className='space-y-2'>
            <div className='flex justify-between'>
              <span className='text-gray-600'>Subtotal</span>
              <span className='text-gray-900'>
                {formatPrice(
                  order.items?.reduce(
                    (acc, item) => acc + item.unit_price * item.quantity,
                    0
                  ) || 0
                )}
              </span>
            </div>
            {order.tax_total && (
              <div className='flex justify-between'>
                <span className='text-gray-600'>Tax</span>
                <span className='text-gray-900'>
                  {formatPrice(order.tax_total)}
                </span>
              </div>
            )}
            {order.shipping_total !== undefined && (
              <div className='flex justify-between'>
                <span className='text-gray-600'>Shipping</span>
                <span className='text-gray-900'>
                  {order.shipping_total === 0
                    ? 'Free'
                    : formatPrice(order.shipping_total)}
                </span>
              </div>
            )}
            <div className='border-t pt-2'>
              <div className='flex justify-between text-lg font-semibold'>
                <span className='text-gray-900'>Total</span>
                <span className='text-gray-900'>
                  {formatPrice(
                    order.items?.reduce(
                      (acc, item) => acc + item.unit_price * item.quantity,
                      0
                    ) || 0
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Shipping Address */}
        {order.shipping_address && (
          <div className='bg-white rounded-lg shadow-sm p-6 mb-6'>
            <h2 className='text-xl font-semibold text-gray-900 mb-4'>
              Shipping Address
            </h2>
            <div className='text-gray-600'>
              <p className='font-medium'>
                {order.shipping_address.first_name}{' '}
                {order.shipping_address.last_name}
              </p>
              <p>{order.shipping_address.address_1}</p>
              {order.shipping_address.address_2 && (
                <p>{order.shipping_address.address_2}</p>
              )}
              <p>
                {order.shipping_address.city}, {order.shipping_address.province}{' '}
                {order.shipping_address.postal_code}
              </p>
              <p>{order.shipping_address.country_code}</p>
              {order.shipping_address.phone && (
                <p>Phone: {order.shipping_address.phone}</p>
              )}
            </div>
          </div>
        )}

        {/* Payment Method */}
        <div className='bg-white rounded-lg shadow-sm p-6'>
          <h2 className='text-xl font-semibold text-gray-900 mb-4'>
            Payment Method
          </h2>
          <div className='flex items-center'>
            <div className='bg-green-100 p-2 rounded-full mr-3'>
              <TruckIcon className='w-5 h-5 text-green-600' />
            </div>
            <div>
              <p className='font-medium text-gray-900'>
                Cash on Delivery (COD)
              </p>
              <p className='text-sm text-gray-600'>
                Pay when your order is delivered
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function OrderStatusBadge({ status }: { status: string }) {
  let bgColor = '';
  let textColor = '';

  switch (status) {
    case 'delivered':
      bgColor = 'bg-green-100';
      textColor = 'text-green-800';
      break;
    case 'processing':
    case 'shipped':
    case 'in_transit':
      bgColor = 'bg-blue-100';
      textColor = 'text-blue-800';
      break;
    case 'pending':
      bgColor = 'bg-yellow-100';
      textColor = 'text-yellow-800';
      break;
    case 'cancelled':
      bgColor = 'bg-red-100';
      textColor = 'text-red-800';
      break;
    default:
      bgColor = 'bg-gray-100';
      textColor = 'text-gray-800';
  }

  return (
    <span
      className={`px-3 py-1 rounded-full text-sm font-medium ${bgColor} ${textColor}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
    </span>
  );
}

function TrackingTimeline({ updates }: { updates: any[] }) {
  return (
    <div className='flow-root'>
      <ul className='-mb-8'>
        {updates.map((update, updateIdx) => (
          <li key={update.status}>
            <div className='relative pb-8'>
              {updateIdx !== updates.length - 1 ? (
                <span
                  className={`absolute top-4 left-4 -ml-px h-full w-0.5 ${
                    update.completed ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                  aria-hidden='true'
                />
              ) : null}
              <div className='relative flex space-x-3'>
                <div>
                  <span
                    className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                      update.completed ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    {update.completed ? (
                      <CheckIcon
                        className='h-5 w-5 text-white'
                        aria-hidden='true'
                      />
                    ) : (
                      <div className='h-2.5 w-2.5 bg-gray-400 rounded-full' />
                    )}
                  </span>
                </div>
                <div className='min-w-0 flex-1 pt-1.5 flex justify-between space-x-4'>
                  <div>
                    <p
                      className={`text-sm font-medium ${
                        update.completed ? 'text-gray-900' : 'text-gray-500'
                      }`}
                    >
                      {update.title}
                    </p>
                    <p
                      className={`text-sm ${
                        update.completed ? 'text-gray-600' : 'text-gray-400'
                      }`}
                    >
                      {update.description}
                    </p>
                  </div>
                  <div className='text-right text-sm whitespace-nowrap text-gray-500'>
                    {update.timestamp && (
                      <time dateTime={update.timestamp}>
                        {new Date(update.timestamp).toLocaleDateString(
                          'en-US',
                          {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          }
                        )}
                      </time>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}

// export default function OrderTrackingPage() {
//   const params = useParams();
//   const orderId = params.id as string;
//   const [order, setOrder] = useState(MOCK_ORDER);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);
//   const [isNewOrder, setIsNewOrder] = useState(false);

//   useEffect(() => {
//     async function fetchOrder() {
//       if (!orderId) return;

//       setLoading(true);
//       setError(null);

//       try {
//         console.log('[OrderDetail] Fetching order:', orderId);

//         // First, check localStorage for newly placed orders
//         const localOrderData = localStorage.getItem(`order_${orderId}`);
//         if (localOrderData) {
//           try {
//             const orderData = JSON.parse(localOrderData);
//             console.log('[OrderDetail] Found order in localStorage:', orderData);
//             setIsNewOrder(true); // Mark as newly placed order

//             // Transform localStorage data to match our interface
//             const transformedOrder = {
//               id: orderData.id,
//               orderNumber: orderData.orderNumber,
//               customer: {
//                 name: `${orderData.customer.firstName || ''} ${orderData.customer.lastName || ''}`.trim() || 'Unknown Customer',
//                 email: orderData.customer.email || '<EMAIL>',
//                 phone: orderData.customer.phone || 'N/A',
//               },
//               orderDate: orderData.orderDate,
//               estimatedDelivery: orderData.estimatedDelivery,
//               total: orderData.totals.total,
//               subtotal: orderData.totals.subtotal,
//               shipping: orderData.totals.shipping,
//               tax: orderData.totals.tax,
//               status: orderData.status,
//               paymentStatus: orderData.paymentStatus,
//               paymentMethod: orderData.paymentMethod,
//               shippingAddress: {
//                 name: `${orderData.shippingAddress.firstName || ''} ${orderData.shippingAddress.lastName || ''}`.trim(),
//                 street: orderData.shippingAddress.address,
//                 city: orderData.shippingAddress.city,
//                 state: orderData.shippingAddress.state,
//                 postalCode: orderData.shippingAddress.postalCode,
//                 country: orderData.shippingAddress.country,
//               },
//               billingAddress: {
//                 name: `${orderData.billingAddress.firstName || ''} ${orderData.billingAddress.lastName || ''}`.trim(),
//                 street: orderData.billingAddress.address,
//                 city: orderData.billingAddress.city,
//                 state: orderData.billingAddress.state,
//                 postalCode: orderData.billingAddress.postalCode,
//                 country: orderData.billingAddress.country,
//               },
//               items: orderData.items.map((item: any) => ({
//                 id: item.id,
//                 name: item.name,
//                 brand: item.brand || 'ONDC Seller',
//                 price: item.price,
//                 quantity: item.quantity,
//                 image: item.image || 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
//                 total: item.price * item.quantity,
//               })),
//               tracking: {
//                 carrier: 'ONDC Express',
//                 trackingNumber: `ONDC${orderId.slice(-9)}`,
//                 estimatedDelivery: orderData.estimatedDelivery,
//                 currentStatus: 'processing',
//                 updates: [
//                   {
//                     status: 'order_placed',
//                     title: 'Order Confirmed',
//                     description: 'Your order has been confirmed and is being processed',
//                     timestamp: orderData.orderDate,
//                     completed: true
//                   },
//                   {
//                     status: 'processing',
//                     title: 'Processing',
//                     description: 'Your order is being prepared for shipment',
//                     timestamp: orderData.orderDate,
//                     completed: true
//                   },
//                   {
//                     status: 'shipped',
//                     title: 'Shipped',
//                     description: 'Your order has been shipped and is on its way',
//                     timestamp: null,
//                     completed: false
//                   },
//                   {
//                     status: 'delivered',
//                     title: 'Delivered',
//                     description: 'Your order has been delivered successfully',
//                     timestamp: null,
//                     completed: false
//                   },
//                 ]
//               },
//             };
//             setOrder(transformedOrder);
//             console.log('[OrderDetail] Successfully loaded order from localStorage');
//             return; // Exit early since we found the order
//           } catch (parseError) {
//             console.warn('[OrderDetail] Failed to parse localStorage order data:', parseError);
//           }
//         }

//         // Try to fetch from API if not found in localStorage
//         try {
//           const response = await ordersAPI.getOrder(orderId);
//           if (response.data) {
//             // Transform API response to match our interface
//             const apiOrder = response.data;
//             const transformedOrder = {
//               id: apiOrder.id,
//               orderNumber: apiOrder.order_number || apiOrder.orderNumber || `ONDC-${new Date().getFullYear()}-${(apiOrder.id || '').slice(-6)}`,
//               customer: {
//                 name: apiOrder.customer ? `${apiOrder.customer.first_name || ''} ${apiOrder.customer.last_name || ''}`.trim() || 'Unknown Customer' : 'Unknown Customer',
//                 email: apiOrder.customer?.email || '<EMAIL>',
//                 phone: apiOrder.customer?.phone || 'N/A',
//               },
//               orderDate: apiOrder.created_at || new Date().toISOString(),
//               estimatedDelivery: apiOrder.estimated_delivery || new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
//               total: (apiOrder.total || 0) / 100, // Convert from cents
//               subtotal: (apiOrder.subtotal || 0) / 100,
//               shipping: (apiOrder.shipping_total || 0) / 100,
//               tax: (apiOrder.tax_total || 0) / 100,
//               status: apiOrder.status || 'pending',
//               paymentStatus: apiOrder.payment_status || 'pending',
//               paymentMethod: 'Credit Card ending in ****',
//               shippingAddress: apiOrder.shipping_address || {
//                 name: 'Unknown',
//                 street: 'Unknown',
//                 city: 'Unknown',
//                 state: 'Unknown',
//                 postalCode: 'Unknown',
//                 country: 'Unknown',
//               },
//               billingAddress: apiOrder.billing_address || apiOrder.shipping_address || {
//                 name: 'Unknown',
//                 street: 'Unknown',
//                 city: 'Unknown',
//                 state: 'Unknown',
//                 postalCode: 'Unknown',
//                 country: 'Unknown',
//               },
//               items: apiOrder.items || [],
//               tracking: MOCK_ORDER.tracking, // Keep mock tracking for now
//             };
//             setOrder(transformedOrder);
//             console.log('[OrderDetail] Successfully loaded order from API');
//           } else {
//             throw new Error('Order not found');
//           }
//         } catch (apiError) {
//           console.warn('[OrderDetail] API call failed, using mock data:', apiError);
//           // Fallback to mock data with the current order ID
//           const mockOrderWithId = {
//             ...MOCK_ORDER,
//             id: orderId,
//           };
//           setOrder(mockOrderWithId);
//         }
//       } catch (err) {
//         console.error('[OrderDetail] Error loading order:', err);
//         setError('Failed to load order details');
//       } finally {
//         setLoading(false);
//       }
//     }
//     fetchOrder();
//   }, [orderId]);

//   // Format currency
//   const formatCurrency = (amount: number) => {
//     return new Intl.NumberFormat('en-IN', {
//       style: 'currency',
//       currency: 'INR'
//     }).format(amount);
//   };

//   // Format date
//   const formatDate = (dateString: string) => {
//     return new Date(dateString).toLocaleDateString('en-US', {
//       year: 'numeric',
//       month: 'long',
//       day: 'numeric',
//       hour: '2-digit',
//       minute: '2-digit'
//     });
//   };

//   if (loading) {
//     return (
//       <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
//           <p className="mt-4 text-gray-600">Loading order details...</p>
//         </div>
//       </div>
//     );
//   }

//   if (error) {
//     return (
//       <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//         <div className="text-center">
//           <div className="text-red-600 text-xl mb-4">⚠️</div>
//           <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Order</h1>
//           <p className="text-gray-600 mb-4">{error}</p>
//           <button
//             onClick={() => window.location.reload()}
//             className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
//           >
//             Try Again
//           </button>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="container mx-auto px-4 py-6">
//       {/* Breadcrumbs */}
//       <Breadcrumbs
//         items={[
//           { label: 'Home', href: '/' },
//           { label: 'Orders', href: '/orders' },
//           { label: `Order ${order.orderNumber}`, href: `/orders/${orderId}`, active: true },
//         ]}
//       />

//       {/* Success Banner for New Orders */}
//       {isNewOrder && (
//         <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
//           <div className="flex items-center">
//             <CheckIcon className="h-6 w-6 text-green-600 mr-3" />
//             <div>
//               <h3 className="text-lg font-semibold text-green-900">Order Successfully Placed!</h3>
//               <p className="text-sm text-green-700 mt-1">
//                 Thank you for your order. We've sent a confirmation email to {order.customer.email}
//               </p>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Header */}
//       <div className="mt-6 mb-8">
//         <div className="flex flex-col md:flex-row md:items-center md:justify-between">
//           <div>
//             <h1 className="text-3xl font-bold text-gray-900">Order {order.orderNumber}</h1>
//             <p className="mt-2 text-gray-600">
//               Placed on {formatDate(order.orderDate)}
//             </p>
//           </div>
//           <div className="mt-4 md:mt-0 flex items-center space-x-4">
//             <OrderStatusBadge status={order.status} />
//             <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
//               <TruckIcon className="mr-2 h-4 w-4" />
//               Track Package
//             </button>
//           </div>
//         </div>
//       </div>

//       <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
//         {/* Main Content */}
//         <div className="lg:col-span-2 space-y-8">
//           {/* Order Tracking */}
//           <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
//             <div className="flex items-center mb-6">
//               <TruckIcon className="h-6 w-6 text-blue-600 mr-3" />
//               <h2 className="text-xl font-semibold text-gray-900">Order Tracking</h2>
//             </div>

//             {order.tracking && (
//               <div className="mb-6 p-4 bg-blue-50 rounded-lg">
//                 <div className="flex items-center justify-between">
//                   <div>
//                     <p className="text-sm font-medium text-blue-900">Tracking Number</p>
//                     <p className="text-lg font-semibold text-blue-800">{order.tracking.trackingNumber}</p>
//                   </div>
//                   <div className="text-right">
//                     <p className="text-sm font-medium text-blue-900">Estimated Delivery</p>
//                     <p className="text-lg font-semibold text-blue-800">
//                       {new Date(order.tracking.estimatedDelivery).toLocaleDateString('en-US', {
//                         month: 'short',
//                         day: 'numeric'
//                       })}
//                     </p>
//                   </div>
//                 </div>
//               </div>
//             )}

//             <TrackingTimeline updates={order.tracking.updates} />
//           </div>

//           {/* Order Items */}
//           <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
//             <div className="flex items-center mb-6">
//               <TruckIcon className="h-6 w-6 text-blue-600 mr-3" />
//               <h2 className="text-xl font-semibold text-gray-900">Order Items</h2>
//             </div>

//             <div className="space-y-4">
//               {order.items.map((item) => (
//                 <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
//                   <img
//                     src={item.image}
//                     alt={item.name}
//                     className="h-16 w-16 object-cover rounded-lg"
//                   />
//                   <div className="flex-1">
//                     <h3 className="text-lg font-medium text-gray-900">{item.name}</h3>
//                     <p className="text-sm text-gray-600">{item.brand}</p>
//                     <p className="text-sm text-gray-500">SKU: {item.sku}</p>
//                   </div>
//                   <div className="text-right">
//                     <p className="text-lg font-semibold text-gray-900">{formatCurrency(item.price)}</p>
//                     <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
//                   </div>
//                 </div>
//               ))}
//             </div>
//           </div>
//         </div>

//         {/* Sidebar */}
//         <div className="space-y-6">
//           {/* Order Summary */}
//           <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
//             <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

//             <div className="space-y-3">
//               <div className="flex justify-between">
//                 <span className="text-gray-600">Subtotal</span>
//                 <span className="font-medium">{formatCurrency(order.subtotal)}</span>
//               </div>
//               <div className="flex justify-between">
//                 <span className="text-gray-600">Shipping</span>
//                 <span className="font-medium">{formatCurrency(order.shipping)}</span>
//               </div>
//               <div className="flex justify-between">
//                 <span className="text-gray-600">Tax</span>
//                 <span className="font-medium">{formatCurrency(order.tax)}</span>
//               </div>
//               <div className="border-t pt-3">
//                 <div className="flex justify-between">
//                   <span className="text-lg font-semibold">Total</span>
//                   <span className="text-lg font-semibold">{formatCurrency(order.total)}</span>
//                 </div>
//               </div>
//             </div>
//           </div>

//           {/* Payment Information */}
//           <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
//             <div className="flex items-center mb-4">
//               <CreditCardIcon className="h-5 w-5 text-blue-600 mr-2" />
//               <h3 className="text-lg font-semibold text-gray-900">Payment</h3>
//             </div>

//             <div className="space-y-3">
//               <div>
//                 <span className="text-sm text-gray-600">Payment Method</span>
//                 <p className="font-medium">{order.paymentMethod}</p>
//               </div>
//               <div>
//                 <span className="text-sm text-gray-600">Payment Status</span>
//                 <p className="font-medium text-green-600 capitalize">{order.paymentStatus}</p>
//               </div>
//             </div>
//           </div>

//           {/* Shipping Address */}
//           <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
//             <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>

//             <div className="text-sm">
//               <p className="font-medium">{order.shippingAddress.name}</p>
//               <p>{order.shippingAddress.street}</p>
//               <p>
//                 {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}
//               </p>
//               <p>{order.shippingAddress.country}</p>
//             </div>
//           </div>

//           {/* Customer Support */}
//           <div className="bg-blue-50 rounded-lg p-6">
//             <h3 className="text-lg font-semibold text-blue-900 mb-2">Need Help?</h3>
//             <p className="text-sm text-blue-800 mb-4">
//               Contact our customer support team for any questions about your order.
//             </p>
//             <Link
//               href="/support"
//               className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
//             >
//               Contact Support
//             </Link>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
