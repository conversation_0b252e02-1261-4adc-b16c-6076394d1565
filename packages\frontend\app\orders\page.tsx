'use client';

import { useState, useEffect } from 'react';
import { ordersAPI } from '@/lib/api/orders'; // <- your actual API client
import Link from 'next/link';
import Breadcrumbs from '@/components/Breadcrumbs';
import medusaAPI from '@/lib/medusa-backend-api';
// import { Tooltip } from 'recharts';
import Tooltip from '@mui/material/Tooltip';

// ✅ Reusable badge component
function OrderStatusBadge({ status }: { status: string }) {
  let bg = '',
    text = '';
  const safeStatus = status?.toLowerCase() || 'unknown';

  switch (safeStatus) {
    case 'completed':
      bg = 'bg-green-100';
      text = 'text-green-800';
      break;
    case 'processing':
      bg = 'bg-blue-100';
      text = 'text-blue-800';
      break;
    case 'pending':
      bg = 'bg-yellow-100';
      text = 'text-yellow-800';
      break;
    case 'cancelled':
      bg = 'bg-red-100';
      text = 'text-red-800';
      break;
    default:
      bg = 'bg-gray-100';
      text = 'text-gray-800';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${bg} ${text}`}
    >
      {safeStatus.charAt(0).toUpperCase() + safeStatus.slice(1)}
    </span>
  );
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await medusaAPI.getOrders({ email: '<EMAIL>' });

        if (response?.success && Array.isArray(response.orders)) {
          setOrders(response.orders);
        } else {
          setOrders([]);
          setError('No orders found.');
        }
      } catch (err) {
        console.error('[OrdersPage] Error fetching orders:', err);
        setOrders([]);
        setError('Failed to fetch orders.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  const filteredOrders = orders.filter(order => {
    const orderId = order.id || '';
    const name =
      order.shipping_address?.first_name || order.shipping_address?.last_name
        ? `${order.shipping_address.first_name || ''} ${
            order.shipping_address.last_name || ''
          }`.trim()
        : order.email || 'Guest';

    const matchesSearch =
      orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' || order.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const formatDate = (date: string) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  };

  if (loading) {
    return (
      <div className='min-h-[40vh] flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin h-12 w-12 rounded-full border-4 border-blue-600 border-b-transparent mx-auto mb-4' />
          <p className='text-gray-500'>Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='max-w-7xl mx-auto px-4 py-6'>
      <Breadcrumbs
        items={[
          { label: 'Home', href: '/' },
          { label: 'Orders', href: '/orders', active: true },
        ]}
      />

      {error && (
        <div className='bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded my-6'>
          {error}
        </div>
      )}

      <div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4 mt-6 mb-4'>
        <h1 className='text-3xl font-bold text-gray-900'>Orders</h1>

        <div className='flex gap-4'>
          <input
            type='text'
            placeholder='Search by ID or name'
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className='w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm'
          />
          <select
            value={statusFilter}
            onChange={e => setStatusFilter(e.target.value)}
            className='w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm'
          >
            <option value='all'>All statuses</option>
            <option value='pending'>Pending</option>
            <option value='processing'>Processing</option>
            <option value='completed'>Completed</option>
            <option value='cancelled'>Cancelled</option>
          </select>
        </div>
      </div>

      <div className='bg-white border rounded-lg shadow overflow-x-auto'>
        <table className='min-w-full divide-y divide-gray-200 text-sm'>
          <thead className='bg-gray-50 text-gray-600 uppercase tracking-wide'>
            <tr>
              <th className='px-6 py-3 text-left'>Order ID</th>
              <th className='px-6 py-3 text-left'>Customer</th>
              <th className='px-6 py-3 text-left'>Date</th>
              <th className='px-6 py-3 text-left'>Total</th>
              <th className='px-6 py-3 text-left'>Status</th>
              <th className='px-6 py-3 text-left'>Items</th>
              <th className='px-6 py-3 text-right'>Action</th>
            </tr>
          </thead>
          <tbody className='divide-y divide-gray-100'>
            {filteredOrders.length === 0 ? (
              <tr>
                <td colSpan={7} className='text-center py-8 text-gray-500'>
                  No orders found.
                </td>
              </tr>
            ) : (
              filteredOrders.map(order => {
                const name =
                  order.shipping_address?.first_name ||
                  order.shipping_address?.last_name
                    ? `${order.shipping_address.first_name || ''} ${
                        order.shipping_address.last_name || ''
                      }`.trim()
                    : order.email || 'Guest';

                return (
                  <tr key={order.id} className='hover:bg-gray-50'>
                    {/* <td className="px-6 py-4 text-blue-600 font-medium">{order.id}</td> */}
                    <td className='px-6 py-4 text-blue-600 font-medium'>
                      <Tooltip title={order.id} arrow>
                        <span className='truncate max-w-[160px] inline-block '>
                          {order.id.slice(0, 10)}...
                        </span>
                      </Tooltip>
                    </td>

                    <td className='px-6 py-4 text-gray-900'>{name}</td>
                    <td className='px-6 py-4 text-gray-500'>
                      {formatDate(order.created_at)}
                    </td>
                    <td className='px-6 py-4 text-gray-900 font-semibold'>
                      {formatCurrency(
                        order.items?.reduce(
                          (acc, item) => acc + item.unit_price * item.quantity,
                          0
                        ) || 0
                      )}
                    </td>
                    <td className='px-6 py-4'>
                      <OrderStatusBadge status={order.status || 'processing'} />
                    </td>
                    <td className='px-6 py-4 text-gray-700'>
                      {order.items?.length || 0} item(s)
                    </td>
                    <td className='px-6 py-4 text-right'>
                      <Link
                        href={`/orders/${order.id}`}
                        className='text-blue-600 hover:underline'
                      >
                        View
                      </Link>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
