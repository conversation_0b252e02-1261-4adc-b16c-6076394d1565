'use client';

// import FeaturedCategories from '@/components/homepage/FeaturedCategories';
// import MultiTenantHeroBanner from '@/components/homepage/MultiTenantHeroBanner';
// import MedusaProductsCarousel from '@/components/homepage/MedusaProductsCarousel';
// import MedusaTopDeals from '@/components/homepage/MedusaTopDeals';
// import MedusaFeaturedProducts from '@/components/homepage/MedusaFeaturedProducts';
// import MedusaHotPicks from '@/components/homepage/MedusaHotPicks';
// import TopDeals from '@/components/homepage/TopDeals';
// import FeaturedProducts from '@/components/homepage/FeaturedProducts';
// import ProductCarousel from '@/components/homepage/ProductCarousel';
import React, { useEffect, useMemo, useState } from 'react';
import HeroBanner from '@/components/homepage/HeroBanner';
import ValueProps from '@/components/homepage/ValueProps';
import { MedusaCartProvider } from '@/hooks/useMedusaCart';
import LatestDeals from '@/components/homepage/LatestDeals';
import DealCollection from '@/components/homepage/DealCollection';
import {
  useMedusaBackendCollections,
  useMedusaBackendProducts,
} from '@/hooks/useMedusaBackendProducts';
import { useCollectionStore } from '@/stores/collectionStore';
import { useCategoryStore } from '@/stores/categoriesStore';

const HomePage = () => {
  const [pageKey, setPageKey] = useState(0);
  const [productsByCollection, setProductsByCollection] = useState([]);
  const [loading, setLoading] = useState(true);
  const { error, fetchProducts } = useMedusaBackendProducts();
  const { fetchCollections, collections } = useMedusaBackendCollections();

  const setSelectedProductId = useCategoryStore(
    state => state.setSelectedProductId
  );

  const setCollectionData = useCollectionStore(
    state => state.setCollectionData
  );

  const fetchDealProducts = async () => {
    try {
      await fetchCollections();
    } catch (error) {}
  };

  const handleViewAllCollection = async (collectionId: string) => {
    const collectionData = collections.find(
      collection => collection.id === collectionId
    );
    const products = await fetchProducts({
      collection_id: [collectionId],
      limit: 10000,
    });
    const sampleCollection = {
      collection_id: collectionData?.id,
      collection_name: collectionData?.title,
      collection_slug: collectionData?.handle,
      collection_products: products || [],
    };

    setCollectionData([sampleCollection]);
  };

  const fetchCollectionProducts = async () => {
    setLoading(true);
    const results = await Promise.all(
      collections.map(async data => {
        const products = await fetchProducts({
          collection_id: [data.id],
          limit: 8,
          fields: [
            'id',
            'title',
            'created_at',
            'updated_at',
            'metadata',
            'subtitle',
            'description',
            'handle',
            'thumbnail',
            'collection_id',
            'tags',
            'variants',
            'image',
          ],
        });

        if (!products || products.length === 0) return null; // Skip empty collections

        return {
          collectionId: data.id,
          collectionName: data.title,
          products,
          slug: data.handle,
        };
      })
    );

    // Filter out null results (i.e., collections with no products)
    const filteredResults = results.filter(Boolean);
    setProductsByCollection(filteredResults);
    setLoading(false);
  };

  const handleProductClick = (productId: string) => {
    try {
      console.log('productId::::::::', productId);
      setSelectedProductId(productId);
    } catch (error) {
      console.error('Error:::::', error);
    }
  };

  useEffect(() => {
    if (collections.length < 1) return;
    fetchCollectionProducts();
  }, [collections]);

  useEffect(() => {
    // Force re-render when component mounts
    setPageKey(prev => prev + 1);
  }, []);

  // Listen for route changes and refresh banner
  useEffect(() => {
    const handleRouteChange = () => {
      setPageKey(prev => prev + 1);
    };

    // Listen for browser navigation events
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  useEffect(() => {
    fetchDealProducts();
  }, []);
  return (
    <MedusaCartProvider>
      <div className='min-h-screen bg-white mb-8'>
        {/* Hero Banner with Carousel Component */}
        <HeroBanner key={`hero-banner-${pageKey}`} />

        <LatestDeals
          title='New arrivals'
          subtitle='Check out our newest products'
          maxProducts={8}
          productType='New arrivals'
          showViewAll={false}
          viewAllLink='/deals?type=new-arrivals'
          handleCardClick={handleProductClick}
        />
        <LatestDeals
          title='Top Selling'
          subtitle='Handpicked products just for you'
          maxProducts={8}
          productType='latest'
          showViewAll={false}
          viewAllLink='/products'
          handleCardClick={handleProductClick}
        />

        {/* Collection-based Product Sections */}
        {productsByCollection.map((collection, index) => (
          <DealCollection
            data={collection}
            key={index}
            loading={loading}
            handleClick={handleViewAllCollection}
          />
        ))}

        {/* Value Propositions */}
        {/* <ValueProps
          title='Why Choose Our ONDC Store'
          subtitle='Experience the benefits of shopping through the Open Network for Digital Commerce'
          layout='grid'
          showBackground={true}
        /> */}
      </div>
    </MedusaCartProvider>
  );
};

export default HomePage;
