import React from 'react';
import Breadcrumbs from '@/components/Breadcrumbs';
import PageHero from '@/components/ui/PageHero';
import ContentCard from '@/components/ui/ContentCard';
import StyledContent from '@/components/ui/StyledContent';

import { getPageBySlug, type Page } from '@/lib/strapi-api';

// Server-side function to fetch page content
async function getPrivacyPolicyPageContent(): Promise<Page | null> {
  try {
    console.log('Server: Fetching privacy-policy page content from Strapi...');
    const content = await getPageBySlug('privacy-policy');
    console.log('Server: Fetched privacy-policy content:', content?.title);
    return content;
  } catch (error) {
    console.error('Server: Error fetching privacy-policy page:', error);
    return null;
  }
}

export default async function PrivacyPolicyPage() {
  // Fetch content on the server side
  const pageContent = await getPrivacyPolicyPageContent();
  console.log('inside privancy:::::::::', pageContent);
  // Fallback content if Strapi content is not available
  const fallbackContent: Page = {
    id: 21,
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content: `<h2>Privacy Policy</h2>
<p><strong>Last updated:</strong> June 10, 2025</p>

<h3>1. Information We Collect</h3>
<p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>

<h3>2. How We Use Your Information</h3>
<p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

<h3>3. Information Sharing</h3>
<p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

<h3>4. Data Security</h3>
<p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

<h3>5. Your Rights</h3>
<p>You have the right to access, update, or delete your personal information. Contact us if you wish to exercise these rights.</p>

<h3>6. Contact Us</h3>
<p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>`,
    excerpt:
      'Our privacy policy explains how we collect, use, and protect your personal information on the ONDC Seller Platform.',
    metaTitle: 'Privacy Policy - ONDC Seller Platform',
    metaDescription:
      'Read our privacy policy to understand how ONDC Seller Platform collects, uses, and protects your personal information.',
    status: 'published',
    template: 'default',
    featured: false,
    author: 'Admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // Use Strapi content if available, otherwise use fallback
  const displayContent = pageContent;
  // console.log({ displayContent });

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Hero Section */}
      <PageHero
        title={displayContent.title}
        description={
          displayContent.excerpt ||
          'Learn how we collect, use, and protect your personal information on the ONDC Seller Platform.'
        }
        icon={
          <svg
            className='w-12 h-12 text-white'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
            />
          </svg>
        }
        gradient='blue'
      />

      {/* Main Content */}
      <div className='px-4 sm:px-6 lg:px-8 py-12'>
        {/* Breadcrumbs */}
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'Privacy Policy', href: '/privacy-policy', active: true },
          ]}
        />

        <div className='max-w-4xl mx-auto'>
          {/* Main Content */}
          <ContentCard variant='elevated' padding='xl'>
            <StyledContent content={displayContent.content} />
          </ContentCard>
        </div>
      </div>
    </div>
  );
}
