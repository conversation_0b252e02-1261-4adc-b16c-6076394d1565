'use client';

import * as React from 'react';
import { useCategoryStore } from '@/stores/categoriesStore';
import MedusaProductDetailPage from '../../../components/products/MedusaProductDetailPage';
import { MedusaCartProvider } from '@/hooks/useMedusaCart';
import { ToastProvider } from '@/components/common/ToastProvider';

const ProductPage = ({ params }: { params: Promise<{ id: string }> }) => {
  const { id: productId } = React.use(params);
  const selectedSubCategoryId = useCategoryStore(
    state => state.selectedSubCategoryId
  );
  const selectedCategoryId = useCategoryStore(
    state => state.selectedCategoryId
  );

  return (
    <MedusaCartProvider>
      <ToastProvider>
        <MedusaProductDetailPage
          productId={productId}
          categoryId={selectedCategoryId}
          subCategoryId={selectedSubCategoryId}
        />
      </ToastProvider>
    </MedusaCartProvider>
  );
};

export default ProductPage;
