import { Metadata, ResolvingMetadata } from 'next';
import { getCategoryInfo } from '../../../../lib/category-api';
import { productCategories } from '../../../../data/categories';

interface CategoryLayoutProps {
  children: React.ReactNode;
  params: {
    categoryId: string;
  };
}

export async function generateMetadata(
  { params }: CategoryLayoutProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { categoryId } = params;

  // Get category information
  const categoryInfo = getCategoryInfo(categoryId);

  // If category doesn't exist, use fallback metadata
  if (!categoryInfo) {
    return {
      title: `Products | ONDC Seller Platform`,
      description: 'Browse our product catalog on the ONDC Seller Platform.',
    };
  }

  // Generate SEO metadata based on category
  return {
    title: `${categoryInfo.name} | ONDC Seller Platform`,
    description:
      categoryInfo.description ||
      `Explore our ${categoryInfo.name} collection featuring top brands and latest products.`,
    openGraph: {
      title: `${categoryInfo.name} | ONDC Seller Platform`,
      description:
        categoryInfo.description ||
        `Explore our ${categoryInfo.name} collection featuring top brands and latest products.`,
      type: 'website',
      locale: 'en_IN',
      siteName: 'ONDC Seller Platform',
    },
  };
}

export default function CategoryLayout({ children }: CategoryLayoutProps) {
  return <>{children}</>;
}
