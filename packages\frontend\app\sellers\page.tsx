'use client';

import React, { useState } from 'react';
import Image from '@/components/ui/Image';
import Link from 'next/link';

interface Seller {
  id: string;
  name: string;
  description: string;
  image: string;
  coverImage: string;
  rating: number;
  totalSales: number;
  verified: boolean;
  location: string;
  categories: string[];
  featuredProducts: {
    id: string;
    name: string;
    price: number;
    image: string;
  }[];
  stats: {
    productsCount: number;
    customersServed: number;
    avgDeliveryTime: string;
  };
}

const SellersPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // This would typically come from an API
  const sellers: Seller[] = [
    {
      id: '1',
      name: 'Tech World',
      description:
        'Your one-stop shop for all things tech. We offer the latest gadgets and accessories.',
      image: '/api/images/products/seller-avatar.jpg',
      coverImage: '/api/images/products/seller-cover.jpg',
      rating: 4.8,
      totalSales: 1500,
      verified: true,
      location: 'Mumbai, India',
      categories: ['Electronics', 'Accessories', 'Gadgets'],
      featuredProducts: [
        {
          id: '1',
          name: 'Premium Wireless Headphones',
          price: 199.99,
          image: '/api/images/products/headphones.jpg',
        },
        // Add more products...
      ],
      stats: {
        productsCount: 250,
        customersServed: 5000,
        avgDeliveryTime: '2-3 days',
      },
    },
    // Add more sellers...
  ];

  const categories = [
    'All Categories',
    'Electronics',
    'Fashion',
    'Home & Living',
    'Beauty',
    'Sports',
    'Books',
  ];

  const filteredSellers = sellers.filter(seller => {
    const matchesSearch = seller.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesCategory =
      !selectedCategory ||
      selectedCategory === 'All Categories' ||
      seller.categories.includes(selectedCategory);
    return matchesSearch && matchesCategory;
  });

  return (
    <div className='px-4 sm:px-6 lg:px-8 py-8'>
      <div className='text-center mb-12'>
        <h1 className='text-3xl font-bold text-gray-900'>
          Our Verified Sellers
        </h1>
        <p className='mt-4 text-lg text-gray-600'>
          Shop from our curated list of trusted sellers offering quality
          products
        </p>
      </div>

      {/* Search and Filter */}
      <div className='flex flex-col sm:flex-row gap-4 mb-8'>
        <div className='flex-1'>
          <div className='relative'>
            <input
              type='text'
              placeholder='Search sellers...'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            />
            <svg
              className='absolute left-3 top-2.5 h-5 w-5 text-gray-400'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
              />
            </svg>
          </div>
        </div>
        <div className='w-full sm:w-48'>
          <select
            value={selectedCategory || 'All Categories'}
            onChange={e => setSelectedCategory(e.target.value)}
            className='w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Sellers Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
        {filteredSellers.map(seller => (
          <Link
            key={seller.id}
            href={`/sellers/${seller.id}`}
            className='group bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-200 overflow-hidden'
          >
            <div className='relative h-48'>
              <Image
                src={seller.coverImage}
                alt={`${seller.name} cover`}
                fill
                className='object-cover'
                fallback='/images/placeholder.jpg'
              />
              <div className='absolute inset-0 bg-gradient-to-t from-black/60 to-transparent'></div>
              <div className='absolute bottom-4 left-4 right-4 flex items-center'>
                <div className='relative w-16 h-16 rounded-full overflow-hidden border-2 border-white'>
                  <Image
                    src={seller.image}
                    alt={seller.name}
                    fill
                    className='object-cover'
                    fallback='/images/placeholder.jpg'
                  />
                </div>
                <div className='ml-4'>
                  <h3 className='text-xl font-bold text-white'>
                    {seller.name}
                    {seller.verified && (
                      <svg
                        className='inline-block ml-2 w-5 h-5 text-blue-500'
                        fill='currentColor'
                        viewBox='0 0 20 20'
                      >
                        <path
                          fillRule='evenodd'
                          d='M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z'
                          clipRule='evenodd'
                        />
                      </svg>
                    )}
                  </h3>
                  <p className='text-white/80 text-sm'>{seller.location}</p>
                </div>
              </div>
            </div>

            <div className='p-6'>
              <div className='flex items-center justify-between mb-4'>
                <div className='flex items-center'>
                  <svg
                    className='w-5 h-5 text-yellow-400'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' />
                  </svg>
                  <span className='ml-2 text-gray-900'>
                    {seller.rating} / 5
                  </span>
                </div>
                <span className='text-gray-600'>
                  {seller.totalSales}+ sales
                </span>
              </div>

              <p className='text-gray-600 mb-4'>{seller.description}</p>

              <div className='flex flex-wrap gap-2 mb-4'>
                {seller.categories.map(category => (
                  <span
                    key={category}
                    className='px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full'
                  >
                    {category}
                  </span>
                ))}
              </div>

              <div className='grid grid-cols-3 gap-4 mb-4'>
                <div className='text-center'>
                  <div className='text-lg font-semibold text-gray-900'>
                    {seller.stats.productsCount}+
                  </div>
                  <div className='text-sm text-gray-600'>Products</div>
                </div>
                <div className='text-center'>
                  <div className='text-lg font-semibold text-gray-900'>
                    {seller.stats.customersServed}+
                  </div>
                  <div className='text-sm text-gray-600'>Customers</div>
                </div>
                <div className='text-center'>
                  <div className='text-lg font-semibold text-gray-900'>
                    {seller.stats.avgDeliveryTime}
                  </div>
                  <div className='text-sm text-gray-600'>Delivery</div>
                </div>
              </div>

              <div>
                <h4 className='text-sm font-medium text-gray-900 mb-2'>
                  Featured Products
                </h4>
                <div className='grid grid-cols-3 gap-2'>
                  {seller.featuredProducts.map(product => (
                    <div
                      key={product.id}
                      className='relative aspect-square rounded-lg overflow-hidden'
                    >
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className='object-cover'
                        fallback='/images/placeholder.jpg'
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {filteredSellers.length === 0 && (
        <div className='text-center py-16'>
          <svg
            className='mx-auto h-12 w-12 text-gray-400'
            fill='none'
            viewBox='0 0 24 24'
            stroke='currentColor'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={1}
              d='M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z'
            />
          </svg>
          <h2 className='mt-4 text-lg font-medium text-gray-900'>
            No sellers found
          </h2>
          <p className='mt-2 text-gray-500'>
            Try adjusting your search or filter to find what you're looking for.
          </p>
        </div>
      )}
    </div>
  );
};

export default SellersPage;
