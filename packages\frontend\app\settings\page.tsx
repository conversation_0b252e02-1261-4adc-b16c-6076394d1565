'use client';

import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Stack,
  Divider,
  Alert,
  Snackbar,
  CircularProgress,
  Tabs,
  Tab,
  Container,
  Paper,
  IconButton,
  InputAdornment,
} from '@mui/material';
import {
  Save as SaveIcon,
  Edit as EditIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Store as StoreIcon,
  Settings as SettingsIcon,
  LocalShipping as ShippingIcon,
  Payment as PaymentIcon,
  Notifications as NotificationsIcon,
  People as PeopleIcon,
  Extension as ExtensionIcon,
  AccountBalance as TaxIcon,
} from '@mui/icons-material';
import Breadcrumbs from '@/components/Breadcrumbs';
import ClientTenantDisplay from '@/components/ClientTenantDisplay';
import { useSettingsFormValidation } from '@/hooks/useFormValidation';

// Settings tabs with icons
const SETTINGS_TABS = [
  { id: 'general', name: 'General', icon: SettingsIcon },
  { id: 'store', name: 'Store Profile', icon: StoreIcon },
  { id: 'ondc', name: 'ONDC Settings', icon: ExtensionIcon },
  { id: 'shipping', name: 'Shipping', icon: ShippingIcon },
  { id: 'tax', name: 'Tax', icon: TaxIcon },
  { id: 'payment', name: 'Payment Methods', icon: PaymentIcon },
  { id: 'notifications', name: 'Notifications', icon: NotificationsIcon },
  { id: 'users', name: 'Users & Permissions', icon: PeopleIcon },
  { id: 'integrations', name: 'Integrations', icon: ExtensionIcon },
];

// Sample store settings with ONDC Seller branding
const SAMPLE_STORE_SETTINGS = {
  name: 'ONDC Seller Store',
  email: '<EMAIL>',
  phone: '+91 9876543210',
  address: {
    line1: '123 Business Park',
    line2: 'Sector 5',
    city: 'Mumbai',
    state: 'Maharashtra',
    postalCode: '400001',
    country: 'India',
  },
  currency: 'INR',
  timezone: 'Asia/Kolkata',
  logo: '/images/ondc-seller-logo.png',
  ondcSettings: {
    sellerId: 'ONDC-SELLER-123456',
    apiEndpoint: 'https://api.ondc.org/v1',
    apiKey: '********-****-****-****-************',
    subscriberId: 'ONDC-SUB-789012',
    subscriberUrl: 'https://ondcseller.com/ondc-api',
    categories: ['electronics', 'mobile', 'computer', 'fashion', 'home'],
    fulfillmentTypes: ['delivery', 'pickup'],
  },
};

// Interface for store settings
interface StoreSettings {
  name: string;
  email: string;
  phone: string;
  address: {
    line1: string;
    line2: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  currency: string;
  timezone: string;
  logo: string;
  ondcSettings: {
    sellerId: string;
    apiEndpoint: string;
    apiKey: string;
    subscriberId: string;
    subscriberUrl: string;
    categories: string[];
    fulfillmentTypes: string[];
  };
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('general');
  const [isEditing, setIsEditing] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [submitError, setSubmitError] = useState<string>('');
  const [showApiKey, setShowApiKey] = useState(false);

  // Enhanced form validation with comprehensive error handling
  const {
    data: formData,
    errors,
    touched,
    isValid,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    resetForm,
  } = useSettingsFormValidation({
    initialData: SAMPLE_STORE_SETTINGS,
    onSubmit: async (data: Record<string, any>) => {
      try {
        setSubmitError('');
        console.log('Saving customer settings:', data);

        // Simulate API call with proper error handling
        await new Promise((resolve, reject) => {
          setTimeout(() => {
            // Simulate random API failures for testing
            if (Math.random() > 0.8) {
              reject(new Error('Network error: Unable to save settings'));
            } else {
              resolve(data);
            }
          }, 1500);
        });

        setIsEditing(false);
        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 5000);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to save settings';
        setSubmitError(errorMessage);
        console.error('Customer settings save error:', error);
        throw error; // Re-throw to be handled by the form validation hook
      }
    },
    onError: (formErrors: Record<string, string>) => {
      console.error('Customer settings form validation errors:', formErrors);
    },
  });

  // Handle Select component changes (Material-UI specific)
  const handleSelectChange = useCallback(
    (name: string) => (event: any) => {
      handleChange({
        target: {
          name,
          value: event.target.value,
        },
      } as React.ChangeEvent<HTMLInputElement>);
    },
    [handleChange]
  );

  // Handle nested field changes (e.g., address.city)
  const handleNestedChange = useCallback(
    (parentKey: string, childKey: string) =>
      (event: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = {
          ...formData[parentKey],
          [childKey]: event.target.value,
        };
        setFieldValue(parentKey, newValue);
      },
    [formData, setFieldValue]
  );

  // Handle tab change
  const handleTabChange = useCallback(
    (event: React.SyntheticEvent, newValue: string) => {
      setActiveTab(newValue);
    },
    []
  );

  // Handle edit mode toggle
  const handleEditToggle = useCallback(() => {
    if (isEditing) {
      resetForm();
    }
    setIsEditing(!isEditing);
  }, [isEditing, resetForm]);

  return (
    <Container maxWidth='lg' sx={{ py: 3 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        items={[
          { label: 'Home', href: '/' },
          { label: 'Settings', href: '/settings', active: true },
        ]}
      />

      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mt: 3,
          mb: 4,
        }}
      >
        <Typography variant='h3' component='h1' fontWeight='bold'>
          Settings
        </Typography>

        <Box>
          {isEditing ? (
            <Stack direction='row' spacing={2}>
              <Button
                variant='outlined'
                startIcon={<CancelIcon />}
                onClick={handleEditToggle}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                startIcon={
                  isSubmitting ? (
                    <CircularProgress size={20} color='inherit' />
                  ) : (
                    <SaveIcon />
                  )
                }
                onClick={handleSubmit}
                disabled={isSubmitting || !isValid}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </Stack>
          ) : (
            <Button
              variant='contained'
              startIcon={<EditIcon />}
              onClick={handleEditToggle}
            >
              Edit Settings
            </Button>
          )}
        </Box>
      </Box>

      {/* Success Message */}
      <Snackbar
        open={showSuccessMessage}
        autoHideDuration={5000}
        onClose={() => setShowSuccessMessage(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setShowSuccessMessage(false)}
          severity='success'
          sx={{ width: '100%' }}
        >
          Settings saved successfully!
        </Alert>
      </Snackbar>

      {/* Error Message */}
      {submitError && (
        <Alert
          severity='error'
          sx={{ mb: 3 }}
          onClose={() => setSubmitError('')}
        >
          {submitError}
        </Alert>
      )}

      {/* Form Validation Errors */}
      {Object.keys(errors).length > 0 && (
        <Alert severity='warning' sx={{ mb: 3 }}>
          <Typography variant='body2' fontWeight='medium'>
            Please fix the following errors:
          </Typography>
          <Box component='ul' sx={{ mt: 1, pl: 2 }}>
            {Object.entries(errors).map(([field, error]) => (
              <Typography component='li' key={field} variant='body2'>
                {error}
              </Typography>
            ))}
          </Box>
        </Alert>
      )}

      {/* Multi-Tenant Info */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Stack direction='row' alignItems='center' spacing={2}>
            <Box
              sx={{
                bgcolor: 'primary.light',
                borderRadius: '50%',
                p: 1.5,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SettingsIcon color='primary' />
            </Box>
            <Box>
              <Typography variant='h6' fontWeight='medium'>
                Multi-Tenant Settings
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                Current Tenant: <ClientTenantDisplay />
              </Typography>
            </Box>
          </Stack>
        </CardContent>
      </Card>

      {/* Settings Card with Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant='scrollable'
            scrollButtons='auto'
            sx={{ px: 2 }}
          >
            {SETTINGS_TABS.map(tab => {
              const IconComponent = tab.icon;
              return (
                <Tab
                  key={tab.id}
                  label={
                    <Stack direction='row' alignItems='center' spacing={1}>
                      <IconComponent fontSize='small' />
                      <span>{tab.name}</span>
                    </Stack>
                  }
                  value={tab.id}
                  sx={{ textTransform: 'none', minHeight: 64 }}
                />
              );
            })}
          </Tabs>
        </Box>

        {/* Settings content */}
        <CardContent sx={{ p: 4 }}>
          {activeTab === 'general' && (
            <Box component='form' onSubmit={handleSubmit}>
              <Stack spacing={4}>
                <Box>
                  <Typography variant='h6' fontWeight='medium'>
                    General Settings
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Basic information about your store.
                  </Typography>
                </Box>

                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                    gap: 3,
                  }}
                >
                  <TextField
                    fullWidth
                    label='Store Name'
                    name='name'
                    value={formData.name || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    disabled={!isEditing || isSubmitting}
                    error={Boolean(errors.name && touched.name)}
                    helperText={
                      (errors.name && touched.name && errors.name) ||
                      'The name of your store as it appears to customers'
                    }
                    variant='outlined'
                  />
                  <TextField
                    fullWidth
                    label='Email Address'
                    name='email'
                    type='email'
                    value={formData.email || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    disabled={!isEditing || isSubmitting}
                    error={Boolean(errors.email && touched.email)}
                    helperText={
                      (errors.email && touched.email && errors.email) ||
                      'Primary contact email for your store'
                    }
                    variant='outlined'
                  />
                  <TextField
                    fullWidth
                    label='Phone Number'
                    name='phone'
                    value={formData.phone || ''}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    disabled={!isEditing || isSubmitting}
                    error={Boolean(errors.phone && touched.phone)}
                    helperText={
                      (errors.phone && touched.phone && errors.phone) ||
                      'Contact phone number for customer support'
                    }
                    variant='outlined'
                  />
                  <FormControl fullWidth disabled={!isEditing || isSubmitting}>
                    <InputLabel>Currency</InputLabel>
                    <Select
                      name='currency'
                      value={formData.currency || ''}
                      onChange={handleSelectChange('currency')}
                      onBlur={handleBlur}
                      label='Currency'
                      error={Boolean(errors.currency && touched.currency)}
                    >
                      <MenuItem value='INR'>Indian Rupee (₹)</MenuItem>
                      <MenuItem value='USD'>US Dollar ($)</MenuItem>
                      <MenuItem value='EUR'>Euro (€)</MenuItem>
                      <MenuItem value='GBP'>British Pound (£)</MenuItem>
                    </Select>
                    {errors.currency && touched.currency && (
                      <Typography
                        variant='caption'
                        color='error'
                        sx={{ mt: 0.5, ml: 1.5 }}
                      >
                        {errors.currency}
                      </Typography>
                    )}
                  </FormControl>
                  <FormControl fullWidth disabled={!isEditing || isSubmitting}>
                    <InputLabel>Timezone</InputLabel>
                    <Select
                      name='timezone'
                      value={formData.timezone || ''}
                      onChange={handleSelectChange('timezone')}
                      onBlur={handleBlur}
                      label='Timezone'
                      error={Boolean(errors.timezone && touched.timezone)}
                    >
                      <MenuItem value='Asia/Kolkata'>India (GMT+5:30)</MenuItem>
                      <MenuItem value='America/New_York'>
                        Eastern Time (GMT-5:00)
                      </MenuItem>
                      <MenuItem value='America/Los_Angeles'>
                        Pacific Time (GMT-8:00)
                      </MenuItem>
                      <MenuItem value='Europe/London'>
                        London (GMT+0:00)
                      </MenuItem>
                    </Select>
                    {errors.timezone && touched.timezone && (
                      <Typography
                        variant='caption'
                        color='error'
                        sx={{ mt: 0.5, ml: 1.5 }}
                      >
                        {errors.timezone}
                      </Typography>
                    )}
                  </FormControl>
                </Box>

                <Divider />

                <Box>
                  <Typography variant='h6' fontWeight='medium'>
                    Store Address
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Your store's physical address.
                  </Typography>
                </Box>

                <Stack spacing={3}>
                  <TextField
                    fullWidth
                    label='Address Line 1'
                    name='address.line1'
                    value={formData.address?.line1 || ''}
                    onChange={handleNestedChange('address', 'line1')}
                    onBlur={handleBlur}
                    disabled={!isEditing || isSubmitting}
                    error={Boolean(
                      errors['address.line1'] && touched['address.line1']
                    )}
                    helperText={
                      (errors['address.line1'] &&
                        touched['address.line1'] &&
                        errors['address.line1']) ||
                      'Street address, building number, etc.'
                    }
                    variant='outlined'
                  />
                  <TextField
                    fullWidth
                    label='Address Line 2'
                    name='address.line2'
                    value={formData.address?.line2 || ''}
                    onChange={handleNestedChange('address', 'line2')}
                    onBlur={handleBlur}
                    disabled={!isEditing || isSubmitting}
                    error={Boolean(
                      errors['address.line2'] && touched['address.line2']
                    )}
                    helperText={
                      (errors['address.line2'] &&
                        touched['address.line2'] &&
                        errors['address.line2']) ||
                      'Apartment, suite, unit, building, floor, etc. (optional)'
                    }
                    variant='outlined'
                  />
                  <Box
                    sx={{
                      display: 'grid',
                      gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                      gap: 3,
                    }}
                  >
                    <TextField
                      fullWidth
                      label='City'
                      name='address.city'
                      value={formData.address?.city || ''}
                      onChange={handleNestedChange('address', 'city')}
                      onBlur={handleBlur}
                      disabled={!isEditing || isSubmitting}
                      error={Boolean(
                        errors['address.city'] && touched['address.city']
                      )}
                      helperText={
                        errors['address.city'] &&
                        touched['address.city'] &&
                        errors['address.city']
                      }
                      variant='outlined'
                    />
                    <TextField
                      fullWidth
                      label='State / Province'
                      name='address.state'
                      value={formData.address?.state || ''}
                      onChange={handleNestedChange('address', 'state')}
                      onBlur={handleBlur}
                      disabled={!isEditing || isSubmitting}
                      error={Boolean(
                        errors['address.state'] && touched['address.state']
                      )}
                      helperText={
                        errors['address.state'] &&
                        touched['address.state'] &&
                        errors['address.state']
                      }
                      variant='outlined'
                    />
                    <TextField
                      fullWidth
                      label='ZIP / Postal Code'
                      name='address.postalCode'
                      value={formData.address?.postalCode || ''}
                      onChange={handleNestedChange('address', 'postalCode')}
                      onBlur={handleBlur}
                      disabled={!isEditing || isSubmitting}
                      error={Boolean(
                        errors['address.postalCode'] &&
                          touched['address.postalCode']
                      )}
                      helperText={
                        errors['address.postalCode'] &&
                        touched['address.postalCode'] &&
                        errors['address.postalCode']
                      }
                      variant='outlined'
                    />
                    <FormControl
                      fullWidth
                      disabled={!isEditing || isSubmitting}
                    >
                      <InputLabel>Country</InputLabel>
                      <Select
                        name='address.country'
                        value={formData.address?.country || ''}
                        onChange={event => {
                          const newValue = {
                            ...formData.address,
                            country: event.target.value,
                          };
                          setFieldValue('address', newValue);
                        }}
                        onBlur={handleBlur}
                        label='Country'
                        error={Boolean(
                          errors['address.country'] &&
                            touched['address.country']
                        )}
                      >
                        <MenuItem value='India'>India</MenuItem>
                        <MenuItem value='United States'>United States</MenuItem>
                        <MenuItem value='United Kingdom'>
                          United Kingdom
                        </MenuItem>
                        <MenuItem value='Canada'>Canada</MenuItem>
                        <MenuItem value='Australia'>Australia</MenuItem>
                      </Select>
                      {errors['address.country'] &&
                        touched['address.country'] && (
                          <Typography
                            variant='caption'
                            color='error'
                            sx={{ mt: 0.5, ml: 1.5 }}
                          >
                            {errors['address.country']}
                          </Typography>
                        )}
                    </FormControl>
                  </Box>
                </Stack>
              </Stack>
            </Box>
          )}

          {activeTab === 'ondc' && (
            <Box component='form' onSubmit={handleSubmit}>
              <Stack spacing={4}>
                <Box>
                  <Typography variant='h6' fontWeight='medium'>
                    ONDC Settings
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Configure your Open Network for Digital Commerce (ONDC)
                    integration.
                  </Typography>
                </Box>

                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                    gap: 3,
                  }}
                >
                  <TextField
                    fullWidth
                    label='ONDC Seller ID'
                    name='ondcSettings.sellerId'
                    value={formData.ondcSettings?.sellerId || ''}
                    onChange={handleNestedChange('ondcSettings', 'sellerId')}
                    onBlur={handleBlur}
                    disabled={!isEditing || isSubmitting}
                    error={Boolean(
                      errors['ondcSettings.sellerId'] &&
                        touched['ondcSettings.sellerId']
                    )}
                    helperText={
                      (errors['ondcSettings.sellerId'] &&
                        touched['ondcSettings.sellerId'] &&
                        errors['ondcSettings.sellerId']) ||
                      'Your unique seller identifier in the ONDC network'
                    }
                    variant='outlined'
                  />
                  <TextField
                    fullWidth
                    label='ONDC Subscriber ID'
                    name='ondcSettings.subscriberId'
                    value={formData.ondcSettings?.subscriberId || ''}
                    onChange={handleNestedChange(
                      'ondcSettings',
                      'subscriberId'
                    )}
                    onBlur={handleBlur}
                    disabled={!isEditing || isSubmitting}
                    error={Boolean(
                      errors['ondcSettings.subscriberId'] &&
                        touched['ondcSettings.subscriberId']
                    )}
                    helperText={
                      (errors['ondcSettings.subscriberId'] &&
                        touched['ondcSettings.subscriberId'] &&
                        errors['ondcSettings.subscriberId']) ||
                      'Your ONDC subscriber identification'
                    }
                    variant='outlined'
                  />
                </Box>

                <TextField
                  fullWidth
                  label='ONDC API Endpoint'
                  name='ondcSettings.apiEndpoint'
                  value={formData.ondcSettings?.apiEndpoint || ''}
                  onChange={handleNestedChange('ondcSettings', 'apiEndpoint')}
                  onBlur={handleBlur}
                  disabled={!isEditing || isSubmitting}
                  error={Boolean(
                    errors['ondcSettings.apiEndpoint'] &&
                      touched['ondcSettings.apiEndpoint']
                  )}
                  helperText={
                    (errors['ondcSettings.apiEndpoint'] &&
                      touched['ondcSettings.apiEndpoint'] &&
                      errors['ondcSettings.apiEndpoint']) ||
                    'The ONDC API endpoint URL for your integration'
                  }
                  variant='outlined'
                />

                <TextField
                  fullWidth
                  label='Subscriber URL'
                  name='ondcSettings.subscriberUrl'
                  value={formData.ondcSettings?.subscriberUrl || ''}
                  onChange={handleNestedChange('ondcSettings', 'subscriberUrl')}
                  onBlur={handleBlur}
                  disabled={!isEditing || isSubmitting}
                  error={Boolean(
                    errors['ondcSettings.subscriberUrl'] &&
                      touched['ondcSettings.subscriberUrl']
                  )}
                  helperText={
                    (errors['ondcSettings.subscriberUrl'] &&
                      touched['ondcSettings.subscriberUrl'] &&
                      errors['ondcSettings.subscriberUrl']) ||
                    'Your callback URL for ONDC notifications'
                  }
                  variant='outlined'
                />

                <TextField
                  fullWidth
                  label='API Key'
                  name='ondcSettings.apiKey'
                  type={showApiKey ? 'text' : 'password'}
                  value={formData.ondcSettings?.apiKey || ''}
                  onChange={handleNestedChange('ondcSettings', 'apiKey')}
                  onBlur={handleBlur}
                  disabled={!isEditing || isSubmitting}
                  error={Boolean(
                    errors['ondcSettings.apiKey'] &&
                      touched['ondcSettings.apiKey']
                  )}
                  helperText={
                    (errors['ondcSettings.apiKey'] &&
                      touched['ondcSettings.apiKey'] &&
                      errors['ondcSettings.apiKey']) ||
                    'Your secure API key for ONDC authentication'
                  }
                  variant='outlined'
                  InputProps={{
                    endAdornment: isEditing && (
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label='toggle password visibility'
                          onClick={() => setShowApiKey(!showApiKey)}
                          edge='end'
                        >
                          {showApiKey ? (
                            <VisibilityOffIcon />
                          ) : (
                            <VisibilityIcon />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Stack>
            </Box>
          )}

          {activeTab !== 'general' && activeTab !== 'ondc' && (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  mb: 2,
                  color: 'text.secondary',
                }}
              >
                <SettingsIcon sx={{ fontSize: 48 }} />
              </Box>
              <Typography
                variant='h6'
                fontWeight='medium'
                color='text.primary'
                gutterBottom
              >
                No settings configured
              </Typography>
              <Typography variant='body2' color='text.secondary' sx={{ mb: 3 }}>
                This settings section is coming soon.
              </Typography>
              <Button
                variant='contained'
                startIcon={<SettingsIcon />}
                disabled
                sx={{ textTransform: 'none' }}
              >
                Configure{' '}
                {SETTINGS_TABS.find(tab => tab.id === activeTab)?.name}
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>
    </Container>
  );
}
