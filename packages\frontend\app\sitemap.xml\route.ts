import { NextRequest, NextResponse } from 'next/server';
import { medusaAPI } from '@/lib/medusa-backend-api';

// Cache the sitemap for 1 hour
export const revalidate = 3600;

export async function GET(request: NextRequest) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://ondcseller.com';
    
    // Static pages
    const staticPages = [
      '',
      '/products',
      '/categories',
      '/search',
      '/cart',
      '/account',
      '/auth/login',
      '/auth/register',
      '/terms',
      '/privacy',
      '/legal/terms-of-service',
      '/legal/privacy-policy',
      '/legal/cookie-policy',
      '/legal/refund-policy',
    ];

    // Fetch dynamic content
    const [productsResponse, categoriesResponse] = await Promise.allSettled([
      medusaAPI.getProducts({ limit: 1000 }),
      medusaAPI.getCategories(),
    ]);

    let products: any[] = [];
    let categories: any[] = [];

    if (productsResponse.status === 'fulfilled' && productsResponse.value.success) {
      products = productsResponse.value.products || [];
    }

    if (categoriesResponse.status === 'fulfilled' && categoriesResponse.value.success) {
      categories = categoriesResponse.value.categories || [];
    }

    // Generate sitemap XML
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
${staticPages
  .map(
    (page) => `  <url>
    <loc>${baseUrl}${page}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${page === '' ? 'daily' : page.includes('/legal/') ? 'monthly' : 'weekly'}</changefreq>
    <priority>${page === '' ? '1.0' : page === '/products' ? '0.9' : '0.8'}</priority>
  </url>`
  )
  .join('\n')}
${products
  .map(
    (product) => `  <url>
    <loc>${baseUrl}/products/${product.id}</loc>
    <lastmod>${product.updated_at ? new Date(product.updated_at).toISOString() : new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
    ${product.images && product.images.length > 0 ? 
      product.images.map((image: any) => `    <image:image>
      <image:loc>${image.url}</image:loc>
      <image:title>${product.title}</image:title>
      <image:caption>${product.description || product.title}</image:caption>
    </image:image>`).join('\n') : ''
    }
  </url>`
  )
  .join('\n')}
${categories
  .map(
    (category) => `  <url>
    <loc>${baseUrl}/categories/${category.handle || category.id}</loc>
    <lastmod>${category.updated_at ? new Date(category.updated_at).toISOString() : new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`
  )
  .join('\n')}
</urlset>`;

    return new NextResponse(sitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    
    // Return a basic sitemap if there's an error
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://ondcseller.com';
    const basicSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${baseUrl}/products</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;

    return new NextResponse(basicSitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    });
  }
}
