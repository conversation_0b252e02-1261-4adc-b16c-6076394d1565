'use client';

import React, { useState } from 'react';
import {
  Plus,
  Search,
  Filter,
  Toggle,
  Edit,
  Trash2,
  Flag,
  Users,
  Calendar,
  AlertCircle,
  CheckCircle,
  Eye,
  Settings,
} from 'lucide-react';

interface FeatureFlag {
  id: string;
  name: string;
  key: string;
  description: string;
  is_enabled: boolean;
  rollout_percentage: number;
  target_plans: string[];
  target_tenants: string[];
  environment: 'development' | 'staging' | 'production';
  created_at: string;
  updated_at: string;
  created_by: string;
}

const FeatureFlagsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterEnvironment, setFilterEnvironment] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Mock feature flags data
  const [featureFlags, setFeatureFlags] = useState<FeatureFlag[]>([
    {
      id: 'flag_1',
      name: 'Advanced Analytics',
      key: 'advanced_analytics',
      description:
        'Enable advanced analytics dashboard with detailed insights and custom reports',
      is_enabled: true,
      rollout_percentage: 100,
      target_plans: ['pro', 'enterprise'],
      target_tenants: [],
      environment: 'production',
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-20T14:30:00Z',
      created_by: '<EMAIL>',
    },
    {
      id: 'flag_2',
      name: 'Multi-Currency Support',
      key: 'multi_currency',
      description:
        'Allow tenants to sell in multiple currencies with automatic conversion',
      is_enabled: false,
      rollout_percentage: 25,
      target_plans: ['enterprise'],
      target_tenants: ['tenant_1', 'tenant_2'],
      environment: 'staging',
      created_at: '2024-01-10T08:00:00Z',
      updated_at: '2024-01-25T16:45:00Z',
      created_by: '<EMAIL>',
    },
    {
      id: 'flag_3',
      name: 'AI Product Recommendations',
      key: 'ai_recommendations',
      description:
        'Machine learning powered product recommendations for customers',
      is_enabled: true,
      rollout_percentage: 50,
      target_plans: ['pro', 'enterprise'],
      target_tenants: [],
      environment: 'production',
      created_at: '2024-01-05T12:00:00Z',
      updated_at: '2024-01-28T09:15:00Z',
      created_by: '<EMAIL>',
    },
    {
      id: 'flag_4',
      name: 'Social Media Integration',
      key: 'social_media_integration',
      description:
        'Connect and sync products with social media platforms for marketing',
      is_enabled: false,
      rollout_percentage: 0,
      target_plans: ['basic', 'pro', 'enterprise'],
      target_tenants: [],
      environment: 'development',
      created_at: '2024-01-20T14:00:00Z',
      updated_at: '2024-01-22T11:30:00Z',
      created_by: '<EMAIL>',
    },
    {
      id: 'flag_5',
      name: 'Inventory Forecasting',
      key: 'inventory_forecasting',
      description:
        'Predictive analytics for inventory management and demand forecasting',
      is_enabled: true,
      rollout_percentage: 75,
      target_plans: ['enterprise'],
      target_tenants: [],
      environment: 'production',
      created_at: '2024-01-12T16:00:00Z',
      updated_at: '2024-01-26T13:20:00Z',
      created_by: '<EMAIL>',
    },
  ]);

  const environments = [
    { value: 'all', label: 'All Environments' },
    { value: 'development', label: 'Development' },
    { value: 'staging', label: 'Staging' },
    { value: 'production', label: 'Production' },
  ];

  const statusOptions = [
    { value: 'all', label: 'All Flags' },
    { value: 'enabled', label: 'Enabled' },
    { value: 'disabled', label: 'Disabled' },
  ];

  const filteredFlags = featureFlags.filter(flag => {
    const matchesSearch =
      flag.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      flag.key.toLowerCase().includes(searchQuery.toLowerCase()) ||
      flag.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesEnvironment =
      filterEnvironment === 'all' || flag.environment === filterEnvironment;
    const matchesStatus =
      filterStatus === 'all' ||
      (filterStatus === 'enabled' && flag.is_enabled) ||
      (filterStatus === 'disabled' && !flag.is_enabled);

    return matchesSearch && matchesEnvironment && matchesStatus;
  });

  const getEnvironmentColor = (environment: string) => {
    switch (environment) {
      case 'production':
        return 'bg-green-100 text-green-800';
      case 'staging':
        return 'bg-yellow-100 text-yellow-800';
      case 'development':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRolloutColor = (percentage: number) => {
    if (percentage === 0) return 'bg-gray-100 text-gray-800';
    if (percentage < 25) return 'bg-red-100 text-red-800';
    if (percentage < 50) return 'bg-yellow-100 text-yellow-800';
    if (percentage < 75) return 'bg-blue-100 text-blue-800';
    return 'bg-green-100 text-green-800';
  };

  const toggleFlag = (flagId: string) => {
    setFeatureFlags(flags =>
      flags.map(flag =>
        flag.id === flagId
          ? {
              ...flag,
              is_enabled: !flag.is_enabled,
              updated_at: new Date().toISOString(),
            }
          : flag
      )
    );
  };

  const deleteFlag = (flagId: string) => {
    if (confirm('Are you sure you want to delete this feature flag?')) {
      setFeatureFlags(flags => flags.filter(flag => flag.id !== flagId));
    }
  };

  // Calculate summary stats
  const totalFlags = featureFlags.length;
  const enabledFlags = featureFlags.filter(f => f.is_enabled).length;
  const productionFlags = featureFlags.filter(
    f => f.environment === 'production'
  ).length;
  const stagingFlags = featureFlags.filter(
    f => f.environment === 'staging'
  ).length;

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Feature Flags</h1>
          <p className='text-gray-600'>
            Manage feature rollouts and experiments across environments
          </p>
        </div>
        <button className='inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors'>
          <Plus className='w-4 h-4 mr-2' />
          Create Flag
        </button>
      </div>

      {/* Summary Cards */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center'>
              <Flag className='w-4 h-4 text-blue-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Total Flags</p>
              <p className='text-2xl font-bold text-gray-900'>{totalFlags}</p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center'>
              <CheckCircle className='w-4 h-4 text-green-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Enabled</p>
              <p className='text-2xl font-bold text-gray-900'>{enabledFlags}</p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center'>
              <Settings className='w-4 h-4 text-green-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Production</p>
              <p className='text-2xl font-bold text-gray-900'>
                {productionFlags}
              </p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center'>
              <AlertCircle className='w-4 h-4 text-yellow-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Staging</p>
              <p className='text-2xl font-bold text-gray-900'>{stagingFlags}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className='bg-white rounded-lg shadow-sm border p-6'>
        <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
            <input
              type='text'
              placeholder='Search feature flags...'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            />
          </div>

          <select
            value={filterEnvironment}
            onChange={e => setFilterEnvironment(e.target.value)}
            className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
          >
            {environments.map(env => (
              <option key={env.value} value={env.value}>
                {env.label}
              </option>
            ))}
          </select>

          <select
            value={filterStatus}
            onChange={e => setFilterStatus(e.target.value)}
            className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
          >
            {statusOptions.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          <button className='flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors'>
            <Filter className='w-4 h-4 mr-2' />
            More Filters
          </button>
        </div>
      </div>

      {/* Feature Flags Table */}
      <div className='bg-white rounded-lg shadow-sm border overflow-hidden'>
        <div className='overflow-x-auto'>
          <table className='min-w-full divide-y divide-gray-200'>
            <thead className='bg-gray-50'>
              <tr>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Feature Flag
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Environment
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Status
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Rollout
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Target Plans
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Updated
                </th>
                <th className='relative px-6 py-3'>
                  <span className='sr-only'>Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className='bg-white divide-y divide-gray-200'>
              {filteredFlags.map(flag => (
                <tr key={flag.id} className='hover:bg-gray-50'>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <div>
                      <div className='text-sm font-medium text-gray-900'>
                        {flag.name}
                      </div>
                      <div className='text-sm text-gray-500'>{flag.key}</div>
                      <div className='text-xs text-gray-400 mt-1 max-w-xs truncate'>
                        {flag.description}
                      </div>
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEnvironmentColor(
                        flag.environment
                      )}`}
                    >
                      {flag.environment.charAt(0).toUpperCase() +
                        flag.environment.slice(1)}
                    </span>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <div className='flex items-center'>
                      <button
                        onClick={() => toggleFlag(flag.id)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          flag.is_enabled ? 'bg-green-600' : 'bg-gray-200'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            flag.is_enabled ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                      <span
                        className={`ml-2 text-sm ${
                          flag.is_enabled ? 'text-green-600' : 'text-gray-500'
                        }`}
                      >
                        {flag.is_enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <div className='flex items-center'>
                      <div className='w-16 bg-gray-200 rounded-full h-2 mr-2'>
                        <div
                          className='bg-blue-600 h-2 rounded-full'
                          style={{ width: `${flag.rollout_percentage}%` }}
                        />
                      </div>
                      <span
                        className={`text-xs font-medium px-2 py-1 rounded ${getRolloutColor(
                          flag.rollout_percentage
                        )}`}
                      >
                        {flag.rollout_percentage}%
                      </span>
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <div className='flex flex-wrap gap-1'>
                      {flag.target_plans.length > 0 ? (
                        flag.target_plans.map(plan => (
                          <span
                            key={plan}
                            className='inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800'
                          >
                            {plan}
                          </span>
                        ))
                      ) : (
                        <span className='text-xs text-gray-500'>All plans</span>
                      )}
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                    <div className='flex items-center'>
                      <Calendar className='w-3 h-3 mr-1' />
                      {new Date(flag.updated_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                    <div className='flex items-center justify-end space-x-2'>
                      <button
                        className='text-blue-600 hover:text-blue-900'
                        title='View details'
                      >
                        <Eye className='w-4 h-4' />
                      </button>
                      <button
                        className='text-gray-600 hover:text-gray-900'
                        title='Edit flag'
                      >
                        <Edit className='w-4 h-4' />
                      </button>
                      <button
                        onClick={() => deleteFlag(flag.id)}
                        className='text-red-600 hover:text-red-900'
                        title='Delete flag'
                      >
                        <Trash2 className='w-4 h-4' />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredFlags.length === 0 && (
          <div className='text-center py-12'>
            <Flag className='w-12 h-12 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No feature flags found
            </h3>
            <p className='text-gray-600 mb-4'>
              {searchQuery ||
              filterEnvironment !== 'all' ||
              filterStatus !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Create your first feature flag to get started'}
            </p>
            {!searchQuery &&
              filterEnvironment === 'all' &&
              filterStatus === 'all' && (
                <button className='inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors'>
                  <Plus className='w-4 h-4 mr-2' />
                  Create Flag
                </button>
              )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FeatureFlagsPage;
