'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import {
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Pause,
  Play,
  Eye,
  Users,
  CreditCard,
  Calendar,
  Globe,
  AlertCircle,
  CheckCircle,
  Clock,
} from 'lucide-react';
// TODO: Replace with real API integration
interface MockTenant {
  id: string;
  name: string;
  domain: string;
  status: string;
  plan: string;
  theme: {
    primary: string;
    secondary: string;
  };
}

const SuperAdminTenantsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterPlan, setFilterPlan] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [tenants, setTenants] = useState<MockTenant[]>([
    // TODO: Replace with real API call
  ]);

  const plans = [
    { value: 'all', label: 'All Plans' },
    { value: 'basic', label: 'Basic' },
    { value: 'pro', label: 'Pro' },
    { value: 'enterprise', label: 'Enterprise' },
  ];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'trial', label: 'Trial' },
    { value: 'suspended', label: 'Suspended' },
  ];

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch =
      tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tenant.handle.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (tenant.domain &&
        tenant.domain.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesPlan =
      filterPlan === 'all' || tenant.subscription_plan === filterPlan;
    const matchesStatus =
      filterStatus === 'all' || tenant.status === filterStatus;

    return matchesSearch && matchesPlan && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'trial':
        return 'bg-blue-100 text-blue-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className='w-3 h-3' />;
      case 'trial':
        return <Clock className='w-3 h-3' />;
      case 'suspended':
        return <AlertCircle className='w-3 h-3' />;
      default:
        return <AlertCircle className='w-3 h-3' />;
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'basic':
        return 'bg-gray-100 text-gray-800';
      case 'pro':
        return 'bg-blue-100 text-blue-800';
      case 'enterprise':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const toggleTenantStatus = (tenantId: string) => {
    setTenants(tenants =>
      tenants.map(tenant =>
        tenant.id === tenantId
          ? {
              ...tenant,
              status: tenant.status === 'active' ? 'suspended' : 'active',
              updated_at: new Date().toISOString(),
            }
          : tenant
      )
    );
  };

  const deleteTenant = (tenantId: string) => {
    if (
      confirm(
        'Are you sure you want to delete this tenant? This action cannot be undone.'
      )
    ) {
      setTenants(tenants => tenants.filter(tenant => tenant.id !== tenantId));
    }
  };

  // Calculate summary stats
  const totalTenants = tenants.length;
  const activeTenants = tenants.filter(t => t.status === 'active').length;
  const trialTenants = tenants.filter(t => t.status === 'trial').length;
  const suspendedTenants = tenants.filter(t => t.status === 'suspended').length;

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>
            Tenant Management
          </h1>
          <p className='text-gray-600'>
            Manage all tenants, subscriptions, and billing
          </p>
        </div>
        <Link
          href='/super-admin/tenants/new'
          className='inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors'
        >
          <Plus className='w-4 h-4 mr-2' />
          Add Tenant
        </Link>
      </div>

      {/* Summary Cards */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center'>
              <Users className='w-4 h-4 text-blue-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Total Tenants</p>
              <p className='text-2xl font-bold text-gray-900'>{totalTenants}</p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center'>
              <CheckCircle className='w-4 h-4 text-green-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Active</p>
              <p className='text-2xl font-bold text-gray-900'>
                {activeTenants}
              </p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center'>
              <Clock className='w-4 h-4 text-blue-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Trial</p>
              <p className='text-2xl font-bold text-gray-900'>{trialTenants}</p>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <div className='flex items-center'>
            <div className='w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center'>
              <AlertCircle className='w-4 h-4 text-red-600' />
            </div>
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-600'>Suspended</p>
              <p className='text-2xl font-bold text-gray-900'>
                {suspendedTenants}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className='bg-white rounded-lg shadow-sm border p-6'>
        <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
            <input
              type='text'
              placeholder='Search tenants...'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            />
          </div>

          <select
            value={filterPlan}
            onChange={e => setFilterPlan(e.target.value)}
            className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
          >
            {plans.map(plan => (
              <option key={plan.value} value={plan.value}>
                {plan.label}
              </option>
            ))}
          </select>

          <select
            value={filterStatus}
            onChange={e => setFilterStatus(e.target.value)}
            className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
          >
            {statusOptions.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          <button className='flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors'>
            <Filter className='w-4 h-4 mr-2' />
            More Filters
          </button>
        </div>
      </div>

      {/* Tenants Table */}
      <div className='bg-white rounded-lg shadow-sm border overflow-hidden'>
        <div className='overflow-x-auto'>
          <table className='min-w-full divide-y divide-gray-200'>
            <thead className='bg-gray-50'>
              <tr>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Tenant
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Plan
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Status
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Domain
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Created
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Last Updated
                </th>
                <th className='relative px-6 py-3'>
                  <span className='sr-only'>Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className='bg-white divide-y divide-gray-200'>
              {filteredTenants.map(tenant => (
                <tr key={tenant.id} className='hover:bg-gray-50'>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <div className='flex items-center'>
                      <div
                        className='w-10 h-10 rounded-lg flex items-center justify-center text-white font-medium'
                        style={{ backgroundColor: tenant.theme.primary_color }}
                      >
                        {tenant.name[0]}
                      </div>
                      <div className='ml-4'>
                        <div className='text-sm font-medium text-gray-900'>
                          {tenant.name}
                        </div>
                        <div className='text-sm text-gray-500'>
                          @{tenant.handle}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPlanColor(
                        tenant.subscription_plan
                      )}`}
                    >
                      <CreditCard className='w-3 h-3 mr-1' />
                      {tenant.subscription_plan.charAt(0).toUpperCase() +
                        tenant.subscription_plan.slice(1)}
                    </span>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                        tenant.status
                      )}`}
                    >
                      {getStatusIcon(tenant.status)}
                      <span className='ml-1 capitalize'>{tenant.status}</span>
                    </span>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                    {tenant.domain ? (
                      <div className='flex items-center'>
                        <Globe className='w-3 h-3 mr-1' />
                        <a
                          href={`https://${tenant.domain}`}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-blue-600 hover:text-blue-800'
                        >
                          {tenant.domain}
                        </a>
                      </div>
                    ) : (
                      <span className='text-gray-400'>No domain</span>
                    )}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                    <div className='flex items-center'>
                      <Calendar className='w-3 h-3 mr-1' />
                      {new Date(tenant.created_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                    <div className='flex items-center'>
                      <Calendar className='w-3 h-3 mr-1' />
                      {new Date(tenant.updated_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                    <div className='flex items-center justify-end space-x-2'>
                      <Link
                        href={`/super-admin/tenants/${tenant.id}`}
                        className='text-blue-600 hover:text-blue-900'
                        title='View details'
                      >
                        <Eye className='w-4 h-4' />
                      </Link>
                      <Link
                        href={`/super-admin/tenants/${tenant.id}/edit`}
                        className='text-gray-600 hover:text-gray-900'
                        title='Edit tenant'
                      >
                        <Edit className='w-4 h-4' />
                      </Link>
                      <button
                        onClick={() => toggleTenantStatus(tenant.id)}
                        className={`${
                          tenant.status === 'active'
                            ? 'text-red-600 hover:text-red-900'
                            : 'text-green-600 hover:text-green-900'
                        }`}
                        title={
                          tenant.status === 'active' ? 'Suspend' : 'Activate'
                        }
                      >
                        {tenant.status === 'active' ? (
                          <Pause className='w-4 h-4' />
                        ) : (
                          <Play className='w-4 h-4' />
                        )}
                      </button>
                      <button
                        onClick={() => deleteTenant(tenant.id)}
                        className='text-red-600 hover:text-red-900'
                        title='Delete tenant'
                      >
                        <Trash2 className='w-4 h-4' />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredTenants.length === 0 && (
          <div className='text-center py-12'>
            <Users className='w-12 h-12 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No tenants found
            </h3>
            <p className='text-gray-600 mb-4'>
              {searchQuery || filterPlan !== 'all' || filterStatus !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first tenant'}
            </p>
            {!searchQuery && filterPlan === 'all' && filterStatus === 'all' && (
              <Link
                href='/super-admin/tenants/new'
                className='inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors'
              >
                <Plus className='w-4 h-4 mr-2' />
                Add Tenant
              </Link>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SuperAdminTenantsPage;
