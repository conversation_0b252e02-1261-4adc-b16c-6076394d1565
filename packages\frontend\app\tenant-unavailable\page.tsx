'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import {
  AlertTriangle,
  RefreshCw,
  Home,
  Mail,
  Clock,
  Shield,
  Globe,
} from 'lucide-react';

type ErrorType =
  | 'suspended'
  | 'not-found'
  | 'dns-error'
  | 'maintenance'
  | 'expired';

interface ErrorConfig {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  showRetry: boolean;
  showContact: boolean;
}

const TenantUnavailablePage: React.FC = () => {
  const searchParams = useSearchParams();
  const [errorType, setErrorType] = useState<ErrorType>('not-found');
  const [tenantName, setTenantName] = useState<string>('');
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    const type = (searchParams.get('type') as ErrorType) || 'not-found';
    const name = searchParams.get('tenant') || '';
    setErrorType(type);
    setTenantName(name);
  }, [searchParams]);

  const errorConfigs: Record<ErrorType, ErrorConfig> = {
    suspended: {
      title: 'Store Temporarily Suspended',
      description:
        'This store has been temporarily suspended. Please contact the store owner for more information.',
      icon: <Shield className='w-16 h-16' />,
      color: 'text-red-500',
      showRetry: false,
      showContact: true,
    },
    'not-found': {
      title: 'Store Not Found',
      description:
        "The store you're looking for doesn't exist or may have been moved.",
      icon: <Globe className='w-16 h-16' />,
      color: 'text-gray-500',
      showRetry: true,
      showContact: true,
    },
    'dns-error': {
      title: 'Connection Problem',
      description:
        "We're having trouble connecting to this store. This might be a temporary issue.",
      icon: <AlertTriangle className='w-16 h-16' />,
      color: 'text-yellow-500',
      showRetry: true,
      showContact: true,
    },
    maintenance: {
      title: 'Store Under Maintenance',
      description:
        'This store is currently undergoing maintenance. Please check back later.',
      icon: <Clock className='w-16 h-16' />,
      color: 'text-blue-500',
      showRetry: true,
      showContact: false,
    },
    expired: {
      title: 'Store Subscription Expired',
      description:
        "This store's subscription has expired. Please contact the store owner to renew.",
      icon: <AlertTriangle className='w-16 h-16' />,
      color: 'text-orange-500',
      showRetry: false,
      showContact: true,
    },
  };

  const config = errorConfigs[errorType];

  const handleRetry = async () => {
    setIsRetrying(true);

    // Simulate retry delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Reload the page
    window.location.reload();
  };

  const getStatusCode = () => {
    switch (errorType) {
      case 'not-found':
        return '404';
      case 'suspended':
      case 'expired':
        return '403';
      case 'maintenance':
        return '503';
      case 'dns-error':
        return '502';
      default:
        return '500';
    }
  };

  return (
    <div className='min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8'>
      <div className='sm:mx-auto sm:w-full sm:max-w-md'>
        <div className='bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10'>
          <div className='text-center'>
            {/* Error Icon */}
            <div className={`mx-auto mb-4 ${config.color}`}>{config.icon}</div>

            {/* Status Code */}
            <div className='text-6xl font-bold text-gray-300 mb-4'>
              {getStatusCode()}
            </div>

            {/* Error Title */}
            <h1 className='text-2xl font-bold text-gray-900 mb-2'>
              {config.title}
            </h1>

            {/* Tenant Name */}
            {tenantName && (
              <p className='text-sm text-gray-600 mb-4'>
                Store: <span className='font-medium'>{tenantName}</span>
              </p>
            )}

            {/* Error Description */}
            <p className='text-gray-600 mb-8'>{config.description}</p>

            {/* Action Buttons */}
            <div className='space-y-4'>
              {config.showRetry && (
                <button
                  onClick={handleRetry}
                  disabled={isRetrying}
                  className='w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed'
                >
                  {isRetrying ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                      Retrying...
                    </>
                  ) : (
                    <>
                      <RefreshCw className='w-4 h-4 mr-2' />
                      Try Again
                    </>
                  )}
                </button>
              )}

              <Link
                href='/'
                className='w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
              >
                <Home className='w-4 h-4 mr-2' />
                Go to Homepage
              </Link>

              {config.showContact && (
                <a
                  href='mailto:<EMAIL>'
                  className='w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                >
                  <Mail className='w-4 h-4 mr-2' />
                  Contact Support
                </a>
              )}
            </div>

            {/* Additional Information */}
            <div className='mt-8 pt-6 border-t border-gray-200'>
              <div className='text-xs text-gray-500 space-y-2'>
                <p>
                  <strong>Error Code:</strong> TENANT_{getStatusCode()}_
                  {errorType.toUpperCase()}
                </p>
                <p>
                  <strong>Time:</strong> {new Date().toLocaleString()}
                </p>
                {tenantName && (
                  <p>
                    <strong>Requested Store:</strong> {tenantName}
                  </p>
                )}
              </div>
            </div>

            {/* Help Text */}
            <div className='mt-6 text-xs text-gray-500'>
              <p>
                If you believe this is an error, please contact our support team
                with the error code above.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Context for Different Error Types */}
      {errorType === 'maintenance' && (
        <div className='sm:mx-auto sm:w-full sm:max-w-md mt-6'>
          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
            <div className='flex items-center'>
              <Clock className='w-5 h-5 text-blue-600 mr-2' />
              <div className='text-sm text-blue-800'>
                <p className='font-medium'>Scheduled Maintenance</p>
                <p>
                  We're working to improve your experience. This should only
                  take a few minutes.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {errorType === 'suspended' && (
        <div className='sm:mx-auto sm:w-full sm:max-w-md mt-6'>
          <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
            <div className='flex items-center'>
              <Shield className='w-5 h-5 text-red-600 mr-2' />
              <div className='text-sm text-red-800'>
                <p className='font-medium'>Account Suspended</p>
                <p>
                  This store has been temporarily suspended. The store owner
                  should contact support for assistance.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {errorType === 'expired' && (
        <div className='sm:mx-auto sm:w-full sm:max-w-md mt-6'>
          <div className='bg-orange-50 border border-orange-200 rounded-lg p-4'>
            <div className='flex items-center'>
              <AlertTriangle className='w-5 h-5 text-orange-600 mr-2' />
              <div className='text-sm text-orange-800'>
                <p className='font-medium'>Subscription Expired</p>
                <p>
                  This store's subscription has expired. The store owner needs
                  to renew their subscription to restore access.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TenantUnavailablePage;
