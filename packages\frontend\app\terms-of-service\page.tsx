import React from 'react';
import Breadcrumbs from '@/components/Breadcrumbs';
import PageHero from '@/components/ui/PageHero';
import ContentCard from '@/components/ui/ContentCard';
import StyledContent from '@/components/ui/StyledContent';

import { getPageBySlug, type Page } from '@/lib/strapi-api';

// Server-side function to fetch page content
async function getTermsOfServicePageContent(): Promise<Page | null> {
  try {
    console.log(
      'Server: Fetching terms-of-service page content from Strapi...'
    );
    const content = await getPageBySlug('terms-of-service');
    console.log('Server: Fetched terms-of-service content:', content?.title);
    return content;
  } catch (error) {
    console.error('Server: Error fetching terms-of-service page:', error);
    return null;
  }
}

export default async function TermsOfServicePage() {
  // Fetch content on the server side
  const pageContent = await getTermsOfServicePageContent();

  // Fallback content if Strapi content is not available
  const fallbackContent: Page = {
    id: 25,
    title: 'Terms of Service',
    slug: 'terms-of-service',
    content: `<h2>Terms of Service</h2>
<p><strong>Last updated:</strong> June 10, 2025</p>

<h3>1. Acceptance of Terms</h3>
<p>By accessing and using the ONDC Seller Platform, you accept and agree to be bound by the terms and provision of this agreement.</p>

<h3>2. Use License</h3>
<p>Permission is granted to temporarily download one copy of the materials on ONDC Seller Platform for personal, non-commercial transitory viewing only.</p>

<h3>3. Account Registration</h3>
<p>To access certain features of our Service, you must register for an account. You agree to provide accurate, current, and complete information during registration.</p>

<h3>4. User Responsibilities</h3>
<p>As a user of our platform, you agree to comply with all applicable laws and regulations, provide accurate product information, and maintain good customer service.</p>

<h3>5. Prohibited Activities</h3>
<p>You may not use our Service to violate any laws or regulations, infringe on intellectual property rights, or engage in fraudulent activities.</p>

<h3>6. Payment Terms</h3>
<p>All payments are processed securely through our payment partners. By making a purchase, you agree to pay all charges incurred.</p>

<h3>7. Disclaimer</h3>
<p>The materials on ONDC Seller Platform are provided on an 'as is' basis. ONDC Seller Platform makes no warranties, expressed or implied.</p>

<h3>8. Contact Information</h3>
<p>If you have any questions about these Terms of Service, please contact <NAME_EMAIL> or +91 **********.</p>`,
    excerpt:
      'Read our terms of service to understand the rules and regulations for using the ONDC Seller Platform.',
    metaTitle: 'Terms of Service - ONDC Seller Platform',
    metaDescription:
      'Review the terms of service for using ONDC Seller Platform. Understand your rights and responsibilities as a user.',
    status: 'published',
    template: 'default',
    featured: false,
    author: 'Admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // Use Strapi content if available, otherwise use fallback
  const displayContent = pageContent || fallbackContent;

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Hero Section */}
      <PageHero
        title={displayContent.title}
        description={
          displayContent.excerpt ||
          'Review the terms of service for using ONDC Seller Platform. Understand your rights and responsibilities.'
        }
        icon={
          <svg
            className='w-12 h-12 text-white'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
            />
          </svg>
        }
        gradient='indigo'
      />

      {/* Main Content */}
      <div className='px-4 sm:px-6 lg:px-8 py-12'>
        {/* Breadcrumbs */}
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            {
              label: 'Terms of Service',
              href: '/terms-of-service',
              active: true,
            },
          ]}
        />

        <div className='max-w-4xl mx-auto'>
          {/* Main Content */}
          <ContentCard variant='elevated' padding='xl'>
            <StyledContent content={displayContent.content} />
          </ContentCard>
        </div>
      </div>
    </div>
  );
}
