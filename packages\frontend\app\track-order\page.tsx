'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  TextField,
  Alert,
  AlertTitle,
  CircularProgress,
  Chip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Typography,
  Box,
  Grid,
  Paper,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  LocalShipping as ShippingIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../../theme/mui-theme';
import Breadcrumbs from '@/components/Breadcrumbs';
import { trackingAPI } from '@/lib/api/tracking';
import {
  TrackingSearchSchema,
  TrackingSearchFormData,
  TrackingInfo,
  OrderTrackingStatus,
} from '@/lib/types/tracking';

const TrackOrderPage = () => {
  const searchParams = useSearchParams();
  const [trackingResult, setTrackingResult] = useState<TrackingInfo | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form setup with Zod validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<TrackingSearchFormData>({
    resolver: zodResolver(TrackingSearchSchema),
    defaultValues: {
      order_number: '',
      email: '',
      phone: '',
      tracking_number: '',
    },
  });

  // Pre-fill form from URL params
  useEffect(() => {
    const orderNumber = searchParams.get('order_number');
    const email = searchParams.get('email');
    const trackingNumber = searchParams.get('tracking_number');

    if (orderNumber) setValue('order_number', orderNumber);
    if (email) setValue('email', email);
    if (trackingNumber) setValue('tracking_number', trackingNumber);

    // Auto-search if params are provided
    if (orderNumber || trackingNumber) {
      handleSubmit(onSubmit)();
    }
  }, [searchParams, setValue, handleSubmit]);

  // Handle form submission
  const onSubmit = async (data: TrackingSearchFormData) => {
    setIsLoading(true);
    setError(null);
    setTrackingResult(null);

    try {
      console.log('[TrackOrder] Searching with data:', data);

      // Call real API
      const response = await trackingAPI.searchOrder(data);

      if (response.success && response.data) {
        setTrackingResult(response.data);
        console.log('[TrackOrder] Successfully loaded tracking data');
      } else {
        // Fallback to mock data for development
        console.warn(
          '[TrackOrder] API call failed, using mock data:',
          response.error
        );
        setTrackingResult(getMockTrackingData(data));
      }
    } catch (apiError) {
      console.error('[TrackOrder] API error:', apiError);
      // Fallback to mock data
      setTrackingResult(getMockTrackingData(data));
    } finally {
      setIsLoading(false);
    }
  };

  // Mock data fallback for development
  const getMockTrackingData = (
    searchData: TrackingSearchFormData
  ): TrackingInfo => ({
    id: 'track_123456',
    order_id: 'order_123456',
    order_number: searchData.order_number || 'ONDC-2025-001234',
    tracking_number: searchData.tracking_number || 'TRK123456789',
    current_status: 'in_transit',
    estimated_delivery: '2025-01-25T18:00:00Z',
    carrier: {
      id: 'carrier_1',
      name: 'Express Logistics',
      code: 'EXP',
      tracking_url: 'https://express.com/track',
      contact_info: {
        phone: '+91-1800-123-456',
        email: '<EMAIL>',
      },
    },
    tracking_steps: [
      {
        id: 'step_1',
        status: 'confirmed',
        date: '2025-01-20',
        time: '10:30 AM',
        description: 'Order confirmed and payment received',
        completed: true,
        location: 'Mumbai, Maharashtra',
      },
      {
        id: 'step_2',
        status: 'processing',
        date: '2025-01-21',
        time: '02:45 PM',
        description: 'Order is being prepared for shipment',
        completed: true,
        location: 'Mumbai Warehouse',
      },
      {
        id: 'step_3',
        status: 'shipped',
        date: '2025-01-22',
        time: '09:20 AM',
        description: 'Package has been shipped',
        completed: true,
        location: 'Mumbai Distribution Center',
        carrier: 'Express Logistics',
        tracking_number: 'TRK123456789',
      },
      {
        id: 'step_4',
        status: 'in_transit',
        date: '2025-01-23',
        time: '08:00 AM',
        description: 'Package is in transit to destination',
        completed: true,
        location: 'Delhi Hub',
      },
      {
        id: 'step_5',
        status: 'out_for_delivery',
        date: '2025-01-25',
        time: 'Expected by 6:00 PM',
        description: 'Package will be out for delivery',
        completed: false,
        estimated: true,
      },
    ],
    shipping_address: {
      first_name: 'John',
      last_name: 'Doe',
      address_1: '123 Main Street, Apartment 4B',
      city: 'Delhi',
      province: 'Delhi',
      postal_code: '110001',
      country_code: 'IN',
      phone: '+91-9876543210',
      email: searchData.email || '<EMAIL>',
    },
    tenant_id: 'tenant_1',
    created_at: '2025-01-20T10:30:00Z',
    updated_at: '2025-01-23T08:00:00Z',
  });

  // Status color mapping
  const getStatusColor = (
    status: OrderTrackingStatus
  ):
    | 'default'
    | 'primary'
    | 'secondary'
    | 'error'
    | 'info'
    | 'success'
    | 'warning' => {
    switch (status) {
      case 'delivered':
        return 'success';
      case 'shipped':
      case 'in_transit':
      case 'out_for_delivery':
        return 'primary';
      case 'processing':
      case 'packed':
        return 'info';
      case 'cancelled':
      case 'failed_delivery':
        return 'error';
      case 'returned_to_sender':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: OrderTrackingStatus, completed: boolean) => {
    if (completed) {
      return <CheckCircleIcon color='success' />;
    }
    switch (status) {
      case 'shipped':
      case 'in_transit':
      case 'out_for_delivery':
        return <ShippingIcon color='primary' />;
      default:
        return <ScheduleIcon color='disabled' />;
    }
  };

  return (
    <ThemeProvider theme={muiTheme}>
      <div className='min-h-screen bg-gray-50'>
        {/* Hero Section */}
        <Box
          sx={{
            background: 'linear-gradient(135deg, #1976d2 0%, #9c27b0 100%)',
            color: 'white',
            py: 8,
          }}
        >
          <Box
            sx={{ maxWidth: '1200px', mx: 'auto', px: 3, textAlign: 'center' }}
          >
            <Typography
              variant='h2'
              component='h1'
              sx={{ fontWeight: 'bold', mb: 2 }}
            >
              Track Your Order
            </Typography>
            <Typography
              variant='h6'
              sx={{ opacity: 0.9, maxWidth: '600px', mx: 'auto' }}
            >
              Enter your order details to get real-time tracking information
            </Typography>
          </Box>
        </Box>

        {/* Main Content */}
        <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 3, py: 6 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            items={[
              { label: 'Home', href: '/' },
              { label: 'Track Order', href: '/track-order', active: true },
            ]}
          />

          {/* Search Form */}
          <Card sx={{ mb: 4 }}>
            <CardHeader>
              <CardTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SearchIcon />
                  Enter Order Details
                </Box>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {error && (
                <Alert severity='error' sx={{ mb: 3 }}>
                  <AlertTitle>Error</AlertTitle>
                  {error}
                </Alert>
              )}

              <Box
                component='form'
                onSubmit={handleSubmit(onSubmit)}
                sx={{ mt: 2 }}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      {...register('order_number')}
                      label='Order Number'
                      placeholder='e.g., ONDC-2025-001234'
                      fullWidth
                      error={!!errors.order_number}
                      helperText={errors.order_number?.message}
                      InputProps={{
                        startAdornment: (
                          <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      {...register('email')}
                      label='Email Address'
                      type='email'
                      placeholder='<EMAIL>'
                      fullWidth
                      error={!!errors.email}
                      helperText={errors.email?.message}
                      InputProps={{
                        startAdornment: (
                          <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      {...register('phone')}
                      label='Phone Number'
                      placeholder='+91-9876543210'
                      fullWidth
                      error={!!errors.phone}
                      helperText={errors.phone?.message}
                      InputProps={{
                        startAdornment: (
                          <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      {...register('tracking_number')}
                      label='Tracking Number'
                      placeholder='TRK123456789'
                      fullWidth
                      error={!!errors.tracking_number}
                      helperText={errors.tracking_number?.message}
                      InputProps={{
                        startAdornment: (
                          <ShippingIcon
                            sx={{ mr: 1, color: 'text.secondary' }}
                          />
                        ),
                      }}
                    />
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Button
                    type='submit'
                    variant='contained'
                    size='large'
                    disabled={isLoading}
                    startIcon={
                      isLoading ? (
                        <CircularProgress size={20} />
                      ) : (
                        <SearchIcon />
                      )
                    }
                    sx={{
                      minWidth: 200,
                      background:
                        'linear-gradient(135deg, #1976d2 0%, #9c27b0 100%)',
                      '&:hover': {
                        background:
                          'linear-gradient(135deg, #1565c0 0%, #8e24aa 100%)',
                      },
                    }}
                  >
                    {isLoading ? 'Searching...' : 'Track Order'}
                  </Button>
                </Box>

                <Typography
                  variant='body2'
                  color='text.secondary'
                  sx={{ mt: 2, textAlign: 'center' }}
                >
                  Enter at least one field to search for your order
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* Tracking Results */}
          {trackingResult && (
            <Box sx={{ space: 3 }}>
              {/* Order Summary */}
              <Card sx={{ mb: 3 }}>
                <CardHeader>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      flexWrap: 'wrap',
                      gap: 2,
                    }}
                  >
                    <Box>
                      <Typography
                        variant='h4'
                        component='h2'
                        sx={{ fontWeight: 'bold', mb: 1 }}
                      >
                        Order #{trackingResult.order_number}
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          mb: 1,
                        }}
                      >
                        <Typography variant='body1' color='text.secondary'>
                          Status:
                        </Typography>
                        <Chip
                          label={trackingResult.current_status
                            .replace('_', ' ')
                            .toUpperCase()}
                          color={getStatusColor(trackingResult.current_status)}
                          variant='filled'
                        />
                      </Box>
                      {trackingResult.tracking_number && (
                        <Typography variant='body2' color='text.secondary'>
                          Tracking: {trackingResult.tracking_number}
                        </Typography>
                      )}
                    </Box>
                    <Box sx={{ textAlign: { xs: 'left', md: 'right' } }}>
                      <Typography variant='body2' color='text.secondary'>
                        Estimated Delivery
                      </Typography>
                      <Typography variant='h6' sx={{ fontWeight: 'bold' }}>
                        {new Date(
                          trackingResult.estimated_delivery
                        ).toLocaleDateString('en-IN', {
                          weekday: 'short',
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                        })}
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        by{' '}
                        {new Date(
                          trackingResult.estimated_delivery
                        ).toLocaleTimeString('en-IN', {
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </Typography>
                    </Box>
                  </Box>
                </CardHeader>
                <CardContent>
                  {/* Carrier Information */}
                  {trackingResult.carrier && (
                    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
                      <Typography
                        variant='subtitle1'
                        sx={{ fontWeight: 'bold', mb: 1 }}
                      >
                        Shipping Carrier
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          flexWrap: 'wrap',
                          gap: 2,
                        }}
                      >
                        <Box>
                          <Typography
                            variant='body1'
                            sx={{ fontWeight: 'medium' }}
                          >
                            {trackingResult.carrier.name}
                          </Typography>
                          {trackingResult.carrier.contact_info?.phone && (
                            <Typography variant='body2' color='text.secondary'>
                              📞 {trackingResult.carrier.contact_info.phone}
                            </Typography>
                          )}
                        </Box>
                        {trackingResult.carrier.tracking_url && (
                          <Button
                            variant='outlined'
                            size='small'
                            href={`${trackingResult.carrier.tracking_url}?tracking=${trackingResult.tracking_number}`}
                            target='_blank'
                            rel='noopener noreferrer'
                          >
                            Track on Carrier Site
                          </Button>
                        )}
                      </Box>
                    </Paper>
                  )}

                  {/* Progress Indicator */}
                  <Box sx={{ mb: 4 }}>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 2,
                      }}
                    >
                      <Typography
                        variant='subtitle1'
                        sx={{ fontWeight: 'bold' }}
                      >
                        Order Progress
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        {
                          trackingResult.tracking_steps.filter(
                            step => step.completed
                          ).length
                        }{' '}
                        of {trackingResult.tracking_steps.length} steps
                        completed
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        width: '100%',
                        height: 8,
                        bgcolor: 'grey.200',
                        borderRadius: 1,
                        overflow: 'hidden',
                      }}
                    >
                      <Box
                        sx={{
                          width: `${
                            (trackingResult.tracking_steps.filter(
                              step => step.completed
                            ).length /
                              trackingResult.tracking_steps.length) *
                            100
                          }%`,
                          height: '100%',
                          background:
                            'linear-gradient(90deg, #1976d2 0%, #9c27b0 100%)',
                          transition: 'width 0.5s ease-in-out',
                        }}
                      />
                    </Box>
                  </Box>

                  {/* Tracking Timeline */}
                  <Stepper orientation='vertical' sx={{ mt: 2 }}>
                    {trackingResult.tracking_steps.map((step, index) => (
                      <Step
                        key={step.id}
                        active={step.completed}
                        completed={step.completed}
                      >
                        <StepLabel
                          StepIconComponent={() =>
                            getStatusIcon(step.status, step.completed)
                          }
                          sx={{
                            '& .MuiStepLabel-label': {
                              fontWeight: step.completed ? 'bold' : 'normal',
                              color: step.completed
                                ? 'text.primary'
                                : 'text.secondary',
                            },
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'flex-start',
                              width: '100%',
                            }}
                          >
                            <Box>
                              <Typography
                                variant='subtitle1'
                                sx={{
                                  fontWeight: step.completed
                                    ? 'bold'
                                    : 'normal',
                                  textTransform: 'capitalize',
                                }}
                              >
                                {step.status.replace('_', ' ')}
                              </Typography>
                              <Typography
                                variant='body2'
                                color='text.secondary'
                                sx={{ mt: 0.5 }}
                              >
                                {step.description}
                              </Typography>
                              {step.location && (
                                <Box
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5,
                                    mt: 0.5,
                                  }}
                                >
                                  <LocationIcon
                                    sx={{
                                      fontSize: 16,
                                      color: 'text.secondary',
                                    }}
                                  />
                                  <Typography
                                    variant='caption'
                                    color='text.secondary'
                                  >
                                    {step.location}
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                            <Box sx={{ textAlign: 'right', ml: 2 }}>
                              <Typography
                                variant='body2'
                                color={
                                  step.completed
                                    ? 'text.primary'
                                    : 'text.secondary'
                                }
                              >
                                {step.date}
                              </Typography>
                              <Typography
                                variant='caption'
                                color='text.secondary'
                              >
                                {step.estimated ? 'Expected' : ''} {step.time}
                              </Typography>
                            </Box>
                          </Box>
                        </StepLabel>
                        <StepContent>
                          {step.carrier && (
                            <Typography
                              variant='caption'
                              color='text.secondary'
                            >
                              Carrier: {step.carrier}
                            </Typography>
                          )}
                          {step.tracking_number && (
                            <Typography
                              variant='caption'
                              color='text.secondary'
                              sx={{ display: 'block' }}
                            >
                              Tracking: {step.tracking_number}
                            </Typography>
                          )}
                        </StepContent>
                      </Step>
                    ))}
                  </Stepper>
                </CardContent>
              </Card>

              {/* Shipping Address */}
              <Card>
                <CardHeader>
                  <CardTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocationIcon />
                      Shipping Address
                    </Box>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography
                      variant='subtitle1'
                      sx={{ fontWeight: 'bold', mb: 1 }}
                    >
                      {trackingResult.shipping_address.first_name}{' '}
                      {trackingResult.shipping_address.last_name}
                    </Typography>
                    {trackingResult.shipping_address.company && (
                      <Typography
                        variant='body2'
                        color='text.secondary'
                        sx={{ mb: 1 }}
                      >
                        {trackingResult.shipping_address.company}
                      </Typography>
                    )}
                    <Typography variant='body2' color='text.secondary'>
                      {trackingResult.shipping_address.address_1}
                    </Typography>
                    {trackingResult.shipping_address.address_2 && (
                      <Typography variant='body2' color='text.secondary'>
                        {trackingResult.shipping_address.address_2}
                      </Typography>
                    )}
                    <Typography variant='body2' color='text.secondary'>
                      {trackingResult.shipping_address.city},{' '}
                      {trackingResult.shipping_address.province}{' '}
                      {trackingResult.shipping_address.postal_code}
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      {trackingResult.shipping_address.country_code}
                    </Typography>
                    {trackingResult.shipping_address.phone && (
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                          mt: 1,
                        }}
                      >
                        <PhoneIcon sx={{ fontSize: 16 }} />
                        <Typography variant='body2'>
                          {trackingResult.shipping_address.phone}
                        </Typography>
                      </Box>
                    )}
                    {trackingResult.shipping_address.email && (
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                          mt: 0.5,
                        }}
                      >
                        <EmailIcon sx={{ fontSize: 16 }} />
                        <Typography variant='body2'>
                          {trackingResult.shipping_address.email}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Box>
          )}
        </Box>
      </div>
    </ThemeProvider>
  );
};

export default TrackOrderPage;
