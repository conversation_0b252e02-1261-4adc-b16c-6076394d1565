'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';

const WishlistPage = () => {
  const [wishlistItems, setWishlistItems] = useState([
    {
      id: 1,
      name: 'Wireless Bluetooth Headphones',
      price: 2999,
      originalPrice: 3999,
      image: '/api/placeholder/300/300',
      rating: 4.5,
      reviews: 128,
      inStock: true,
      seller: 'TechStore India',
    },
    {
      id: 2,
      name: 'Smart Fitness Watch',
      price: 5999,
      originalPrice: 7999,
      image: '/api/placeholder/300/300',
      rating: 4.3,
      reviews: 89,
      inStock: true,
      seller: 'FitTech Solutions',
    },
    {
      id: 3,
      name: 'Organic Cotton T-Shirt',
      price: 899,
      originalPrice: 1299,
      image: '/api/placeholder/300/300',
      rating: 4.7,
      reviews: 234,
      inStock: false,
      seller: 'EcoFashion',
    },
    {
      id: 4,
      name: 'Portable Phone Charger',
      price: 1499,
      originalPrice: 1999,
      image: '/api/placeholder/300/300',
      rating: 4.4,
      reviews: 156,
      inStock: true,
      seller: 'PowerTech',
    },
  ]);

  const removeFromWishlist = (itemId: number) => {
    setWishlistItems(items => items.filter(item => item.id !== itemId));
  };

  const moveToCart = (itemId: number) => {
    // Simulate moving to cart
    console.log(`Moving item ${itemId} to cart`);
    // You would typically call an API here
  };

  const calculateDiscount = (original: number, current: number) => {
    return Math.round(((original - current) / original) * 100);
  };

  if (wishlistItems.length === 0) {
    return (
      <div className='min-h-screen bg-gray-50'>
        {/* Header */}
        <div>
          <h1 className='text-4xl md:text-5xl font-bold mb-4'>My Wishlist</h1>
          <p className='text-xl text-blue-100'>Save items you love for later</p>
        </div>
        {/* <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
          <div className="w-full px-4 py-16">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">My Wishlist</h1>
              <p className="text-xl text-blue-100">Save items you love for later</p>
            </div>
          </div>
        </div> */}

        {/* Empty State */}
        <div className='w-full px-4 py-16'>
          <div className='max-w-2xl mx-auto text-center'>
            <div className='w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-8'>
              <svg
                className='w-16 h-16 text-gray-400'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={1.5}
                  d='M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'
                />
              </svg>
            </div>
            <h2 className='text-2xl font-bold text-gray-900 mb-4'>
              Your wishlist is empty
            </h2>
            <p className='text-gray-600 mb-8'>
              Start adding items to your wishlist by clicking the heart icon on
              products you love.
            </p>
            <Link
              href='/products'
              className='inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200'
            >
              Start Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header */}
      <div className='p-5'>
        <h1 className='text-4xl font-bold mb-4'>My Wishlist</h1>
        <p className='text-xl '>
          {wishlistItems.length} {wishlistItems.length === 1 ? 'item' : 'items'}{' '}
          saved for later
        </p>
      </div>
      {/* <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="w-full px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">My Wishlist</h1>
            <p className="text-xl text-blue-100">
              {wishlistItems.length} {wishlistItems.length === 1 ? 'item' : 'items'} saved for later
            </p>
          </div>
        </div>
      </div> */}

      {/* Wishlist Items */}
      <div className='w-full px-4 py-12'>
        <div className='max-w-6xl mx-auto'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
            {wishlistItems.map(item => (
              <div
                key={item.id}
                className='bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden group hover:shadow-md transition-shadow duration-200 h-96 flex flex-col'
              >
                {/* Product Image */}
                <div className='relative flex-shrink-0'>
                  <img
                    src={item.image}
                    alt={item.name}
                    className='w-full h-48 object-cover'
                  />
                  {!item.inStock && (
                    <div className='absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center'>
                      <span className='text-white font-semibold'>
                        Out of Stock
                      </span>
                    </div>
                  )}
                  <button
                    onClick={() => removeFromWishlist(item.id)}
                    className='absolute top-2 right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-red-50 transition-colors duration-200'
                  >
                    <svg
                      className='w-4 h-4 text-red-500'
                      fill='currentColor'
                      viewBox='0 0 20 20'
                    >
                      <path
                        fillRule='evenodd'
                        d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
                        clipRule='evenodd'
                      />
                    </svg>
                  </button>
                  {item.originalPrice > item.price && (
                    <div className='absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded'>
                      {calculateDiscount(item.originalPrice, item.price)}% OFF
                    </div>
                  )}
                </div>

                {/* Product Info */}
                <div className='p-4 flex-1 flex flex-col'>
                  <h3
                    className='font-semibold text-gray-900 mb-2 truncate'
                    title={item.name}
                  >
                    {item.name}
                  </h3>

                  {/* Rating */}
                  <div className='flex items-center mb-2'>
                    <div className='flex items-center'>
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-4 h-4 ${
                            i < Math.floor(item.rating)
                              ? 'text-yellow-400'
                              : 'text-gray-300'
                          }`}
                          fill='currentColor'
                          viewBox='0 0 20 20'
                        >
                          <path d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' />
                        </svg>
                      ))}
                    </div>
                    <span className='text-sm text-gray-600 ml-2'>
                      ({item.reviews})
                    </span>
                  </div>

                  {/* Seller */}
                  <p className='text-sm text-gray-600 mb-2'>by {item.seller}</p>

                  {/* Price */}
                  <div className='flex items-center mb-4'>
                    <span className='text-lg font-bold text-gray-900'>
                      ₹{item.price.toLocaleString()}
                    </span>
                    {item.originalPrice > item.price && (
                      <span className='text-sm text-gray-500 line-through ml-2'>
                        ₹{item.originalPrice.toLocaleString()}
                      </span>
                    )}
                  </div>

                  {/* Actions */}
                  <div className='space-y-2 mt-auto'>
                    <button
                      onClick={() => moveToCart(item.id)}
                      disabled={!item.inStock}
                      className={`w-full py-2 px-4 rounded-lg font-medium transition-all duration-200 ${
                        item.inStock
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700'
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      {item.inStock ? 'Add to Cart' : 'Out of Stock'}
                    </button>
                    <Link
                      href={`/products/${item.id}`}
                      className='block w-full py-2 px-4 border border-gray-300 text-gray-700 rounded-lg font-medium text-center hover:bg-gray-50 transition-colors duration-200'
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Continue Shopping */}
          <div className='text-center mt-12'>
            <Link
              href='/products'
              className='inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200'
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WishlistPage;
