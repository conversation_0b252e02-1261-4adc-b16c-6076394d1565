'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import AdminLayout from '@/components/layouts/AdminLayout';
import CustomerLayout from '@/components/layouts/CustomerLayout';
import AuthLayout from '@/components/layouts/AuthLayout';

interface LayoutWrapperProps {
  children: React.ReactNode;
}

export default function LayoutWrapper({ children }: LayoutWrapperProps) {
  const pathname = usePathname();

  // Determine which layout to use based on the current path
  const getLayout = () => {
    // API Demo routes (no layout wrapper)
    if (pathname.startsWith('/api-demo')) {
      return children;
    }

    // Onboarding routes (standalone interface)
    if (pathname.startsWith('/onboarding')) {
      return children; // No layout wrapper for onboarding
    }

    // Admin routes
    if (pathname.startsWith('/admin')) {
      return <AdminLayout>{children}</AdminLayout>;
    }

    // Auth routes (login, register, etc.)
    if (
      pathname.startsWith('/auth') ||
      pathname === '/login' ||
      pathname === '/register' ||
      pathname === '/forgot-password'
    ) {
      return <AuthLayout>{children}</AuthLayout>;
    }

    // Customer/Store routes (default)
    return <CustomerLayout>{children}</CustomerLayout>;
  };

  return <div className='min-h-screen'>{getLayout()}</div>;
}
