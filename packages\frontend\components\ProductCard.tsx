/**
 * ProductCard Component
 * Displays product information in a card format
 */

import React, { useRef } from 'react';

export interface Product {
  id: string;
  title: string;
  description: string;
  status: string;
  price?: number;
  image?: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

export interface ProductCardProps {
  product: Product;
  onEdit?: (product: Product) => void;
  onDelete?: (productId: string) => void;
  onView?: (product: Product) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onEdit,
  onDelete,
  onView,
}) => {
  const lastClickTime = useRef<{ [key: string]: number }>({});

  const debounceClick = (key: string, callback: () => void, delay = 100) => {
    const now = Date.now();
    const lastClick = lastClickTime.current[key] || 0;

    if (now - lastClick > delay) {
      lastClickTime.current[key] = now;
      callback();
    }
  };

  const handleEdit = () => {
    debounceClick('edit', () => {
      try {
        onEdit?.(product.id);
      } catch (error) {
        console.error('Error in onEdit callback:', error);
      }
    });
  };

  const handleDelete = () => {
    debounceClick('delete', () => {
      try {
        onDelete?.(product.id);
      } catch (error) {
        console.error('Error in onDelete callback:', error);
      }
    });
  };

  const handleView = () => {
    debounceClick('view', () => {
      try {
        onView?.(product.id);
      } catch (error) {
        console.error('Error in onView callback:', error);
      }
    });
  };

  const handleKeyDown = (event: React.KeyboardEvent, action: () => void) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      action();
    }
  };

  return (
    <div
      className='product-card border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow'
      data-testid='product-card'
    >
      {product.image && (
        <img
          src={product.image}
          alt={product.title}
          className='w-full h-48 object-cover rounded-md mb-3'
        />
      )}

      <div className='product-content'>
        <h3
          className='text-lg font-semibold mb-2 text-gray-900'
          data-testid='product-title'
        >
          {product.title}
        </h3>

        <p
          className='text-gray-600 text-sm mb-3 line-clamp-2'
          data-testid='product-description'
        >
          {product.description}
        </p>

        <div className='flex items-center justify-between mb-3'>
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              product.status === 'published'
                ? 'bg-green-100 text-green-800'
                : 'bg-yellow-100 text-yellow-800'
            }`}
            data-testid='product-status'
          >
            {product.status}
          </span>

          {product.price && (
            <span
              className='text-lg font-bold text-gray-900'
              data-testid='product-price'
            >
              ₹{product.price}
            </span>
          )}
        </div>

        <div className='text-xs text-gray-500 mb-3'>
          <span data-testid='product-tenant'>Tenant: {product.tenant_id}</span>
        </div>

        <div className='flex gap-2'>
          {onView && (
            <button
              onClick={handleView}
              onKeyDown={e => handleKeyDown(e, handleView)}
              className='flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors'
              data-testid='view-button'
            >
              View
            </button>
          )}

          {onEdit && (
            <button
              onClick={handleEdit}
              onKeyDown={e => handleKeyDown(e, handleEdit)}
              className='flex-1 px-3 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors'
              data-testid='edit-button'
            >
              Edit
            </button>
          )}

          {onDelete && (
            <button
              onClick={handleDelete}
              onKeyDown={e => handleKeyDown(e, handleDelete)}
              className='flex-1 px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors'
              data-testid='delete-button'
            >
              Delete
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
