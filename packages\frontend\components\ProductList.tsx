'use client';

import React, { useState, useEffect } from 'react';
import { useChannelAPI, useChannel } from '../contexts/ChannelContext';

interface Product {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  variants?: Array<{
    id: string;
    title: string;
    prices?: Array<{
      amount: number;
      currency_code: string;
    }>;
  }>;
}

interface ProductListProps {
  limit?: number;
  searchQuery?: string;
  categoryId?: string;
}

export function ProductList({ limit = 20, searchQuery, categoryId }: ProductListProps) {
  const { currentChannel } = useChannel();
  const { getProducts } = useChannelAPI();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log(`🛍️ Fetching products for channel: ${currentChannel.name}`);

        const params: Record<string, any> = { limit };
        if (searchQuery) params.q = searchQuery;
        if (categoryId) params.category_id = categoryId;

        const response = await getProducts(params);
        setProducts(response.products || []);

        console.log(`✅ Loaded ${response.products?.length || 0} products from ${currentChannel.name}`);
      } catch (err) {
        console.error('❌ Error fetching products:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch products');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentChannel.id, limit, searchQuery, categoryId, getProducts]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium">Error Loading Products</h3>
          <p className="text-sm text-gray-500 mt-2">{error}</p>
        </div>
        <button
          onClick={() => window.location.reload()}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-2.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7" />
          </svg>
          <h3 className="text-lg font-medium">No Products Found</h3>
          <p className="text-sm mt-2">
            No products are available in the <strong>{currentChannel.name}</strong> channel.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Channel indicator */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg border-l-4" style={{ borderLeftColor: currentChannel.theme?.primaryColor }}>
        <div className="flex items-center">
          {currentChannel.logo && (
            <img 
              src={currentChannel.logo} 
              alt={currentChannel.name}
              className="w-8 h-8 mr-3 rounded"
            />
          )}
          <div>
            <h2 className="font-medium text-gray-900">
              Showing products from <span style={{ color: currentChannel.theme?.primaryColor }}>{currentChannel.name}</span>
            </h2>
            <p className="text-sm text-gray-600">{currentChannel.description}</p>
          </div>
        </div>
      </div>

      {/* Products grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="aspect-square bg-gray-100">
              {product.thumbnail ? (
                <img
                  src={product.thumbnail}
                  alt={product.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              )}
            </div>
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">{product.title}</h3>
              {product.description && (
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.description}</p>
              )}
              {product.variants && product.variants.length > 0 && product.variants[0].prices && (
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold" style={{ color: currentChannel.theme?.primaryColor }}>
                    ₹{product.variants[0].prices[0]?.amount || 0}
                  </span>
                  <button
                    className="px-3 py-1 text-sm rounded-md text-white hover:opacity-90 transition-opacity"
                    style={{ backgroundColor: currentChannel.theme?.primaryColor }}
                  >
                    Add to Cart
                  </button>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
