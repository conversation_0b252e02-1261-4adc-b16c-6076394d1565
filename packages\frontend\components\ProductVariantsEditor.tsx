import React from 'react';
import {
  Card,
  IconButton,
  Stack,
  Typography,
  Grid,
  TextField,
  Button,
  Divider,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

export interface VariantRow {
  id: string;
  title: string;
  sku: string;
  weight: number | string;
  width: number | string;
  length: number | string;
  height: number | string;
  originalPrice: number | string;
  salePrice: number | string;
  variantQuantity: number | string;
  inventoryStatus: 'in_stock' | 'out_of_stock';
  material?: string;
  manualSku?: boolean; // internal: was SKU manually edited?
}

function slugify(str: string) {
  return str
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)+/g, '');
}

interface ProductVariantsEditorProps {
  variants: VariantRow[];
  onChange: (variants: VariantRow[]) => void;
}

export const ProductVariantsEditor: React.FC<ProductVariantsEditorProps> = ({
  variants,
  onChange,
}) => {
  // Add a new empty variant
  const handleAddVariant = () => {
    const newVariant: VariantRow = {
      id: crypto.randomUUID(),
      title: '',
      sku: '',
      weight: '',
      width: '',
      length: '',
      height: '',
      originalPrice: '',
      salePrice: '',
      variantQuantity: '',
      inventoryStatus: 'in_stock',
      material: '',
      manualSku: false,
    };
    onChange([...variants, newVariant]);
  };

  // Change a field on a variant
  const handleVariantChange = (
    id: string,
    field: keyof VariantRow,
    value: string | number
  ) => {
    onChange(
      variants.map(v => {
        if (v.id !== id) return v;

        // Auto-fill SKU when editing title unless SKU has been edited manually
        if (field === 'title' && !v.manualSku) {
          return {
            ...v,
            title: value,
            sku: slugify(String(value)),
          };
        }

        // If SKU is being edited, set manualSku flag
        if (field === 'sku') {
          return {
            ...v,
            sku: value,
            manualSku: true,
          };
        }

        return { ...v, [field]: value };
      })
    );
  };

  // Remove a variant
  const handleDeleteVariant = (id: string) => {
    onChange(variants.filter(v => v.id !== id));
  };

  return (
    <Card sx={{ mt: 2, mb: 4, p: 3, borderRadius: 3, boxShadow: 4 }} size={12}>
      <Box
        display='flex'
        alignItems='center'
        justifyContent='space-between'
        mb={2}
      >
        <Typography variant='h6' fontWeight='bold'>
          Variants &amp; Pricing
        </Typography>
        <Box>
          <Typography
            component='span'
            color='text.secondary'
            fontSize={14}
            mr={2}
          >
            Add variants if the product comes in multiple options
          </Typography>
          <Button
            variant='contained'
            size='small'
            startIcon={<AddIcon />}
            onClick={handleAddVariant}
          >
            Add Variant
          </Button>
        </Box>
      </Box>
      <Divider sx={{ mb: 2 }} />

      {variants.length === 0 && (
        <Typography color='text.secondary' sx={{ mb: 2 }}>
          No variants yet
        </Typography>
      )}

      {variants.map((variant, idx) => (
        <Card
          key={variant.id}
          sx={{ mb: 3, p: 2, borderRadius: 2, boxShadow: 1 }}
        >
          <Stack
            direction='row'
            justifyContent='space-between'
            alignItems='center'
            mb={2}
          >
            <Typography fontWeight='bold' fontSize={16}>
              Variant {idx + 1}
            </Typography>
            <IconButton
              onClick={() => handleDeleteVariant(variant.id)}
              size='small'
              color='error'
              sx={{ ml: 1 }}
            >
              <DeleteIcon />
            </IconButton>
          </Stack>
          <Grid container spacing={2}>
            <Grid item size={{ xs: 12, md: 3 }}>
              <TextField
                label='Title'
                fullWidth
                value={variant?.title || ''}
                onChange={e =>
                  handleVariantChange(variant.id, 'title', e.target.value)
                }
              />
            </Grid>
            <Grid item size={{ xs: 12, md: 3 }}>
              <TextField
                label='SKU'
                fullWidth
                value={variant?.sku || ''}
                onChange={e =>
                  handleVariantChange(variant.id, 'sku', e.target.value)
                }
                helperText='Edit to override.'
              />
            </Grid>
            <Grid item size={{ xs: 6, md: 3 }}>
              <TextField
                label='Weight'
                type='number'
                fullWidth
                value={variant?.weight || ''}
                onChange={e =>
                  handleVariantChange(
                    variant.id,
                    'weight',
                    Number(e.target.value)
                  )
                }
              />
            </Grid>
            <Grid item size={{ xs: 6, md: 3 }}>
              <TextField
                label='Width'
                type='number'
                fullWidth
                value={variant?.width || ''}
                onChange={e =>
                  handleVariantChange(
                    variant.id,
                    'width',
                    Number(e.target.value)
                  )
                }
              />
            </Grid>
            <Grid item size={{ xs: 6, md: 3 }}>
              <TextField
                label='Length'
                type='number'
                fullWidth
                value={variant?.length || ''}
                onChange={e =>
                  handleVariantChange(
                    variant.id,
                    'length',
                    Number(e.target.value)
                  )
                }
              />
            </Grid>
            <Grid item size={{ xs: 6, md: 3 }}>
              <TextField
                label='Height'
                type='number'
                fullWidth
                value={variant?.height || ''}
                onChange={e =>
                  handleVariantChange(
                    variant.id,
                    'height',
                    Number(e.target.value)
                  )
                }
              />
            </Grid>
            <Grid item size={{ xs: 12, md: 3 }}>
              <TextField
                label='Original Price'
                type='number'
                fullWidth
                value={variant?.originalPrice || ''}
                onChange={e =>
                  handleVariantChange(
                    variant.id,
                    'originalPrice',
                    Number(e.target.value)
                  )
                }
              />
            </Grid>
            <Grid item size={{ xs: 12, md: 3 }}>
              <TextField
                label='Sale Price'
                type='number'
                fullWidth
                value={variant?.salePrice || ''}
                onChange={e =>
                  handleVariantChange(
                    variant.id,
                    'salePrice',
                    Number(e.target.value)
                  )
                }
              />
            </Grid>
            <Grid item size={{ xs: 12, md: 3 }}>
              <TextField
                label='Quantity'
                type='number'
                fullWidth
                value={variant?.variantQuantity || ''}
                onChange={e =>
                  handleVariantChange(
                    variant.id,
                    'variantQuantity',
                    Number(e.target.value)
                  )
                }
              />
            </Grid>
            <Grid item size={{ xs: 12, md: 3 }}>
              <FormControl fullWidth>
                <InputLabel id={`inv-status-label-${variant.id}`}>
                  Stock Status
                </InputLabel>
                <Select
                  labelId={`inv-status-label-${variant.id}`}
                  value={variant?.inventoryStatus || ''}
                  label='Stock Status'
                  MenuProps={{
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left',
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left',
                    },
                    getContentAnchorEl: null,
                    PaperProps: { style: { maxHeight: 115 } },
                  }}
                  onChange={e =>
                    handleVariantChange(
                      variant.id,
                      'inventoryStatus',
                      e.target.value as 'in_stock' | 'out_of_stock'
                    )
                  }
                >
                  <MenuItem value='in_stock'>In Stock</MenuItem>
                  <MenuItem value='out_of_stock'>Out of Stock</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Card>
      ))}
    </Card>
  );
};
