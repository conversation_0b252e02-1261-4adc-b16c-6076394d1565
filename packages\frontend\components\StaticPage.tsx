'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';
import { getPageBySlug } from '@/lib/strapi-api';
import type { Page } from '@/lib/strapi-api';

interface StaticPageProps {
  slug: string;
  fallbackTitle?: string;
  fallbackContent?: string;
}

interface BreadcrumbItem {
  name: string;
  href: string;
  current?: boolean;
}

const StaticPage: React.FC<StaticPageProps> = ({
  slug,
  fallbackTitle = 'Page Not Found',
  fallbackContent = '<p>The requested page could not be found.</p>',
}) => {
  const [page, setPage] = useState<Page | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPage = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log(`🚀 Fetching page: ${slug}`);
        const pageData = await getPageBySlug(slug);

        if (pageData) {
          setPage(pageData);
          console.log(`✅ Page loaded: ${pageData.title}`);
        } else {
          console.warn(`⚠️ Page not found: ${slug}`);
          setError('Page not found');
        }
      } catch (err) {
        console.error(`❌ Error loading page ${slug}:`, err);
        setError(err instanceof Error ? err.message : 'Failed to load page');
      } finally {
        setLoading(false);
      }
    };

    fetchPage();
  }, [slug]);

  // Generate breadcrumbs based on page type and slug
  const generateBreadcrumbs = (page: Page | null): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [{ name: 'Home', href: '/' }];

    if (page) {
      // Add category breadcrumb based on page type
      if (page.pageType === 'legal') {
        breadcrumbs.push({ name: 'Legal', href: '#' });
      } else if (page.pageType === 'support') {
        breadcrumbs.push({ name: 'Support', href: '#' });
      }

      breadcrumbs.push({
        name: page.title,
        href: `/${page.slug}`,
        current: true,
      });
    } else {
      breadcrumbs.push({
        name: fallbackTitle,
        href: `/${slug}`,
        current: true,
      });
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs(page);
  const pageTitle = page?.title || fallbackTitle;
  const pageContent = page?.content || fallbackContent;
  const metaTitle = page?.metaTitle || `${pageTitle} - ONDC Seller Platform`;
  const metaDescription =
    page?.metaDescription || `${pageTitle} page on ONDC Seller Platform`;

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12'>
          <div className='animate-pulse'>
            <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
            <div className='h-4 bg-gray-200 rounded w-1/2 mb-8'></div>
            <div className='space-y-4'>
              <div className='h-4 bg-gray-200 rounded'></div>
              <div className='h-4 bg-gray-200 rounded w-5/6'></div>
              <div className='h-4 bg-gray-200 rounded w-4/6'></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>{metaTitle}</title>
        <meta name='description' content={metaDescription} />
        {page?.metaKeywords && (
          <meta name='keywords' content={page.metaKeywords} />
        )}
        <meta property='og:title' content={metaTitle} />
        <meta property='og:description' content={metaDescription} />
        <meta property='og:type' content='website' />
        <link
          rel='canonical'
          href={`${
            process.env.NEXT_PUBLIC_SITE_URL || 'https://ondcseller.com'
          }/${slug}`}
        />
      </Head>

      <div className='min-h-screen bg-gray-50'>
        {/* Breadcrumb Navigation */}
        <div className='bg-white border-b border-gray-200'>
          <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4'>
            <nav className='flex' aria-label='Breadcrumb'>
              <ol className='flex items-center space-x-4'>
                {breadcrumbs.map((item, index) => (
                  <li key={item.name}>
                    <div className='flex items-center'>
                      {index === 0 ? (
                        <HomeIcon
                          className='flex-shrink-0 h-5 w-5 text-gray-400'
                          aria-hidden='true'
                        />
                      ) : (
                        <ChevronRightIcon
                          className='flex-shrink-0 h-5 w-5 text-gray-400'
                          aria-hidden='true'
                        />
                      )}
                      {item.current ? (
                        <span
                          className='ml-4 text-sm font-medium text-gray-500'
                          aria-current='page'
                        >
                          {item.name}
                        </span>
                      ) : (
                        <Link
                          href={item.href}
                          className='ml-4 text-sm font-medium text-gray-500 hover:text-gray-700'
                        >
                          {item.name}
                        </Link>
                      )}
                    </div>
                  </li>
                ))}
              </ol>
            </nav>
          </div>
        </div>

        {/* Page Content */}
        <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12'>
          {error ? (
            <div className='bg-red-50 border border-red-200 rounded-md p-4'>
              <div className='flex'>
                <div className='ml-3'>
                  <h3 className='text-sm font-medium text-red-800'>
                    Error Loading Page
                  </h3>
                  <div className='mt-2 text-sm text-red-700'>
                    <p>{error}</p>
                  </div>
                  <div className='mt-4'>
                    <Link
                      href='/'
                      className='text-sm font-medium text-red-800 hover:text-red-600'
                    >
                      Return to Home
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <article className='prose prose-lg max-w-none'>
              <div
                dangerouslySetInnerHTML={{ __html: pageContent }}
                className='prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-a:hover:text-blue-800'
              />
            </article>
          )}

          {/* Page Footer */}
          <div className='mt-12 pt-8 border-t border-gray-200'>
            <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0'>
              <div className='text-sm text-gray-500'>
                {page?.updatedAt && (
                  <p>
                    Last updated:{' '}
                    {new Date(page.updatedAt).toLocaleDateString()}
                  </p>
                )}
              </div>
              <div className='flex space-x-4'>
                <Link
                  href='/contact'
                  className='text-sm font-medium text-blue-600 hover:text-blue-800'
                >
                  Contact Support
                </Link>
                <Link
                  href='/faq'
                  className='text-sm font-medium text-blue-600 hover:text-blue-800'
                >
                  FAQ
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default StaticPage;
