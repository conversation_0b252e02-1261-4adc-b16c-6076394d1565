import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import Link from '@mui/material/Link';
import IconButton from '@mui/material/IconButton';
import DeleteIcon from '@mui/icons-material/Delete';
import InputBase from '@mui/material/InputBase';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';

interface StoreLogoDropzoneProps {
  storeLogo: File | null;
  storeLogoUrl: string;
  setStoreLogo: (file: File | null) => void;
  setStoreLogoUrl: (url: string) => void;
}

const StoreLogoDropzone: React.FC<StoreLogoDropzoneProps> = ({
  storeLogo,
  storeLogoUrl,
  setStoreLogo,
  setStoreLogoUrl,
}) => {
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [tempUrl, setTempUrl] = useState('');

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        setStoreLogo(acceptedFiles[0]);
        setStoreLogoUrl('');
      }
    },
    [setStoreLogo, setStoreLogoUrl]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'image/*': [] },
    maxFiles: 1,
  });

  const handleAddUrl = () => {
    if (tempUrl.trim()) {
      setStoreLogoUrl(tempUrl.trim());
      setStoreLogo(null);
      setShowUrlInput(false);
      setTempUrl('');
    }
  };

  const handleRemove = () => {
    setStoreLogo(null);
    setStoreLogoUrl('');
  };

  const renderPreview = () => {
    if (storeLogo) {
      return (
        <>
          <Typography
            variant='caption'
            color='text.secondary'
            sx={{ mt: 1, wordBreak: 'break-all' }}
          >
            {storeLogo.name}
          </Typography>

          <Box mt={1}>
            <img
              src={URL.createObjectURL(storeLogo)}
              alt='Store logo preview'
              style={{
                maxWidth: 90,
                maxHeight: 90,
                objectFit: 'contain',
                borderRadius: 6,
              }}
            />
          </Box>
        </>
      );
    }
    if (storeLogoUrl) {
      return (
        <>
          <Typography
            variant='caption'
            color='text.secondary'
            sx={{ mt: 1, wordBreak: 'break-all' }}
          >
            {storeLogoUrl}
          </Typography>
          <Box mt={1}>
            <img
              src={storeLogoUrl}
              alt='Store logo preview'
              style={{
                maxWidth: 90,
                maxHeight: 90,
                objectFit: 'contain',
                borderRadius: 6,
              }}
              onError={handleRemove}
            />
          </Box>
        </>
      );
    }
    return null;
  };

  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 0.5,
        }}
      >
        <Typography
          variant='body1'
          fontWeight={500}
          sx={{ mb: 0, fontSize: 15 }}
        >
          Store Logo{' '}
          <Typography component='span' variant='caption' color='text.secondary'>
            (Optional)
          </Typography>
        </Typography>
        {(storeLogo || storeLogoUrl) && (
          <IconButton
            size='small'
            onClick={handleRemove}
            aria-label='Remove logo'
          >
            <DeleteIcon fontSize='small' />
          </IconButton>
        )}
      </Box>
      <Paper
        {...getRootProps()}
        variant='outlined'
        sx={{
          p: 3,
          minHeight: 140,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          backgroundColor: isDragActive ? 'primary.50' : 'grey.50',
          cursor: 'pointer',
          transition: 'border-color 0.2s, background-color 0.2s',
          outline: 'none',
          position: 'relative',
        }}
        aria-label='Dropzone for store logo upload'
        tabIndex={0}
      >
        <input {...getInputProps()} aria-label='Upload Store Logo' />
        {!storeLogo && !storeLogoUrl && (
          <>
            <CloudUploadIcon sx={{ fontSize: 38, color: 'grey.400', mb: 1 }} />
            <Typography variant='body2' color='text.secondary'>
              Click or drag file here to upload
            </Typography>
          </>
        )}
        {renderPreview()}
      </Paper>
      {!showUrlInput && (
        <Box textAlign='right' mt={1}>
          <Link
            href='#'
            underline='hover'
            fontSize={13}
            onClick={e => {
              e.preventDefault();
              setShowUrlInput(true);
            }}
          >
            Paste image URL instead
          </Link>
        </Box>
      )}
      {showUrlInput && (
        <Box mt={1} display='flex' alignItems='center' gap={1}>
          <InputBase
            value={tempUrl}
            onChange={e => setTempUrl(e.target.value)}
            placeholder='Paste image URL'
            fullWidth
            sx={{
              border: '1px solid #ccc',
              borderRadius: 1,
              pl: 1,
              height: 36,
              fontSize: 14,
              background: '#fff',
            }}
            inputProps={{ 'aria-label': 'Image URL' }}
          />
          <Button
            variant='contained'
            size='small'
            onClick={handleAddUrl}
            disabled={!tempUrl.trim()}
            sx={{ minWidth: 40 }}
          >
            Add
          </Button>
          <Button
            variant='text'
            size='small'
            onClick={() => setShowUrlInput(false)}
            sx={{ minWidth: 40 }}
          >
            Cancel
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default StoreLogoDropzone;
