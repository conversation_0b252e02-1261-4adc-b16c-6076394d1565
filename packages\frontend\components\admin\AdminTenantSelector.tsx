'use client';

import React, { useState, useEffect } from 'react';
import {
  ChevronDownIcon,
  BuildingStorefrontIcon,
  CheckIcon,
  GlobeAltIcon,
  UserGroupIcon,
  CogIcon,
} from '@heroicons/react/24/outline';
import { useAdminTenant } from '@/hooks/useAdminTenant';
import { Seller } from '@/lib/api/multi-tenant';

interface AdminTenantSelectorProps {
  className?: string;
  showStats?: boolean;
}

export default function AdminTenantSelector({
  className = '',
  showStats = true,
}: AdminTenantSelectorProps) {
  const { selectedTenant, sellers, loading, switchTenant } = useAdminTenant();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-tenant-selector]')) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isOpen]);

  // Filter sellers based on search term
  const filteredSellers = sellers.filter(
    seller =>
      String(seller.name || '')
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      String(seller.business_type || '')
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
  );

  const handleTenantSelect = async (tenant: Seller) => {
    try {
      await switchTenant(tenant);
      setIsOpen(false);
      setSearchTerm('');
    } catch (error) {
      console.error('Error switching tenant:', error);
      // Could add toast notification here
    }
  };

  // Tenant persistence is now handled by useAdminTenant hook

  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className='flex items-center space-x-3 px-4 py-2 bg-gray-100 rounded-lg animate-pulse'>
          <BuildingStorefrontIcon className='h-5 w-5 text-gray-400' />
          <div className='flex-1'>
            <div className='h-4 bg-gray-300 rounded w-32 mb-1'></div>
            <div className='h-3 bg-gray-300 rounded w-20'></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} data-tenant-selector>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className='flex items-center space-x-3 w-full px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm'
      >
        {/* Tenant Avatar */}
        <div className='flex-shrink-0'>
          <div className='h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center'>
            <span className='text-white font-semibold text-sm'>
              {selectedTenant?.name?.charAt(0)?.toUpperCase() || 'T'}
            </span>
          </div>
        </div>

        {/* Tenant Info */}
        <div className='flex-1 text-left min-w-0'>
          <div className='text-sm font-medium text-gray-900 truncate'>
            {selectedTenant?.name || 'Select Tenant'}
          </div>
          {selectedTenant?.business_type && (
            <div className='text-xs text-gray-500 truncate'>
              {selectedTenant.business_type}
            </div>
          )}
        </div>

        {/* Stats Badge (if enabled) */}
        {showStats && selectedTenant && (
          <div className='flex items-center space-x-1 text-xs text-gray-500'>
            <UserGroupIcon className='h-3 w-3' />
            <span>Active</span>
          </div>
        )}

        {/* Dropdown Arrow */}
        <ChevronDownIcon
          className={`h-4 w-4 text-gray-600 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div className='fixed inset-0 z-10' />

          {/* Dropdown Content */}
          <div className='absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-96 overflow-hidden'>
            {/* Search Header */}
            <div className='p-3 border-b border-gray-200'>
              <div className='relative'>
                <input
                  type='text'
                  placeholder='Search tenants...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='w-full pl-8 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                />
                <GlobeAltIcon className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-400' />
              </div>
            </div>

            {/* Tenant List */}
            <div className='max-h-64 overflow-y-auto'>
              {filteredSellers.length === 0 ? (
                <div className='p-4 text-center text-sm text-gray-500'>
                  No tenants found matching "{searchTerm}"
                </div>
              ) : (
                filteredSellers.map(seller => (
                  <button
                    key={seller.id}
                    onClick={() => handleTenantSelect(seller)}
                    className={`w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 ${
                      selectedTenant?.id === seller.id
                        ? 'bg-blue-50 border-l-4 border-l-blue-500'
                        : ''
                    }`}
                  >
                    <div className='flex items-center space-x-3'>
                      {/* Tenant Avatar */}
                      <div className='flex-shrink-0'>
                        <div className='h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center'>
                          <span className='text-white font-semibold text-sm'>
                            {seller.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      </div>

                      {/* Tenant Details */}
                      <div className='flex-1 min-w-0'>
                        <div className='flex items-center space-x-2'>
                          <p className='text-sm font-medium text-gray-900 truncate'>
                            {String(seller.name)}
                          </p>
                          {selectedTenant?.id === seller.id && (
                            <CheckIcon className='h-4 w-4 text-blue-600' />
                          )}
                        </div>
                        {seller.business_type && (
                          <p className='text-xs text-gray-500 truncate'>
                            {String(seller.business_type)}
                          </p>
                        )}
                        {seller.description && (
                          <p className='text-xs text-gray-400 truncate mt-1'>
                            {String(seller.description)}
                          </p>
                        )}
                      </div>

                      {/* Status Indicator */}
                      <div className='flex-shrink-0'>
                        <div className='h-2 w-2 bg-green-400 rounded-full'></div>
                      </div>
                    </div>
                  </button>
                ))
              )}
            </div>

            {/* Footer */}
            <div className='p-3 border-t border-gray-200 bg-gray-50'>
              <div className='flex items-center justify-between text-xs text-gray-500'>
                <span>
                  {filteredSellers.length} tenant
                  {filteredSellers.length !== 1 ? 's' : ''}
                </span>
                <div className='flex items-center space-x-1'>
                  <CogIcon className='h-3 w-3' />
                  <span>Admin Panel</span>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
