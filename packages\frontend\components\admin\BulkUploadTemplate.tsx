'use client';

import React from 'react';
import {
  DocumentArrowDownIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

export interface BulkUploadTemplateProps {
  onDownloadTemplate: () => void;
  isDownloading?: boolean;
  className?: string;
}

// Template structure for optimized product import
// Required fields as specified by user
const TEMPLATE_COLUMNS = [
  // Required Product Information
  {
    name: 'title',
    required: true,
    description: 'Product name/title (Required)',
    example: 'Wireless Bluetooth Headphones',
    type: 'text',
  },
  {
    name: 'description',
    required: true,
    description: 'Product description (Required)',
    example: 'High-quality wireless headphones with noise cancellation',
    type: 'text',
  },
  {
    name: 'handle',
    required: true,
    description: 'URL-friendly product identifier (Required)',
    example: 'wireless-bluetooth-headphones',
    type: 'text',
  },
  {
    name: 'status',
    required: true,
    description: 'Product status: published or draft (Required)',
    example: 'published',
    type: 'select',
  },
  {
    name: 'product_overview',
    required: true,
    description: 'Product overview/summary (Required)',
    example: 'Premium audio experience with advanced noise cancellation',
    type: 'text',
  },

  // Required Variant Information
  {
    name: 'variant_title',
    required: true,
    description: 'Variant title (Required)',
    example: 'Default Variant',
    type: 'text',
  },
  {
    name: 'variant_sku',
    required: true,
    description: 'Variant SKU - unique identifier (Required)',
    example: 'WBH-001',
    type: 'text',
  },
  {
    name: 'variant_inventory_quantity',
    required: true,
    description: 'Available inventory quantity (Required)',
    example: '100',
    type: 'number',
  },
  {
    name: 'variant_manage_inventory',
    required: true,
    description: 'Whether to manage inventory: true or false (Required)',
    example: 'true',
    type: 'boolean',
  },

  // Required Pricing Information
  {
    name: 'original_price',
    required: true,
    description: 'Original/list price in rupees (Required)',
    example: '3999',
    type: 'number',
  },
  {
    name: 'sale_price',
    required: true,
    description: 'Sale/current price in rupees (Required)',
    example: '2999',
    type: 'number',
  },
  {
    name: 'inventory_status',
    required: true,
    description:
      'Inventory status: in_stock, out_of_stock, or low_stock (Required)',
    example: 'in_stock',
    type: 'select',
  },
  // Optional Product Information
  {
    name: 'thumbnail',
    required: false,
    description: 'Product thumbnail image URL',
    example: 'https://example.com/thumbnail.jpg',
    type: 'text',
  },
  {
    name: 'images',
    required: false,
    description: 'Product images (comma-separated URLs)',
    example: 'https://example.com/img1.jpg,https://example.com/img2.jpg',
    type: 'text',
  },
  {
    name: 'category',
    required: false,
    description: 'Product category',
    example: 'Electronics',
    type: 'text',
  },
  {
    name: 'brand',
    required: false,
    description: 'Product brand',
    example: 'TechBrand',
    type: 'text',
  },

  // Optional Variant Information
  {
    name: 'variant_weight',
    required: false,
    description: 'Variant weight in grams',
    example: '250',
    type: 'number',
  },
  {
    name: 'variant_length',
    required: false,
    description: 'Variant length in cm',
    example: '20',
    type: 'number',
  },
  {
    name: 'variant_width',
    required: false,
    description: 'Variant width in cm',
    example: '15',
    type: 'number',
  },
  {
    name: 'variant_height',
    required: false,
    description: 'Variant height in cm',
    example: '8',
    type: 'number',
  },
  {
    name: 'variant_material',
    required: false,
    description: 'Variant material',
    example: 'Plastic',
    type: 'text',
  },

  // Optional ONDC Fields
  {
    name: 'hsn_code',
    required: false,
    description: 'HSN (Harmonized System of Nomenclature) code',
    example: '85183000',
    type: 'text',
  },
  {
    name: 'country_of_origin',
    required: false,
    description: 'Country of origin',
    example: 'India',
    type: 'text',
  },
  {
    name: 'manufacturer',
    required: false,
    description: 'Manufacturer name',
    example: 'TechBrand Manufacturing',
    type: 'text',
  },
];

export const BulkUploadTemplate = ({
  onDownloadTemplate,
  isDownloading = false,
  className,
}: BulkUploadTemplateProps) => {
  const handleDownload = () => {
    if (onDownloadTemplate) {
      onDownloadTemplate();
    }
  };

  const requiredColumns = TEMPLATE_COLUMNS.filter(col => col.required);
  const optionalColumns = TEMPLATE_COLUMNS.filter(col => !col.required);

  return (
    <div
      className={cn(
        'w-full bg-white border border-gray-200 rounded-lg p-4 sm:p-6 overflow-hidden',
        className
      )}
    >
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4'>
        <div className='min-w-0 flex-1'>
          <h3 className='text-lg font-medium text-gray-900 break-words'>
            Download Template
          </h3>
          <p className='text-sm text-gray-500 mt-1 break-words'>
            Get the CSV template with proper column headers and sample data
          </p>
        </div>

        <button
          onClick={handleDownload}
          disabled={isDownloading}
          className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0'
        >
          {isDownloading ? (
            <>
              <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
              <span className='whitespace-nowrap'>Downloading...</span>
            </>
          ) : (
            <>
              <DocumentArrowDownIcon className='w-4 h-4 mr-2 flex-shrink-0' />
              <span className='whitespace-nowrap'>Download Template</span>
            </>
          )}
        </button>
      </div>

      {/* Important Notes */}
      <div className='mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md'>
        <div className='flex items-start space-x-2'>
          <ExclamationTriangleIcon className='w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0' />
          <div>
            <h4 className='text-sm font-medium text-yellow-800 mb-2'>
              Important Notes:
            </h4>
            <ul className='text-sm text-yellow-700 space-y-1'>
              <li>• Keep the first row as column headers (do not modify)</li>
              <li>• Required columns must have values for all products</li>
              <li>• Use comma-separated values for tags and multiple images</li>
              <li>
                • Price should be in Indian Rupees (INR) without currency symbol
              </li>
              <li>• Status can be: active, draft, or archived</li>
              <li>• Maximum file size: 10MB</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Required Columns */}
      <div className='mb-6'>
        <h4 className='text-sm font-medium text-gray-900 mb-3 flex items-center'>
          <span className='w-2 h-2 bg-red-500 rounded-full mr-2'></span>
          Required Columns
        </h4>
        <div className='space-y-3'>
          {requiredColumns.map(column => (
            <div
              key={column.name}
              className='border border-gray-200 rounded-md p-3'
            >
              <div className='flex items-start justify-between'>
                <div className='flex-1'>
                  <h5 className='text-sm font-medium text-gray-900'>
                    {column.name}
                  </h5>
                  <p className='text-xs text-gray-600 mt-1'>
                    {column.description}
                  </p>
                </div>
                <span className='text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded'>
                  {column.type}
                </span>
              </div>
              <div className='mt-2'>
                <span className='text-xs text-gray-500'>Example: </span>
                <code className='text-xs bg-gray-100 px-1 py-0.5 rounded'>
                  {column.example}
                </code>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Optional Columns */}
      <div className='mb-6'>
        <h4 className='text-sm font-medium text-gray-900 mb-3 flex items-center'>
          <span className='w-2 h-2 bg-blue-500 rounded-full mr-2'></span>
          Optional Columns
        </h4>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
          {optionalColumns.map(column => (
            <div
              key={column.name}
              className='border border-gray-200 rounded-md p-3'
            >
              <div className='flex items-start justify-between'>
                <div className='flex-1'>
                  <h5 className='text-sm font-medium text-gray-900'>
                    {column.name}
                  </h5>
                  <p className='text-xs text-gray-600 mt-1'>
                    {column.description}
                  </p>
                </div>
                <span className='text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded'>
                  {column.type}
                </span>
              </div>
              {column.example && (
                <div className='mt-2'>
                  <span className='text-xs text-gray-500'>Example: </span>
                  <code className='text-xs bg-gray-100 px-1 py-0.5 rounded'>
                    {column.example}
                  </code>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Additional Information */}
      <div className='p-4 bg-blue-50 border border-blue-200 rounded-md'>
        <div className='flex items-start space-x-2'>
          <InformationCircleIcon className='w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0' />
          <div>
            <h4 className='text-sm font-medium text-blue-800 mb-2'>
              Template Features:
            </h4>
            <ul className='text-sm text-blue-700 space-y-1'>
              <li>• Includes sample data to help you understand the format</li>
              <li>
                • Compatible with Excel, Google Sheets, and other CSV editors
              </li>
              <li>• Follows ONDC product data standards</li>
              <li>• Supports bulk import of up to 1000 products per file</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
