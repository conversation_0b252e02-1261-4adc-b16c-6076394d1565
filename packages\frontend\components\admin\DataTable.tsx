'use client';

import React, { useState } from 'react';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

interface Column {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  searchable?: boolean;
  filterable?: boolean;
  pagination?: boolean;
  pageSize?: number;
  onView?: (row: any) => void;
  onEdit?: (row: any) => void;
  onDelete?: (row: any) => void;
  loading?: boolean;
  emptyMessage?: string;
}

export default function DataTable({
  columns,
  data,
  searchable = true,
  filterable = false,
  pagination = true,
  pageSize = 10,
  onView,
  onEdit,
  onDelete,
  loading = false,
  emptyMessage = 'No data available',
}: DataTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Filter data based on search term
  const filteredData = data?.filter(row =>
    Object.values(row).some(value =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // Sort data
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortColumn) return 0;

    const aValue = a[sortColumn];
    const bValue = b[sortColumn];

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Paginate data
  const totalPages = Math.ceil(sortedData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedData = pagination
    ? sortedData.slice(startIndex, startIndex + pageSize)
    : sortedData;

  const handleSort = (columnKey: string) => {
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  };

  const hasActions = onView || onEdit || onDelete;

  return (
    <div className='bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden'>
      {/* Header with search and filters */}
      {(searchable || filterable) && (
        <div className='px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50'>
          <div className='flex items-center justify-between'>
            {searchable && (
              <div className='flex-1 max-w-lg'>
                <div className='relative'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    <MagnifyingGlassIcon className='h-5 w-5 text-gray-400' />
                  </div>
                  <input
                    type='text'
                    className='block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500'
                    placeholder='Search...'
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            )}
            {/* {filterable && (
              <button className='ml-4 inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50'>
                <FunnelIcon className='h-4 w-4 mr-2' />
                Filters
              </button>
            )} */}
          </div>
        </div>
      )}

      {/* Table */}
      <div className='overflow-x-auto'>
        <table className='min-w-full divide-y divide-gray-200'>
          <thead>
            <tr>
              {columns.map(column => (
                <th
                  key={column.key}
                  className={`px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider transition-all duration-200 ${
                    column.sortable ? 'cursor-pointer hover:bg-gray-50' : ''
                  }`}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className='flex items-center'>
                    {column.label}
                    {column.sortable && sortColumn === column.key && (
                      <span className='ml-1 text-gray-700'>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {hasActions && (
                <th className='px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider'>
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className='bg-white divide-y divide-gray-200'>
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length + (hasActions ? 1 : 0)}
                  className='px-6 py-4 text-center'
                >
                  <div className='flex justify-center'>
                    <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600'></div>
                  </div>
                </td>
              </tr>
            ) : paginatedData.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (hasActions ? 1 : 0)}
                  className='px-6 py-4 text-center text-gray-500'
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              paginatedData.map((row, index) => (
                <tr
                  key={index}
                  className={`transition-all duration-200 hover:table-row-hover-gradient ${
                    index % 2 === 0 ? 'bg-white' : 'table-row-even-gradient'
                  }`}
                >
                  {columns.map(column => (
                    <td
                      key={column.key}
                      className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'
                    >
                      {column.render
                        ? column.render(row[column.key], row)
                        : row[column.key]}
                    </td>
                  ))}
                  {hasActions && (
                    <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                      <div className='flex items-center justify-end space-x-2'>
                        {onView && (
                          <button
                            onClick={() => onView(row)}
                            className='p-1.5 rounded-md text-blue-600 hover:text-blue-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 transition-all duration-200'
                            title='View'
                          >
                            <EyeIcon className='h-4 w-4' />
                          </button>
                        )}
                        {onEdit && (
                          <button
                            onClick={() => onEdit(row)}
                            className='p-1.5 rounded-md text-green-600 hover:text-green-900 hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 transition-all duration-200'
                            title='Edit'
                          >
                            <PencilIcon className='h-4 w-4' />
                          </button>
                        )}
                        {onDelete && (
                          <button
                            onClick={() => onDelete(row)}
                            className='p-1.5 rounded-md text-red-600 hover:text-red-900 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 transition-all duration-200'
                            title='Delete'
                          >
                            <TrashIcon className='h-4 w-4' />
                          </button>
                        )}
                      </div>
                    </td>
                  )}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className='px-6 py-3 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50 flex items-center justify-between'>
          <div className='text-sm text-gray-700'>
            Showing {startIndex + 1} to{' '}
            {Math.min(startIndex + pageSize, sortedData.length)} of{' '}
            {sortedData.length} results
          </div>
          <div className='flex items-center space-x-2'>
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className='p-2 rounded-md border border-gray-300 bg-gradient-to-r from-white to-gray-50 text-gray-500 hover:from-blue-50 hover:to-blue-100 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200'
            >
              <ChevronLeftIcon className='h-4 w-4' />
            </button>
            <span className='text-sm text-gray-700'>
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 1))
              }
              disabled={currentPage === totalPages}
              className='p-2 rounded-md border border-gray-300 bg-gradient-to-r from-white to-gray-50 text-gray-500 hover:from-blue-50 hover:to-blue-100 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200'
            >
              <ChevronRightIcon className='h-4 w-4' />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
