'use client';

import React from 'react';
import { ExclamationCircleIcon } from '@heroicons/react/24/outline';

interface FormFieldProps {
  label: string;
  name: string;
  type?:
    | 'text'
    | 'email'
    | 'password'
    | 'number'
    | 'textarea'
    | 'select'
    | 'file'
    | 'checkbox'
    | 'radio'
    | 'date';
  value?: any;
  onChange?: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
  onBlur?: (
    e: React.FocusEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  help?: string;
  options?: Array<{ value: string; label: string }>;
  rows?: number;
  accept?: string;
  multiple?: boolean;
  children?: React.ReactNode;
}

export default function FormField({
  label,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  error,
  help,
  options = [],
  rows = 3,
  accept,
  multiple = false,
  children,
}: FormFieldProps) {
  const baseInputClasses = `
    block w-full rounded-md border-gray-300 shadow-sm 
    focus:border-blue-500 focus:ring-blue-500 
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
  `;

  const renderInput = () => {
    switch (type) {
      case 'textarea':
        return (
          <textarea
            id={name}
            name={name}
            value={value || ''}
            onChange={onChange}
            onBlur={onBlur}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            rows={rows}
            className={baseInputClasses}
          />
        );

      case 'select':
        return (
          <select
            id={name}
            name={name}
            value={value || ''}
            onChange={onChange}
            onBlur={onBlur}
            required={required}
            disabled={disabled}
            className={baseInputClasses}
          >
            <option value=''>Select {label.toLowerCase()}</option>
            {options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'checkbox':
        return (
          <div className='flex items-center'>
            <input
              id={name}
              name={name}
              type='checkbox'
              checked={value || false}
              onChange={onChange}
              disabled={disabled}
              className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:cursor-not-allowed'
            />
            <label htmlFor={name} className='ml-2 block text-sm text-gray-900'>
              {label}
            </label>
          </div>
        );

      case 'radio':
        return (
          <div className='space-y-2'>
            {options.map(option => (
              <div key={option.value} className='flex items-center'>
                <input
                  id={`${name}-${option.value}`}
                  name={name}
                  type='radio'
                  value={option.value}
                  checked={value === option.value}
                  onChange={onChange}
                  disabled={disabled}
                  className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 disabled:cursor-not-allowed'
                />
                <label
                  htmlFor={`${name}-${option.value}`}
                  className='ml-2 block text-sm text-gray-900'
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        );

      case 'file':
        return (
          <input
            id={name}
            name={name}
            type='file'
            onChange={onChange}
            required={required}
            disabled={disabled}
            accept={accept}
            multiple={multiple}
            className='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:cursor-not-allowed'
          />
        );

      default:
        return (
          <input
            id={name}
            name={name}
            type={type}
            value={value || ''}
            onChange={onChange}
            onBlur={onBlur}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            className={baseInputClasses}
          />
        );
    }
  };

  if (type === 'checkbox') {
    return (
      <div className='space-y-1'>
        {renderInput()}
        {error && (
          <div className='flex items-center text-sm text-red-600'>
            <ExclamationCircleIcon className='h-4 w-4 mr-1' />
            {error}
          </div>
        )}
        {help && !error && <p className='text-sm text-gray-500'>{help}</p>}
      </div>
    );
  }

  return (
    <div className='space-y-1'>
      <label htmlFor={name} className='block text-sm font-medium text-gray-700'>
        {label}
        {required && <span className='text-red-500 ml-1'>*</span>}
      </label>
      {renderInput()}
      {children}
      {error && (
        <div className='flex items-center text-sm text-red-600'>
          <ExclamationCircleIcon className='h-4 w-4 mr-1' />
          {error}
        </div>
      )}
      {help && !error && <p className='text-sm text-gray-500'>{help}</p>}
    </div>
  );
}

// Form container component
interface FormContainerProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  onSubmit?: (e: React.FormEvent) => void;
  actions?: React.ReactNode;
}

export function FormContainer({
  title,
  description,
  children,
  onSubmit,
  actions,
}: FormContainerProps) {
  return (
    <div className='bg-white shadow-sm rounded-lg border border-gray-200'>
      {(title || description) && (
        <div className='px-6 py-4 border-b border-gray-200'>
          {title && (
            <h3 className='text-lg font-medium text-gray-900'>{title}</h3>
          )}
          {description && (
            <p className='mt-1 text-sm text-gray-500'>{description}</p>
          )}
        </div>
      )}
      <form onSubmit={onSubmit} className='px-6 py-4 space-y-6'>
        {children}
        {actions && (
          <div className='pt-4 border-t border-gray-200'>
            <div className='flex justify-end space-x-3'>{actions}</div>
          </div>
        )}
      </form>
    </div>
  );
}

// Form section component for grouping related fields
interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
}

export function FormSection({
  title,
  description,
  children,
}: FormSectionProps) {
  return (
    <div className='space-y-4'>
      <div>
        <h4 className='text-base font-medium text-gray-900'>{title}</h4>
        {description && (
          <p className='mt-1 text-sm text-gray-500'>{description}</p>
        )}
      </div>
      <div className='space-y-4'>{children}</div>
    </div>
  );
}
