import React, { useState, useCallback, useEffect } from 'react';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  Checkbox,
  FormControlLabel,
  Switch,
  Radio,
  RadioGroup,
  FormLabel,
  Box,
  Typography,
  Chip,
  Autocomplete,
  TextareaAutosize,
  styled,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import {
  validateField,
  debounceValidation,
  FIELD_VALIDATION_RULES,
  ValidationRule,
} from '@/lib/validation';
// Date picker temporarily disabled due to import issues
// import { DatePicker } from '@mui/x-date-pickers/DatePicker';
// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';

// Styled textarea component
const StyledTextarea = styled(TextareaAutosize)(({ theme }) => ({
  width: '100%',
  padding: '12px 14px',
  border: `1px solid ${theme.palette.grey[300]}`,
  borderRadius: theme.shape.borderRadius,
  fontFamily: theme.typography.fontFamily,
  fontSize: '1rem',
  lineHeight: 1.5,
  resize: 'vertical',
  '&:focus': {
    outline: 'none',
    borderColor: theme.palette.primary.main,
    borderWidth: '2px',
  },
  '&:hover': {
    borderColor: theme.palette.grey[400],
  },
}));

interface Option {
  value: string | number;
  label: string;
}

interface MuiFormFieldProps {
  label?: string;
  name: string;
  value?: any;
  onChange: (e: React.ChangeEvent<any>) => void;
  onBlur?: (e: React.FocusEvent<any>) => void;
  type?:
    | 'text'
    | 'email'
    | 'password'
    | 'number'
    | 'tel'
    | 'url'
    | 'search'
    | 'select'
    | 'multiselect'
    | 'checkbox'
    | 'switch'
    | 'radio'
    | 'textarea'
    | 'date'
    | 'autocomplete';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  help?: string;
  options?: Option[];
  multiple?: boolean;
  rows?: number;
  minRows?: number;
  maxRows?: number;
  checked?: boolean;
  size?: 'small' | 'medium';
  fullWidth?: boolean;
  variant?: 'outlined' | 'filled' | 'standard';
  sx?: any;
  // Validation props
  validationRules?: ValidationRule;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  // Currency/number formatting
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  // Auto-generation
  autoGenerate?: boolean;
  generateFrom?: string;
}

export default function MuiFormField({
  label,
  name,
  value = '',
  onChange,
  onBlur,
  type = 'text',
  placeholder,
  required = false,
  disabled = false,
  error,
  help,
  options = [],
  multiple = false,
  rows = 4,
  minRows = 3,
  maxRows = 10,
  checked = false,
  size = 'medium',
  fullWidth = true,
  variant = 'outlined',
  sx = {},
  validationRules,
  validateOnChange = true,
  validateOnBlur = true,
  startAdornment,
  endAdornment,
  autoGenerate = false,
  generateFrom,
}: MuiFormFieldProps) {
  const [internalError, setInternalError] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);
  const [touched, setTouched] = useState(false);

  // Use external error if provided, otherwise use internal validation error
  const displayError = error || (touched ? internalError : '');
  const hasError = Boolean(displayError);

  // Debounced validation function
  const debouncedValidate = useCallback(
    debounceValidation((fieldValue: any) => {
      const rules = validationRules || FIELD_VALIDATION_RULES[name];
      if (rules) {
        const validationError = validateField(name, fieldValue, rules);
        setInternalError(validationError || '');
      }
    }, 300),
    [name, validationRules]
  );

  // Handle input change with validation
  const handleChange = useCallback(
    (e: React.ChangeEvent<any>) => {
      const newValue =
        e.target.type === 'checkbox' ? e.target.checked : e.target.value;

      // Auto-generate values if configured
      if (autoGenerate && generateFrom && name !== generateFrom) {
        // This will be handled by the parent component
      }

      onChange(e);

      // Validate on change if enabled
      if (validateOnChange && touched) {
        debouncedValidate(newValue);
      }
    },
    [
      onChange,
      validateOnChange,
      touched,
      debouncedValidate,
      autoGenerate,
      generateFrom,
      name,
    ]
  );

  // Handle input blur with validation
  const handleBlur = useCallback(
    (e: React.FocusEvent<any>) => {
      setTouched(true);

      if (validateOnBlur) {
        const fieldValue =
          e.target.type === 'checkbox' ? e.target.checked : e.target.value;
        debouncedValidate(fieldValue);
      }

      if (onBlur) {
        onBlur(e);
      }
    },
    [onBlur, validateOnBlur, debouncedValidate]
  );

  // Password visibility toggle
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Get input adornments
  const getStartAdornment = () => {
    if (startAdornment) return startAdornment;
    if (type === 'number' && name.toLowerCase().includes('price')) {
      return <Typography sx={{ mr: 1 }}>₹</Typography>;
    }
    return null;
  };

  const getEndAdornment = () => {
    if (type === 'password') {
      return (
        <InputAdornment position='end'>
          <IconButton
            aria-label='toggle password visibility'
            onClick={togglePasswordVisibility}
            edge='end'
            size='small'
          >
            {showPassword ? (
              <EyeSlashIcon className='h-4 w-4' />
            ) : (
              <EyeIcon className='h-4 w-4' />
            )}
          </IconButton>
        </InputAdornment>
      );
    }
    if (endAdornment) return endAdornment;
    if (type === 'number' && name.toLowerCase().includes('percentage')) {
      return <Typography sx={{ ml: 1 }}>%</Typography>;
    }
    return null;
  };

  // Handle different input types
  const renderField = () => {
    switch (type) {
      case 'select':
        return (
          <FormControl
            fullWidth={fullWidth}
            error={hasError}
            size={size}
            variant={variant}
          >
            {label && <InputLabel required={required}>{label}</InputLabel>}
            <Select
              name={name}
              value={value}
              onChange={event => {
                const syntheticEvent = {
                  target: { name, value: event.target.value },
                } as React.ChangeEvent<any>;
                handleChange(syntheticEvent);
              }}
              onBlur={handleBlur}
              label={label}
              disabled={disabled}
              multiple={multiple}
              renderValue={
                multiple
                  ? selected => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map(val => {
                          const option = options.find(opt => opt.value === val);
                          return (
                            <Chip
                              key={val}
                              label={option?.label || val}
                              size='small'
                            />
                          );
                        })}
                      </Box>
                    )
                  : undefined
              }
            >
              {options.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {(displayError || help) && (
              <FormHelperText>{displayError || help}</FormHelperText>
            )}
          </FormControl>
        );

      case 'multiselect':
        return (
          <FormControl fullWidth={fullWidth} error={hasError} size={size}>
            {label && <InputLabel required={required}>{label}</InputLabel>}
            <Select
              name={name}
              value={value || []}
              onChange={event => {
                const syntheticEvent = {
                  target: { name, value: event.target.value },
                } as React.ChangeEvent<any>;
                handleChange(syntheticEvent);
              }}
              onBlur={handleBlur}
              label={label}
              disabled={disabled}
              multiple
              renderValue={selected => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {(selected as string[]).map(val => {
                    const option = options.find(opt => opt.value === val);
                    return (
                      <Chip
                        key={val}
                        label={option?.label || val}
                        size='small'
                      />
                    );
                  })}
                </Box>
              )}
            >
              {options.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {(displayError || help) && (
              <FormHelperText>{displayError || help}</FormHelperText>
            )}
          </FormControl>
        );

      case 'autocomplete':
        return (
          <Autocomplete
            options={options}
            getOptionLabel={option => option.label}
            value={options.find(opt => opt.value === value) || null}
            onChange={(event, newValue) => {
              const value = Array.isArray(newValue)
                ? newValue.map(item => item.value)
                : newValue?.value || '';
              const syntheticEvent = {
                target: { name, value },
              } as React.ChangeEvent<any>;
              handleChange(syntheticEvent);
            }}
            onBlur={handleBlur}
            disabled={disabled}
            multiple={multiple}
            renderInput={params => (
              <TextField
                {...params}
                label={label}
                placeholder={placeholder}
                required={required}
                error={hasError}
                helperText={displayError || help}
                size={size}
                variant={variant}
                fullWidth={fullWidth}
              />
            )}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant='outlined'
                  label={option.label}
                  size='small'
                  {...getTagProps({ index })}
                />
              ))
            }
          />
        );

      case 'checkbox':
        return (
          <FormControlLabel
            control={
              <Checkbox
                name={name}
                checked={checked}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={disabled}
                size={size}
              />
            }
            label={label}
            sx={sx}
          />
        );

      case 'switch':
        return (
          <FormControlLabel
            control={
              <Switch
                name={name}
                checked={checked}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={disabled}
                size={size}
              />
            }
            label={label}
            sx={sx}
          />
        );

      case 'radio':
        return (
          <FormControl
            component='fieldset'
            error={hasError}
            disabled={disabled}
          >
            {label && (
              <FormLabel component='legend' required={required}>
                {label}
              </FormLabel>
            )}
            <RadioGroup
              name={name}
              value={value}
              onChange={handleChange}
              onBlur={handleBlur}
              row={options.length <= 3}
            >
              {options.map(option => (
                <FormControlLabel
                  key={option.value}
                  value={option.value}
                  control={<Radio size={size} />}
                  label={option.label}
                />
              ))}
            </RadioGroup>
            {(displayError || help) && (
              <FormHelperText>{displayError || help}</FormHelperText>
            )}
          </FormControl>
        );

      case 'textarea':
        return (
          <Box>
            {label && (
              <Typography
                variant='body2'
                component='label'
                sx={{ mb: 1, display: 'block', fontWeight: 500 }}
              >
                {label} {required && <span style={{ color: 'red' }}>*</span>}
              </Typography>
            )}
            <StyledTextarea
              name={name}
              value={value}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder={placeholder}
              disabled={disabled}
              minRows={minRows}
              maxRows={maxRows}
              style={{
                borderColor: hasError ? '#EF4444' : undefined,
                ...sx,
              }}
            />
            {(error || help) && (
              <Typography
                variant='caption'
                sx={{
                  mt: 0.5,
                  display: 'block',
                  color: hasError ? 'error.main' : 'text.secondary',
                }}
              >
                {displayError || help}
              </Typography>
            )}
          </Box>
        );

      case 'date':
        return (
          <TextField
            name={name}
            label={label}
            type='date'
            value={value}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            error={hasError}
            helperText={displayError || help}
            size={size}
            fullWidth={fullWidth}
            variant={variant}
            sx={sx}
            InputLabelProps={{
              shrink: true,
            }}
          />
        );

      default:
        return (
          <TextField
            name={name}
            label={label}
            type={
              type === 'password' ? (showPassword ? 'text' : 'password') : type
            }
            value={value}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            error={hasError}
            helperText={displayError || help}
            size={size}
            fullWidth={fullWidth}
            variant={variant}
            multiline={false}
            rows={undefined}
            sx={sx}
            InputProps={{
              startAdornment: getStartAdornment(),
              endAdornment: getEndAdornment(),
            }}
          />
        );
    }
  };

  return <Box sx={{ mb: 2, ...sx }}>{renderField()}</Box>;
}

// Export additional helper components
export const MuiFormContainer = ({
  children,
  title,
  description,
  actions,
  onSubmit,
  sx = {},
}: {
  children: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  onSubmit?: (e: React.FormEvent) => void;
  sx?: any;
}) => (
  <Box
    component='form'
    onSubmit={onSubmit}
    sx={{
      backgroundColor: 'background.paper',
      borderRadius: 2,
      p: 3,
      border: '1px solid',
      borderColor: 'grey.200',
      ...sx,
    }}
  >
    {title && (
      <Typography variant='h5' component='h2' sx={{ mb: 1 }}>
        {title}
      </Typography>
    )}
    {description && (
      <Typography variant='body2' color='text.secondary' sx={{ mb: 3 }}>
        {description}
      </Typography>
    )}
    {children}
    {actions && (
      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        {actions}
      </Box>
    )}
  </Box>
);
