'use client';

import React, {
  useState,
  useCallback,
  useTransition,
  useRef,
  useEffect,
  memo,
} from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useOptimizedNavigation } from '@/contexts/OptimizedLoadingContext';
import { LoadingSpinner } from '@/components/skeletons/SkeletonBase';

interface OptimizedAdminNavLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  activeClassName?: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
  showLoadingIndicator?: boolean;
  prefetch?: boolean;
}

/**
 * Optimized navigation link with minimal loading overhead
 */
const OptimizedAdminNavLink = memo(function OptimizedAdminNavLink({
  href,
  children,
  className = '',
  activeClassName = '',
  icon: Icon,
  onClick,
  showLoadingIndicator = true,
  prefetch = true,
}: OptimizedAdminNavLinkProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { startNavigation, finishLoading } = useOptimizedNavigation();
  const [isPending, startNavigationTransition] = useTransition();
  const [isNavigating, setIsNavigating] = useState(false);
  const linkRef = useRef<HTMLAnchorElement>(null);
  const navigationTimeoutRef = useRef<NodeJS.Timeout>();

  const isActive = pathname === href;
  const isLoading = isNavigating || isPending;

  // Optimized navigation handler
  const handleClick = useCallback(
    async (e: React.MouseEvent<HTMLAnchorElement>) => {
      e.preventDefault();

      // Call custom onClick if provided
      onClick?.();

      // Don't navigate if already on the same page
      if (isActive) return;

      // Don't navigate if already loading
      if (isLoading) return;

      // Start optimized navigation
      const navigationId = startNavigation(`Loading ${children}...`);

      // Use React 18 transitions for non-blocking navigation
      startNavigationTransition(() => {
        setIsNavigating(true);

        // Navigate immediately
        router.push(href);

        // Set a timeout to ensure loading state is cleared
        navigationTimeoutRef.current = setTimeout(() => {
          setIsNavigating(false);
          finishLoading(navigationId);
        }, 100);
      });
    },
    [
      href,
      isActive,
      isLoading,
      onClick,
      router,
      startNavigation,
      finishLoading,
      children,
    ]
  );

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current);
      }
    };
  }, []);

  const linkClassName = `
    ${className}
    ${isActive ? activeClassName : ''}
    ${isLoading ? 'opacity-75 cursor-wait' : 'cursor-pointer'}
    transition-opacity duration-150 ease-in-out
    relative
  `.trim();

  return (
    <Link
      ref={linkRef}
      href={href}
      onClick={handleClick}
      className={linkClassName}
      prefetch={prefetch}
    >
      <div className='flex items-center'>
        {Icon && (
          <Icon
            className={`h-5 w-5 flex-shrink-0 ${
              isActive
                ? 'text-blue-600 group-hover:text-blue-700'
                : 'text-gray-400 group-hover:text-gray-500'
            } ${isLoading ? 'animate-pulse' : ''}`}
          />
        )}
        <span className='ml-3'>{children}</span>
        {showLoadingIndicator && isLoading && (
          <div className='ml-auto'>
            <LoadingSpinner size='sm' />
          </div>
        )}
      </div>
    </Link>
  );
});

/**
 * Optimized sidebar navigation link
 */
export const OptimizedSidebarNavLink = memo(function OptimizedSidebarNavLink({
  href,
  children,
  icon: Icon,
  onClick,
  isCollapsed = false,
}: {
  href: string;
  children: React.ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
  isCollapsed?: boolean;
}) {
  const pathname = usePathname();
  const isActive = pathname === href;

  return (
    <OptimizedAdminNavLink
      href={href}
      onClick={onClick}
      className={`
        group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-150 ease-in-out
        ${
          isActive
            ? 'bg-blue-50 text-blue-900 border-r-2 border-blue-500 font-semibold shadow-sm'
            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
        }
        ${isCollapsed ? 'justify-center px-2' : ''}
      `}
      activeClassName='bg-blue-50 text-blue-900'
      icon={Icon}
    >
      {!isCollapsed && children}
    </OptimizedAdminNavLink>
  );
});

/**
 * Optimized dropdown navigation link
 */
export const OptimizedDropdownNavLink = memo(function OptimizedDropdownNavLink({
  href,
  children,
  icon: Icon,
  onClick,
}: {
  href: string;
  children: React.ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
}) {
  const pathname = usePathname();
  const isActive = pathname === href;

  return (
    <OptimizedAdminNavLink
      href={href}
      onClick={onClick}
      className={`
        group flex items-center px-4 py-2.5 text-sm font-medium transition-colors duration-150
        ${
          isActive
            ? 'bg-blue-50 text-blue-900 border-r-2 border-blue-500 font-semibold'
            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
        }
      `}
      activeClassName='bg-blue-50 text-blue-900'
      icon={Icon}
    >
      {children}
    </OptimizedAdminNavLink>
  );
});

export default OptimizedAdminNavLink;
