'use client';

import React from 'react';
import Link from 'next/link';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

interface BreadcrumbItem {
  label: string;
  href?: string;
  active?: boolean;
}

interface PageHeaderProps {
  title: string;
  description?: string;
  breadcrumbs?: BreadcrumbItem[];
  actions?: React.ReactNode;
}

export default function PageHeader({
  title,
  description,
  breadcrumbs,
  actions,
}: PageHeaderProps) {
  return (
    <div className='bg-white shadow-sm border-b border-gray-200'>
      <div className='px-4 sm:px-6 lg:px-8'>
        {/* Breadcrumbs */}
        {breadcrumbs && breadcrumbs.length > 0 && (
          <nav className='flex pt-4 pb-2' aria-label='Breadcrumb'>
            <ol className='flex items-center space-x-2'>
              <li>
                <div>
                  <Link
                    href='/admin'
                    className='text-gray-400 hover:text-gray-500'
                  >
                    <HomeIcon
                      className='flex-shrink-0 h-4 w-4'
                      aria-hidden='true'
                    />
                    <span className='sr-only'>Home</span>
                  </Link>
                </div>
              </li>
              {breadcrumbs.map((item, index) => (
                <li key={index}>
                  <div className='flex items-center'>
                    <ChevronRightIcon
                      className='flex-shrink-0 h-4 w-4 text-gray-400'
                      aria-hidden='true'
                    />
                    {item.href && !item.active ? (
                      <Link
                        href={item.href}
                        className='ml-2 text-sm font-medium text-gray-500 hover:text-gray-700'
                      >
                        {item.label}
                      </Link>
                    ) : (
                      <span
                        className='ml-2 text-sm font-medium text-gray-900'
                        aria-current='page'
                      >
                        {item.label}
                      </span>
                    )}
                  </div>
                </li>
              ))}
            </ol>
          </nav>
        )}

        {/* Page header */}
        <div className='py-6'>
          <div className='flex items-center justify-between'>
            <div className='flex-1 min-w-0'>
              <h1 className='text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate'>
                {title}
              </h1>
              {description && (
                <p className='mt-1 text-sm text-gray-500'>{description}</p>
              )}
            </div>
            {actions && (
              <div className='mt-4 flex md:mt-0 md:ml-4'>{actions}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Reusable action buttons
interface ActionButtonProps {
  href?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  children: React.ReactNode;
  icon?: React.ComponentType<any>;
  disabled?: boolean;
}

export function ActionButton({
  href,
  onClick,
  variant = 'primary',
  children,
  icon: Icon,
  disabled = false,
}: ActionButtonProps) {
  const baseClasses =
    'inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variantClasses = {
    primary:
      'border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
    secondary:
      'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500',
    danger:
      'border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500',
  };

  const className = `${baseClasses} ${variantClasses[variant]}`;

  const content = (
    <>
      {Icon && <Icon className='-ml-1 mr-2 h-5 w-5' />}
      {children}
    </>
  );

  if (href) {
    return (
      <Link href={href} className={className}>
        {content}
      </Link>
    );
  }

  return (
    <button onClick={onClick} className={className} disabled={disabled}>
      {content}
    </button>
  );
}
