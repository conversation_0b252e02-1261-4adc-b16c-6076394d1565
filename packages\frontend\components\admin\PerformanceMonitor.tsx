'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useOptimizedLoading } from '@/contexts/OptimizedLoadingContext';
import { usePathname } from 'next/navigation';
import { ChartBarIcon, ClockIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface PerformanceMetrics {
  averageLoadTime: number;
  totalOperations: number;
  navigationTimes: { route: string; time: number; timestamp: number }[];
  slowOperations: { operation: string; time: number; timestamp: number }[];
}

interface PerformanceMonitorProps {
  showDetails?: boolean;
  className?: string;
}

/**
 * Performance monitoring component for admin dashboard
 */
export default function PerformanceMonitor({ 
  showDetails = false, 
  className = '' 
}: PerformanceMonitorProps) {
  const { getPerformanceMetrics } = useOptimizedLoading();
  const pathname = usePathname();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    averageLoadTime: 0,
    totalOperations: 0,
    navigationTimes: [],
    slowOperations: [],
  });
  const [isExpanded, setIsExpanded] = useState(false);
  const [navigationStartTime, setNavigationStartTime] = useState<number | null>(null);

  // Track navigation performance
  useEffect(() => {
    const startTime = performance.now();
    setNavigationStartTime(startTime);

    // Measure when the page is fully loaded
    const measureNavigation = () => {
      const endTime = performance.now();
      const navigationTime = endTime - startTime;
      
      setMetrics(prev => ({
        ...prev,
        navigationTimes: [
          ...prev.navigationTimes.slice(-9), // Keep last 10 entries
          {
            route: pathname,
            time: navigationTime,
            timestamp: Date.now(),
          }
        ]
      }));

      // Track slow operations (>2 seconds)
      if (navigationTime > 2000) {
        setMetrics(prev => ({
          ...prev,
          slowOperations: [
            ...prev.slowOperations.slice(-4), // Keep last 5 entries
            {
              operation: `Navigation to ${pathname}`,
              time: navigationTime,
              timestamp: Date.now(),
            }
          ]
        }));
      }
    };

    // Use requestIdleCallback for better performance
    if ('requestIdleCallback' in window) {
      requestIdleCallback(measureNavigation);
    } else {
      setTimeout(measureNavigation, 0);
    }
  }, [pathname]);

  // Update metrics from loading context
  useEffect(() => {
    const updateMetrics = () => {
      const loadingMetrics = getPerformanceMetrics();
      setMetrics(prev => ({
        ...prev,
        averageLoadTime: loadingMetrics.averageLoadTime,
        totalOperations: loadingMetrics.totalOperations,
      }));
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [getPerformanceMetrics]);

  const formatTime = useCallback((time: number) => {
    if (time < 1000) {
      return `${Math.round(time)}ms`;
    }
    return `${(time / 1000).toFixed(1)}s`;
  }, []);

  const getPerformanceStatus = useCallback(() => {
    const avgTime = metrics.averageLoadTime;
    if (avgTime < 1000) return { status: 'excellent', color: 'text-green-600', bg: 'bg-green-50' };
    if (avgTime < 2000) return { status: 'good', color: 'text-blue-600', bg: 'bg-blue-50' };
    if (avgTime < 3000) return { status: 'fair', color: 'text-yellow-600', bg: 'bg-yellow-50' };
    return { status: 'poor', color: 'text-red-600', bg: 'bg-red-50' };
  }, [metrics.averageLoadTime]);

  const performanceStatus = getPerformanceStatus();

  if (!showDetails && !isExpanded) {
    return (
      <button
        onClick={() => setIsExpanded(true)}
        className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${performanceStatus.bg} ${performanceStatus.color} hover:opacity-80 transition-opacity ${className}`}
        title="Click to view performance details"
      >
        <ClockIcon className="w-3 h-3 mr-1" />
        {formatTime(metrics.averageLoadTime)}
      </button>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <ChartBarIcon className="w-5 h-5 text-gray-600 mr-2" />
          <h3 className="text-sm font-medium text-gray-900">Performance Monitor</h3>
        </div>
        {isExpanded && (
          <button
            onClick={() => setIsExpanded(false)}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Minimize
          </button>
        )}
      </div>

      {/* Performance Summary */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className={`text-lg font-semibold ${performanceStatus.color}`}>
            {formatTime(metrics.averageLoadTime)}
          </div>
          <div className="text-xs text-gray-500">Avg Load Time</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">
            {metrics.totalOperations}
          </div>
          <div className="text-xs text-gray-500">Total Operations</div>
        </div>
        <div className="text-center">
          <div className={`text-lg font-semibold ${
            metrics.slowOperations.length === 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {metrics.slowOperations.length}
          </div>
          <div className="text-xs text-gray-500">Slow Operations</div>
        </div>
      </div>

      {/* Performance Status */}
      <div className={`p-3 rounded-lg ${performanceStatus.bg} mb-4`}>
        <div className="flex items-center">
          <CheckCircleIcon className={`w-4 h-4 mr-2 ${performanceStatus.color}`} />
          <span className={`text-sm font-medium ${performanceStatus.color} capitalize`}>
            {performanceStatus.status} Performance
          </span>
        </div>
      </div>

      {/* Recent Navigation Times */}
      {metrics.navigationTimes.length > 0 && (
        <div className="mb-4">
          <h4 className="text-xs font-medium text-gray-700 mb-2">Recent Navigation Times</h4>
          <div className="space-y-1">
            {metrics.navigationTimes.slice(-5).map((nav, index) => (
              <div key={index} className="flex justify-between items-center text-xs">
                <span className="text-gray-600 truncate">{nav.route}</span>
                <span className={`font-medium ${
                  nav.time > 2000 ? 'text-red-600' : nav.time > 1000 ? 'text-yellow-600' : 'text-green-600'
                }`}>
                  {formatTime(nav.time)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Slow Operations Alert */}
      {metrics.slowOperations.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <h4 className="text-xs font-medium text-red-800 mb-2">Recent Slow Operations</h4>
          <div className="space-y-1">
            {metrics.slowOperations.slice(-3).map((op, index) => (
              <div key={index} className="text-xs text-red-700">
                <div className="font-medium">{op.operation}</div>
                <div className="text-red-600">{formatTime(op.time)}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Tips */}
      {metrics.averageLoadTime > 2000 && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-xs font-medium text-blue-800 mb-1">Performance Tips</h4>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Clear browser cache and reload</li>
            <li>• Check network connection</li>
            <li>• Close unused browser tabs</li>
          </ul>
        </div>
      )}
    </div>
  );
}

/**
 * Lightweight performance indicator for header/toolbar
 */
export function PerformanceIndicator({ className = '' }: { className?: string }) {
  return (
    <PerformanceMonitor 
      showDetails={false} 
      className={className}
    />
  );
}

/**
 * Detailed performance panel for admin settings
 */
export function PerformancePanel({ className = '' }: { className?: string }) {
  return (
    <PerformanceMonitor 
      showDetails={true} 
      className={className}
    />
  );
}
