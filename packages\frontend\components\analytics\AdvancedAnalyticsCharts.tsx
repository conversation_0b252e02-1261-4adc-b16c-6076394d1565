/**
 * Advanced Analytics Charts Component
 *
 * Comprehensive analytics charts for sales, orders, and performance metrics
 */

'use client';

import React, { useState, useMemo } from 'react';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  ComposedChart,
  Legend,
} from 'recharts';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Grid,
  Paper,
  ButtonGroup,
  Button,
} from '@mui/material';
import {
  TrendingUpIcon,
  TrendingDownIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';

// Types
interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
  category?: string;
  [key: string]: any;
}

interface AnalyticsChartsProps {
  className?: string;
  timeRange?: '7d' | '30d' | '90d' | '1y';
  onTimeRangeChange?: (range: string) => void;
}

// Mock data generators
const generateSalesData = (days: number): ChartDataPoint[] => {
  const data: ChartDataPoint[] = [];
  const baseValue = 50000;

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);

    const trend = Math.sin((i / days) * Math.PI * 2) * 0.3 + 1;
    const noise = (Math.random() - 0.5) * 0.4;
    const value = Math.round(baseValue * trend * (1 + noise));

    data.push({
      date: date.toISOString().split('T')[0],
      value,
      revenue: value,
      orders: Math.round(value / 1200),
      customers: Math.round(value / 2000),
      label: date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      }),
    });
  }

  return data;
};

const generateOrderStatusData = () => [
  { name: 'Completed', value: 1247, color: '#10B981', percentage: 68.2 },
  { name: 'Processing', value: 324, color: '#F59E0B', percentage: 17.7 },
  { name: 'Shipped', value: 189, color: '#3B82F6', percentage: 10.3 },
  { name: 'Cancelled', value: 67, color: '#EF4444', percentage: 3.7 },
  { name: 'Returned', value: 2, color: '#6B7280', percentage: 0.1 },
];

const generateChannelData = () => [
  { channel: 'ONDC Network', sales: 450000, orders: 1200, growth: 15.3 },
  { channel: 'Direct Website', sales: 280000, orders: 800, growth: 8.7 },
  { channel: 'Mobile App', sales: 320000, orders: 950, growth: 22.1 },
  { channel: 'Partner APIs', sales: 180000, orders: 450, growth: -2.4 },
];

const generatePerformanceData = (days: number) => {
  const data = [];
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split('T')[0],
      conversionRate: 3.2 + Math.random() * 2.8,
      avgOrderValue: 1800 + Math.random() * 600,
      customerSatisfaction: 4.2 + Math.random() * 0.6,
      returnRate: 1.5 + Math.random() * 2,
      label: date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      }),
    });
  }
  return data;
};

// Custom Tooltip Component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <Paper sx={{ p: 2, border: '1px solid #e0e0e0' }}>
        <Typography variant='subtitle2' sx={{ mb: 1 }}>
          {label}
        </Typography>
        {payload.map((entry: any, index: number) => (
          <Box key={index} display='flex' alignItems='center' gap={1}>
            <Box
              sx={{
                width: 12,
                height: 12,
                backgroundColor: entry.color,
                borderRadius: '50%',
              }}
            />
            <Typography variant='body2'>
              {entry.name}:{' '}
              {typeof entry.value === 'number'
                ? entry.value.toLocaleString()
                : entry.value}
            </Typography>
          </Box>
        ))}
      </Paper>
    );
  }
  return null;
};

// Sales Trend Chart
const SalesTrendChart: React.FC<{
  data: ChartDataPoint[];
  timeRange: string;
}> = ({ data, timeRange }) => {
  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title='Sales Performance'
        subheader='Revenue, orders, and customer trends'
        action={
          <Chip
            label={`Last ${
              timeRange === '7d'
                ? '7 days'
                : timeRange === '30d'
                  ? '30 days'
                  : timeRange === '90d'
                    ? '90 days'
                    : '1 year'
            }`}
            size='small'
            color='primary'
            variant='outlined'
          />
        }
      />
      <CardContent sx={{ height: 'calc(100% - 80px)' }}>
        <ResponsiveContainer width='100%' height='100%'>
          <ComposedChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray='3 3' stroke='#f0f0f0' />
            <XAxis
              dataKey='label'
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <YAxis
              yAxisId='revenue'
              orientation='left'
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              tickFormatter={value => `₹${(value / 1000).toFixed(0)}k`}
            />
            <YAxis
              yAxisId='orders'
              orientation='right'
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Area
              yAxisId='revenue'
              type='monotone'
              dataKey='revenue'
              fill='url(#revenueGradient)'
              stroke='#3B82F6'
              strokeWidth={2}
              fillOpacity={0.3}
              name='Revenue (₹)'
            />
            <Bar
              yAxisId='orders'
              dataKey='orders'
              fill='#10B981'
              name='Orders'
              opacity={0.8}
            />
            <Line
              yAxisId='orders'
              type='monotone'
              dataKey='customers'
              stroke='#F59E0B'
              strokeWidth={2}
              dot={{ fill: '#F59E0B', strokeWidth: 2, r: 4 }}
              name='New Customers'
            />
            <defs>
              <linearGradient id='revenueGradient' x1='0' y1='0' x2='0' y2='1'>
                <stop offset='5%' stopColor='#3B82F6' stopOpacity={0.8} />
                <stop offset='95%' stopColor='#3B82F6' stopOpacity={0.1} />
              </linearGradient>
            </defs>
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// Order Status Distribution Chart
const OrderStatusChart: React.FC = () => {
  const data = generateOrderStatusData();

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title='Order Status Distribution'
        subheader='Current order status breakdown'
      />
      <CardContent sx={{ height: 'calc(100% - 80px)' }}>
        <ResponsiveContainer width='100%' height='100%'>
          <PieChart>
            <Pie
              data={data}
              cx='50%'
              cy='50%'
              innerRadius={60}
              outerRadius={120}
              paddingAngle={2}
              dataKey='value'
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number, name: string) => [
                `${value.toLocaleString()} orders`,
                name,
              ]}
            />
            <Legend
              verticalAlign='bottom'
              height={36}
              formatter={(value, entry: any) => (
                <span style={{ color: entry.color }}>
                  {value} ({entry.payload.percentage}%)
                </span>
              )}
            />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// Channel Performance Chart
const ChannelPerformanceChart: React.FC = () => {
  const data = generateChannelData();

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title='Sales by Channel'
        subheader='Performance across different sales channels'
      />
      <CardContent sx={{ height: 'calc(100% - 80px)' }}>
        <ResponsiveContainer width='100%' height='100%'>
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray='3 3' stroke='#f0f0f0' />
            <XAxis
              dataKey='channel'
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              angle={-45}
              textAnchor='end'
              height={80}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              tickFormatter={value => `₹${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip
              content={<CustomTooltip />}
              formatter={(value: number, name: string) => [
                name === 'sales'
                  ? `₹${value.toLocaleString()}`
                  : value.toLocaleString(),
                name === 'sales' ? 'Sales' : 'Orders',
              ]}
            />
            <Bar
              dataKey='sales'
              fill='#3B82F6'
              name='Sales'
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// Performance Metrics Chart
const PerformanceMetricsChart: React.FC<{ data: ChartDataPoint[] }> = ({
  data,
}) => {
  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title='Performance Metrics'
        subheader='Key performance indicators over time'
      />
      <CardContent sx={{ height: 'calc(100% - 80px)' }}>
        <ResponsiveContainer width='100%' height='100%'>
          <LineChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray='3 3' stroke='#f0f0f0' />
            <XAxis
              dataKey='label'
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line
              type='monotone'
              dataKey='conversionRate'
              stroke='#3B82F6'
              strokeWidth={2}
              dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
              name='Conversion Rate (%)'
            />
            <Line
              type='monotone'
              dataKey='customerSatisfaction'
              stroke='#10B981'
              strokeWidth={2}
              dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
              name='Customer Satisfaction'
            />
            <Line
              type='monotone'
              dataKey='returnRate'
              stroke='#EF4444'
              strokeWidth={2}
              dot={{ fill: '#EF4444', strokeWidth: 2, r: 4 }}
              name='Return Rate (%)'
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// Main Advanced Analytics Charts Component
export default function AdvancedAnalyticsCharts({
  className,
  timeRange = '30d',
  onTimeRangeChange,
}: AnalyticsChartsProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);

  const handleTimeRangeChange = (newRange: string) => {
    setSelectedTimeRange(newRange);
    onTimeRangeChange?.(newRange);
  };

  const salesData = useMemo(() => {
    const days =
      selectedTimeRange === '7d'
        ? 7
        : selectedTimeRange === '30d'
          ? 30
          : selectedTimeRange === '90d'
            ? 90
            : 365;
    return generateSalesData(days);
  }, [selectedTimeRange]);

  const performanceData = useMemo(() => {
    const days =
      selectedTimeRange === '7d'
        ? 7
        : selectedTimeRange === '30d'
          ? 30
          : selectedTimeRange === '90d'
            ? 90
            : 365;
    return generatePerformanceData(days);
  }, [selectedTimeRange]);

  return (
    <Box className={className}>
      {/* Time Range Selector */}
      <Box
        display='flex'
        justifyContent='space-between'
        alignItems='center'
        sx={{ mb: 3 }}
      >
        <Typography variant='h5' fontWeight='bold'>
          Analytics Dashboard
        </Typography>
        <ButtonGroup variant='outlined' size='small'>
          {['7d', '30d', '90d', '1y'].map(range => (
            <Button
              key={range}
              variant={selectedTimeRange === range ? 'contained' : 'outlined'}
              onClick={() => handleTimeRangeChange(range)}
            >
              {range === '7d'
                ? '7 Days'
                : range === '30d'
                  ? '30 Days'
                  : range === '90d'
                    ? '90 Days'
                    : '1 Year'}
            </Button>
          ))}
        </ButtonGroup>
      </Box>

      {/* Charts Grid */}
      <Grid container spacing={3}>
        {/* Sales Trend Chart - Full Width */}
        <Grid item size={{ xs: 12, md: 6 }}>
          <Box sx={{ height: 450 }}>
            <SalesTrendChart data={salesData} timeRange={selectedTimeRange} />
          </Box>
        </Grid>

        {/* Order Status and Channel Performance */}
        <Grid item size={{ xs: 12, md: 6 }}>
          <Box sx={{ height: 450 }}>
            <OrderStatusChart />
          </Box>
        </Grid>
        <Grid item size={{ xs: 12, md: 6 }}>
          <Box sx={{ height: 450 }}>
            <ChannelPerformanceChart />
          </Box>
        </Grid>

        {/* Performance Metrics - Full Width */}
        <Grid item size={{ xs: 12, md: 6 }}>
          <Box sx={{ height: 450 }}>
            <PerformanceMetricsChart data={performanceData} />
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
}
