'use client';

import React, { ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useTenant } from '@/contexts/TenantContext';
import { useRBAC } from '@/hooks/useRBAC';
import {
  Box,
  CircularProgress,
  Alert,
  Button,
  Typography,
  Paper,
} from '@mui/material';
import {
  ShieldExclamationIcon,
  LockClosedIcon,
} from '@heroicons/react/24/outline';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string | string[];
  requiredPermissions?: string[];
  requireAllPermissions?: boolean;
  minimumRoleLevel?: number;
  requireTenant?: boolean;
  fallbackPath?: string;
  showFallback?: boolean;
}

/**
 * ProtectedRoute Component
 *
 * Provides comprehensive route protection with:
 * - Authentication checking
 * - Role-based access control
 * - Permission-based access control
 * - Tenant context validation
 * - Graceful error handling and fallbacks
 */
export default function ProtectedRoute({
  children,
  requiredRole,
  requiredPermissions = [],
  requireAllPermissions = false,
  minimumRoleLevel,
  requireTenant = false,
  fallbackPath = '/auth/login',
  showFallback = true,
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const { selectedTenant, loading: tenantLoading } = useTenant();
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasMinimumRoleLevel,
    userRole,
  } = useRBAC();
  const router = useRouter();

  // Show loading state while checking authentication
  if (isLoading || (requireTenant && tenantLoading)) {
    return (
      <Box
        display='flex'
        justifyContent='center'
        alignItems='center'
        minHeight='50vh'
        flexDirection='column'
        gap={2}
      >
        <CircularProgress size={40} />
        <Typography variant='body2' color='text.secondary'>
          Verifying access...
        </Typography>
      </Box>
    );
  }

  // Check authentication
  if (!isAuthenticated || !user) {
    if (showFallback) {
      return (
        <Box
          display='flex'
          justifyContent='center'
          alignItems='center'
          minHeight='50vh'
          p={3}
        >
          <Paper
            elevation={3}
            sx={{ p: 4, maxWidth: 400, textAlign: 'center' }}
          >
            <LockClosedIcon className='h-12 w-12 text-gray-400 mx-auto mb-4' />
            <Typography variant='h5' gutterBottom>
              Authentication Required
            </Typography>
            <Typography variant='body2' color='text.secondary' paragraph>
              You need to be logged in to access this page.
            </Typography>
            <Button
              variant='contained'
              onClick={() => router.push(fallbackPath)}
              fullWidth
            >
              Sign In
            </Button>
          </Paper>
        </Box>
      );
    }
    router.push(fallbackPath);
    return null;
  }

  // Check role-based access
  let hasRoleAccess = true;
  if (requiredRole) {
    if (Array.isArray(requiredRole)) {
      hasRoleAccess =
        requiredRole.includes(userRole) || userRole === 'super_admin';
    } else {
      hasRoleAccess = userRole === requiredRole || userRole === 'super_admin';
    }
  }

  // Check minimum role level
  if (minimumRoleLevel && hasRoleAccess) {
    hasRoleAccess = hasMinimumRoleLevel(minimumRoleLevel);
  }

  // Check permission-based access
  let hasPermissionAccess = true;
  if (requiredPermissions.length > 0) {
    if (requireAllPermissions) {
      hasPermissionAccess = hasAllPermissions(requiredPermissions);
    } else {
      hasPermissionAccess = hasAnyPermission(requiredPermissions);
    }
  }

  // Combined access check
  const hasAccess = hasRoleAccess && hasPermissionAccess;

  if (!hasAccess) {
    if (showFallback) {
      return (
        <Box
          display='flex'
          justifyContent='center'
          alignItems='center'
          minHeight='50vh'
          p={3}
        >
          <Paper
            elevation={3}
            sx={{ p: 4, maxWidth: 400, textAlign: 'center' }}
          >
            <ShieldExclamationIcon className='h-12 w-12 text-red-400 mx-auto mb-4' />
            <Typography variant='h5' gutterBottom>
              Access Denied
            </Typography>
            <Typography variant='body2' color='text.secondary' paragraph>
              {!hasRoleAccess && requiredRole && (
                <>
                  You need{' '}
                  {Array.isArray(requiredRole)
                    ? requiredRole.join(' or ')
                    : requiredRole}{' '}
                  privileges to access this page.
                </>
              )}
              {!hasPermissionAccess && requiredPermissions.length > 0 && (
                <>
                  You don't have the required permissions to access this page.
                </>
              )}
            </Typography>
            <Typography variant='caption' color='text.secondary' paragraph>
              Current role: {userRole}
            </Typography>
            <Button variant='outlined' onClick={() => router.back()} fullWidth>
              Go Back
            </Button>
          </Paper>
        </Box>
      );
    }
    router.push('/');
    return null;
  }

  // Check permission-based access
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission =>
      hasPermission(permission)
    );

    if (!hasAllPermissions) {
      if (showFallback) {
        return (
          <Box
            display='flex'
            justifyContent='center'
            alignItems='center'
            minHeight='50vh'
            p={3}
          >
            <Paper
              elevation={3}
              sx={{ p: 4, maxWidth: 400, textAlign: 'center' }}
            >
              <ShieldExclamationIcon className='h-12 w-12 text-orange-400 mx-auto mb-4' />
              <Typography variant='h5' gutterBottom>
                Insufficient Permissions
              </Typography>
              <Typography variant='body2' color='text.secondary' paragraph>
                You don't have the required permissions to access this page.
              </Typography>
              <Typography variant='caption' color='text.secondary' paragraph>
                Required: {requiredPermissions.join(', ')}
              </Typography>
              <Button
                variant='outlined'
                onClick={() => router.back()}
                fullWidth
              >
                Go Back
              </Button>
            </Paper>
          </Box>
        );
      }
      router.push('/');
      return null;
    }
  }

  // Check tenant context if required
  if (requireTenant && !selectedTenant) {
    if (showFallback) {
      return (
        <Box
          display='flex'
          justifyContent='center'
          alignItems='center'
          minHeight='50vh'
          p={3}
        >
          <Paper
            elevation={3}
            sx={{ p: 4, maxWidth: 400, textAlign: 'center' }}
          >
            <Alert severity='warning' sx={{ mb: 2 }}>
              <Typography variant='h6' gutterBottom>
                Tenant Required
              </Typography>
              <Typography variant='body2'>
                Please select a tenant to access this page.
              </Typography>
            </Alert>
            <Button
              variant='contained'
              onClick={() => router.push('/tenant-selection')}
              fullWidth
            >
              Select Tenant
            </Button>
          </Paper>
        </Box>
      );
    }
    router.push('/tenant-selection');
    return null;
  }

  // All checks passed, render the protected content
  return <>{children}</>;
}

// Higher-order component for easy wrapping
export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'>
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Specific role-based HOCs
export const withAdminRoute = <P extends object>(
  Component: React.ComponentType<P>
) => withProtectedRoute(Component, { requiredRole: 'admin' });

export const withSellerRoute = <P extends object>(
  Component: React.ComponentType<P>
) => withProtectedRoute(Component, { requiredRole: 'seller' });

export const withCustomerRoute = <P extends object>(
  Component: React.ComponentType<P>
) => withProtectedRoute(Component, { requiredRole: 'customer' });

// Permission-based HOCs
export const withPermissions = <P extends object>(
  Component: React.ComponentType<P>,
  permissions: string[]
) => withProtectedRoute(Component, { requiredPermissions: permissions });
