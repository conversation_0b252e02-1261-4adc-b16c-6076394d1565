'use client';

import React, { useState, useEffect } from 'react';
import { Carousel } from '@/components/ui';
import { useTenant } from '@/contexts/TenantContext';
import { multiTenantAPI } from '@/lib/api/multi-tenant';
import { safeExtractText } from '@/lib/utils/richTextParser';
import { cn } from '@/lib/utils';

interface BannerSlide {
  id: number;
  title: string;
  subtitle?: string;
  description?: string;
  image?: string;
  buttonText?: string;
  buttonLink?: string;
  backgroundColor: string;
}

const defaultSlides: BannerSlide[] = [
  {
    id: 1,
    title: 'Welcome to Our ONDC Store',
    subtitle: 'Discover Amazing Products',
    description:
      'Explore our wide range of quality products at great prices through the Open Network for Digital Commerce',
    buttonText: 'Shop Now',
    buttonLink: '/products',
    backgroundColor: 'bg-gradient-to-r from-blue-600 to-purple-600',
  },
  {
    id: 2,
    title: 'New Arrivals',
    subtitle: 'Fresh Collection',
    description:
      'Check out the latest additions to our catalog with exclusive ONDC benefits',
    buttonText: 'Explore',
    buttonLink: '/categories',
    backgroundColor: 'bg-gradient-to-r from-green-500 to-teal-600',
  },
  {
    id: 3,
    title: 'Special Offers',
    subtitle: 'Limited Time Deals',
    description:
      "Don't miss out on our exclusive offers and discounts available only on ONDC",
    buttonText: 'View Deals',
    buttonLink: '/deals',
    backgroundColor: 'bg-gradient-to-r from-orange-500 to-red-600',
  },
];

// Helper function to get gradient colors for banner backgrounds
const getGradientByIndex = (index: number): string => {
  const gradients = [
    'from-blue-600 to-purple-600',
    'from-green-500 to-teal-600',
    'from-purple-600 to-pink-600',
    'from-orange-500 to-red-600',
    'from-indigo-600 to-blue-600',
    'from-teal-500 to-cyan-600',
    'from-pink-500 to-rose-600',
  ];
  return gradients[index % gradients.length];
};

export default function HeroBanner() {
  const { tenantId, selectedTenant } = useTenant();
  const [slides, setSlides] = useState<BannerSlide[]>(defaultSlides);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('🚀 [HeroBanner] Fetching banners from Strapi CMS...');

        // Fetch banners from Strapi
        const response = await multiTenantAPI.getActiveBanners();

        console.log('✅ [HeroBanner] Strapi API Response:', response);

        if (
          response.data &&
          Array.isArray(response.data) &&
          response.data.length > 0
        ) {
          // Transform Strapi banners to slides format
          const transformedSlides: BannerSlide[] = response.data.map(
            (banner: any, index: number) => {
              // Check if data is nested under attributes (Strapi v4) or at root level
              const bannerData = banner.attributes || banner;

              // Extract image URL from Strapi response
              const imageUrl = bannerData.image?.url;
              const fullImageUrl = imageUrl
                ? imageUrl.startsWith('http')
                  ? imageUrl
                  : `http://localhost:1337${imageUrl}`
                : undefined;

              const slide = {
                id: banner.id || index,
                // title: String(bannerData.title || 'Welcome'),
                // subtitle: String(bannerData.subtitle || ''),
                // description: safeExtractText(bannerData.description, ''),
                image: fullImageUrl,
                buttonText: String(bannerData.buttonText || 'Learn More'),
                buttonLink: String(
                  bannerData.buttonLink || bannerData.link || '#'
                ),
                // backgroundColor: `bg-gradient-to-r ${getGradientByIndex(
                //   index
                // )}`,
              };

              console.log(
                `🔍 [HeroBanner] Transformed slide ${index + 1}:`,
                slide
              );
              return slide;
            }
          );

          console.log(
            '✅ [HeroBanner] All transformed banner slides:',
            transformedSlides
          );
          setSlides(transformedSlides);
        } else {
          console.log(
            '⚠️ [HeroBanner] No banners found - using default slides'
          );
          setSlides(defaultSlides);
        }
      } catch (err) {
        console.error('❌ [HeroBanner] Error fetching banners:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to fetch banners'
        );
        setSlides(defaultSlides);
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, [tenantId, selectedTenant]);

  const renderSlide = (slide: BannerSlide) => (
    <div className='relative h-96 md:h-[500px] flex items-center justify-center overflow-hidden'>
      {/* Background Image or Gradient */}
      {slide.image ? (
        <div
          className='absolute inset-0 bg-cover bg-center bg-no-repeat'
          style={{ backgroundImage: `url(${slide.image})` }}
        />
      ) : (
        <div className={cn('absolute inset-0', slide.backgroundColor)} />
      )}

      {/* Overlay for better text readability */}
      {/* <div className='absolute inset-0 bg-black bg-opacity-40' /> */}

      {/* Content */}
      <div className='relative flex items-end h-[90%] z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto'>
        {/* <h1 className='text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 drop-shadow-lg animate-fade-in-up'>
          {slide.title}
        </h1> 
       {slide.subtitle && (
          <p className='text-lg sm:text-xl md:text-2xl mb-4 text-blue-100 drop-shadow-md animate-fade-in-up'>
            {slide.subtitle}
          </p>
        )}
        {slide.description && (
          <p className='text-base sm:text-lg md:text-xl mb-8 text-gray-100 max-w-2xl mx-auto drop-shadow-md animate-fade-in-up'>
            {slide.description}
          </p>
        )}  */}
        {slide.buttonText && slide.buttonLink && (
          <a
            href={slide.buttonLink}
            className='inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 animate-fade-in-up'
          >
            {slide.buttonText}
            <svg
              className='ml-2 w-4 h-4'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 5l7 7-7 7'
              />
            </svg>
          </a>
        )}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className='relative h-96 md:h-[500px] bg-gray-200 animate-pulse'>
        <div className='absolute inset-0 flex items-center justify-center'>
          <div className='text-center'>
            <div className='h-8 bg-gray-300 rounded w-64 mb-4 animate-pulse' />
            <div className='h-4 bg-gray-300 rounded w-48 mb-2 animate-pulse' />
            <div className='h-4 bg-gray-300 rounded w-32 animate-pulse' />
          </div>
        </div>
      </div>
    );
  }

  if (slides.length === 0) {
    return null;
  }

  return (
    <div className='relative'>
      <Carousel
        autoPlay={true}
        autoPlayInterval={3000}
        showDots={slides.length > 1}
        showArrows={slides.length > 1}
        infinite={slides.length > 1}
        className='hero-banner'
      >
        {slides.map(slide => (
          <div key={slide.id}>{renderSlide(slide)}</div>
        ))}
      </Carousel>

      {/* Error Display */}
      {error && (
        <div className='absolute bottom-4 right-4 z-20 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded animate-slide-in-right'>
          <p className='text-sm'>Failed to load banners: {error}</p>
        </div>
      )}
    </div>
  );
}
