'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useMedusaBackendProducts } from '@/hooks/useMedusaBackendProducts';
import { useMedusaCartContext } from '@/hooks/useMedusaCart';
import { MedusaProduct } from '@/lib/medusa-backend-api';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Autoplay, Pagination } from 'swiper/modules';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  StarIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';
import {
  StarIcon as StarSolidIcon,
  HeartIcon as HeartSolidIcon,
} from '@heroicons/react/24/solid';
import Image from 'next/image';
import NewProductCard from '../product/NewProductCard';

interface MedusaProductsCarouselProps {
  title: string;
  maxProducts?: number;
  showViewAll?: boolean;
  viewAllLink?: string;
  productType?: 'latest' | 'featured' | 'top_deals' | 'hot_picks';
}

export default function MedusaProductsCarousel({
  title,
  maxProducts = 8,
  showViewAll = true,
  viewAllLink = '/products',
  productType = 'latest',
}: MedusaProductsCarouselProps) {
  const {
    products,
    loading,
    error,
    fetchProducts,
    getFeaturedProducts,
    getTopDeals,
    getHotPicks,
  } = useMedusaBackendProducts();

  const { addItem, isAddingItem } = useMedusaCartContext();
  const [addingProductId, setAddingProductId] = useState<string | null>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        switch (productType) {
          case 'featured':
            await getFeaturedProducts(maxProducts);
            break;
          case 'top_deals':
            await getTopDeals(maxProducts);
            break;
          case 'hot_picks':
            await getHotPicks(maxProducts);
            break;
          default:
            await fetchProducts({ limit: maxProducts });
            break;
        }
      } catch (error) {
        console.error(`Error loading ${productType} products:`, error);
      }
    };

    loadProducts();
  }, [
    productType,
    maxProducts,
    fetchProducts,
    getFeaturedProducts,
    getTopDeals,
    getHotPicks,
  ]);

  const handleAddToCart = async (product: MedusaProduct) => {
    if (!product.variants || product.variants.length === 0) {
      console.error('Product has no variants');
      return;
    }

    const defaultVariant = product.variants[0];
    setAddingProductId(product.id);

    try {
      await addItem(defaultVariant.id, 1);
      // Show success feedback
      console.log(`Added ${product.title} to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setAddingProductId(null);
    }
  };

  const formatPrice = (variant: any) => {
    if (variant.prices && variant.prices.length > 0) {
      const price = variant.prices[0];
      return `€${(price.amount / 100).toFixed(2)}`;
    }
    return 'Price not available';
  };

  if (loading) {
    return (
      <section className='py-12 bg-gray-50'>
        <div className='px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center mb-8'>
            <h2 className='text-3xl font-bold text-gray-900'>{title}</h2>
          </div>
          <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'>
            {Array.from({ length: maxProducts }).map((_, index) => (
              <div
                key={index}
                className='bg-white rounded-lg shadow-md p-4 animate-pulse'
              >
                <div className='w-full h-48 bg-gray-300 rounded-lg mb-4'></div>
                <div className='h-4 bg-gray-300 rounded mb-2'></div>
                <div className='h-4 bg-gray-300 rounded w-2/3 mb-4'></div>
                <div className='h-8 bg-gray-300 rounded'></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className='py-12 bg-gray-50'>
        <div className='px-4 sm:px-6 lg:px-8'>
          <div className='text-center'>
            <h2 className='text-3xl font-bold text-gray-900 mb-4'>{title}</h2>
            <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
              <p className='text-red-600'>Error loading products: {error}</p>
              <button
                onClick={() => window.location.reload()}
                className='mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700'
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (!products || products.length === 0) {
    return (
      <section className='py-12 bg-gray-50'>
        <div className='px-4 sm:px-6 lg:px-8'>
          <div className='text-center'>
            <h2 className='text-3xl font-bold text-gray-900 mb-4'>{title}</h2>
            <p className='text-gray-600'>
              No products available at the moment.
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className='py-12 bg-gray-50'>
      <div className='px-4 sm:px-6 lg:px-8'>
        {/* Header */}
        <div className='flex justify-between items-center mb-8'>
          <h2 className='text-3xl font-bold text-gray-900'>{title}</h2>
          {/* {showViewAll && (
            <Link 
              href={viewAllLink}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              View All →
            </Link>
          )} */}
        </div>

        {/* Products Grid */}

        <div className='relative'>
          <Swiper
            modules={[Navigation, Autoplay]}
            spaceBetween={24}
            navigation={{
              prevEl: '.product-prev',
              nextEl: '.product-next',
            }}
            pagination={{
              clickable: true,
              el: '.top-deals-pagination',
            }}
            autoplay={{
              delay: 5000,
              disableOnInteraction: false,
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
              },
              768: {
                slidesPerView: 3,
              },
              1024: {
                slidesPerView: 4,
              },
            }}
            slidesPerView={1}
            className='pb-4 h-full'
          >
            {products.map(product => (
              <SwiperSlide key={product.id} className='h-auto'>
                <NewProductCard
                  id={product.id}
                  title={product.title}
                  description={product.description}
                  thumbnail={product.thumbnail}
                  price={product.price || '1200'}
                  originalPrice={product.originalPrice}
                  badge={product.badge}
                />
              </SwiperSlide>
            ))}
          </Swiper>
          {/* Custom Navigation Buttons */}
          <button className='product-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-200'>
            <ChevronLeftIcon className='h-5 w-5 text-gray-600' />
          </button>
          <button className='product-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-200'>
            <ChevronRightIcon className='h-5 w-5 text-gray-600' />
          </button>
          {/* Custom Pagination */}
          <div className='product-pagination flex justify-center mt-8'></div>
        </div>
        {/* Show View All button if there are more products */}
        {showViewAll && products.length >= maxProducts && (
          <div className='text-center mt-8'>
            <Link
              href={viewAllLink}
              className='inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors'
            >
              View All {title}
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
