'use client';

// import React from 'react';
import { useState, useEffect } from 'react';
import Link from 'next/link';

interface AuthLayoutProps {
  children: React.ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  const [title, setTitle] = useState('');
  const text = 'One Store';

  useEffect(() => {
    let i = 0;
    const interval = setInterval(() => {
      setTitle(text.slice(0, i + 1));
      i++;
      if (i === text.length) clearInterval(interval);
    }, 150);
    return () => clearInterval(interval);
  }, []);

  return (
    <div
      className='min-h-screen flex flex-col justify-between bg-cover bg-center'
      style={{
        backgroundImage:
          "url('https://plus.unsplash.com/premium_photo-1681487933632-c9eda34fcaf1?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')",
      }}
    >
      {/* Centered card */}
      <div className='flex-1 flex flex-col justify-center items-center mb-10'>
        <div className='sm:w-full max-w-[650px]'>
          <div className='bg-white/90 shadow-lg rounded-lg '>{children}</div>
        </div>
      </div>

      {/* Footer always at the bottom */}
      <footer className='w-full text-center py-4 bg-white/70 backdrop-blur-md'>
        <p className='text-sm text-gray-600'>
          © {new Date().getFullYear()} ONDC Seller. All Rights Reserved.
          <br />
          <span className='text-xs text-gray-400'>Version 1.2.2</span>
        </p>
      </footer>
    </div>
  );
}
