'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';
import { useCartItemsCount, useCartStore } from '@/stores/cartStore';
import { useTenant } from '@/contexts/TenantContext';
import { getStoreName } from '@/lib/api/multi-tenant';
import MiniCart from '@/components/cart/MiniCart';
import EnhancedSearch from '@/components/search/EnhancedSearch';
import MegaMenu from '@/components/navigation/MegaMenu';
import TenantSelector from '@/components/common/TenantSelector';
// import { CustomerBreadcrumbs } from '@/components/navigation/Breadcrumbs';
import ResponsiveContainer from '@/components/layouts/ResponsiveContainer';

import {
  Bars3Icon,
  XMarkIcon,
  ShoppingCartIcon,
  UserIcon,
  HeartIcon,
  MagnifyingGlassIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';

interface CustomerLayoutProps {
  children: React.ReactNode;
}

interface NavItem {
  name: string;
  href: string;
  children?: NavItem[];
}

const navigation: NavItem[] = [
  // { name: 'Products', href: '/products' },
  { name: 'Wishlist', href: '/wishlist' },
];

const CustomerLayout = ({ children }: CustomerLayoutProps) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { totalItems } = useCart();
  const zustandCartCount = useCartItemsCount();
  const { selectedTenant } = useTenant();

  const cartId = useCartStore(state => state.cartId);
  const cartProducts = useCartStore(state => state.cartProducts);
  // Use the higher count between the two cart systems for compatibility
  const cartItemCount = Math.max(totalItems, zustandCartCount);

  // Get store name with fallback
  const storeName = getStoreName(selectedTenant);

  // Handle navigation to home page with refresh
  const handleHomeNavigation = (e: React.MouseEvent) => {
    e.preventDefault();
    console.log('Navigating to home page...');

    // If we're already on the home page, force a refresh
    if (pathname === '/') {
      window.location.reload();
    } else {
      router.push('/');
    }

    // Close mobile menu if open
    setMobileMenuOpen(false);
  };

  return (
    <div className='min-h-screen bg-white'>
      {/* Header */}
      <header className='sticky top-0 z-50 bg-white shadow-sm border-b border-gray-200'>
        <div className='px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center h-16'>
            {/* Mobile menu button */}
            <div className='flex items-center md:hidden'>
              <button
                type='button'
                className='text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500'
                onClick={() => setMobileMenuOpen(true)}
              >
                <Bars3Icon className='h-6 w-6' />
              </button>
            </div>

            {/* Logo */}
            <div className='flex-shrink-0 flex items-center mr-3'>
              <a
                href='/'
                onClick={handleHomeNavigation}
                className='flex items-center cursor-pointer'
              >
                {selectedTenant?.logo ? (
                  <img
                    src={selectedTenant.logo}
                    alt={storeName}
                    className='h-8 w-8 rounded-lg mr-3 object-cover'
                    onError={e => {
                      // Fallback to default logo if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                    }}
                  />
                ) : null}
                {!selectedTenant?.logo && (
                  <div className='h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3'>
                    <span className='text-white font-bold text-lg'>
                      {storeName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <span className='text-xl font-bold text-gray-900 hidden sm:block'>
                  {storeName}
                </span>
              </a>
            </div>

            {/* Tenant Selector - Desktop */}
            {/* <div className='hidden md:flex items-center mr-4'>
              <TenantSelector />
            </div> */}

            {/* Centered Search Bar - Desktop */}
            <div className='hidden md:flex flex-1 max-w-lg mx-8'>
              <EnhancedSearch
                placeholder='Search products, categories, brands...'
                className='w-full'
              />
            </div>

            {/* Desktop navigation */}
            <nav className='hidden md:flex space-x-8'>
              {navigation.map(item => (
                <div key={item.name} className='relative group'>
                  {item.children ? (
                    <div>
                      <button className='text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium flex items-center'>
                        {item.name}
                        <ChevronDownIcon className='ml-1 h-4 w-4' />
                      </button>

                      {/* Dropdown menu */}
                      <div className='absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50'>
                        <div className='py-1'>
                          {item.children.map(child => (
                            <Link
                              key={child.name}
                              href={child.href}
                              className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                            >
                              {child.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={`text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium ${
                        pathname === item.href ? 'text-gray-900' : ''
                      }`}
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
            </nav>

            {/* Right side icons */}
            <div className='flex items-center space-x-4'>
              {/* Mini Cart */}

              <MiniCart cartProducts={cartProducts} cartId={cartId} />

              {/* User account */}
              {isAuthenticated ? (
                <div className='relative group'>
                  <button className='flex items-center text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 rounded-full'>
                    <div className='relative'>
                      <img
                        className='h-8 w-8 rounded-full object-cover border-2 border-gray-200 hover:border-blue-300 transition-colors duration-200'
                        src={user?.avatar || '/images/avatars/default.svg'}
                        alt={user?.name || 'User'}
                        onError={e => {
                          // Fallback to default avatar if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.src = '/images/avatars/default.svg';
                        }}
                      />
                      {/* Online indicator */}
                      <div className='absolute bottom-0 right-0 h-2.5 w-2.5 bg-green-400 border-2 border-white rounded-full'></div>
                    </div>
                  </button>

                  {/* User dropdown */}
                  <div className='absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50'>
                    <div className='py-1'>
                      {/* User info header */}
                      <div className='px-4 py-2 border-b border-gray-100'>
                        <p className='text-sm font-medium text-gray-900'>
                          {user?.name || 'User'}
                        </p>
                        <p className='text-xs text-gray-500'>
                          {user?.email || '<EMAIL>'}
                        </p>
                      </div>
                      <Link
                        href='/account'
                        className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      >
                        My Account
                      </Link>
                      <Link
                        href='/orders'
                        className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      >
                        My Orders
                      </Link>
                      <Link
                        href='/wishlist'
                        className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      >
                        Wishlist
                      </Link>
                      <hr className='my-1' />
                      <Link
                        href='/auth/login'
                        className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      >
                        Logout
                      </Link>
                    </div>
                  </div>
                </div>
              ) : (
                <Link
                  href='/auth/login'
                  className='flex items-center justify-center h-8 w-8 rounded-full bg-gray-100 text-gray-400 hover:text-gray-500 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors duration-200'
                  title='Sign in to your account'
                >
                  <UserIcon className='h-5 w-5' />
                </Link>
              )}
            </div>
          </div>
        </div>

        {/* Secondary Navigation with Mega Menu */}
        <div className='border-t border-gray-100'>
          <div className='px-4 sm:px-6 lg:px-8'>
            <div className='relative flex justify-center py-3'>
              <MegaMenu key={`mega-menu-${pathname}-${user?.id || 'guest'}`} />
            </div>
          </div>
        </div>
      </header>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className='fixed inset-0 flex z-40 md:hidden'>
          <div
            className='fixed inset-0 bg-black bg-opacity-25'
            onClick={() => setMobileMenuOpen(false)}
          />
          <div className='relative flex-1 flex flex-col max-w-xs w-full bg-white'>
            <div className='absolute top-0 right-0 -mr-12 pt-2'>
              <button
                className='ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white'
                onClick={() => setMobileMenuOpen(false)}
              >
                <XMarkIcon className='h-6 w-6 text-white' />
              </button>
            </div>

            <div className='flex-1 h-0 pt-5 pb-4 overflow-y-auto'>
              <div className='flex-shrink-0 flex items-center px-4'>
                <a
                  href='/'
                  onClick={handleHomeNavigation}
                  className='flex items-center cursor-pointer'
                >
                  {selectedTenant?.logo ? (
                    <img
                      src={selectedTenant.logo}
                      alt={storeName}
                      className='h-8 w-8 rounded-lg mr-3 object-cover'
                      onError={e => {
                        // Fallback to default logo if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback =
                          target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  {!selectedTenant?.logo && (
                    <div className='h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3'>
                      <span className='text-white font-bold text-lg'>
                        {storeName.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <span className='text-2xl font-bold text-gray-900'>
                    {storeName}
                  </span>
                </a>
              </div>

              <nav className='mt-5 px-2 space-y-1'>
                {/* User Account Section for Mobile */}
                {isAuthenticated ? (
                  <div className='border-b border-gray-200 pb-4 mb-4'>
                    <div className='flex items-center px-2 py-3'>
                      <img
                        className='h-10 w-10 rounded-full object-cover border-2 border-gray-200'
                        src={user?.avatar || '/images/avatars/default.svg'}
                        alt={user?.name || 'User'}
                        onError={e => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/images/avatars/default.svg';
                        }}
                      />
                      <div className='ml-3'>
                        <p className='text-base font-medium text-gray-900'>
                          {user?.name || 'User'}
                        </p>
                        <p className='text-sm text-gray-500'>
                          {user?.email || '<EMAIL>'}
                        </p>
                      </div>
                    </div>
                    <div className='space-y-1'>
                      <Link
                        href='/account'
                        className='text-gray-600 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium'
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        My Account
                      </Link>
                      <Link
                        href='/orders'
                        className='text-gray-600 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium'
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        My Orders
                      </Link>
                      <Link
                        href='/wishlist'
                        className='text-gray-600 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium'
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Wishlist
                      </Link>
                      <button className='w-full text-left text-gray-600 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium'>
                        Logout
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className='border-b border-gray-200 pb-4 mb-4'>
                    <Link
                      href='/auth/login'
                      className='text-blue-600 hover:text-blue-700 group flex items-center px-2 py-2 text-base font-medium'
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <UserIcon className='h-5 w-5 mr-3' />
                      Sign In
                    </Link>
                  </div>
                )}

                {navigation.map(item => (
                  <div key={item.name}>
                    {item.children ? (
                      <div>
                        <div className='text-gray-500 group flex items-center px-2 py-2 text-base font-medium'>
                          {item.name}
                        </div>
                        <div className='ml-4 space-y-1'>
                          {item.children.map(child => (
                            <Link
                              key={child.name}
                              href={child.href}
                              className='text-gray-600 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium'
                              onClick={() => setMobileMenuOpen(false)}
                            >
                              {child.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        className='text-gray-600 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium'
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {item.name}
                      </Link>
                    )}
                  </div>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      <main className='flex-1'>
        <ResponsiveContainer>
          {/* Page content */}
          {children}
        </ResponsiveContainer>
      </main>

      {/* Footer */}
      <footer className='bg-gray-900 text-white'>
        <div className='max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
            {/* Company Info */}
            <div className='lg:col-span-2'>
              <div className='flex items-center mb-4'>
                {selectedTenant?.logo ? (
                  <img
                    src={selectedTenant.logo}
                    alt={storeName}
                    className='h-8 w-8 rounded-lg mr-3 object-cover'
                    onError={e => {
                      // Fallback to default logo if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                    }}
                  />
                ) : null}
                {!selectedTenant?.logo && (
                  <div className='h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3'>
                    <span className='text-white font-bold text-lg'>
                      {storeName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <span className='text-xl font-bold'>{storeName}</span>
              </div>
              <p className='text-gray-300 mb-4 max-w-md'>
                Your trusted ONDC marketplace partner offering quality products
                with exceptional customer service. Shop with confidence and
                discover amazing deals every day.
              </p>
              <div className='space-y-2 text-sm text-gray-300'>
                <p>📍 123 Commerce Street, Business District</p>
                <p>📞 +1 (555) 123-4567</p>
                <p>✉️ <EMAIL></p>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className='text-sm font-semibold text-white tracking-wider uppercase mb-4'>
                Quick Links
              </h3>
              <ul className='space-y-3'>
                <li>
                  <Link
                    href='/contact'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    Contact
                  </Link>
                </li>
                <li>
                  <Link
                    href='/privacy-policy'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href='/terms-of-service'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    href='/faq'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link
                    href='/returns'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    Returns & Refunds
                  </Link>
                </li>
              </ul>
            </div>

            {/* Account Links */}
            <div>
              <h3 className='text-sm font-semibold text-white tracking-wider uppercase mb-4'>
                My Account
              </h3>
              <ul className='space-y-3'>
                <li>
                  <Link
                    href='/auth/login'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    Login
                  </Link>
                </li>
                <li>
                  <Link
                    href='/auth/register'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    Register
                  </Link>
                </li>
                <li>
                  <Link
                    href='/account'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    My Account
                  </Link>
                </li>
                <li>
                  <Link
                    href='/orders'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    My Orders
                  </Link>
                </li>
                <li>
                  <Link
                    href='/wishlist'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    Wishlist
                  </Link>
                </li>
                <li>
                  <Link
                    href='/track-order'
                    className='text-gray-300 hover:text-white transition-colors duration-200'
                  >
                    Track Order
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Social Media & Copyright */}
          <div className='mt-12 pt-8 border-t border-gray-700'>
            <div className='flex flex-col md:flex-row justify-between items-center'>
              <div className='flex space-x-6 mb-4 md:mb-0'>
                <a
                  href='#'
                  className='text-gray-400 hover:text-white transition-colors duration-200'
                >
                  <span className='sr-only'>Facebook</span>
                  <svg
                    className='h-6 w-6'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      fillRule='evenodd'
                      d='M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z'
                      clipRule='evenodd'
                    />
                  </svg>
                </a>
                <a
                  href='#'
                  className='text-gray-400 hover:text-white transition-colors duration-200'
                >
                  <span className='sr-only'>Twitter</span>
                  <svg
                    className='h-6 w-6'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84' />
                  </svg>
                </a>
                <a
                  href='#'
                  className='text-gray-400 hover:text-white transition-colors duration-200'
                >
                  <span className='sr-only'>Instagram</span>
                  <svg
                    className='h-6 w-6'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      fillRule='evenodd'
                      d='M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718-1.297c-.875.897-2.026 1.387-3.323 1.387s-2.448-.49-3.323-1.297c-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323C9.902 8.198 11.053 7.708 12.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323z'
                      clipRule='evenodd'
                    />
                  </svg>
                </a>
                <a
                  href='#'
                  className='text-gray-400 hover:text-white transition-colors duration-200'
                >
                  <span className='sr-only'>LinkedIn</span>
                  <svg
                    className='h-6 w-6'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      fillRule='evenodd'
                      d='M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'
                      clipRule='evenodd'
                    />
                  </svg>
                </a>
              </div>
              <p className='text-gray-400 text-sm'>
                © {new Date().getFullYear()} {storeName}. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CustomerLayout;
