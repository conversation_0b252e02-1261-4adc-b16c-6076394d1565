'use client';

import React, { useState, useRef, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { ChevronDownIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { usecategories, Category } from '@/hooks/usecategories';
import { useCategoryStore } from '@/stores/categoriesStore';
import { useMedusaBackendCategories } from '@/hooks/useMedusaBackendProducts';

interface MegaMenuProps {
  className?: string;
}

interface SubCategory {
  id: string;
  name: string;
  slug: string;
  productCount: number;
  href: string;
}

interface MegaMenuCategory {
  id: string;
  name: string;
  slug: string;
  href: string;
  subcategories: SubCategory[];
}

// Helper function to convert Medusa category to MegaMenu format
const convertMedusaCategoryToMegaMenu = (
  category: Category
): MegaMenuCategory | null => {
  if (
    !Array.isArray(category?.subcategories) ||
    category.subcategories.length === 0
  ) {
    return null;
  }

  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    href: `/categories/${category.slug}`,
    subcategories: category.subcategories.map(sub => ({
      id: sub.id,
      name: sub.name,
      slug: sub.slug,
      productCount: sub.productCount || Math.floor(Math.random() * 200) + 50,
      href: `/categories/${category.slug}/${sub.slug}`,
    })),
  };
};

const MegaMenu: React.FC<MegaMenuProps> = ({ className = '' }) => {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Store functions with null checks
  const setCategories = useCategoryStore(state => state.setCategories);
  const setSelectedCategoryId = useCategoryStore(
    state => state.setSelectedCategoryId
  );
  const setSelectedSubCategoryId = useCategoryStore(
    state => state.setSelectedSubCategoryId
  );

  // Get categories from Medusa backend
  const {
    filterCategories: categories,
    loading,
    error,
    fetchAllCategories,
  } = useMedusaBackendCategories();

  // Transform categories for mega menu
  const megaMenuData: MegaMenuCategory[] = useMemo(() => {
    if (setCategories) {
      setCategories(categories);
    }
    return categories
      .map(convertMedusaCategoryToMegaMenu)
      .filter((cat): cat is MegaMenuCategory => cat !== null);
  }, [categories]);

  const handleMouseEnter = (categoryId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setActiveCategory(categoryId);
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveCategory(null);
      setIsOpen(false);
    }, 150);
  };

  const handleMenuMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleMenuMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveCategory(null);
      setIsOpen(false);
    }, 150);
  };

  useEffect(() => {
    if (fetchAllCategories) {
      fetchAllCategories();
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [fetchAllCategories]);

  const activeCategoryData = megaMenuData.find(
    cat => cat?.id === activeCategory
  );

  // Loading state
  if (loading) {
    return (
      <div className={`${className}`}>
        <div className='flex items-center justify-center space-x-8  bg-white shadow-sm'>
          {[1, 2, 3, 4, 5].map(index => (
            <div key={index} className='animate-pulse'>
              <div className='h-6 w-20 bg-gray-200 rounded'></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error && categories.length === 0) {
    return (
      <div className={`${className}`}>
        <div className='flex items-center justify-center py-4 bg-red-50'>
          <div className='text-sm text-red-600'>
            <span>Failed to load categories.</span>
            <button
              onClick={() => window.location.reload()}
              className='ml-2 text-red-700 hover:text-red-800 underline'
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={menuRef}>
      {/* Main Navigation - Simple Names Only */}
      <nav className='bg-white '>
        <div className='max-w-7xl mx-auto px-4'>
          <div className='flex items-center justify-center space-x-8 '>
            {megaMenuData.map(category => (
              <div
                key={category?.id}
                className='relative'
                onMouseEnter={() => handleMouseEnter(category?.id)}
                onMouseLeave={handleMouseLeave}
              >
                <button
                  type='button'
                  className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-all duration-200 rounded-md ${
                    activeCategory === category?.id
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                  aria-expanded={activeCategory === category?.id}
                  aria-haspopup='true'
                >
                  <span>{category?.name}</span>
                  <ChevronDownIcon
                    className={`h-4 w-4 transition-transform duration-200 ${
                      activeCategory === category?.id ? 'rotate-180' : ''
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
        </div>
      </nav>

      {/* Submenu Dropdown - Single List */}
      {isOpen && activeCategoryData && (
        <div
          className='absolute left-0 right-0 top-full bg-white border-t border-gray-200 shadow-lg z-50'
          onMouseEnter={handleMenuMouseEnter}
          onMouseLeave={handleMenuMouseLeave}
        >
          <div className='max-w-7xl mx-auto p-6'>
            {/* Category Header */}
            <div className='flex items-center justify-between mb-6 pb-4 border-b border-gray-100'>
              <h3 className='text-lg font-semibold text-gray-900'>
                {activeCategoryData.name}
              </h3>
              <Link
                href={activeCategoryData.href}
                className='text-sm text-blue-600 hover:text-blue-700 font-medium'
                onClick={() => setSelectedCategoryId(activeCategoryData.id)}
              >
                View All
              </Link>
            </div>

            {/* Subcategories Grid */}
            <div>
              {activeCategoryData.subcategories.map(subcategory => (
                <Link
                  key={subcategory.id}
                  href={subcategory.href}
                  className='group block p-2 mb-2 rounded-lg border-b border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200 bg-white '
                  onClick={() => setSelectedSubCategoryId(subcategory.id)}
                >
                  <div className='flex items-center justify-between'>
                    <div className='flex-1'>
                      <h4 className='font-medium text-gray-900 group-hover:text-blue-700 transition-colors mb-1'>
                        {subcategory.name}
                      </h4>
                      {/* <p className='text-sm text-gray-500'>
                        {subcategory.productCount.toLocaleString()} items
                      </p> */}
                    </div>
                    <ArrowRightIcon className='h-4 w-4 text-gray-400 group-hover:text-blue-600 transform group-hover:translate-x-1 transition-all duration-200' />
                  </div>
                </Link>
              ))}
            </div>

            {/* Bottom Action */}
            {/* <div className='text-center mt-6 pt-4 border-t border-gray-100'>
              <Link
                href={activeCategoryData.href}
                className='inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200'
                onClick={() => setSelectedCategoryId(activeCategoryData.id)}
              >
                Explore All {activeCategoryData.name}
                <ArrowRightIcon className='h-4 w-4 ml-2' />
              </Link>
            </div> */}
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(MegaMenu);
