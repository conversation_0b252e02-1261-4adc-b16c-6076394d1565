'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/common/ToastProvider';
import {
  Box,
  Container,
  Paper,
  Typography,
  LinearProgress,
  Button,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Card,
  CardContent,
  IconButton,
  Fade,
  Slide,
} from '@mui/material';
import {
  ArrowFor<PERSON>,
  ArrowBack,
  CheckCircle,
  Store,
  Inventory,
  CloudUpload,
  Rocket,
} from '@mui/icons-material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import StoreConfigurationStep from './StoreConfigurationStep';
import AddProductStep from './AddProductStep';
import BulkUploadStep from './BulkUploadStep';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { createStoreConfiguration } from '@/lib/api/store-config';

// Modern theme for onboarding
const onboardingTheme = createTheme({
  palette: {
    primary: {
      main: '#2563eb',
      light: '#3b82f6',
      dark: '#1d4ed8',
    },
    secondary: {
      main: '#10b981',
      light: '#34d399',
      dark: '#059669',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 700,
      fontSize: '1.875rem',
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h6: {
      fontWeight: 600,
      fontSize: '1.25rem',
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          textTransform: 'none',
          fontWeight: 600,
          padding: '12px 24px',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow:
            '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        },
      },
    },
    MuiLinearProgress: {
      styleOverrides: {
        root: {
          height: 8,
          borderRadius: 4,
        },
      },
    },
  },
});

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface StoreConfigurationData {
  // Basic Store Info
  store_name: string;
  store_description: string;
  gst_number: string;
  business_type: string;
  business_category: string;

  // Contact Info
  email: string;
  phone: string;
  website?: string;

  // Address
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;

  // Branding
  store_theme: string;
  store_logo?: File;

  // System fields
  user_id: string;
  onboarding_step: number;
  onboarding_completed: boolean;
}

interface ProductData {
  name: string;
  price: string;
  description: string;
  category: string;
  sku?: string;
  inventory_quantity: string;
}

const OnboardingWizard: React.FC = () => {
  const router = useRouter();
  const {
    user,
    updateUser,
    updateOnboardingStatus,
    completeOnboarding: completeAuthOnboarding,
  } = useAuth();
  const toast = useToast();

  // Use Zustand store
  const {
    currentStep,
    storeData,
    productData,
    bulkUploadFile,
    isLoading,
    error,
    completedSteps,
    setCurrentStep,
    updateStoreData,
    updateProductData,
    setBulkUploadFile,
    saveStoreConfiguration,
    saveProduct,
    handleBulkUpload,
    completeOnboarding,
    setError,
    // Smart navigation methods
    cacheStepData,
    hasStepDataChanged,
    setHasUnsavedChanges,
    clearStepCache,
    stepDataCache,
    hasUnsavedChanges,
    // Backend integration methods
    initializeFromBackend,
    updateBackendOnboardingStatus,
  } = useOnboardingStore();

  // Get current step data for change detection
  const getCurrentStepData = React.useCallback(() => {
    switch (currentStep) {
      case 0:
        return storeData;
      case 1:
        return productData;
      case 2:
        return { bulkUploadFile: bulkUploadFile?.name || null };
      default:
        return {};
    }
  }, [currentStep, storeData, productData, bulkUploadFile]);

  // Initialize onboarding state from backend on mount
  React.useEffect(() => {
    if (user) {
      // Initialize from backend with user's onboarding status
      initializeFromBackend(user);
    }
  }, [user, initializeFromBackend]);

  // Track changes in current step data
  React.useEffect(() => {
    const currentData = getCurrentStepData();
    const dataHasChanged = hasStepDataChanged(currentStep, currentData);
    setHasUnsavedChanges(dataHasChanged);
  }, [
    getCurrentStepData,
    currentStep,
    hasStepDataChanged,
    setHasUnsavedChanges,
  ]);

  const steps: OnboardingStep[] = [
    {
      id: 'store-configuration',
      title: 'Store Configuration',
      description: 'Set up your store details and branding',
      icon: <Store sx={{ fontSize: 28 }} />,
    },
    {
      id: 'add-product',
      title: 'Add Product',
      description: 'Add your first product to get started',
      icon: <Inventory sx={{ fontSize: 28 }} />,
    },
    {
      id: 'bulk-upload',
      title: 'Bulk Upload (Optional)',
      description: 'Upload multiple products at once',
      icon: <CloudUpload sx={{ fontSize: 28 }} />,
    },
  ];

  const progress = ((currentStep + 1) / steps.length) * 100;

  console.log(':::::::currentStep:::::::', currentStep);
  console.log(':::::::storeData:::::::', storeData);
  // Smart navigation: only save if data has changed
  const nextStep = async () => {
    if (currentStep < steps.length - 1) {
      try {
        const currentData = getCurrentStepData();
        const dataHasChanged = hasStepDataChanged(currentStep, currentData);

        console.log(`🔍 Step ${currentStep} data changed:`, dataHasChanged);

        // Only save if data has changed or no cache exists
        if (dataHasChanged) {
          console.log('💾 Saving step data due to changes...');

          // Save progress for current step and update backend
          if (currentStep === 0) {
            // Skip API save for demo purposes - just cache the data
            console.log('📝 Skipping API save for demo - caching data only');

            // Update backend onboarding status
            await updateOnboardingStatus({
              onboarding_store_config: true,
            });

            toast.success('Store configuration saved successfully!');
          } else if (currentStep === 1) {
            // Skip API save for demo purposes - just cache the data
            console.log('📝 Skipping API save for demo - caching data only');

            // Update backend onboarding status
            await updateOnboardingStatus({
              onboarding_add_product: true,
            });

            toast.success('Product added successfully!');
          }

          // Cache the data after successful save
          cacheStepData(currentStep, currentData);
          setHasUnsavedChanges(false);
        } else {
          console.log('⚡ Skipping save - no changes detected');
        }

        setCurrentStep(currentStep + 1);
      } catch (error) {
        console.error('Failed to save step:', error);
      }
    }
  };

  // Smart back navigation: cache current data before going back
  const prevStep = () => {
    if (currentStep > 0) {
      const currentData = getCurrentStepData();
      cacheStepData(currentStep, currentData);
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinish = async () => {
    try {
      // Handle bulk upload if file is selected
      await handleBulkUpload();

      // Update backend to mark bulk upload as completed
      await updateOnboardingStatus({
        onboarding_bulk_upload: true,
      });

      // Complete onboarding in both stores and backend
      await completeOnboarding();
      await completeAuthOnboarding();

      // Update user to remove isNewUser flag
      if (user) {
        updateUser({ isNewUser: false });
      }

      toast.success(
        '🎉 Onboarding completed successfully! Welcome to your admin dashboard.'
      );
      router.push('/admin?onboarding=complete');
    } catch (error) {
      console.error('Onboarding completion failed:', error);
      toast.error('Failed to complete onboarding. Please try again.');
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 0: // Store Configuration
        return (
          storeData.store_name.trim() &&
          storeData.store_description.trim() &&
          storeData.gst_number.trim() &&
          storeData.email.trim() &&
          storeData.phone.trim() &&
          storeData.address_line_1.trim() &&
          storeData.city.trim() &&
          storeData.state.trim() &&
          storeData.pincode.trim() &&
          storeData.business_type &&
          storeData.business_category
        );
      case 1: // Add Product
        return (
          productData.name.trim() &&
          productData.price.trim() &&
          productData.description.trim() &&
          productData.category.trim() &&
          productData.inventory_quantity.trim()
        );
      case 2: // Bulk Upload (Optional)
        return true; // This step is optional
      default:
        return true;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <StoreConfigurationStep
            data={storeData}
            updateData={(field, value) => updateStoreData({ [field]: value })}
          />
        );

      case 1:
        return (
          <AddProductStep
            data={productData}
            updateData={(field, value) => updateProductData({ [field]: value })}
          />
        );

      case 2:
        return (
          <BulkUploadStep file={bulkUploadFile} setFile={setBulkUploadFile} />
        );

      default:
        return null;
    }
  };

  return (
    <ThemeProvider theme={onboardingTheme}>
      <Box
        sx={{
          minHeight: '100vh',
          backgroundColor: '#f8fafc',
          py: 0,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container
          maxWidth='lg'
          sx={{ py: 4, position: 'relative', zIndex: 1 }}
        >
          {/* Header */}
          <Fade in timeout={800}>
            <Box textAlign='center' mb={6}>
              <Typography
                variant='h4'
                component='h1'
                sx={{
                  color: '#1e293b',
                  mb: 2,
                  fontWeight: 700,
                  textShadow: 'none',
                }}
              >
                Welcome to Your Store Setup
              </Typography>
              <Typography
                variant='h6'
                sx={{
                  color: '#64748b',
                  fontWeight: 400,
                  textShadow: '0 1px 2px rgba(0,0,0,0.1)',
                }}
              >
                Let's get your store ready for customers in just a few steps
              </Typography>
            </Box>
          </Fade>

          {/* Progress Bar */}
          <Slide in direction='down' timeout={1000}>
            <Card sx={{ mb: 4, overflow: 'visible' }}>
              <CardContent sx={{ p: 4 }}>
                <Box mb={3}>
                  <Box
                    display='flex'
                    justifyContent='space-between'
                    alignItems='center'
                    mb={2}
                  >
                    <Typography variant='h6' color='primary' fontWeight={600}>
                      Setup Progress
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      Step {currentStep + 1} of {steps.length}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant='determinate'
                    value={progress}
                    sx={{
                      mb: 3,
                      '& .MuiLinearProgress-bar': {
                        background:
                          'linear-gradient(90deg, #2563eb 0%, #10b981 100%)',
                      },
                    }}
                  />

                  {/* Step indicators */}
                  <Box
                    display='flex'
                    justifyContent='space-between'
                    alignItems='center'
                  >
                    {steps.map((step, index) => (
                      <Box
                        key={step.id}
                        display='flex'
                        flexDirection='column'
                        alignItems='center'
                        flex={1}
                      >
                        <Box
                          sx={{
                            width: 56,
                            height: 56,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mb: 2,
                            background:
                              index < currentStep
                                ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                                : index === currentStep
                                  ? 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)'
                                  : '#f1f5f9',
                            color: index <= currentStep ? 'white' : '#64748b',
                            boxShadow:
                              index <= currentStep
                                ? '0 4px 12px rgba(37, 99, 235, 0.3)'
                                : '0 2px 4px rgba(0, 0, 0, 0.1)',
                            transition: 'all 0.3s ease',
                          }}
                        >
                          {index < currentStep ? (
                            <CheckCircle sx={{ fontSize: 28 }} />
                          ) : (
                            step.icon
                          )}
                        </Box>
                        <Typography
                          variant='body2'
                          fontWeight={index <= currentStep ? 600 : 400}
                          color={
                            index <= currentStep ? 'primary' : 'text.secondary'
                          }
                          textAlign='center'
                          sx={{ maxWidth: 120 }}
                        >
                          {step.title}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>

                {/* Current step title and description */}
                <Box textAlign='center' mt={4}>
                  <Typography
                    variant='h5'
                    fontWeight={600}
                    color='primary'
                    mb={1}
                  >
                    {steps[currentStep].title}
                  </Typography>
                  <Typography variant='body1' color='text.secondary'>
                    {steps[currentStep].description}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Slide>

          {/* Step Content */}
          <Slide in direction='up' timeout={800}>
            <Card sx={{ mb: 4 }}>
              <CardContent sx={{ p: 4 }}>{renderStepContent()}</CardContent>
            </Card>
          </Slide>

          {/* Navigation */}
          <Box
            display='flex'
            justifyContent='space-between'
            alignItems='center'
          >
            <Button
              onClick={prevStep}
              disabled={currentStep === 0}
              variant='outlined'
              startIcon={<ArrowBack />}
              sx={{
                minWidth: 120,
                visibility: currentStep === 0 ? 'hidden' : 'visible',
              }}
            >
              Previous
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button
                onClick={handleFinish}
                disabled={isLoading}
                variant='contained'
                size='large'
                startIcon={isLoading ? null : <Rocket />}
                sx={{
                  minWidth: 160,
                  background:
                    'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                  '&:hover': {
                    background:
                      'linear-gradient(135deg, #059669 0%, #047857 100%)',
                  },
                }}
              >
                {isLoading ? (
                  <>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        border: '2px solid rgba(255,255,255,0.3)',
                        borderTop: '2px solid white',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite',
                        mr: 1,
                        '@keyframes spin': {
                          '0%': { transform: 'rotate(0deg)' },
                          '100%': { transform: 'rotate(360deg)' },
                        },
                      }}
                    />
                    Launching...
                  </>
                ) : (
                  'Launch Store'
                )}
              </Button>
            ) : (
              <Button
                onClick={nextStep}
                disabled={!isStepValid()}
                variant='contained'
                size='large'
                endIcon={<ArrowForward />}
                sx={{ minWidth: 120 }}
              >
                Next
              </Button>
            )}
          </Box>
        </Container>
      </Box>
    </ThemeProvider>
  );
};

export default OnboardingWizard;
