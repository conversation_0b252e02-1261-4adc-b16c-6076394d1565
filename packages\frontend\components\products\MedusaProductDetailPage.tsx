'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { useMedusaBackendProduct } from '@/hooks/useMedusaBackendProducts';
import { useMedusaCartContext } from '@/hooks/useMedusaCart';
import { MedusaProduct, MedusaProductVariant } from '@/lib/medusa-backend-api';
import { useCartStore, useHydratedCartStore } from '@/stores/cartStore';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Stack,
  Grid,
  Chip,
  Divider,
  Paper,
  Tabs,
  Tab,
  Rating,
  Avatar,
  Card,
  CardContent,
} from '@mui/material';
import {
  ShoppingCartIcon,
  HeartIcon,
  ShareIcon,
  StarIcon,
  CheckCircleIcon,
  TruckIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  PhotoIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

import { useToast } from '@/components/common/ToastProvider';
import ProductOptionsSelector, {
  ProductOption,
  ProductVariant,
} from '@/components/ui/ProductOptionsSelector';
import { useProductOptions } from '@/hooks/useProductOptions';
import StickyMobileCart from '@/components/ui/StickyMobileCart';
import RichTextRenderer from '@/components/ui/RichTextRenderer';
import RelatedProductsCarousel from '@/components/ui/RelatedProductsCarousel';
import { useCategoryStore } from '@/stores/categoriesStore';
import { formatCurrency } from '@/utils/formatCurrency';
import { convertToHtml } from '@/utils/utils';
import { useRouter } from 'next/navigation';
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`product-tabpanel-${index}`}
      aria-labelledby={`product-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

interface MedusaProductDetailPageProps {
  productId: string;
  categoryId: string;
  subCategoryId: string;
  collectionData?: any;
}

// Mock data for demonstration
const mockProductDetails = {
  overview: `
    <h3>Product Overview</h3>
    <p>Experience the perfect blend of style and functionality with this premium product. Crafted with attention to detail and built to last, this item represents the pinnacle of quality in its category.</p>
    <ul>
      <li>Premium materials for durability</li>
      <li>Ergonomic design for comfort</li>
      <li>Advanced features for enhanced performance</li>
      <li>Eco-friendly manufacturing process</li>
    </ul>
  `,
  features: `
    <h3>Key Features</h3>
    <ul>
      <li><strong>Advanced Technology:</strong> Cutting-edge innovation for superior performance</li>
      <li><strong>Durable Construction:</strong> Built to withstand daily use</li>
      <li><strong>User-Friendly Design:</strong> Intuitive interface for easy operation</li>
      <li><strong>Energy Efficient:</strong> Reduced power consumption</li>
      <li><strong>Versatile Usage:</strong> Suitable for various applications</li>
    </ul>
  `,
  specifications: `
    <h3>Technical Specifications</h3>
    <table style="width: 100%; border-collapse: collapse;">
      <tr style="border-bottom: 1px solid #e5e7eb;">
        <td style="padding: 8px; font-weight: 600;">Brand</td>
        <td style="padding: 8px;">Premium Brand</td>
      </tr>
      <tr style="border-bottom: 1px solid #e5e7eb;">
        <td style="padding: 8px; font-weight: 600;">Model</td>
        <td style="padding: 8px;">PB-2024-Pro</td>
      </tr>
      <tr style="border-bottom: 1px solid #e5e7eb;">
        <td style="padding: 8px; font-weight: 600;">Weight</td>
        <td style="padding: 8px;">2.5 kg</td>
      </tr>
      <tr style="border-bottom: 1px solid #e5e7eb;">
        <td style="padding: 8px; font-weight: 600;">Warranty</td>
        <td style="padding: 8px;">2 Years Comprehensive</td>
      </tr>
      <tr style="border-bottom: 1px solid #e5e7eb;">
        <td style="padding: 8px; font-weight: 600;">Certification</td>
        <td style="padding: 8px;">ISO 9001, CE Certified</td>
      </tr>
    </table>
  `,
  reviews: [
    {
      id: 1,
      user: 'John Doe',
      rating: 5,
      comment:
        'Excellent product! Exactly what I was looking for. Great quality and fast delivery.',
      date: '2024-01-15',
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
    },
    {
      id: 2,
      user: 'Sarah Johnson',
      rating: 4,
      comment:
        'Very good quality product. Would recommend to others. Minor issue with packaging but product itself is great.',
      date: '2024-01-10',
      avatar:
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face',
    },
    {
      id: 3,
      user: 'Mike Chen',
      rating: 5,
      comment:
        'Outstanding! Exceeded my expectations. The build quality is superb and it works perfectly.',
      date: '2024-01-08',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
    },
  ],
};

const MedusaProductDetailPag = ({
  productId,
  categoryId,
  subCategoryId,
  collectionData,
}: MedusaProductDetailPageProps) => {
  const router = useRouter();
  const { product, loading, error, refetch } =
    useMedusaBackendProduct(productId);
  const { addItem, isAddingItem } = useMedusaCartContext();

  const [selectedVariant, setSelectedVariant] =
    useState<MedusaProductVariant | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAdding, setIsAdding] = useState(false);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);

  const toast = useToast();
  const cartProduct = useCategoryStore(state => state.cartProduct);

  // Transform Medusa product data to ProductOptionsSelector format
  const transformedOptions = useMemo((): ProductOption[] => {
    if (!product?.options || product.options.length < 1) return [];

    return product.options.map(option => ({
      id: option.id,
      name: option.title,
      type: option.title.toLowerCase().includes('color')
        ? 'color'
        : option.title.toLowerCase().includes('size')
          ? 'size'
          : 'text',
      values: option.values.map(value => ({
        id: value.id,
        value: value.value,
        label: value.value,
        available: true,
      })),
      required: true,
    }));
  }, [product?.options]);

  const transformedVariants = useMemo((): ProductVariant[] => {
    if (!product?.variants) return [];

    return product.variants.map(variant => ({
      id: variant.id,
      title: variant.title,
      sku: variant.sku,
      price: variant.prices?.[0]?.amount || 0,
      compare_at_price: variant.original_price,
      available: variant.inventory_quantity > 0,
      inventory_quantity: variant.inventory_quantity,
      options:
        variant.options?.reduce(
          (acc, opt) => {
            acc[opt.option_id] = opt.value_id;
            return acc;
          },
          {} as Record<string, string>
        ) || {},
    }));
  }, [product?.variants]);

  // Use the product options hook
  const {
    selectedOptions,
    selectedVariant: optionsSelectedVariant,
    isValidSelection,
    hasRequiredOptions,
    setSelectedOptions,
  } = useProductOptions({
    options: transformedOptions,
    variants: transformedVariants,
    defaultVariant: transformedVariants[0],
  });

  // Set default variant and image when product loads
  useEffect(() => {
    if (product) {
      if (product.variants && product.variants.length > 0) {
        setSelectedVariant(product.variants[0]);
      }
      if (product.thumbnail) {
        setSelectedImage(product.thumbnail);
      } else if (product.images && product.images.length > 0) {
        setSelectedImage(product.images[0].url);
      }
    }
  }, [product]);

  // Sync selected variant with options selector
  useEffect(() => {
    if (optionsSelectedVariant) {
      const medusaVariant = product?.variants?.find(
        v => v.id === optionsSelectedVariant.id
      );
      if (medusaVariant) {
        setSelectedVariant(medusaVariant);
      }
    }
  }, [optionsSelectedVariant, product?.variants]);

  const handleAddToCart = async () => {
    try {
      if (updateButtonstatus()) return router.push('/cart');

      // Check if selectedVariant is available before adding to cart
      if (!selectedVariant?.id) {
        toast.error('Please select a product variant first.');
        return;
      }

      setIsAdding(true);
      const response = await addItem(selectedVariant.id, quantity);
      if (response?.cart?.id) {
        toast.success(`Added ${quantity} ${product?.title} to cart!`);
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart. Please try again.');
    } finally {
      setIsAdding(false);
    }
  };

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.title,
          text: product?.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Product link copied to clipboard!');
    }
  };

  const handleImageNavigation = (direction: 'prev' | 'next') => {
    if (!product?.images?.length) return;

    const newIndex =
      direction === 'prev'
        ? (currentImageIndex - 1 + product.images.length) %
          product.images.length
        : (currentImageIndex + 1) % product.images.length;

    setCurrentImageIndex(newIndex);
    setSelectedImage(product.images[newIndex].url);
  };

  const formatPrice = (variant: MedusaProductVariant) => {
    if (variant.prices && variant.prices.length > 0) {
      const price = variant.prices[0];
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(price.amount);
    }
    return 'Price not available';
  };

  const getBreadcrumbs = () => {};

  const updateButtonstatus = () => {
    try {
      // Check if required data is available before comparison
      if (!selectedVariant?.id || !product?.id || !cartProduct?.items) {
        return false;
      }

      return cartProduct.items.some(
        cartItem =>
          cartItem.variant_id === selectedVariant.id &&
          cartItem.product_id === product.id
      );
    } catch (error) {
      console.error('error:::::', { error });
      return false;
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const breadcrumbs = useMemo(() => {
    const breadcrumbs = [{ name: 'Home', href: '/' }];
    if (product?.categories && product?.categories.length > 0) {
      const productCategory = product?.categories.find(
        data => String(data.id) === String(categoryId)
      );
      if (productCategory) {
        breadcrumbs.push({
          name: productCategory.name,
          href: `/categories/${productCategory.handle}`,
        });
      }
      const productSubCategory = product?.categories.find(
        data => String(data.id) === String(subCategoryId)
      );
      if (productSubCategory && productCategory) {
        breadcrumbs.push({
          name: productSubCategory.name,
          href: `/categories/${productCategory?.handle}/${productSubCategory.handle}`,
        });
      }
    }

    if (collectionData && collectionData[0]?.collection_id) {
      breadcrumbs.push({
        name: collectionData[0].collection_name,
        href: `/deals?type=${collectionData[0].collection_slug}`,
      });
    }
    console.log('product::::::::::::', product);
    if (product) {
      breadcrumbs.push({
        name: product.title,
        href: `/products/${product.id}`,
      });
    }

    return breadcrumbs;
  }, [product, collectionData, categoryId, subCategoryId]);

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
          <div className='animate-pulse'>
            {/* Breadcrumb skeleton */}
            <div className='h-4 bg-gray-300 rounded w-1/3 mb-8'></div>

            <div className='grid grid-cols-1 lg:grid-cols-2 gap-12'>
              {/* Image skeleton */}
              <div className='space-y-4'>
                <div className='w-full h-[500px] bg-gray-300 rounded-lg'></div>
                <div className='flex space-x-2'>
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div
                      key={i}
                      className='w-20 h-20 bg-gray-300 rounded'
                    ></div>
                  ))}
                </div>
              </div>

              {/* Content skeleton */}
              <div className='space-y-6'>
                <div className='h-8 bg-gray-300 rounded w-3/4'></div>
                <div className='h-6 bg-gray-300 rounded w-1/2'></div>
                <div className='h-4 bg-gray-300 rounded w-full'></div>
                <div className='h-4 bg-gray-300 rounded w-5/6'></div>
                <div className='h-12 bg-gray-300 rounded w-full'></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
          <div className='text-center'>
            <h1 className='text-2xl font-bold text-gray-900 mb-4'>
              Error Loading Product
            </h1>
            <div className='bg-red-50 border border-red-200 rounded-lg p-4 mb-4'>
              <p className='text-red-600'>{error}</p>
            </div>
            <button
              onClick={refetch}
              className='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors'
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
          <div className='text-center'>
            <h1 className='text-2xl font-bold text-gray-900 mb-4'>
              Product Not Found
            </h1>
            <p className='text-gray-600 mb-4'>
              The product you're looking for doesn't exist.
            </p>
            <Link
              href='/products'
              className='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors'
            >
              Browse Products
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        {/* Breadcrumbs */}
        <nav className='flex mb-8' aria-label='Breadcrumb'>
          <ol className='flex items-center space-x-2 text-sm'>
            {breadcrumbs.map((breadcrumb, index) => (
              <li key={breadcrumb.href} className='flex items-center'>
                {index > 0 && (
                  <svg
                    className='w-4 h-4 text-gray-400 mx-2'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path
                      fillRule='evenodd'
                      d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z'
                      clipRule='evenodd'
                    />
                  </svg>
                )}
                {index === breadcrumbs.length - 1 ? (
                  <span className='text-gray-500 font-medium'>
                    {breadcrumb.name}
                  </span>
                ) : (
                  <Link
                    href={breadcrumb.href}
                    className='text-blue-600 hover:text-blue-800 transition-colors'
                  >
                    {breadcrumb.name}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </nav>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-12'>
          {/* Product Images */}
          <div className='space-y-4'>
            {/* Main Image */}
            <div className='relative group'>
              <div className='w-full h-[500px] bg-white rounded-lg overflow-hidden shadow-lg'>
                {selectedImage ? (
                  <img
                    src={selectedImage}
                    alt={product.title}
                    className='w-full h-full object-cover cursor-zoom-in'
                    onClick={() => setIsImageModalOpen(true)}
                  />
                ) : (
                  <div className='w-full h-full flex items-center justify-center text-gray-400'>
                    <PhotoIcon className='w-16 h-16' />
                    <span className='ml-2'>No Image Available</span>
                  </div>
                )}
              </div>

              {/* Image Navigation */}
              {product.images && product.images.length > 1 && (
                <>
                  <button
                    onClick={() => handleImageNavigation('prev')}
                    className='absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity'
                  >
                    <ChevronLeftIcon className='w-6 h-6 text-gray-600' />
                  </button>
                  <button
                    onClick={() => handleImageNavigation('next')}
                    className='absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity'
                  >
                    <ChevronRightIcon className='w-6 h-6 text-gray-600' />
                  </button>
                </>
              )}
            </div>

            {/* Thumbnail Images */}
            {product.images && product.images.length > 1 && (
              <div className='flex space-x-2 overflow-x-auto pb-2'>
                {product.images.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => {
                      setSelectedImage(image.url);
                      setCurrentImageIndex(index);
                    }}
                    className={`flex-shrink-0 w-20 h-20 rounded-lg border-2 overflow-hidden transition-all ${
                      selectedImage === image.url
                        ? 'border-blue-500 ring-2 ring-blue-200'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <img
                      src={image.url}
                      alt={`${product.title} ${index + 1}`}
                      className='w-full h-full object-cover'
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className='space-y-6'>
            <div>
              <h1 className='text-3xl font-bold text-gray-900 mb-2'>
                {product.title}
              </h1>

              {/* Rating and Reviews */}
              <div className='flex items-center space-x-4 mb-4'>
                <div className='flex items-center'>
                  <Rating value={4.5} readOnly precision={0.5} size='small' />
                  <span className='ml-2 text-sm text-gray-600'>(4.5)</span>
                </div>
                <span className='text-sm text-gray-500'>142 Reviews</span>
              </div>

              {/* Price */}
              {selectedVariant && (
                <div className='flex items-center space-x-3 mb-4'>
                  <span className='text-3xl font-bold text-green-600'>
                    {formatCurrency(selectedVariant.metadata.sale_price)}
                  </span>
                  {selectedVariant.metadata.original_price && (
                    <>
                      <span className='text-xl text-gray-500 line-through'>
                        {formatCurrency(
                          selectedVariant.metadata.original_price
                        )}
                      </span>
                      <Chip
                        label={`${Math.round(
                          ((selectedVariant.metadata.original_price -
                            selectedVariant.metadata.sale_price) /
                            selectedVariant.metadata.original_price) *
                            100
                        )} % OFF`}
                        color='error'
                        size='small'
                      />
                    </>
                  )}
                </div>
              )}

              {/* Stock Status */}
              <div className='flex items-center space-x-2 mb-4'>
                {selectedVariant?.metadata.product_quantity > 0 ? (
                  <>
                    <CheckCircleIcon className='w-5 h-5 text-green-500' />
                    <span className='text-sm text-green-600 font-medium'>
                      In Stock ({selectedVariant?.metadata.product_quantity}
                      available)
                    </span>
                  </>
                ) : (
                  <>
                    <XMarkIcon className='w-5 h-5 text-red-500' />
                    <span className='text-sm text-red-600 font-medium'>
                      Out of Stock
                    </span>
                  </>
                )}
              </div>
            </div>

            {/* Short Description */}
            {product.description && (
              <div className='prose prose-sm max-w-none'>
                <p className='text-gray-700 leading-relaxed'>
                  {product.description.length > 200
                    ? `${product.description.substring(0, 200)}...`
                    : product.description}
                </p>
              </div>
            )}

            {/* Trust Badges */}
            <div className='grid grid-cols-3 gap-4 py-4 border-t border-b border-gray-200'>
              <div className='flex items-center space-x-2'>
                <TruckIcon className='w-5 h-5 text-blue-600' />
                <span className='text-sm text-gray-700'>Free Delivery</span>
              </div>
              <div className='flex items-center space-x-2'>
                <ShieldCheckIcon className='w-5 h-5 text-green-600' />
                <span className='text-sm text-gray-700'>2 Year Warranty</span>
              </div>
              <div className='flex items-center space-x-2'>
                <CurrencyDollarIcon className='w-5 h-5 text-orange-600' />
                <span className='text-sm text-gray-700'>Best Price</span>
              </div>
            </div>

            {/* Variant and Quantity Selection */}
            <div className='space-y-4'>
              <Grid container spacing={2}>
                {/* Variant Selection */}
                <Grid item size={{ xs: 12, sm: 8 }}>
                  <FormControl fullWidth variant='outlined'>
                    <InputLabel id='variant-select-label'>
                      Select Variant
                    </InputLabel>
                    <Select
                      labelId='variant-select-label'
                      value={selectedVariant?.id || ''}
                      onChange={e => {
                        const selectVariant = product.variants.find(
                          v => v.id === e.target.value
                        );
                        setSelectedVariant(selectVariant);
                      }}
                      label='Select Variant'
                      MenuProps={{
                        anchorOrigin: {
                          vertical: 'bottom',
                          horizontal: 'left',
                        },
                        transformOrigin: {
                          vertical: 'top',
                          horizontal: 'left',
                        },
                        getContentAnchorEl: null,
                        PaperProps: { style: { maxHeight: 115 } },
                      }}
                    >
                      {product.variants.map(variant => (
                        <MenuItem key={variant.id} value={variant.id}>
                          {variant.title} - {formatPrice(variant)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Quantity Selection */}
                <Grid item size={{ xs: 12, sm: 4 }}>
                  <FormControl fullWidth variant='outlined'>
                    <InputLabel id='quantity-select-label'>Quantity</InputLabel>
                    <Select
                      id='quantity-select'
                      labelId='quantity-select-label'
                      label='Quantity'
                      MenuProps={{
                        anchorOrigin: {
                          vertical: 'bottom',
                          horizontal: 'left',
                        },
                        transformOrigin: {
                          vertical: 'top',
                          horizontal: 'left',
                        },
                        getContentAnchorEl: null,
                        PaperProps: { style: { maxHeight: 115 } },
                      }}
                      value={quantity}
                      onChange={e => setQuantity(Number(e.target.value))}
                    >
                      {Array.from({ length: 10 }, (_, i) => i + 1).map(num => (
                        <MenuItem key={num} value={num}>
                          {num}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </div>

            {/* Action Buttons */}
            <div className='flex flex-col sm:flex-row gap-4'>
              <button
                onClick={handleAddToCart}
                disabled={
                  isAdding ||
                  isAddingItem ||
                  !selectedVariant ||
                  selectedVariant.inventory_quantity === 0
                }
                className='flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2'
              >
                <ShoppingCartIcon className='w-5 h-5' />
                <span>
                  {isAdding || isAddingItem
                    ? 'Adding to Cart...'
                    : updateButtonstatus()
                      ? 'View Cart'
                      : 'Add to Cart'}
                </span>
              </button>

              <button
                onClick={handleWishlist}
                className='px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2'
              >
                {isWishlisted ? (
                  <HeartIconSolid className='w-5 h-5 text-red-500' />
                ) : (
                  <HeartIcon className='w-5 h-5 text-gray-600' />
                )}
                <span className='text-gray-700'>Wishlist</span>
              </button>

              <button
                onClick={handleShare}
                className='px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2'
              >
                <ShareIcon className='w-5 h-5 text-gray-600' />
                <span className='text-gray-700'>Share</span>
              </button>
            </div>

            {/* Product Details Summary */}
            {selectedVariant && (
              <Paper elevation={1} className='p-4 bg-gray-50'>
                <h3 className='text-lg font-semibold text-gray-900 mb-3'>
                  Product Summary
                </h3>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  {selectedVariant.sku && (
                    <div>
                      <span className='font-medium text-gray-900'>SKU:</span>
                      <span className='text-gray-600 ml-2'>
                        {selectedVariant.sku}
                      </span>
                    </div>
                  )}
                  <div>
                    <span className='font-medium text-gray-900'>Status:</span>
                    <span className='text-gray-600 ml-2'>
                      {product?.status}
                    </span>
                  </div>
                  <div>
                    <span className='font-medium text-gray-900'>Category:</span>
                    <span className='text-gray-600 ml-2'>
                      {product?.categories?.[0]?.name || 'Uncategorized'}
                    </span>
                  </div>
                  <div>
                    <span className='font-medium text-gray-900'>Brand:</span>
                    <span className='text-gray-600 ml-2'>Premium Brand</span>
                  </div>
                </div>
              </Paper>
            )}
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className='mt-16'>
          <Paper elevation={1}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label='product details tabs'
              variant='scrollable'
              scrollButtons='auto'
            >
              <Tab label='Overview' />
              <Tab label='Features' />
              <Tab label='Specifications' />
              <Tab label='Reviews' />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              <div className='prose prose-lg max-w-none'>
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      convertToHtml(
                        product?.metadata?.additional_data?.product_overview
                      ) || mockProductDetails.overview,
                  }}
                />
              </div>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <div className='prose prose-lg max-w-none'>
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      convertToHtml(
                        product?.metadata?.additional_data?.product_features
                      ) || mockProductDetails.features,
                  }}
                />
              </div>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <div className='prose prose-lg max-w-none'>
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      convertToHtml(
                        product?.metadata?.additional_data
                          ?.product_specifications
                      ) || mockProductDetails.specifications,
                  }}
                />
              </div>
            </TabPanel>

            <TabPanel value={tabValue} index={3}>
              <div className='space-y-6'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-xl font-semibold'>Customer Reviews</h3>
                  <div className='flex items-center space-x-2'>
                    <Rating value={4.5} readOnly precision={0.5} />
                    <span className='text-sm text-gray-600'>4.5 out of 5</span>
                  </div>
                </div>

                <div className='space-y-4'>
                  {mockProductDetails.reviews.map(review => (
                    <Card key={review.id} variant='outlined'>
                      <CardContent>
                        <div className='flex items-start space-x-4'>
                          <Avatar
                            src={review.avatar}
                            alt={review.user}
                            sx={{ width: 48, height: 48 }}
                          />
                          <div className='flex-1'>
                            <div className='flex items-center justify-between mb-2'>
                              <h4 className='font-semibold text-gray-900'>
                                {review.user}
                              </h4>
                              <span className='text-sm text-gray-500'>
                                {review.date}
                              </span>
                            </div>
                            <Rating
                              value={review.rating}
                              readOnly
                              size='small'
                            />
                            <p className='text-gray-700 mt-2'>
                              {review.comment}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabPanel>
          </Paper>
        </div>
      </div>

      {/* Image Modal */}
      {isImageModalOpen && (
        <div className='fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4'>
          <div className='relative max-w-4xl max-h-full'>
            <button
              onClick={() => setIsImageModalOpen(false)}
              className='absolute top-4 right-4 bg-white/20 hover:bg-white/30 rounded-full p-2 z-10'
            >
              <XMarkIcon className='w-6 h-6 text-white' />
            </button>
            <img
              src={selectedImage}
              alt={product.title}
              className='max-w-full max-h-full object-contain'
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default MedusaProductDetailPag;
