'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';

export interface CarouselProps {
  children: React.ReactNode[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  infinite?: boolean;
  slidesToShow?: number;
  slidesToScroll?: number;
  responsive?: {
    breakpoint: number;
    settings: {
      slidesToShow?: number;
      slidesToScroll?: number;
    };
  }[];
  className?: string;
  itemClassName?: string;
}

const Carousel: React.FC<CarouselProps> = ({
  children,
  autoPlay = false,
  autoPlayInterval = 3000,
  showDots = true,
  showArrows = true,
  infinite = true,
  slidesToShow = 1,
  slidesToScroll = 1,
  responsive = [],
  className,
  itemClassName,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentSlidesToShow, setCurrentSlidesToShow] = useState(slidesToShow);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const totalSlides = children.length;
  const maxIndex = Math.max(0, totalSlides - currentSlidesToShow);

  // Handle responsive settings
  useEffect(() => {
    const handleResize = () => {
      const windowWidth = window.innerWidth;
      let newSlidesToShow = slidesToShow;

      for (const breakpoint of responsive) {
        if (windowWidth <= breakpoint.breakpoint) {
          newSlidesToShow = breakpoint.settings.slidesToShow || slidesToShow;
          break;
        }
      }

      setCurrentSlidesToShow(newSlidesToShow);

      // Adjust current index if needed
      const newMaxIndex = Math.max(0, totalSlides - newSlidesToShow);
      if (currentIndex > newMaxIndex) {
        setCurrentIndex(newMaxIndex);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [responsive, slidesToShow, currentIndex, totalSlides]);

  // Auto play functionality
  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      goToNext();
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, currentIndex, maxIndex, infinite]);

  const goToNext = useCallback(() => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setTimeout(() => setIsTransitioning(false), 300);

    if (currentIndex >= maxIndex) {
      if (infinite) {
        setCurrentIndex(0);
      }
    } else {
      setCurrentIndex(prev => Math.min(prev + slidesToScroll, maxIndex));
    }
  }, [currentIndex, maxIndex, infinite, slidesToScroll, isTransitioning]);

  const goToPrev = useCallback(() => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setTimeout(() => setIsTransitioning(false), 300);

    if (currentIndex <= 0) {
      if (infinite) {
        setCurrentIndex(maxIndex);
      }
    } else {
      setCurrentIndex(prev => Math.max(prev - slidesToScroll, 0));
    }
  }, [currentIndex, maxIndex, infinite, slidesToScroll, isTransitioning]);

  const goToSlide = useCallback(
    (index: number) => {
      if (isTransitioning) return;

      setIsTransitioning(true);
      setTimeout(() => setIsTransitioning(false), 300);

      setCurrentIndex(Math.max(0, Math.min(index, maxIndex)));
    },
    [maxIndex, isTransitioning]
  );

  const slideWidth = 100 / currentSlidesToShow;
  const translateX = -(currentIndex * slideWidth);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Main carousel container */}
      <div className='relative'>
        <div
          className='flex transition-transform duration-300 ease-in-out'
          style={{ transform: `translateX(${translateX}%)` }}
        >
          {children.map((child, index) => (
            <div
              key={index}
              className={cn('flex-shrink-0', itemClassName)}
              style={{ width: `${slideWidth}%` }}
            >
              {child}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation arrows */}
      {showArrows && totalSlides > currentSlidesToShow && (
        <>
          <button
            onClick={goToPrev}
            disabled={!infinite && currentIndex === 0}
            className={cn(
              'h-[70px] rounded-[4px] left-0 absolute top-1/2 -translate-y-1/2 z-10 p-2 bg-white shadow-lg hover:bg-gray-50 transition-colors',
              !infinite && currentIndex === 0 && 'opacity-50 cursor-not-allowed'
            )}
          >
            <svg
              className='w-5 h-5 text-gray-600'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M15 19l-7-7 7-7'
              />
            </svg>
          </button>

          <button
            onClick={goToNext}
            disabled={!infinite && currentIndex >= maxIndex}
            className={cn(
              'h-[70px] rounded-[4px] right-0 absolute top-1/2 -translate-y-1/2 z-10 p-2 bg-white shadow-lg hover:bg-gray-50 transition-colors',
              !infinite &&
                currentIndex >= maxIndex &&
                'opacity-50 cursor-not-allowed'
            )}
          >
            <svg
              className='w-5 h-5 text-gray-600'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 5l7 7-7 7'
              />
            </svg>
          </button>
        </>
      )}

      {/* Dots indicator */}
      {showDots && totalSlides > currentSlidesToShow && (
        <div className='flex justify-center space-x-2 mt-4'>
          {Array.from({ length: maxIndex + 1 }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={cn(
                'w-2 h-2 rounded-full transition-colors',
                index === currentIndex
                  ? 'bg-blue-600'
                  : 'bg-gray-300 hover:bg-gray-400'
              )}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Carousel;
