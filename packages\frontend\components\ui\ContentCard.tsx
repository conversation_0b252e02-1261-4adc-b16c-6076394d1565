import React from 'react';

interface ContentCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'bordered';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
}

const ContentCard: React.FC<ContentCardProps> = ({
  children,
  className = '',
  variant = 'default',
  padding = 'lg',
}) => {
  const variantClasses = {
    default: 'bg-white shadow-lg border border-gray-100',
    elevated:
      'bg-white shadow-xl border border-gray-100 hover:shadow-2xl transition-shadow duration-300',
    bordered:
      'bg-white border-2 border-blue-100 hover:border-blue-200 transition-colors duration-300',
  };

  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };

  return (
    <div
      className={`
      ${variantClasses[variant]}
      ${paddingClasses[padding]}
      rounded-xl
      ${className}
    `}
    >
      {children}
    </div>
  );
};

export default ContentCard;
