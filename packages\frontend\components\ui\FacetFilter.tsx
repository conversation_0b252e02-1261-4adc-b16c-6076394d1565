'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import Button from './Button';

export interface FilterOption {
  id: string;
  label: string;
  value: string;
  count?: number;
  disabled?: boolean;
}

export interface FilterGroup {
  id: string;
  title: string;
  type: 'checkbox' | 'radio' | 'range' | 'color' | 'size';
  options?: FilterOption[];
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface FacetFilterProps {
  groups: FilterGroup[];
  selectedFilters: Record<string, string[]>;
  onFilterChange: (groupId: string, values: string[]) => void;
  onClearAll: () => void;
  className?: string;
  showClearAll?: boolean;
  isLoading?: boolean;
}

const FacetFilter: React.FC<FacetFilterProps> = ({
  groups,
  selectedFilters,
  onFilterChange,
  onClearAll,
  className,
  showClearAll = true,
  isLoading = false,
}) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
    new Set(groups.filter(group => group.defaultExpanded !== false).map(group => group.id))
  );

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const handleOptionChange = (groupId: string, optionValue: string, type: string) => {
    const currentValues = selectedFilters[groupId] || [];
    
    if (type === 'radio') {
      onFilterChange(groupId, [optionValue]);
    } else {
      const newValues = currentValues.includes(optionValue)
        ? currentValues.filter(v => v !== optionValue)
        : [...currentValues, optionValue];
      onFilterChange(groupId, newValues);
    }
  };

  const handleRangeChange = (groupId: string, min: number, max: number) => {
    onFilterChange(groupId, [`${min}-${max}`]);
  };

  const getTotalSelectedCount = () => {
    return Object.values(selectedFilters).reduce((total, values) => total + values.length, 0);
  };

  const renderCheckboxGroup = (group: FilterGroup) => (
    <div className="space-y-2">
      {group.options?.map((option) => {
        const isSelected = selectedFilters[group.id]?.includes(option.value) || false;
        return (
          <label
            key={option.id}
            className={cn(
              'flex items-center space-x-2 cursor-pointer',
              option.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => handleOptionChange(group.id, option.value, group.type)}
              disabled={option.disabled || isLoading}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700 flex-1">{option.label}</span>
            {option.count !== undefined && (
              <span className="text-xs text-gray-500">({option.count})</span>
            )}
          </label>
        );
      })}
    </div>
  );

  const renderRadioGroup = (group: FilterGroup) => (
    <div className="space-y-2">
      {group.options?.map((option) => {
        const isSelected = selectedFilters[group.id]?.includes(option.value) || false;
        return (
          <label
            key={option.id}
            className={cn(
              'flex items-center space-x-2 cursor-pointer',
              option.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            <input
              type="radio"
              name={group.id}
              checked={isSelected}
              onChange={() => handleOptionChange(group.id, option.value, group.type)}
              disabled={option.disabled || isLoading}
              className="border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700 flex-1">{option.label}</span>
            {option.count !== undefined && (
              <span className="text-xs text-gray-500">({option.count})</span>
            )}
          </label>
        );
      })}
    </div>
  );

  const renderColorGroup = (group: FilterGroup) => (
    <div className="grid grid-cols-6 gap-2">
      {group.options?.map((option) => {
        const isSelected = selectedFilters[group.id]?.includes(option.value) || false;
        return (
          <button
            key={option.id}
            onClick={() => handleOptionChange(group.id, option.value, group.type)}
            disabled={option.disabled || isLoading}
            className={cn(
              'w-8 h-8 rounded-full border-2 transition-all',
              isSelected ? 'border-gray-900 scale-110' : 'border-gray-300',
              option.disabled && 'opacity-50 cursor-not-allowed'
            )}
            style={{ backgroundColor: option.value }}
            title={option.label}
          />
        );
      })}
    </div>
  );

  const renderSizeGroup = (group: FilterGroup) => (
    <div className="grid grid-cols-4 gap-2">
      {group.options?.map((option) => {
        const isSelected = selectedFilters[group.id]?.includes(option.value) || false;
        return (
          <button
            key={option.id}
            onClick={() => handleOptionChange(group.id, option.value, group.type)}
            disabled={option.disabled || isLoading}
            className={cn(
              'px-3 py-2 text-sm border rounded-md transition-colors',
              isSelected
                ? 'border-blue-600 bg-blue-50 text-blue-600'
                : 'border-gray-300 text-gray-700 hover:border-gray-400',
              option.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            {option.label}
          </button>
        );
      })}
    </div>
  );

  const renderRangeGroup = (group: FilterGroup) => {
    const currentRange = selectedFilters[group.id]?.[0]?.split('-') || [group.min, group.max];
    const [minValue, maxValue] = currentRange.map(Number);

    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <label className="block text-xs text-gray-500 mb-1">Min</label>
            <input
              type="number"
              min={group.min}
              max={group.max}
              step={group.step || 1}
              value={minValue}
              onChange={(e) => handleRangeChange(group.id, Number(e.target.value), maxValue)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>
          <div className="flex-1">
            <label className="block text-xs text-gray-500 mb-1">Max</label>
            <input
              type="number"
              min={group.min}
              max={group.max}
              step={group.step || 1}
              value={maxValue}
              onChange={(e) => handleRangeChange(group.id, minValue, Number(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>
        </div>
        {group.unit && (
          <p className="text-xs text-gray-500">
            Range: {minValue} - {maxValue} {group.unit}
          </p>
        )}
      </div>
    );
  };

  const renderGroupContent = (group: FilterGroup) => {
    switch (group.type) {
      case 'checkbox':
        return renderCheckboxGroup(group);
      case 'radio':
        return renderRadioGroup(group);
      case 'color':
        return renderColorGroup(group);
      case 'size':
        return renderSizeGroup(group);
      case 'range':
        return renderRangeGroup(group);
      default:
        return null;
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        {showClearAll && getTotalSelectedCount() > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearAll}
            disabled={isLoading}
            className="text-blue-600 hover:text-blue-700"
          >
            Clear All ({getTotalSelectedCount()})
          </Button>
        )}
      </div>

      {/* Filter Groups */}
      <div className="space-y-4">
        {groups.map((group) => {
          const isExpanded = expandedGroups.has(group.id);
          
          return (
            <div key={group.id} className="border-b border-gray-200 pb-4 last:border-b-0">
              {/* Group Header */}
              <button
                onClick={() => group.collapsible !== false && toggleGroup(group.id)}
                className={cn(
                  'flex items-center justify-between w-full py-2 text-left',
                  group.collapsible !== false && 'hover:text-gray-600'
                )}
                disabled={isLoading}
              >
                <span className="font-medium text-gray-900">{group.title}</span>
                {group.collapsible !== false && (
                  <svg
                    className={cn(
                      'w-4 h-4 transition-transform',
                      isExpanded ? 'rotate-180' : 'rotate-0'
                    )}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                )}
              </button>

              {/* Group Content */}
              {(isExpanded || group.collapsible === false) && (
                <div className="mt-3">
                  {renderGroupContent(group)}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-4">
          <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
};

export default FacetFilter;
