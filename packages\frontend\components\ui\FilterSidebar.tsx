'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import FacetFilter, { FilterGroup } from './FacetFilter';
import Button from './Button';

export interface FilterSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  filters: Record<string, string[]>;
  onFiltersChange: (filters: Record<string, string[]>) => void;
  filterGroups?: FilterGroup[];
  className?: string;
  showMobileOverlay?: boolean;
  position?: 'left' | 'right';
  width?: 'sm' | 'md' | 'lg';
}

const FilterSidebar: React.FC<FilterSidebarProps> = ({
  isOpen = true,
  onClose,
  filters,
  onFiltersChange,
  filterGroups = [],
  className,
  showMobileOverlay = true,
  position = 'left',
  width = 'md',
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (isMobile && isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobile, isOpen]);

  const handleFilterChange = (groupId: string, values: string[]) => {
    const newFilters = { ...filters };
    if (values.length === 0) {
      delete newFilters[groupId];
    } else {
      newFilters[groupId] = values;
    }
    onFiltersChange(newFilters);
  };

  const handleClearAll = () => {
    onFiltersChange({});
  };

  const getWidthClasses = () => {
    switch (width) {
      case 'sm':
        return 'w-64';
      case 'md':
        return 'w-80';
      case 'lg':
        return 'w-96';
      default:
        return 'w-80';
    }
  };

  const getDefaultFilterGroups = (): FilterGroup[] => [
    {
      id: 'price',
      title: 'Price Range',
      type: 'range',
      min: 0,
      max: 10000,
      step: 10,
      unit: '',
      defaultExpanded: true,
    },
    // {
    //   id: 'category',
    //   title: 'Category',
    //   type: 'checkbox',
    //   defaultExpanded: true,
    //   options: [
    //     {
    //       id: 'electronics',
    //       label: 'Electronics',
    //       value: 'electronics',
    //       count: 45,
    //     },
    //     { id: 'fashion', label: 'Fashion', value: 'fashion', count: 32 },
    //     { id: 'home', label: 'Home & Garden', value: 'home', count: 28 },
    //     { id: 'sports', label: 'Sports & Fitness', value: 'sports', count: 19 },
    //     { id: 'books', label: 'Books', value: 'books', count: 15 },
    //     { id: 'beauty', label: 'Beauty & Health', value: 'beauty', count: 12 },
    //   ],
    // },
    // {
    //   id: 'brand',
    //   title: 'Brand',
    //   type: 'checkbox',
    //   defaultExpanded: true,
    //   options: [
    //     { id: 'apple', label: 'Apple', value: 'apple', count: 15 },
    //     { id: 'samsung', label: 'Samsung', value: 'samsung', count: 12 },
    //     { id: 'nike', label: 'Nike', value: 'nike', count: 8 },
    //     { id: 'adidas', label: 'Adidas', value: 'adidas', count: 6 },
    //     { id: 'sony', label: 'Sony', value: 'sony', count: 5 },
    //   ],
    // },
    {
      id: 'rating',
      title: 'Customer Rating',
      type: 'radio',
      defaultExpanded: true,
      options: [
        { id: '4plus', label: '4 Stars & Up', value: '4+', count: 89 },
        { id: '3plus', label: '3 Stars & Up', value: '3+', count: 156 },
        { id: '2plus', label: '2 Stars & Up', value: '2+', count: 203 },
        { id: '1plus', label: '1 Star & Up', value: '1+', count: 245 },
      ],
    },
    {
      id: 'availability',
      title: 'Availability',
      type: 'checkbox',
      defaultExpanded: false,
      options: [
        { id: 'in-stock', label: 'In Stock', value: 'in-stock', count: 198 },
        {
          id: 'out-of-stock',
          label: 'Out of Stock',
          value: 'out-of-stock',
          count: 47,
        },
      ],
    },
    // {
    //   id: 'color',
    //   title: 'Color',
    //   type: 'color',
    //   defaultExpanded: false,
    //   options: [
    //     { id: 'black', label: 'Black', value: '#000000', count: 45 },
    //     { id: 'white', label: 'White', value: '#FFFFFF', count: 38 },
    //     { id: 'blue', label: 'Blue', value: '#3B82F6', count: 32 },
    //     { id: 'red', label: 'Red', value: '#EF4444', count: 28 },
    //     { id: 'green', label: 'Green', value: '#10B981', count: 24 },
    //     { id: 'gray', label: 'Gray', value: '#6B7280', count: 20 },
    //   ],
    // },
    // {
    //   id: 'size',
    //   title: 'Size',
    //   type: 'size',
    //   defaultExpanded: false,
    //   options: [
    //     { id: 'xs', label: 'XS', value: 'xs', count: 12 },
    //     { id: 's', label: 'S', value: 's', count: 28 },
    //     { id: 'm', label: 'M', value: 'm', count: 45 },
    //     { id: 'l', label: 'L', value: 'l', count: 38 },
    //     { id: 'xl', label: 'XL', value: 'xl', count: 22 },
    //     { id: 'xxl', label: 'XXL', value: 'xxl', count: 15 },
    //   ],
    // },
  ];

  const finalFilterGroups =
    filterGroups.length > 0 ? filterGroups : getDefaultFilterGroups();

  // Mobile sidebar
  if (isMobile) {
    return (
      <>
        {/* Mobile Overlay */}
        {showMobileOverlay && isOpen && (
          <div
            className='fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden'
            onClick={onClose}
          />
        )}

        {/* Mobile Sidebar */}
        <div
          className={cn(
            'fixed top-0 h-full bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out lg:hidden',
            position === 'left' ? 'left-0' : 'right-0',
            isOpen
              ? 'translate-x-0'
              : position === 'left'
                ? '-translate-x-full'
                : 'translate-x-full',
            getWidthClasses(),
            className
          )}
        >
          {/* Mobile Header */}
          <div className='flex items-center justify-between p-4 border-b bg-gray-50'>
            <h2 className='text-lg font-semibold text-gray-900'>Filters</h2>
            <button
              onClick={onClose}
              className='p-2 hover:bg-gray-200 rounded-full transition-colors'
            >
              <svg
                className='w-5 h-5'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M6 18L18 6M6 6l12 12'
                />
              </svg>
            </button>
          </div>

          {/* Mobile Content */}
          <div className='flex-1 overflow-y-auto p-4'>
            <FacetFilter
              groups={finalFilterGroups}
              selectedFilters={filters}
              onFilterChange={handleFilterChange}
              onClearAll={handleClearAll}
              showClearAll={true}
            />
          </div>

          {/* Mobile Footer */}
          <div className='border-t p-4 bg-gray-50'>
            <div className='flex space-x-3'>
              <Button
                variant='outline'
                className='flex-1'
                onClick={handleClearAll}
              >
                Clear All
              </Button>
              <Button variant='primary' className='flex-1' onClick={onClose}>
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Desktop sidebar
  console.log('finalFilterGroups:::::::::::::', { finalFilterGroups });

  return (
    <div
      className={cn(
        'hidden lg:block bg-white border-r border-gray-200',
        getWidthClasses(),
        !isOpen && 'hidden',
        className
      )}
    >
      <div className='sticky top-4 p-6'>
        <FacetFilter
          groups={finalFilterGroups}
          selectedFilters={filters}
          onFilterChange={handleFilterChange}
          onClearAll={handleClearAll}
          showClearAll={true}
        />
      </div>
    </div>
  );
};

export default FilterSidebar;
