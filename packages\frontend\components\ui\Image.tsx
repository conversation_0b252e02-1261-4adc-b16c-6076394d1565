'use client';

import React, { useState, useRef, useEffect } from 'react';
import NextImage, { ImageProps as NextImageProps } from 'next/image';
import { cn } from '@/lib/utils';

export interface ImageProps extends Omit<NextImageProps, 'onLoad' | 'onError'> {
  src: string;
  alt: string;
  className?: string;
  containerClassName?: string;
  fallbackSrc?: string;
  lqip?: string; // Low-Quality Image Placeholder
  showLoadingSpinner?: boolean;
  aspectRatio?: 'square' | '4:3' | '16:9' | '3:2' | 'auto';
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  onLoad?: () => void;
  onError?: () => void;
  lazy?: boolean;
}

const Image: React.FC<ImageProps> = ({
  src,
  alt,
  className,
  containerClassName,
  fallbackSrc = '/images/placeholder.jpg',
  lqip,
  showLoadingSpinner = true,
  aspectRatio = 'auto',
  objectFit = 'cover',
  onLoad,
  onError,
  lazy = true,
  width,
  height,
  fill,
  sizes,
  priority = false,
  quality = 75,
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(lqip || src);
  const [isInView, setIsInView] = useState(!lazy);
  const imgRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  // Handle LQIP to high-quality transition
  useEffect(() => {
    if (!lqip || !isInView) return;

    const img = new window.Image();
    img.onload = () => {
      setCurrentSrc(src);
    };
    img.src = src;
  }, [src, lqip, isInView]);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    setCurrentSrc(fallbackSrc);
    onError?.();
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square';
      case '4:3':
        return 'aspect-[4/3]';
      case '16:9':
        return 'aspect-video';
      case '3:2':
        return 'aspect-[3/2]';
      default:
        return '';
    }
  };

  const getObjectFitClass = () => {
    switch (objectFit) {
      case 'cover':
        return 'object-cover';
      case 'contain':
        return 'object-contain';
      case 'fill':
        return 'object-fill';
      case 'none':
        return 'object-none';
      case 'scale-down':
        return 'object-scale-down';
      default:
        return 'object-cover';
    }
  };

  const imageClasses = cn(
    'transition-all duration-300',
    getObjectFitClass(),
    {
      'opacity-0': isLoading && !lqip,
      'opacity-100': !isLoading || lqip,
      'blur-sm': isLoading && lqip && currentSrc === lqip,
      'blur-none': !isLoading || currentSrc === src,
    },
    className
  );

  const containerClasses = cn(
    'relative overflow-hidden bg-gray-100',
    getAspectRatioClass(),
    containerClassName
  );

  // Don't render anything if lazy loading and not in view
  if (lazy && !isInView) {
    return (
      <div ref={imgRef} className={containerClasses}>
        <div className='absolute inset-0 bg-gray-200 animate-pulse' />
      </div>
    );
  }

  return (
    <div ref={imgRef} className={containerClasses}>
      {/* Loading spinner */}
      {isLoading && showLoadingSpinner && !lqip && (
        <div className='absolute inset-0 flex items-center justify-center bg-gray-100'>
          <div className='w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin' />
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className='absolute inset-0 flex flex-col items-center justify-center bg-gray-100 text-gray-500'>
          <svg
            className='w-8 h-8 mb-2'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'
            />
          </svg>
          <span className='text-sm'>Failed to load image</span>
        </div>
      )}

      {/* Main image */}
      <NextImage
        src={currentSrc}
        alt={alt}
        className={imageClasses}
        onLoad={handleLoad}
        onError={handleError}
        width={width}
        height={height}
        fill={fill}
        sizes={sizes}
        priority={priority}
        quality={quality}
        {...props}
      />

      {/* LQIP overlay effect */}
      {lqip && currentSrc === lqip && (
        <div className='absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-white/20 pointer-events-none' />
      )}
    </div>
  );
};

export default Image;
