'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import Image from './Image';
import Modal from './Modal';
import Button from './Button';
import {
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  X,
  Maximize2,
} from 'lucide-react';

export interface GalleryImage {
  id: string;
  src: string;
  alt?: string;
  lqip?: string;
}

export interface ImageGalleryProps {
  images: GalleryImage[];
  productTitle?: string;
  className?: string;
  thumbnailPosition?: 'bottom' | 'left' | 'right';
  enableZoom?: boolean;
  enableFullscreen?: boolean;
  enablePinchZoom?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showThumbnails?: boolean;
  showArrows?: boolean;
  showCounter?: boolean;
  aspectRatio?: 'square' | '4:3' | '16:9' | '3:2' | 'auto';
  onImageChange?: (index: number) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  productTitle = 'Product',
  className,
  thumbnailPosition = 'bottom',
  enableZoom = true,
  enableFullscreen = true,
  enablePinchZoom = true,
  autoPlay = false,
  autoPlayInterval = 5000,
  showThumbnails = true,
  showArrows = true,
  showCounter = true,
  aspectRatio = 'square',
  onImageChange,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [touchStart, setTouchStart] = useState({ x: 0, y: 0, distance: 0 });
  const [isLoading, setIsLoading] = useState(false);
  const [lastTouchDistance, setLastTouchDistance] = useState(0);
  const [touchCenter, setTouchCenter] = useState({ x: 0, y: 0 });
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);

  const mainImageRef = useRef<HTMLDivElement>(null);
  const zoomImageRef = useRef<HTMLDivElement>(null);
  const autoPlayRef = useRef<NodeJS.Timeout>();
  const touchTimeoutRef = useRef<NodeJS.Timeout>();

  const currentImage = images[currentIndex] || images[0];

  // Auto-play functionality
  useEffect(() => {
    if (!autoPlay || images.length <= 1) return;

    autoPlayRef.current = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % images.length);
    }, autoPlayInterval);

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [autoPlay, autoPlayInterval, images.length]);

  // Stop auto-play on user interaction
  const stopAutoPlay = useCallback(() => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      autoPlayRef.current = undefined;
    }
  }, []);

  // Navigation functions
  const goToNext = useCallback(() => {
    stopAutoPlay();
    const nextIndex = (currentIndex + 1) % images.length;
    setCurrentIndex(nextIndex);
    onImageChange?.(nextIndex);
  }, [currentIndex, images.length, onImageChange, stopAutoPlay]);

  const goToPrevious = useCallback(() => {
    stopAutoPlay();
    const prevIndex = (currentIndex - 1 + images.length) % images.length;
    setCurrentIndex(prevIndex);
    onImageChange?.(prevIndex);
  }, [currentIndex, images.length, onImageChange, stopAutoPlay]);

  const goToImage = useCallback(
    (index: number) => {
      stopAutoPlay();
      setCurrentIndex(index);
      onImageChange?.(index);
    },
    [onImageChange, stopAutoPlay]
  );

  // Zoom functions
  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev * 1.5, 4));
    setIsZoomed(true);
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => {
      const newLevel = prev / 1.5;
      if (newLevel <= 1) {
        setIsZoomed(false);
        setZoomPosition({ x: 0, y: 0 });
        return 1;
      }
      return newLevel;
    });
  }, []);

  const resetZoom = useCallback(() => {
    setZoomLevel(1);
    setIsZoomed(false);
    setZoomPosition({ x: 0, y: 0 });
    setPanPosition({ x: 0, y: 0 });
    setIsPanning(false);
  }, []);

  // Mouse events for zoom
  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!enableZoom || !isZoomed || !mainImageRef.current) return;

      const rect = mainImageRef.current.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width - 0.5) * 2;
      const y = ((e.clientY - rect.top) / rect.height - 0.5) * 2;

      setZoomPosition({ x: -x * 50, y: -y * 50 });
    },
    [enableZoom, isZoomed]
  );

  // Enhanced touch events for pinch-to-zoom and panning
  const getTouchDistance = useCallback((touches: TouchList) => {
    if (touches.length < 2) return 0;
    return Math.hypot(
      touches[0].clientX - touches[1].clientX,
      touches[0].clientY - touches[1].clientY
    );
  }, []);

  const getTouchCenter = useCallback((touches: TouchList) => {
    if (touches.length === 1) {
      return { x: touches[0].clientX, y: touches[0].clientY };
    } else if (touches.length === 2) {
      return {
        x: (touches[0].clientX + touches[1].clientX) / 2,
        y: (touches[0].clientY + touches[1].clientY) / 2,
      };
    }
    return { x: 0, y: 0 };
  }, []);

  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (!enablePinchZoom) return;

      // Clear any existing timeout
      if (touchTimeoutRef.current) {
        clearTimeout(touchTimeoutRef.current);
      }

      const touches = e.touches;
      const center = getTouchCenter(touches);
      const distance = getTouchDistance(touches);

      if (touches.length === 1) {
        // Single touch - prepare for panning if zoomed
        if (isZoomed) {
          setIsPanning(true);
          setTouchStart({ x: center.x, y: center.y, distance: 0 });
        }
      } else if (touches.length === 2) {
        // Two fingers - pinch to zoom
        setLastTouchDistance(distance);
        setTouchCenter(center);
        setTouchStart({ x: center.x, y: center.y, distance });
      }
    },
    [enablePinchZoom, isZoomed, getTouchCenter, getTouchDistance]
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!enablePinchZoom) return;

      const touches = e.touches;
      const center = getTouchCenter(touches);

      if (touches.length === 1 && isPanning && isZoomed) {
        // Single finger panning when zoomed
        e.preventDefault();
        const deltaX = center.x - touchStart.x;
        const deltaY = center.y - touchStart.y;

        setPanPosition(prev => ({
          x: prev.x + deltaX * 0.5,
          y: prev.y + deltaY * 0.5,
        }));

        setTouchStart({ x: center.x, y: center.y, distance: 0 });
      } else if (touches.length === 2) {
        // Two finger pinch to zoom
        e.preventDefault();

        const distance = getTouchDistance(touches);

        if (lastTouchDistance > 0) {
          const scale = distance / lastTouchDistance;
          const newZoomLevel = Math.max(0.5, Math.min(4, zoomLevel * scale));

          setZoomLevel(newZoomLevel);
          setIsZoomed(newZoomLevel > 1);

          if (newZoomLevel <= 1) {
            setPanPosition({ x: 0, y: 0 });
            setZoomPosition({ x: 0, y: 0 });
          }
        }

        setLastTouchDistance(distance);
        setTouchCenter(center);
      }
    },
    [
      enablePinchZoom,
      isPanning,
      isZoomed,
      touchStart,
      lastTouchDistance,
      zoomLevel,
      getTouchCenter,
      getTouchDistance,
    ]
  );

  const handleTouchEnd = useCallback(
    (e: React.TouchEvent) => {
      if (!enablePinchZoom) return;

      // Reset panning state
      setIsPanning(false);
      setLastTouchDistance(0);

      // If zoom level is very close to 1, reset to 1
      if (zoomLevel < 1.1 && zoomLevel > 0.9) {
        resetZoom();
      }
    },
    [enablePinchZoom, zoomLevel, resetZoom]
  );

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isFullscreen) {
        switch (e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            goToPrevious();
            break;
          case 'ArrowRight':
            e.preventDefault();
            goToNext();
            break;
          case 'Escape':
            e.preventDefault();
            setIsFullscreen(false);
            break;
          case '+':
          case '=':
            e.preventDefault();
            handleZoomIn();
            break;
          case '-':
            e.preventDefault();
            handleZoomOut();
            break;
          case '0':
            e.preventDefault();
            resetZoom();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [
    isFullscreen,
    goToNext,
    goToPrevious,
    handleZoomIn,
    handleZoomOut,
    resetZoom,
  ]);

  // Render thumbnail
  const renderThumbnail = (image: GalleryImage, index: number) => (
    <button
      key={image.id}
      onClick={() => goToImage(index)}
      className={cn(
        'relative flex-shrink-0 rounded-lg overflow-hidden border-2 transition-all duration-200',
        'hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        {
          'border-blue-500 ring-2 ring-blue-200': index === currentIndex,
          'border-gray-200': index !== currentIndex,
          'w-16 h-16': thumbnailPosition === 'bottom',
          'w-20 h-20':
            thumbnailPosition === 'left' || thumbnailPosition === 'right',
        }
      )}
      aria-label={`View image ${index + 1} of ${images.length}`}
    >
      <Image
        src={image.src}
        alt={image.alt || `${productTitle} ${index + 1}`}
        fill
        objectFit='cover'
        className='transition-transform duration-200 hover:scale-105'
        sizes='80px'
        lqip={image.lqip}
      />
    </button>
  );

  if (!images || images.length === 0) {
    return (
      <div
        className={cn(
          'aspect-square bg-gray-200 rounded-lg flex items-center justify-center',
          className
        )}
      >
        <div className='text-gray-400 text-center'>
          <div className='w-16 h-16 mx-auto mb-2 bg-gray-300 rounded'></div>
          <p>No images available</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={cn('space-y-4', className)}>
        {/* Main Image Container */}
        <div
          className={cn(
            'relative bg-gray-100 rounded-lg overflow-hidden group',
            {
              'aspect-square': aspectRatio === 'square',
              'aspect-[4/3]': aspectRatio === '4:3',
              'aspect-video': aspectRatio === '16:9',
              'aspect-[3/2]': aspectRatio === '3:2',
            }
          )}
        >
          <div
            ref={mainImageRef}
            className='relative w-full h-full cursor-zoom-in'
            onMouseMove={handleMouseMove}
            onMouseLeave={resetZoom}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            onClick={enableZoom ? handleZoomIn : undefined}
          >
            <Image
              src={currentImage.src}
              alt={currentImage.alt || `${productTitle} main image`}
              fill
              objectFit='cover'
              className={cn('transition-transform duration-300', {
                'scale-110': isZoomed && !isFullscreen,
              })}
              style={
                isZoomed && !isFullscreen
                  ? {
                      transform: `scale(${zoomLevel}) translate(${
                        zoomPosition.x + panPosition.x
                      }px, ${zoomPosition.y + panPosition.y}px)`,
                    }
                  : undefined
              }
              sizes='(max-width: 768px) 100vw, 50vw'
              lqip={currentImage.lqip}
              priority={currentIndex === 0}
            />
          </div>

          {/* Navigation Arrows */}
          {showArrows && images.length > 1 && (
            <>
              <Button
                variant='ghost'
                size='sm'
                onClick={goToPrevious}
                className='absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity'
                aria-label='Previous image'
              >
                <ChevronLeft className='w-5 h-5' />
              </Button>
              <Button
                variant='ghost'
                size='sm'
                onClick={goToNext}
                className='absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity'
                aria-label='Next image'
              >
                <ChevronRight className='w-5 h-5' />
              </Button>
            </>
          )}

          {/* Action Buttons */}
          <div className='absolute top-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity'>
            {enableZoom && (
              <Button
                variant='ghost'
                size='sm'
                onClick={isZoomed ? handleZoomOut : handleZoomIn}
                className='bg-white/80 hover:bg-white rounded-full p-2'
                aria-label={isZoomed ? 'Zoom out' : 'Zoom in'}
              >
                {isZoomed ? (
                  <ZoomOut className='w-5 h-5' />
                ) : (
                  <ZoomIn className='w-5 h-5' />
                )}
              </Button>
            )}
            {enableFullscreen && (
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setIsFullscreen(true)}
                className='bg-white/80 hover:bg-white rounded-full p-2'
                aria-label='View fullscreen'
              >
                <Maximize2 className='w-5 h-5' />
              </Button>
            )}
          </div>

          {/* Image Counter */}
          {showCounter && images.length > 1 && (
            <div className='absolute bottom-2 right-2 bg-black/60 text-white px-2 py-1 rounded text-sm'>
              {currentIndex + 1} / {images.length}
            </div>
          )}
        </div>

        {/* Thumbnails */}
        {showThumbnails && images.length > 1 && (
          <div
            className={cn('flex gap-2 overflow-x-auto pb-2', {
              'justify-center': images.length <= 6,
              'justify-start': images.length > 6,
            })}
          >
            {images.map(renderThumbnail)}
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      {enableFullscreen && (
        <Modal
          isOpen={isFullscreen}
          onClose={() => setIsFullscreen(false)}
          className='max-w-none w-full h-full bg-black/95'
          showCloseButton={false}
        >
          <div className='relative w-full h-full flex items-center justify-center p-4'>
            {/* Close Button */}
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setIsFullscreen(false)}
              className='absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2'
              aria-label='Close fullscreen'
            >
              <X className='w-6 h-6' />
            </Button>

            {/* Fullscreen Image */}
            <div className='relative max-w-full max-h-full'>
              <Image
                src={currentImage.src}
                alt={currentImage.alt || `${productTitle} fullscreen`}
                width={1200}
                height={1200}
                className='max-w-full max-h-[80vh] object-contain'
                sizes='100vw'
                priority
              />
            </div>

            {/* Fullscreen Navigation */}
            {images.length > 1 && (
              <>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={goToPrevious}
                  className='absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3'
                  aria-label='Previous image'
                >
                  <ChevronLeft className='w-6 h-6' />
                </Button>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={goToNext}
                  className='absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3'
                  aria-label='Next image'
                >
                  <ChevronRight className='w-6 h-6' />
                </Button>
              </>
            )}

            {/* Fullscreen Thumbnails */}
            {images.length > 1 && (
              <div className='absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 max-w-full overflow-x-auto'>
                {images.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => goToImage(index)}
                    className={cn(
                      'relative flex-shrink-0 w-12 h-12 rounded overflow-hidden border-2 transition-colors',
                      {
                        'border-white': index === currentIndex,
                        'border-white/30 hover:border-white/60':
                          index !== currentIndex,
                      }
                    )}
                    aria-label={`View image ${index + 1}`}
                  >
                    <Image
                      src={image.src}
                      alt={image.alt || `${productTitle} ${index + 1}`}
                      fill
                      objectFit='cover'
                      sizes='48px'
                    />
                  </button>
                ))}
              </div>
            )}

            {/* Fullscreen Counter */}
            <div className='absolute top-4 left-1/2 -translate-x-1/2 bg-black/60 text-white px-3 py-1 rounded'>
              {currentIndex + 1} / {images.length}
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default ImageGallery;
