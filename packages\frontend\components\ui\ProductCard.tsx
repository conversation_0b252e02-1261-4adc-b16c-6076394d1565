'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { formatCurrency } from '@/lib/utils/product-utils';
import Image from './Image';
import Button from './Button';
import Badge from './Badge';

export interface ProductCardProps {
  id: string;
  title: string;
  description?: string;
  price: number;
  originalPrice?: number;
  currency?: string;
  image?: string;
  images?: string[];
  category?: string;
  brand?: string;
  rating?: number;
  reviewCount?: number;
  inStock?: boolean;
  isNew?: boolean;
  isFeatured?: boolean;
  tags?: string[];
  variant?: 'default' | 'compact' | 'detailed';
  showQuickAdd?: boolean;
  showWishlist?: boolean;
  showCompare?: boolean;
  onQuickAdd?: (productId: string) => void;
  onWishlistToggle?: (productId: string) => void;
  onCompareToggle?: (productId: string) => void;
  className?: string;
  imageClassName?: string;
  contentClassName?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  title,
  description,
  price,
  originalPrice,
  currency = 'USD',
  image,
  images,
  category,
  brand,
  rating,
  reviewCount,
  inStock = true,
  isNew = false,
  isFeatured = false,
  tags = [],
  variant = 'default',
  showQuickAdd = true,
  showWishlist = true,
  showCompare = false,
  onQuickAdd,
  onWishlistToggle,
  onCompareToggle,
  className,
  imageClassName,
  contentClassName,
}) => {
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isCompared, setIsCompared] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const hasDiscount = originalPrice && originalPrice > price;
  const discountPercentage = hasDiscount
    ? Math.round(((originalPrice - price) / originalPrice) * 100)
    : 0;

  const productImage =
    image || images?.[0] || '/images/placeholder-product.jpg';

  const handleQuickAdd = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onQuickAdd?.(id);
  };

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    onWishlistToggle?.(id);
  };

  const handleCompareToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsCompared(!isCompared);
    onCompareToggle?.(id);
  };

  const renderRating = () => {
    if (!rating) return null;

    return (
      <div className='flex items-center space-x-1'>
        <div className='flex items-center'>
          {[1, 2, 3, 4, 5].map(star => (
            <svg
              key={star}
              className={cn(
                'w-3 h-3',
                star <= rating
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              )}
              viewBox='0 0 20 20'
            >
              <path d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' />
            </svg>
          ))}
        </div>
        {reviewCount && (
          <span className='text-xs text-gray-500'>({reviewCount})</span>
        )}
      </div>
    );
  };

  const renderBadges = () => {
    const badges = [];

    if (hasDiscount) {
      badges.push(
        <Badge key='discount' variant='destructive' size='sm'>
          -{discountPercentage}%
        </Badge>
      );
    }

    if (isNew) {
      badges.push(
        <Badge key='new' variant='secondary' size='sm'>
          New
        </Badge>
      );
    }

    if (isFeatured) {
      badges.push(
        <Badge key='featured' variant='default' size='sm'>
          Featured
        </Badge>
      );
    }

    if (!inStock) {
      badges.push(
        <Badge key='out-of-stock' variant='outline' size='sm'>
          Out of Stock
        </Badge>
      );
    }

    return badges.length > 0 ? (
      <div className='absolute top-2 left-2 z-10 flex flex-col space-y-1'>
        {badges}
      </div>
    ) : null;
  };

  const renderActionButtons = () => (
    <div className='absolute top-2 right-2 z-10 flex flex-col space-y-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200'>
      {showWishlist && (
        <button
          onClick={handleWishlistToggle}
          className={cn(
            'w-8 h-8 rounded-full flex items-center justify-center transition-colors',
            isWishlisted
              ? 'bg-red-500 text-white'
              : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500'
          )}
          title={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
        >
          <svg
            className='w-4 h-4'
            fill={isWishlisted ? 'currentColor' : 'none'}
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'
            />
          </svg>
        </button>
      )}

      {showCompare && (
        <button
          onClick={handleCompareToggle}
          className={cn(
            'w-8 h-8 rounded-full flex items-center justify-center transition-colors',
            isCompared
              ? 'bg-blue-500 text-white'
              : 'bg-white/80 text-gray-600 hover:bg-white hover:text-blue-500'
          )}
          title={isCompared ? 'Remove from compare' : 'Add to compare'}
        >
          <svg
            className='w-4 h-4'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
            />
          </svg>
        </button>
      )}
    </div>
  );

  const renderQuickAddButton = () => {
    if (!showQuickAdd || !inStock) return null;

    return (
      <div className='absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center'>
        <Button
          onClick={handleQuickAdd}
          variant='primary'
          size='sm'
          className='transform translate-y-2 group-hover:translate-y-0 transition-transform duration-200'
        >
          Quick Add
        </Button>
      </div>
    );
  };

  const cardClasses = cn(
    'group relative bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden',
    !inStock && 'opacity-75',
    variant === 'compact' && 'max-w-xs',
    className
  );

  const imageContainerClasses = cn(
    'relative overflow-hidden bg-gray-100',
    variant === 'compact' ? 'aspect-square' : 'aspect-[4/3]',
    imageClassName
  );

  const contentClasses = cn(
    'p-4',
    variant === 'compact' && 'p-3',
    contentClassName
  );

  return (
    <Link href={`/products/${id}`} className={cardClasses}>
      {/* Product Image */}
      <div className={imageContainerClasses}>
        <img
          src={productImage}
          alt={title}
          // fill
          className='object-cover group-hover:scale-105 transition-transform duration-300'
          sizes='(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw'
          // onLoad={() => setImageLoading(false)}
        />

        {/* Badges */}
        {renderBadges()}

        {/* Action Buttons */}
        {renderActionButtons()}

        {/* Quick Add Overlay */}
        {renderQuickAddButton()}

        {/* Out of Stock Overlay */}
        {!inStock && (
          <div className='absolute inset-0 bg-gray-900/50 flex items-center justify-center'>
            <span className='text-white font-medium text-sm bg-gray-900/75 px-3 py-1 rounded'>
              Out of Stock
            </span>
          </div>
        )}
      </div>

      {/* Product Content */}
      <div className={contentClasses}>
        {/* Category/Brand */}
        {(category || brand) && (
          <p className='text-xs text-gray-500 uppercase tracking-wide mb-1'>
            {brand || category}
          </p>
        )}

        {/* Product Title */}
        <h3
          className={cn(
            'font-medium text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2',
            variant === 'compact' ? 'text-sm' : 'text-base'
          )}
        >
          {title}
        </h3>

        {/* Description (only for detailed variant) */}
        {variant === 'detailed' && description && (
          <p className='text-sm text-gray-600 mb-2 line-clamp-2'>
            {description}
          </p>
        )}

        {/* Rating */}
        {renderRating()}

        {/* Price */}
        <div className='flex items-center space-x-2 mt-2'>
          <span
            className={cn(
              'font-semibold text-gray-900',
              variant === 'compact' ? 'text-base' : 'text-lg'
            )}
          >
            {formatCurrency(price, currency)}
          </span>
          {hasDiscount && (
            <span className='text-sm text-gray-500 line-through'>
              {formatCurrency(originalPrice!, currency)}
            </span>
          )}
        </div>

        {/* Tags (only for detailed variant) */}
        {variant === 'detailed' && tags.length > 0 && (
          <div className='mt-2 flex flex-wrap gap-1'>
            {tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className='inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded'
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </Link>
  );
};

export default ProductCard;
