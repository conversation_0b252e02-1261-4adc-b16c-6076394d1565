'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { Check, X } from 'lucide-react';

// SECTION: Data Interfaces
// NOTE: These interfaces are derived from the variants and used internally.

export interface ProductOption {
  id: string; // e.g., "color"
  name: string; // e.g., "Color"
  type: 'color' | 'size' | 'material' | 'style' | 'text';
  values: ProductOptionValue[];
  required?: boolean;
}

export interface ProductOptionValue {
  id: string; // e.g., "Red"
  value: string; // e.g., "Red"
  label?: string; // e.g., "Red"
  color?: string; // For color swatches, e.g., "red"
}

// NOTE: This interface is updated to match your data source (e.g., Medusa.js).
export interface ProductVariant {
  id: string;
  title: string;
  sku?: string;
  manage_inventory: boolean;
  allow_backorder: boolean;
  inventory_quantity: number;
  options: {
    id: string;
    value: string;
    option: {
      id: string;
      title: string; // This is the Option Name, e.g., "Color"
    };
  }[];
  prices: {
    amount: number;
    currency_code: string;
  }[];
}

// SECTION: Component Props
interface ProductOptionsSelectorProps {
  // The `options` prop is removed; it's now derived from `variants`.
  variants: ProductVariant[];
  selectedOptions: Record<string, string>; // e.g., { color: "Red", size: "S" }
  onOptionsChange: (options: Record<string, string>) => void;
  className?: string;
  disabled?: boolean;
}

// SECTION: Main Component
const ProductOptionsSelector: React.FC<ProductOptionsSelectorProps> = ({
  variants,
  selectedOptions,
  onOptionsChange,
  className,
  disabled = false,
}) => {
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Helper function to check if a variant is available for purchase.
  const isVariantAvailable = (variant: ProductVariant) => {
    return (
      variant.inventory_quantity > 0 ||
      !variant.manage_inventory ||
      variant.allow_backorder
    );
  };

  // Helper to check if a variant's options match a given selection.
  const doesVariantMatchSelections = (
    variant: ProductVariant,
    selections: Record<string, string>
  ) => {
    return Object.entries(selections).every(([optionId, valueId]) =>
      variant.options.some(
        opt =>
          opt.option.title.toLowerCase() === optionId && opt.value === valueId
      )
    );
  };

  // CORE LOGIC: Derive the `options` structure from the `variants` prop.
  const options = useMemo<ProductOption[]>(() => {
    if (!variants?.length) return [];

    const optionMap = new Map<string, ProductOption>();

    for (const variant of variants) {
      for (const v_option of variant.options) {
        const optionTitle = v_option.option.title;
        const optionId = optionTitle.toLowerCase();
        const value = v_option.value;

        if (!optionMap.has(optionId)) {
          let type: ProductOption['type'] = 'text';
          if (optionId.includes('color')) type = 'color';
          else if (optionId.includes('size')) type = 'size';

          optionMap.set(optionId, {
            id: optionId,
            name: optionTitle,
            type,
            values: [],
            required: true, // Assume all options are required for a valid variant
          });
        }

        const currentOption = optionMap.get(optionId)!;
        const valueExists = currentOption.values.some(v => v.id === value);

        if (!valueExists) {
          currentOption.values.push({
            id: value,
            value: value,
            label: value,
            color:
              currentOption.type === 'color' ? value.toLowerCase() : undefined,
          });
        }
      }
    }
    return Array.from(optionMap.values());
  }, [variants]);

  // Find the currently selected variant based on selected options.
  const selectedVariant = useMemo(() => {
    const allOptionsSelected = options.every(opt => selectedOptions[opt.id]);
    if (!allOptionsSelected) return null;

    return variants.find(variant =>
      doesVariantMatchSelections(variant, selectedOptions)
    );
  }, [variants, selectedOptions, options, doesVariantMatchSelections]);

  // Get available values for an option, considering other selections.
  const getAvailableValues = (optionId: string) => {
    const option = options.find(opt => opt.id === optionId);
    if (!option) return [];

    return option.values.filter(value => {
      const testSelection = { ...selectedOptions, [optionId]: value.id };
      return variants.some(
        variant =>
          isVariantAvailable(variant) &&
          doesVariantMatchSelections(variant, testSelection)
      );
    });
  };

  // Handle option selection
  const handleOptionSelect = (optionId: string, valueId: string) => {
    if (disabled) return;

    const newOptions = { ...selectedOptions, [optionId]: valueId };
    onOptionsChange(newOptions);

    if (errors[optionId]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[optionId];
        return newErrors;
      });
    }
  };

  // Validate required options on change
  useEffect(() => {
    const newErrors: Record<string, string> = {};
    options.forEach(option => {
      if (option.required && !selectedOptions[option.id]) {
        newErrors[option.id] = `Please select a ${option.name.toLowerCase()}`;
      }
    });
    setErrors(newErrors);
  }, [options, selectedOptions]);

  // Render color option
  const renderColorOption = (option: ProductOption) => {
    const availableValues = getAvailableValues(option.id);
    const selectedValue = availableValues.find(
      v => v.id === selectedOptions[option.id]
    );

    return (
      <div key={option.id} className='space-y-3'>
        <div className='flex items-center justify-between'>
          <label className='text-sm font-medium text-gray-900'>
            {option.name}
            {option.required && <span className='text-red-500 ml-1'>*</span>}
          </label>
          {selectedValue && (
            <span className='text-sm text-gray-600'>{selectedValue.label}</span>
          )}
        </div>
        <div className='flex flex-wrap gap-2'>
          {option.values.map(value => {
            const isSelected = selectedOptions[option.id] === value.id;
            const isAvailable = availableValues.some(av => av.id === value.id);

            return (
              <button
                key={value.id}
                onClick={() => handleOptionSelect(option.id, value.id)}
                disabled={disabled || !isAvailable}
                className={cn(
                  'relative w-8 h-8 rounded-full border-2 transition-all',
                  isSelected
                    ? 'border-gray-900 ring-2 ring-gray-900 ring-offset-2'
                    : 'border-gray-300',
                  isAvailable
                    ? 'hover:border-gray-400'
                    : 'opacity-50 cursor-not-allowed'
                )}
                style={{ backgroundColor: value.color }}
                title={value.label || value.value}
              >
                {!isAvailable && !isSelected && (
                  <div className='absolute inset-0 flex items-center justify-center'>
                    <X
                      className='w-5 h-5 text-gray-400'
                      style={{
                        clipPath: 'polygon(0 0, 100% 100%, 90% 100%, 0 10%)',
                      }}
                    />
                    <X
                      className='w-5 h-5 text-gray-400'
                      style={{
                        clipPath: 'polygon(100% 0, 0 100%, 10% 100%, 100% 10%)',
                      }}
                    />
                  </div>
                )}
              </button>
            );
          })}
        </div>
        {errors[option.id] && (
          <p className='text-sm text-red-600'>{errors[option.id]}</p>
        )}
      </div>
    );
  };

  // Render size/text option
  const renderButtonOption = (option: ProductOption) => {
    const availableValues = getAvailableValues(option.id);
    return (
      <div key={option.id} className='space-y-3'>
        <label className='text-sm font-medium text-gray-900'>
          {option.name}
          {option.required && <span className='text-red-500 ml-1'>*</span>}
        </label>
        <div className='flex flex-wrap gap-2'>
          {option.values.map(value => {
            const isSelected = selectedOptions[option.id] === value.id;
            const isAvailable = availableValues.some(av => av.id === value.id);

            return (
              <Button
                key={value.id}
                variant={isSelected ? 'default' : 'outline'}
                size='sm'
                onClick={() => handleOptionSelect(option.id, value.id)}
                disabled={disabled || !isAvailable}
                className={cn(!isAvailable && 'text-gray-400 line-through')}
              >
                {value.label || value.value}
              </Button>
            );
          })}
        </div>
        {errors[option.id] && (
          <p className='text-sm text-red-600'>{errors[option.id]}</p>
        )}
      </div>
    );
  };

  const renderOption = (option: ProductOption) => {
    switch (option.type) {
      case 'color':
        return renderColorOption(option);
      default:
        return renderButtonOption(option);
    }
  };

  if (!options.length) return null;

  return (
    <div className={cn('space-y-6', className)}>
      {options.map(renderOption)}

      {selectedVariant && (
        <div className='pt-4 border-t border-gray-200'>
          <div className='flex items-center justify-between text-sm'>
            <span className='text-gray-600'>Price:</span>
            <span className='font-medium'>
              {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: selectedVariant.prices[0]?.currency_code || 'USD',
              }).format((selectedVariant.prices[0]?.amount || 0) / 100)}
            </span>
          </div>
          {selectedVariant.sku && (
            <div className='flex items-center justify-between text-sm mt-1'>
              <span className='text-gray-600'>SKU:</span>
              <span className='font-mono text-xs'>{selectedVariant.sku}</span>
            </div>
          )}
          {selectedVariant.manage_inventory && (
            <div className='flex items-center justify-between text-sm mt-1'>
              <span className='text-gray-600'>Stock:</span>
              <span
                className={cn(
                  'font-medium',
                  isVariantAvailable(selectedVariant)
                    ? 'text-green-600'
                    : 'text-red-600'
                )}
              >
                {isVariantAvailable(selectedVariant)
                  ? 'In Stock'
                  : 'Out of Stock'}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductOptionsSelector;
