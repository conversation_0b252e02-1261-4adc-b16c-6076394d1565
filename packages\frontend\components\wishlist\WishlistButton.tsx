'use client';

import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';

interface WishlistButtonProps {
  productId: string;
  variant?: 'icon' | 'button';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const WishlistButton: React.FC<WishlistButtonProps> = ({
  productId,
  variant = 'icon',
  size = 'md',
  className = '',
}) => {
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load wishlist state from localStorage
  useEffect(() => {
    const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
    setIsInWishlist(wishlist.includes(productId));
  }, [productId]);

  const toggleWishlist = async () => {
    setIsLoading(true);

    try {
      const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
      let updatedWishlist;

      if (isInWishlist) {
        // Remove from wishlist
        updatedWishlist = wishlist.filter((id: string) => id !== productId);
      } else {
        // Add to wishlist
        updatedWishlist = [...wishlist, productId];
      }

      localStorage.setItem('wishlist', JSON.stringify(updatedWishlist));
      setIsInWishlist(!isInWishlist);

      // In a real app, you would also sync with the backend
      // await api.updateWishlist(updatedWishlist);
    } catch (error) {
      console.error('Error updating wishlist:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const buttonSizeClasses = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-3',
  };

  if (variant === 'button') {
    return (
      <button
        onClick={toggleWishlist}
        disabled={isLoading}
        className={`
          flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors
          ${
            isInWishlist
              ? 'bg-red-50 border-red-200 text-red-700 hover:bg-red-100'
              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
          }
          ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
          ${className}
        `}
      >
        <Heart
          className={`${sizeClasses[size]} ${
            isInWishlist ? 'fill-current' : ''
          }`}
        />
        <span className='text-sm font-medium'>
          {isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
        </span>
      </button>
    );
  }

  return (
    <button
      onClick={toggleWishlist}
      disabled={isLoading}
      className={`
        ${buttonSizeClasses[size]} rounded-full transition-colors
        ${
          isInWishlist
            ? 'bg-red-50 text-red-600 hover:bg-red-100'
            : 'bg-white text-gray-600 hover:bg-gray-50'
        }
        ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      title={isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
    >
      <Heart
        className={`${sizeClasses[size]} ${isInWishlist ? 'fill-current' : ''}`}
      />
    </button>
  );
};

export default WishlistButton;
