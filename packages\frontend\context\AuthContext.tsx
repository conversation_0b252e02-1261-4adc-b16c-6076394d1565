'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { useMedusaBackendLogin } from '@/hooks/useMedusaAdminBackend';
import { authService, getAuthConfig } from '@/lib/auth/keycloak';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'customer' | 'seller';
  avatar?: string;
  permissions?: string[];
  lastLogin?: Date;
  isActive: boolean;
  token?: string; // Add token property for authentication
}

export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  hasPermission: (permission: string) => boolean;
  isAdmin: boolean;
  isCustomer: boolean;
  isSeller: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: 'customer' | 'seller';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Development mode credentials as specified in requirements
const DEV_CREDENTIALS = {
  username: 'demo',
  password: 'demo',
};

// Mock users for development
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin',
    avatar: '/images/avatars/admin.jpg',
    permissions: ['*'],
    lastLogin: new Date(),
    isActive: true,
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Seller User',
    role: 'seller',
    avatar: '/images/avatars/seller.jpg',
    permissions: ['products.manage', 'orders.view', 'analytics.view'],
    lastLogin: new Date(),
    isActive: true,
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Customer User',
    role: 'customer',
    avatar: '/images/avatars/customer.jpg',
    permissions: ['orders.view', 'profile.edit'],
    lastLogin: new Date(),
    isActive: true,
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: 'Demo Admin',
    role: 'admin',
    avatar: '/images/avatars/demo.svg',
    permissions: ['*'],
    lastLogin: new Date(),
    isActive: true,
  },
  // Development mode user
  {
    id: '5',
    email: 'demo',
    name: 'Development User',
    role: 'admin',
    avatar: '/images/avatars/dev.svg',
    permissions: ['*'],
    lastLogin: new Date(),
    isActive: true,
  },
];

interface AuthProviderProps {
  children: ReactNode;
}

// Storage keys
const STORAGE_KEYS = {
  TOKEN: 'ondc_auth_token',
  REFRESH_TOKEN: 'ondc_refresh_token',
  USER: 'ondc_user',
} as const;
const ENCRYPTION_SECRET = 'ondc_secret_key'; // Ideally, use env/config

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { adminAuth } = useMedusaBackendLogin();

  useEffect(() => {
    // Check for stored authentication
    const initAuth = async () => {
      try {
        const config = getAuthConfig();

        // Initialize the authentication service
        const initialized = await authService.initialize();

        if (initialized && config.provider === 'keycloak') {
          // Check if user is already authenticated with Keycloak
          const currentUser = authService.getCurrentUser();
          if (currentUser) {
            setUser(currentUser);
            localStorage.setItem('ondc_auth_user', JSON.stringify(currentUser));
            console.log(
              '🔐 Keycloak authentication restored:',
              currentUser.email
            );
          }
        } else {
          // Check for existing session in localStorage (for development and OneSSO)
          const storedUser = localStorage.getItem('ondc_auth_user');
          if (storedUser) {
            const userData = JSON.parse(storedUser);
            setUser(userData);
            console.log('💾 Restored user session:', userData.email);
          } else if (config.provider === 'development') {
            // Auto-login as demo admin for development
            const demoAdmin = mockUsers.find(u => u.email === '<EMAIL>');
            if (demoAdmin) {
              setUser(demoAdmin);
              localStorage.setItem('ondc_auth_user', JSON.stringify(demoAdmin));
              console.log('🔧 Development mode: Auto-logged in as demo admin');
            }
          }
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        localStorage.removeItem('ondc_auth_user');
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // const login = async (email: string, password: string): Promise<void> => {
  //   setIsLoading(true);

  //   // Hardcoded credentials for development
  //   const adminCredentials = {
  //     email: '<EMAIL>',
  //     password: 'supersecret',
  //   };

  //   try {
  //     // For development, we'll use hardcoded credentials
  //     if (email === adminCredentials.email && password === adminCredentials.password) {
  //       // Create a mock user object for development
  //       const userWithLogin: User = {
  //         id: '1',
  //         email: adminCredentials.email,
  //         name: 'Admin User',
  //         role: 'admin',
  //         isActive: true,
  //         lastLogin: new Date(),
  //         token: 'dev-token-12345', // Mock token for development
  //       };

  //       setUser(userWithLogin);
  //       localStorage.setItem('ondc_auth_user', JSON.stringify(userWithLogin));
  //       console.log('Development login successful');
  //       return;
  //     }

  //     // For production, use the actual API call
  //     const response = await fetch(
  //       `${process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000'}/admin/auth`,
  //       {
  //         method: 'POST',
  //         headers: {
  //           'Content-Type': 'application/json',
  //           Accept: 'application/json',
  //         },
  //         credentials: 'include',
  //         body: JSON.stringify({ email, password }),
  //       }
  //     );

  //     if (!response.ok) {
  //       const errorData = await response.json().catch(() => ({}));
  //       throw new Error(
  //         errorData.message || 'Authentication failed. Please check your credentials.'
  //       );
  //     }

  //     const { user: medusaUser } = await response.json();

  //     if (!medusaUser) {
  //       throw new Error('Invalid response from server');
  //     }

  //     // Create user object with data from Medusa
  //     const userWithLogin: User = {
  //       id: medusaUser.id,
  //       email: medusaUser.email,
  //       name:
  //         `${medusaUser.first_name || ''} ${medusaUser.last_name || ''}`.trim() ||
  //         medusaUser.email.split('@')[0],
  //       role: medusaUser.role === 'admin' ? 'admin' : 'customer',
  //       isActive: true,
  //       lastLogin: new Date(),
  //       token: response.headers.get('set-cookie')?.split(';')[0] || '',
  //     };

  //     setUser(userWithLogin);
  //     localStorage.setItem('ondc_auth_user', JSON.stringify(userWithLogin));

  //     console.log('Login successful, user data stored');
  //   } catch (error) {
  //     throw error;
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };
  // Enhanced login function with multi-provider support
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const config = getAuthConfig();

      // Handle authentication based on provider
      switch (config.provider) {
        case 'development':
          // Development mode authentication with hardcoded credentials
          if (
            email === DEV_CREDENTIALS.username &&
            password === DEV_CREDENTIALS.password
          ) {
            const devUser = mockUsers.find(u => u.email === 'demo') || {
              id: '5',
              email: 'demo',
              name: 'Development User',
              role: 'admin' as const,
              avatar: '/images/avatars/dev.svg',
              permissions: ['*'],
              lastLogin: new Date(),
              isActive: true,
              token: 'dev-token-demo-demo',
            };

            setUser(devUser);
            localStorage.setItem('ondc_auth_user', JSON.stringify(devUser));
            localStorage.setItem(
              STORAGE_KEYS.TOKEN,
              devUser.token || 'dev-token-demo-demo'
            );

            console.log(
              '🔧 Development mode: Logged in with demo/demo credentials'
            );
            return;
          }
          break;

        case 'keycloak':
          // Keycloak authentication - redirect to Keycloak login
          const keycloakUser = await authService.login(email, password);
          if (keycloakUser) {
            setUser(keycloakUser);
            localStorage.setItem(
              'ondc_auth_user',
              JSON.stringify(keycloakUser)
            );
            console.log(
              '🔐 Keycloak authentication successful:',
              keycloakUser.email
            );
            return;
          }
          break;

        case 'onesso':
          // OneSSO authentication - redirect to OneSSO login
          await authService.login(email, password);
          // The actual user setting will happen in the callback page
          return;

        default:
          console.warn('Unknown authentication provider:', config.provider);
          break;
      }

      // Production mode authentication
      const response = await adminAuth(email, password);
      if (response) {
        const token = response;
        // Example: fetch user details after login (replace with actual API call if available)
        // For now, mock user object based on email
        let loggedInUser = mockUsers.find(u => u.email === email);
        if (!loggedInUser) {
          // Default to customer if not found
          loggedInUser = {
            id: Date.now().toString(),
            email,
            name: email.split('@')[0],
            role: 'admin',
            avatar: '/images/avatars/default.jpg',
            permissions: ['orders.view', 'profile.edit'],
            lastLogin: new Date(),
            isActive: true,
          };
        }
        // Attach token to user object
        loggedInUser.token = token;
        setUser(loggedInUser);
        localStorage.setItem('ondc_auth_user', JSON.stringify(loggedInUser));
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
      } else {
        throw new Error('Login failed');
      }
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  // Enhanced logout function with multi-provider support
  const logout = async () => {
    try {
      const config = getAuthConfig();

      // Handle logout based on provider
      switch (config.provider) {
        case 'keycloak':
          // Keycloak logout
          await authService.logout();
          console.log('🔐 Keycloak logout successful');
          break;

        case 'onesso':
          // OneSSO logout
          await authService.logout();
          console.log('🔐 OneSSO logout successful');
          break;

        case 'development':
          // Development mode logout
          console.log('🔧 Development mode logout');
          break;

        default:
          console.log('🔓 Standard logout');
          break;
      }

      // Clear local state and storage
      setUser(null);
      localStorage.removeItem('ondc_auth_user');
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local state even if remote logout fails
      setUser(null);
      localStorage.removeItem('ondc_auth_user');
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  };

  const register = async (userData: RegisterData): Promise<void> => {
    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === userData.email);
      if (existingUser) {
        throw new Error('User already exists');
      }

      // Create new user
      const newUser: User = {
        id: Date.now().toString(),
        email: userData.email,
        name: userData.name,
        role: userData.role || 'customer',
        avatar: '/images/avatars/default.jpg',
        permissions:
          userData.role === 'seller'
            ? ['products.manage', 'orders.view', 'analytics.view']
            : ['orders.view', 'profile.edit'],
        lastLogin: new Date(),
        isActive: true,
      };

      // Add to mock users (in real app, this would be an API call)
      mockUsers.push(newUser);

      setUser(newUser);
      localStorage.setItem('ondc_auth_user', JSON.stringify(newUser));
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (userData: Partial<User>): Promise<void> => {
    if (!user) throw new Error('No user logged in');

    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('ondc_auth_user', JSON.stringify(updatedUser));
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    if (user.permissions?.includes('*')) return true;
    return user.permissions?.includes(permission) || false;
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    register,
    updateProfile,
    hasPermission,
    isAdmin:
      !!user && (user.role === 'admin' || user.permissions?.includes('*')),
    isCustomer: !!user && user.role === 'customer',
    isSeller: !!user && user.role === 'seller',
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper hooks for specific roles
export function useAdminAuth() {
  const auth = useAuth();
  if (!auth.isAdmin) {
    throw new Error('Admin access required');
  }
  return auth;
}

export function useCustomerAuth() {
  const auth = useAuth();
  if (!auth.isCustomer) {
    throw new Error('Customer access required');
  }
  return auth;
}

export function useSellerAuth() {
  const auth = useAuth();
  if (!auth.isSeller) {
    throw new Error('Seller access required');
  }
  return auth;
}
