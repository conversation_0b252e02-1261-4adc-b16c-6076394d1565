'use client';

import React, {
  createContext,
  useContext,
  useReducer,
  useEffect,
  ReactNode,
} from 'react';
import { authAPI, User, LoginRequest, LoginResponse } from '@/lib/api/auth';
import medusaAdminAPI from '@/lib/medusa-admin-api';

// Types
interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<{
    user: User;
    token: string;
    shouldRedirectToOnboarding: boolean;
    currentStep: number;
    redirectPath: string;
  }>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  clearError: () => void;
  updateUser: (userData: Partial<User>) => void;
  registerTenant: (payload: any) => Promise<any>;
  updateOnboardingStatus: (statusData: {
    onboarding_status?: 'pending' | 'completed';
    onboarding_store_config?: boolean;
    onboarding_add_product?: boolean;
    onboarding_bulk_upload?: boolean;
  }) => Promise<void>;
  completeOnboarding: () => Promise<void>;
  checkOnboardingStatus: (user: User) => Promise<{
    shouldRedirectToOnboarding: boolean;
    currentStep: number;
    redirectPath: string;
  }>;
  isAdmin: boolean;
}

// Actions
type AuthAction =
  | { type: 'AUTH_START' }
  | {
      type: 'AUTH_SUCCESS';
      payload: { user: User; token: string; refreshToken: string };
    }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | {
      type: 'AUTH_REFRESH_SUCCESS';
      payload: { token: string; refreshToken: string };
    }
  | { type: 'CLEAR_ERROR' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'SET_LOADING'; payload: boolean };

// Initial state
const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case 'AUTH_FAILURE':
      return {
        ...state,
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };

    case 'AUTH_LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };

    case 'AUTH_REFRESH_SUCCESS':
      return {
        ...state,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        error: null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    default:
      return state;
  }
}

// Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  TOKEN: 'ondc_auth_token',
  REFRESH_TOKEN: 'ondc_refresh_token',
  USER: 'ondc_user',
} as const;

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        const userStr = localStorage.getItem(STORAGE_KEYS.USER);

        if (token && refreshToken && userStr) {
          try {
            const user = JSON.parse(userStr);

            // Directly restore auth state from localStorage without API calls
            // This prevents "Access Denied" flash and unnecessary network requests
            dispatch({
              type: 'AUTH_SUCCESS',
              payload: {
                user,
                token,
                refreshToken,
              },
            });

            console.log('✅ Auth restored from localStorage:', user.email);
          } catch (parseError) {
            console.error('Failed to parse stored user data:', parseError);
            clearAuthStorage();
            dispatch({ type: 'AUTH_LOGOUT' });
          }
        } else {
          // No stored auth data, user needs to login
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        clearAuthStorage();
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    };

    initializeAuth();
  }, []);

  // Helper function to clear auth storage
  const clearAuthStorage = () => {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER);
  };

  // Register Tenant
  const registerTenant = async (payload: any) => {
    try {
      dispatch({ type: 'AUTH_START' });

      // Step 1: Register the user
      const registrationResponse =
        await medusaAdminAPI.adminRegisteration(payload);
      console.log('✅ Registration successful:', registrationResponse);

      // Step 2: Automatically log in the newly registered user
      const loginResponse = await medusaAdminAPI.adminLogin({
        email: payload.email,
        password: payload.password,
      });

      console.log('✅ Auto-login successful:', loginResponse);

      if (loginResponse && loginResponse.token) {
        // Create user data from registration payload
        const user: User = {
          id: registrationResponse.user?.id || '1',
          email: payload.email,
          first_name: payload.firstName || 'Admin',
          last_name: payload.lastName || 'User',
          role: 'admin',
          avatar: '/images/avatars/admin.svg',
          status: 'active',
          last_login: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isNewUser: true, // Flag to identify new users for onboarding
          // Initialize onboarding metadata for new users
          onboarding_status: 'pending',
          onboarding_store_config: false,
          onboarding_add_product: false,
          onboarding_bulk_upload: false,
        };

        // Store authentication data
        localStorage.setItem(STORAGE_KEYS.TOKEN, loginResponse.token);
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));

        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user,
            token: loginResponse.token,
            refreshToken: '',
          },
        });

        return {
          ...registrationResponse,
          user,
          isNewUser: true,
          autoLoginSuccess: true,
        };
      }

      // Registration successful but auto-login failed
      dispatch({
        type: 'AUTH_FAILURE',
        payload: 'Auto-login failed after registration',
      });
      return {
        ...registrationResponse,
        autoLoginSuccess: false,
        message: 'Registration successful. Please log in manually.',
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Registration failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error;
    }
  };

  // Check onboarding status and determine redirect
  const checkOnboardingStatus = async (
    user: User
  ): Promise<{
    shouldRedirectToOnboarding: boolean;
    currentStep: number;
    redirectPath: string;
  }> => {
    try {
      // For demo mode, use local user data
      const onboardingStatus = user.onboarding_status || 'pending';
      const storeConfig = user.onboarding_store_config || false;
      const addProduct = user.onboarding_add_product || false;
      const bulkUpload = user.onboarding_bulk_upload || false;

      if (onboardingStatus === 'completed') {
        return {
          shouldRedirectToOnboarding: false,
          currentStep: 3,
          redirectPath: '/admin',
        };
      }

      // Determine current step based on completed flags
      let currentStep = 0;
      if (!storeConfig) {
        currentStep = 0; // Store Configuration
      } else if (!addProduct) {
        currentStep = 1; // Add Product
      } else if (!bulkUpload) {
        currentStep = 2; // Bulk Upload
      }

      return {
        shouldRedirectToOnboarding: true,
        currentStep,
        redirectPath: '/onboarding',
      };
    } catch (error) {
      console.error('Error checking onboarding status:', error);
      // Default to onboarding for safety
      return {
        shouldRedirectToOnboarding: true,
        currentStep: 0,
        redirectPath: '/onboarding',
      };
    }
  };

  // Login function
  const login = async (credentials: LoginRequest) => {
    try {
      dispatch({ type: 'AUTH_START' });

      // const response = await authAPI.login(credentials);
      const response = await medusaAdminAPI.adminLogin(credentials);

      console.log('::::::::response::::::::', response);
      if (response && response.token) {
        // Create admin user data from credentials
        const user: User = {
          id: '1',
          email: credentials.email,
          first_name: 'Admin',
          last_name: 'User',
          role: 'admin',
          avatar: '/images/avatars/admin.svg',
          status: 'active',
          last_login: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          // Initialize onboarding metadata for existing users (demo mode)
          onboarding_status: 'pending',
          onboarding_store_config: false,
          onboarding_add_product: false,
          onboarding_bulk_upload: false,
        };

        // Store in localStorage
        localStorage.setItem(STORAGE_KEYS.TOKEN, response.token);
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.token); // Use token as refresh token for now
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));

        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user,
            token: response.token,
            refreshToken: response.token,
          },
        });

        // Check onboarding status and return redirect info
        const onboardingInfo = await checkOnboardingStatus(user);
        return {
          user,
          token: response.token,
          ...onboardingInfo,
        };
      } else {
        throw new Error('Login failed');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Login failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call logout API if authenticated
      if (state.isAuthenticated) {
        await authAPI.logout();
      }
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Always clear local state and storage
      clearAuthStorage();
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  // Refresh auth function
  const refreshAuth = async () => {
    try {
      if (!state.refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await authAPI.refreshToken({
        refresh_token: state.refreshToken,
      });

      if (response.data) {
        const { token, refresh_token } = response.data;

        // Update localStorage
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refresh_token);

        dispatch({
          type: 'AUTH_REFRESH_SUCCESS',
          payload: { token, refreshToken: refresh_token },
        });
      } else {
        throw new Error('Refresh failed');
      }
    } catch (error) {
      console.error('Auth refresh error:', error);
      clearAuthStorage();
      dispatch({ type: 'AUTH_LOGOUT' });
      throw error;
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Update user function
  const updateUser = (userData: Partial<User>) => {
    if (state.user) {
      const updatedUser = { ...state.user, ...userData };
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(updatedUser));
      dispatch({ type: 'UPDATE_USER', payload: userData });
    }
  };

  // Update onboarding status function
  const updateOnboardingStatus = async (statusData: {
    onboarding_status?: 'pending' | 'completed';
    onboarding_store_config?: boolean;
    onboarding_add_product?: boolean;
    onboarding_bulk_upload?: boolean;
  }) => {
    try {
      if (state.user) {
        // Update local state immediately for better UX
        const updatedUser = { ...state.user, ...statusData };
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(updatedUser));
        dispatch({ type: 'UPDATE_USER', payload: statusData });

        // In production, this would call the backend API
        // await authAPI.updateOnboardingStatus(statusData);
        console.log('📝 Onboarding status updated:', statusData);
      }
    } catch (error) {
      console.error('Failed to update onboarding status:', error);
      throw error;
    }
  };

  // Complete onboarding function
  const completeOnboarding = async () => {
    try {
      await updateOnboardingStatus({
        onboarding_status: 'completed',
        onboarding_store_config: true,
        onboarding_add_product: true,
        onboarding_bulk_upload: true,
      });
      console.log('✅ Onboarding completed successfully');
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
      throw error;
    }
  };

  // Computed properties
  const isAdmin =
    state.user?.role === 'admin' || state.user?.role === 'super_admin';

  const contextValue: AuthContextType = {
    ...state,
    login,
    logout,
    refreshAuth,
    clearError,
    updateUser,
    registerTenant,
    updateOnboardingStatus,
    completeOnboarding,
    checkOnboardingStatus,
    isAdmin,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// HOC for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return <div>Loading...</div>;
    }

    if (!isAuthenticated) {
      return <div>Please log in to access this page.</div>;
    }

    return <Component {...props} />;
  };
}
