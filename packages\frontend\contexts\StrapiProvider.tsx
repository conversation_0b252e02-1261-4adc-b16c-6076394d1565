'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { strapiClient } from '@/lib/api/index.js';

interface StrapiContextType {
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  apiUrl: string;
  // Content fetching methods
  getContent: (endpoint: string, params?: any) => Promise<any>;
  getProducts: (params?: any) => Promise<any>;
  getCategories: (params?: any) => Promise<any>;
  getBanners: (params?: any) => Promise<any>;
  // Cache management
  clearCache: () => void;
  refreshConnection: () => Promise<void>;
}

const StrapiContext = createContext<StrapiContextType | undefined>(undefined);

interface StrapiProviderProps {
  children: ReactNode;
  apiUrl?: string;
}

export function StrapiProvider({ 
  children, 
  apiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339'
}: StrapiProviderProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cache, setCache] = useState<Map<string, any>>(new Map());

  // Test Strapi connection
  const testConnection = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Test basic connection by fetching a simple endpoint
      const response = await fetch(`${apiUrl}/api/categories?pagination[limit]=1`);
      
      if (response.ok) {
        setIsConnected(true);
        console.log('✅ [StrapiProvider] Connected to Strapi CMS');
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      setIsConnected(false);
      console.error('❌ [StrapiProvider] Failed to connect to Strapi:', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize connection on mount
  useEffect(() => {
    testConnection();
  }, [apiUrl]);

  // Generic content fetching method
  const getContent = async (endpoint: string, params: any = {}) => {
    try {
      const cacheKey = `${endpoint}-${JSON.stringify(params)}`;
      
      // Check cache first
      if (cache.has(cacheKey)) {
        console.log(`🎯 [StrapiProvider] Cache hit for ${endpoint}`);
        return cache.get(cacheKey);
      }

      console.log(`🚀 [StrapiProvider] Fetching ${endpoint}...`);
      const data = await strapiClient.get(endpoint, params);
      
      // Cache the result
      setCache(prev => new Map(prev.set(cacheKey, data)));
      
      return data;
    } catch (err) {
      console.error(`❌ [StrapiProvider] Error fetching ${endpoint}:`, err);
      throw err;
    }
  };

  // Specific content methods
  const getProducts = async (params: any = {}) => {
    return getContent('products', {
      populate: ['images', 'category', 'variants'],
      ...params,
    });
  };

  const getCategories = async (params: any = {}) => {
    return getContent('categories', {
      populate: ['image', 'subcategories'],
      ...params,
    });
  };

  const getBanners = async (params: any = {}) => {
    return getContent('banners', {
      populate: ['image'],
      ...params,
    });
  };

  // Cache management
  const clearCache = () => {
    setCache(new Map());
    console.log('🧹 [StrapiProvider] Cache cleared');
  };

  // Refresh connection
  const refreshConnection = async () => {
    clearCache();
    await testConnection();
  };

  const contextValue: StrapiContextType = {
    isConnected,
    isLoading,
    error,
    apiUrl,
    getContent,
    getProducts,
    getCategories,
    getBanners,
    clearCache,
    refreshConnection,
  };

  return (
    <StrapiContext.Provider value={contextValue}>
      {children}
    </StrapiContext.Provider>
  );
}

// Hook to use Strapi context
export function useStrapi(): StrapiContextType {
  const context = useContext(StrapiContext);
  if (context === undefined) {
    throw new Error('useStrapi must be used within a StrapiProvider');
  }
  return context;
}

// Hook for easy content fetching with SWR-like interface
export function useStrapiContent(endpoint: string, params?: any) {
  const { getContent } = useStrapi();
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const result = await getContent(endpoint, params);
        if (isMounted) {
          setData(result);
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Unknown error'));
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [endpoint, JSON.stringify(params), getContent]);

  return { data, error, isLoading };
}

export default StrapiProvider;
