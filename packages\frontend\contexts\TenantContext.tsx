'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { Seller, multiTenantAPI } from '@/lib/api/multi-tenant';
import { tenantCookies } from '@/lib/utils/cookies';

interface TenantContextType {
  selectedTenant: Seller | null;
  setSelectedTenant: (tenant: Seller | null) => void;
  tenantId: string | null;
  sellers: Seller[];
  loading: boolean;
  error: string | null;
  refreshSellers: () => Promise<void>;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

interface TenantProviderProps {
  children: ReactNode;
}

export function TenantProvider({ children }: TenantProviderProps) {
  const [selectedTenant, setSelectedTenant] = useState<Seller | null>(null);
  const [sellers, setSellers] = useState<Seller[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load tenant from cookies on initialization
  useEffect(() => {
    const savedTenantId = tenantCookies.getTenantId();
    if (savedTenantId) {
      console.log(
        '🍪 [TenantContext] Loading tenant from cookies:',
        savedTenantId
      );
      // We'll set the tenant after sellers are loaded
    }
  }, []);

  // Direct Strapi API call to fetch sellers
  const fetchSellers = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(
        '🚀 [TenantContext] Fetching sellers directly from Strapi...'
      );
      const response = await multiTenantAPI.getSellers();

      console.log(
        '✅ [TenantContext] Successfully fetched sellers:',
        response.data
      );
      setSellers(response.data);

      // Auto-select first tenant if none is selected
      if (response.data.length > 0 && !selectedTenant) {
        setSelectedTenant(response.data[0]);
        console.log(
          '🎯 [TenantContext] Auto-selected first tenant:',
          response.data[0]
        );
      }
    } catch (err) {
      console.error('❌ [TenantContext] Failed to fetch sellers:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch sellers');
      setSellers([]);
    } finally {
      setLoading(false);
    }
  };

  // Custom setSelectedTenant that also saves to cookies
  const handleSetSelectedTenant = (tenant: Seller | null) => {
    setSelectedTenant(tenant);
    if (tenant) {
      tenantCookies.setTenantId(tenant.id.toString());
      console.log('🍪 [TenantContext] Saved tenant to cookies:', tenant.id);
    } else {
      tenantCookies.removeTenantId();
      console.log('🍪 [TenantContext] Removed tenant from cookies');
    }
  };

  // Load tenant from cookies after sellers are fetched
  useEffect(() => {
    if (sellers.length > 0 && !selectedTenant) {
      const savedTenantId = tenantCookies.getTenantId();
      if (savedTenantId) {
        const savedTenant = sellers.find(
          s => s.id.toString() === savedTenantId
        );
        if (savedTenant) {
          setSelectedTenant(savedTenant);
          console.log(
            '🍪 [TenantContext] Restored tenant from cookies:',
            savedTenant
          );
        }
      }
    }
  }, [sellers, selectedTenant]);

  // Initial load
  useEffect(() => {
    fetchSellers();
  }, []);

  const tenantId = selectedTenant?.id?.toString() || null;

  const value: TenantContextType = {
    selectedTenant,
    setSelectedTenant: handleSetSelectedTenant,
    tenantId,
    sellers,
    loading,
    error,
    refreshSellers: fetchSellers,
  };

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    // Return default values instead of throwing error for better compatibility
    return {
      selectedTenant: null,
      setSelectedTenant: () => {},
      sellers: [],
      loading: false,
      error: null,
    };
  }
  return context;
}

export default TenantContext;
