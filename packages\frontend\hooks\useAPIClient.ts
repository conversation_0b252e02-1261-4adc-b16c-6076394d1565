/**
 * React hooks for API client using shared endpoint configuration
 * This provides a modern, type-safe way to interact with the backend
 */

import { useState, useEffect, useCallback } from 'react';
import { apiClient, APIClientError } from '../lib/api-client';

// Types
export interface Product {
  id: string;
  title: string;
  description?: string;
  handle: string;
  status: 'draft' | 'proposed' | 'published' | 'rejected';
  thumbnail?: string;
  images?: Array<{
    id: string;
    url: string;
  }>;
  variants?: Array<{
    id: string;
    title: string;
    prices: Array<{
      amount: number;
      currency_code: string;
    }>;
  }>;
  collection?: {
    id: string;
    title: string;
  };
  tags?: Array<{
    id: string;
    value: string;
  }>;
  created_at: string;
  updated_at: string;
}

// Hook for fetching products list
export function useProducts(
  params: {
    limit?: number;
    offset?: number;
    region_id?: string;
    collection_id?: string;
    category_id?: string;
    tags?: string[];
    q?: string;
    autoFetch?: boolean;
  } = {}
) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const { autoFetch = true, ...apiParams } = params;

  const fetchProducts = useCallback(
    async (reset = false) => {
      setLoading(true);
      setError(null);

      try {
        const currentOffset = reset ? 0 : apiParams.offset || 0;
        const response = await apiClient.getProducts({
          ...apiParams,
          offset: currentOffset,
        });

        const newProducts =
          response.data?.products || response.data?.items || [];

        if (reset) {
          setProducts(newProducts);
        } else {
          setProducts(prev => [...prev, ...newProducts]);
        }

        setTotalCount(response.data?.count || 0);
        setHasMore(newProducts.length === (apiParams.limit || 10));
      } catch (err) {
        const errorMessage =
          err instanceof APIClientError
            ? err.message
            : 'Failed to fetch products';
        setError(errorMessage);
        console.error('Error fetching products:', err);
      } finally {
        setLoading(false);
      }
    },
    [apiParams]
  );

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchProducts(false);
    }
  }, [fetchProducts, loading, hasMore]);

  const refresh = useCallback(() => {
    fetchProducts(true);
  }, [fetchProducts]);

  useEffect(() => {
    if (autoFetch) {
      fetchProducts(true);
    }
  }, [fetchProducts, autoFetch]);

  return {
    products,
    loading,
    error,
    totalCount,
    hasMore,
    fetchProducts: () => fetchProducts(true),
    loadMore,
    refresh,
  };
}

// Hook for fetching a single product
export function useProduct(productId: string | null, regionId?: string) {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async () => {
    if (!productId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.getProduct(productId, regionId);
      setProduct(response.data?.product || response.data);
    } catch (err) {
      const errorMessage =
        err instanceof APIClientError ? err.message : 'Failed to fetch product';
      setError(errorMessage);
      console.error('Error fetching product:', err);
    } finally {
      setLoading(false);
    }
  }, [productId, regionId]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch: fetchProduct,
  };
}

// Hook for featured products
export function useFeaturedProducts(limit = 6) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchFeaturedProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.getFeaturedProducts(limit);
      setProducts(response.data?.products || response.data?.items || []);
    } catch (err) {
      const errorMessage =
        err instanceof APIClientError
          ? err.message
          : 'Failed to fetch featured products';
      setError(errorMessage);
      console.error('Error fetching featured products:', err);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchFeaturedProducts();
  }, [fetchFeaturedProducts]);

  return {
    products,
    loading,
    error,
    refetch: fetchFeaturedProducts,
  };
}

// Hook for admin product management
export function useAdminProducts(
  params: {
    limit?: number;
    offset?: number;
    q?: string;
    status?: string[];
    collection_id?: string[];
    tags?: string[];
    autoFetch?: boolean;
  } = {}
) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const { autoFetch = true, ...apiParams } = params;

  const fetchProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.getAdminProducts(apiParams);
      setProducts(response.data?.products || response.data?.items || []);
      setTotalCount(response.data?.count || 0);
    } catch (err) {
      const errorMessage =
        err instanceof APIClientError
          ? err.message
          : 'Failed to fetch admin products';
      setError(errorMessage);
      console.error('Error fetching admin products:', err);
    } finally {
      setLoading(false);
    }
  }, [apiParams]);

  const createProduct = useCallback(
    async (productData: {
      title: string;
      description?: string;
      handle?: string;
      status?: 'draft' | 'proposed' | 'published' | 'rejected';
      thumbnail?: string;
      images?: string[];
      collection_id?: string;
      tags?: Array<{ value: string }>;
    }) => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiClient.createProduct(productData);
        await fetchProducts(); // Refresh the list
        return response.data?.product || response.data;
      } catch (err) {
        const errorMessage =
          err instanceof APIClientError
            ? err.message
            : 'Failed to create product';
        setError(errorMessage);
        console.error('Error creating product:', err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [fetchProducts]
  );

  const updateProduct = useCallback(
    async (id: string, productData: any) => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiClient.updateProduct(id, productData);
        await fetchProducts(); // Refresh the list
        return response.data?.product || response.data;
      } catch (err) {
        const errorMessage =
          err instanceof APIClientError
            ? err.message
            : 'Failed to update product';
        setError(errorMessage);
        console.error('Error updating product:', err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [fetchProducts]
  );

  const deleteProduct = useCallback(
    async (id: string) => {
      setLoading(true);
      setError(null);

      try {
        await apiClient.deleteProduct(id);
        await fetchProducts(); // Refresh the list
      } catch (err) {
        const errorMessage =
          err instanceof APIClientError
            ? err.message
            : 'Failed to delete product';
        setError(errorMessage);
        console.error('Error deleting product:', err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [fetchProducts]
  );

  useEffect(() => {
    if (autoFetch) {
      fetchProducts();
    }
  }, [fetchProducts, autoFetch]);

  return {
    products,
    loading,
    error,
    totalCount,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    refresh: fetchProducts,
  };
}

// Hook for system health
export function useSystemHealth() {
  const [health, setHealth] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkHealth = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.getHealth();
      setHealth(response.data);
    } catch (err) {
      const errorMessage =
        err instanceof APIClientError
          ? err.message
          : 'Failed to check system health';
      setError(errorMessage);
      console.error('Error checking system health:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    checkHealth();
  }, [checkHealth]);

  return {
    health,
    loading,
    error,
    refetch: checkHealth,
  };
}

// Hook for analytics
export function useDashboardAnalytics() {
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.getDashboardAnalytics();
      setAnalytics(response.data);
    } catch (err) {
      const errorMessage =
        err instanceof APIClientError
          ? err.message
          : 'Failed to fetch analytics';
      setError(errorMessage);
      console.error('Error fetching analytics:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  return {
    analytics,
    loading,
    error,
    refetch: fetchAnalytics,
  };
}
