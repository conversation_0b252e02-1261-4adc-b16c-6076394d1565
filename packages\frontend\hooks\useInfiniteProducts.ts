'use client';

import { useState, useCallback, useEffect } from 'react';
import { useMedusaBackendProducts } from './useMedusaBackendProducts';

export interface UseInfiniteProductsOptions {
  limit?: number;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  categoryId?: string;
  collectionId?: string;
  searchQuery?: string;
}

export interface UseInfiniteProductsReturn {
  products: any[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  totalCount: number;
  currentPage: number;
}

export const useInfiniteProducts = (
  options: UseInfiniteProductsOptions = {}
): UseInfiniteProductsReturn => {
  const {
    limit = 12,
    filters = {},
    sortBy = 'created_at',
    sortOrder = 'desc',
    categoryId,
    collectionId,
    searchQuery,
  } = options;

  const { products: allProducts, loading: baseLoading, error: baseError, fetchProducts } = useMedusaBackendProducts();
  
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);

  // Build query parameters
  const buildQueryParams = useCallback((page: number) => {
    const params: any = {
      limit,
      offset: (page - 1) * limit,
    };

    // Add sorting
    if (sortBy) {
      params.order = `${sortBy}:${sortOrder}`;
    }

    // Add category filter
    if (categoryId) {
      params.category_id = [categoryId];
    }

    // Add collection filter
    if (collectionId) {
      params.collection_id = [collectionId];
    }

    // Add search query
    if (searchQuery) {
      params.q = searchQuery;
    }

    // Add custom filters
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== null) {
        params[key] = filters[key];
      }
    });

    return params;
  }, [limit, sortBy, sortOrder, categoryId, collectionId, searchQuery, filters]);

  // Load initial products
  const loadInitialProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🚀 [useInfiniteProducts] Loading initial products...');
      
      const params = buildQueryParams(1);
      const fetchedProducts = await fetchProducts(params);
      
      if (fetchedProducts && Array.isArray(fetchedProducts)) {
        setProducts(fetchedProducts);
        setCurrentPage(1);
        setTotalCount(fetchedProducts.length);
        setHasMore(fetchedProducts.length >= limit);
        
        console.log(`✅ [useInfiniteProducts] Loaded ${fetchedProducts.length} initial products`);
      } else {
        setProducts([]);
        setHasMore(false);
        setTotalCount(0);
      }
    } catch (err) {
      console.error('❌ [useInfiniteProducts] Error loading initial products:', err);
      setError(err instanceof Error ? err.message : 'Failed to load products');
      setProducts([]);
      setHasMore(false);
    } finally {
      setLoading(false);
      setIsInitialized(true);
    }
  }, [buildQueryParams, fetchProducts, limit]);

  // Load more products (for infinite scroll)
  const loadMore = useCallback(async () => {
    if (loading || !hasMore) {
      console.log('⚠️ [useInfiniteProducts] Skipping loadMore - already loading or no more products');
      return;
    }

    try {
      setLoading(true);
      
      const nextPage = currentPage + 1;
      console.log(`🚀 [useInfiniteProducts] Loading page ${nextPage}...`);
      
      const params = buildQueryParams(nextPage);
      const fetchedProducts = await fetchProducts(params);
      
      if (fetchedProducts && Array.isArray(fetchedProducts) && fetchedProducts.length > 0) {
        setProducts(prev => [...prev, ...fetchedProducts]);
        setCurrentPage(nextPage);
        setTotalCount(prev => prev + fetchedProducts.length);
        setHasMore(fetchedProducts.length >= limit);
        
        console.log(`✅ [useInfiniteProducts] Loaded ${fetchedProducts.length} more products (page ${nextPage})`);
      } else {
        setHasMore(false);
        console.log('✅ [useInfiniteProducts] No more products to load');
      }
    } catch (err) {
      console.error('❌ [useInfiniteProducts] Error loading more products:', err);
      setError(err instanceof Error ? err.message : 'Failed to load more products');
    } finally {
      setLoading(false);
    }
  }, [loading, hasMore, currentPage, buildQueryParams, fetchProducts, limit]);

  // Refresh products (reset to first page)
  const refresh = useCallback(async () => {
    console.log('🔄 [useInfiniteProducts] Refreshing products...');
    setProducts([]);
    setCurrentPage(1);
    setHasMore(true);
    setTotalCount(0);
    setIsInitialized(false);
    await loadInitialProducts();
  }, [loadInitialProducts]);

  // Load initial products when options change
  useEffect(() => {
    loadInitialProducts();
  }, [loadInitialProducts]);

  // Handle base loading and error states
  const finalLoading = loading || (baseLoading && !isInitialized);
  const finalError = error || baseError;

  return {
    products,
    loading: finalLoading,
    error: finalError,
    hasMore,
    loadMore,
    refresh,
    totalCount,
    currentPage,
  };
};

// Hook for category-specific infinite products
export const useInfiniteCategoryProducts = (categoryId: string, options: Omit<UseInfiniteProductsOptions, 'categoryId'> = {}) => {
  return useInfiniteProducts({
    ...options,
    categoryId,
  });
};

// Hook for collection-specific infinite products
export const useInfiniteCollectionProducts = (collectionId: string, options: Omit<UseInfiniteProductsOptions, 'collectionId'> = {}) => {
  return useInfiniteProducts({
    ...options,
    collectionId,
  });
};

// Hook for search-based infinite products
export const useInfiniteSearchProducts = (searchQuery: string, options: Omit<UseInfiniteProductsOptions, 'searchQuery'> = {}) => {
  return useInfiniteProducts({
    ...options,
    searchQuery,
  });
};

export default useInfiniteProducts;
