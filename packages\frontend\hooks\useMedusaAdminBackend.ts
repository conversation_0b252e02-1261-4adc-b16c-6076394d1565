'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  medusaAPI,
  MedusaProduct,
  MedusaCategory,
  ProductsResponse,
  CategoriesResponse,
  MedusaAPIError,
} from '../lib/medusa-backend-api';
import medusaAdminAPI from '@/lib/medusa-admin-api';
import { useToast } from '@/components/common/ToastProvider';

interface UseMedusaBackendProductsReturn {
  // Products state
  products: MedusaProduct[];
  categories: MedusaCategory[];
  currentProduct: MedusaProduct | null;

  // Pagination
  totalProducts: number;
  currentPage: number;
  totalPages: number;

  // Loading states
  loading: boolean;
  loadingProduct: boolean;
  loadingCategories: boolean;

  // Error handling
  error: string | null;
  clearError: () => void;

  // Actions
  fetchProducts: (params?: {
    limit?: number;
    offset?: number;
    q?: string;
    category_id?: string[];
    collection_id?: string[];
    tags?: string[];
    type_id?: string[];
  }) => Promise<void>;
  fetchProduct: (id: string) => Promise<void>;
  fetchCategories: (params?: {
    limit?: number;
    offset?: number;
    parent_category_id?: string;
    include_descendants_tree?: boolean;
  }) => Promise<void>;
  fetchCollections: (params?: {
    limit?: number;
    offset?: number;
    parent_category_id?: string;
    include_descendants_tree?: boolean;
  }) => Promise<void>;
  fetchTags: (params?: { limit?: number; offset?: number }) => Promise<void>;
  searchProducts: (query: string, limit?: number) => Promise<void>;
  getProductsByCategory: (categoryId: string, limit?: number) => Promise<void>;
  getFeaturedProducts: (limit?: number) => Promise<void>;
  getTopDeals: (limit?: number) => Promise<void>;
  getHotPicks: (limit?: number) => Promise<void>;
  // getFilteredProducts: (filter?: number) => Promise<void>;

  // Utility functions
  resetProducts: () => void;
  resetCurrentProduct: () => void;
}

interface MedusaPromotion {
  id: string;
  code: string;
  type: 'standard' | 'parent';
  status: 'draft' | 'published' | 'disabled' | 'archived';
  created_at: string;
  updated_at: string;
  /* ⇣ keep the fields you actually use in the UI */
  application_method: {
    id: string;
    type: 'percentage' | 'fixed' | 'free_shipping';
    target_type: 'order' | 'items' | 'shipping_methods';
    value: number;
    currency_code: string | null;
  };
  rules: unknown[];
  campaign_id: string | null;
  /* add any other columns you need */
}
type MedusaProduct = any;
type ProductsResponse = { products: MedusaProduct[] };

export function useMedusaAdminProducts(): UseMedusaBackendProductsReturn {
  const [products, setProducts] = useState<MedusaProduct[]>([]);
  const [categories, setCategories] = useState<MedusaCategory[]>([]);
  const [collections, setCollections] = useState<any[]>([]);
  const [tags, setTags] = useState<any[]>([]);
  const [currentProduct, setCurrentProduct] = useState<MedusaProduct | null>(
    null
  );
  const [totalProducts, setTotalProducts] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [loadingProduct, setLoadingProduct] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loadingCollections, setLoadingCollections] = useState(false);
  const [loadingTags, setLoadingTags] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback((err: unknown, defaultMessage: string) => {
    console.error('Medusa Backend Products error:', err);
    if (err instanceof MedusaAPIError) {
      setError(err.message);
    } else if (err instanceof Error) {
      setError(err.message);
    } else {
      setError(defaultMessage);
    }
  }, []);

  const fetchProducts = useCallback(
    async (params?: {
      limit?: number;
      offset?: number;
      q?: string;
      category_id?: string[];
      collection_id?: string[];
      tags?: string[];
      type_id?: string[];
      fields?: string;
    }) => {
      try {
        setLoading(true);
        setError(null);

        // Optimized parameters for faster loading
        const optimizedParams = {
          ...params,
          limit: params?.limit || 50, // Reduced from default 20 for better UX
          offset: params?.offset || 0,
        };

        const response: ProductsResponse =
          await medusaAdminAPI.getProducts(optimizedParams);
        console.log('fetch api response::::', { response });

        // Set products immediately for faster UI update
        if (response?.products) {
          setProducts(response.products);
          setTotalProducts(response.count || response.products.length);

          // Calculate pagination
          const limit = optimizedParams.limit;
          const offset = optimizedParams.offset;
          setCurrentPage(Math.floor(offset / limit) + 1);
        }
      } catch (err) {
        handleError(err, 'Failed to fetch products');
        // Set empty state on error
        setProducts([]);
        setTotalProducts(0);
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  const fetchProduct = useCallback(
    async (id: string) => {
      try {
        setLoadingProduct(true);
        setError(null);
        console.log('fetching product----------------', id);
        const response = await medusaAdminAPI.getProduct(id);
        console.log('fetch product api response::::', { response });
        setCurrentProduct(response.product);
      } catch (err) {
        handleError(err, 'Failed to fetch product');
      } finally {
        setLoadingProduct(false);
      }
    },
    [handleError]
  );

  const fetchCategories = useCallback(
    async (params?: {
      limit?: number;
      offset?: number;
      parent_category_id?: string;
      include_descendants_tree?: boolean;
    }) => {
      try {
        setLoadingCategories(true);
        setError(null);

        const response: CategoriesResponse =
          await medusaAdminAPI.getCategories(params);
        setCategories(response.product_categories);
      } catch (err) {
        handleError(err, 'Failed to fetch categories');
      } finally {
        setLoadingCategories(false);
      }
    },
    [handleError]
  );

  const searchProducts = useCallback(
    async (query: string, limit = 20) => {
      await fetchProducts({ q: query, limit });
    },
    [fetchProducts]
  );

  const getProductsByCategory = useCallback(
    async (categoryId: string, limit = 20) => {
      await fetchProducts({ category_id: [categoryId], limit });
    },
    [fetchProducts]
  );

  const getFeaturedProducts = useCallback(
    async (limit = 10) => {
      // Use collection_id instead of tags for Medusa v2
      await fetchProducts({
        collection_id: ['pcol_01JZ81RC5AMYQKDP3YQMHRHRD1'],
        limit,
      }); // Featured Products collection
    },
    [fetchProducts]
  );

  const getTopDeals = useCallback(
    async (limit = 10) => {
      // Use collection_id instead of tags for Medusa v2
      await fetchProducts({
        collection_id: ['pcol_01JZ81SMDPT78PA350JRQ3BJTZ'],
        limit,
      }); // Top deals collection
    },
    [fetchProducts]
  );

  const getHotPicks = useCallback(
    async (limit = 10) => {
      // Use collection_id instead of tags for Medusa v2
      await fetchProducts({
        collection_id: ['pcol_01JZ81S667NF4RSB7ZJXK3N0JC'],
        limit,
      }); // Hot picks collection
    },
    [fetchProducts]
  );

  const fetchCollections = useCallback(
    async (params?: {
      limit?: number;
      offset?: number;
      parent_category_id?: string;
      include_descendants_tree?: boolean;
    }) => {
      try {
        setLoadingCollections(true);
        setError(null);

        const response: CategoriesResponse =
          await medusaAdminAPI.getCollections(params);
        setCollections(response.collections);
      } catch (err) {
        handleError(err, 'Failed to fetch categories');
      } finally {
        setLoadingCollections(false);
      }
    },
    [handleError]
  );

  const fetchTags = useCallback(
    async (params?: { limit?: number; offset?: number }) => {
      try {
        setLoadingTags(true);
        setError(null);

        const response: CategoriesResponse =
          await medusaAdminAPI.getTags(params);
        setTags(response.product_tags);
      } catch (err) {
        handleError(err, 'Failed to fetch categories');
      } finally {
        setLoadingTags(false);
      }
    },
    [handleError]
  );

  const resetProducts = useCallback(() => {
    setProducts([]);
    setTotalProducts(0);
    setCurrentPage(1);
  }, []);

  const resetCurrentProduct = useCallback(() => {
    setCurrentProduct(null);
  }, []);

  // Calculate total pages
  const totalPages = Math.ceil(totalProducts / 20); // Assuming default limit of 20

  // Load categories on mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    // Products state
    products,
    categories,
    collections,
    tags,
    currentProduct,

    // Pagination
    totalProducts,
    currentPage,
    totalPages,

    // Loading states
    loading,
    loadingProduct,
    loadingCategories,
    loadingCollections,
    loadingTags,

    // Error handling
    error,
    clearError,

    // Actions
    fetchProducts,
    fetchProduct,
    fetchCategories,
    fetchCollections,
    fetchTags,
    searchProducts,
    getProductsByCategory,
    getFeaturedProducts,
    getTopDeals,
    getHotPicks,

    // Utility functions
    resetProducts,
    resetCurrentProduct,
  };
}

// Hook for single product with automatic loading
export function useMedusaBackendProduct(productId: string | null) {
  const [product, setProduct] = useState<MedusaProduct | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async () => {
    if (!productId) {
      setProduct(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await medusaAdminAPI.getProduct(productId);
      setProduct(response.product);
    } catch (err) {
      console.error('Error fetching product:', err);
      if (err instanceof MedusaAPIError) {
        setError(err.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to fetch product');
      }
    } finally {
      setLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch: fetchProduct,
  };
}

// **
//  * Hook: Medusa product CRUD
//  *
//  * @param initialProductId  –  optional; if supplied the hook will auto-fetch that product on mount.

export function useMedusaBackendProducts() {
  const toast = useToast();
  /* ---------- State ---------- */
  const [products, setProducts] = useState<MedusaProduct[]>([]);
  const [product, setProduct] = useState<MedusaProduct | null>(null);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /* ---------- Utilities ---------- */
  const clearError = useCallback(() => setError(null), []);

  const handleError = useCallback((err: unknown, defaultMessage: string) => {
    console.error('Medusa Backend Products error:', err);

    if (err instanceof MedusaAPIError) setError(err.message);
    else if (err instanceof Error) setError(err.message);
    else setError(defaultMessage);
    toast.error(err?.message || defaultMessage);
  }, []);

  /* ---------- READ ---------- */

  /** List - GET /admin/products */
  const fetchProducts = useCallback(
    async (params?: {
      limit?: number;
      offset?: number;
      q?: string;
      sort?: string;
    }) => {
      try {
        setLoading(true);
        setError(null);

        const res: ProductsResponse = await medusaAdminAPI.getProducts(params);
        setProducts(res.products);
      } catch (err) {
        handleError(err, 'Failed to fetch products');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  /** Single - GET /admin/products/:id */
  const fetchProduct = useCallback(
    async (productId: string | null) => {
      if (!productId) {
        setProduct(null);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const res = await medusaAdminAPI.getProduct(productId);
        setProduct(res.product);
      } catch (err) {
        handleError(err, 'Failed to fetch product');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  /* ---------- CREATE ---------- */

  /** POST /admin/products */
  const createProduct = useCallback(
    async (payload: any) => {
      try {
        setLoading(true);
        setError(null);

        const response = await medusaAdminAPI.addSingleProduct(payload);
        console.log('add product response', response);
        setProduct(response?.product); // show it immediately
        return response;
      } catch (err) {
        handleError(err, 'Failed to create product');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  /* ---------- UPDATE ---------- */

  /** PATCH /admin/products/:id */
  const updateProduct = useCallback(
    async (productId: string, payload: any) => {
      try {
        setLoading(true);
        setError(null);

        const res = await medusaAdminAPI.updateSingleProduct(
          productId,
          payload
        );
        console.log('edit product response', { res });

        setProduct(res.product);
        return res;
      } catch (err) {
        handleError(err, 'Failed to update product');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  /* ---------- DELETE ---------- */

  /** DELETE /admin/products/:id */
  const deleteProduct = useCallback(
    async (productId: string) => {
      try {
        setLoading(true);
        setError(null);

        const response = await medusaAdminAPI.removeSingleProduct(productId);
        setProduct(null); // clear single-view
        setProducts(p => p.filter(prod => prod.id !== productId)); // keep list in sync
        return response;
      } catch (err) {
        handleError(err, 'Failed to delete product');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  /* ---------- Auto-fetch (optional) ---------- */
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  /* ---------- Public API ---------- */
  return {
    /* data */
    products, // list   (empty until you call fetchProducts)
    product, // single (null until you call fetchProduct / createProduct)

    /* status */
    loading,
    error,

    /* helpers */
    clearError,

    /* CRUD */
    fetchProducts,
    fetchProduct,
    createProduct,
    updateProduct,
    deleteProduct,
  };
}

// Hook for categories with automatic loading
export function useMedusaBackendCategories() {
  const [categories, setCategories] = useState<MedusaCategory[]>([]);
  const [singleCategory, setSingleCategory] = useState<MedusaCategory | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback((err: unknown, defaultMessage: string) => {
    console.error('Medusa Backend Products error:', err);
    if (err instanceof MedusaAPIError) {
      setError(err.message);
    } else if (err instanceof Error) {
      setError(err.message);
    } else {
      setError(defaultMessage);
    }
  }, []);

  const fetchCategories = useCallback(
    async (params?: {
      limit?: number;
      offset?: number;
      parent_category_id?: string;
      include_descendants_tree?: boolean;
    }) => {
      try {
        setLoading(true);
        setError(null);

        const response: CategoriesResponse =
          await medusaAdminAPI.getCategories(params);
        setCategories(response.product_categories);
      } catch (err) {
        handleError(err, 'Failed to fetch categories');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  const fetchSingleCategory = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        setError(null);

        const response = await medusaAdminAPI.getSingleCategory({ id });
        console.log('fetch product api response::::', { response });
        setSingleCategory(response?.product_category);
      } catch (err) {
        handleError(err, 'Failed to fetch product');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  /**
   * POST /admin/product-categories
   */
  const createCategory = useCallback(
    async (payload: any) => {
      try {
        setLoading(true);
        setError(null);

        const response = await medusaAdminAPI.addSingleCategory(payload);
        console.log('create category api response::::', { response });

        // If you want to show the newly-created category right away:
        setSingleCategory(response?.product_category);
        // Or push it into a list, invalidate queries, etc.
      } catch (err) {
        handleError(err, 'Failed to create category');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  /**
   * PATCH /admin/product-categories/:id
   */
  const updateCategory = useCallback(
    async (categoryId: string, payload: any) => {
      try {
        setLoading(true);
        setError(null);

        const response = await medusaAdminAPI.updateSingleCategory(
          categoryId,
          payload
        );
        console.log('update category api response::::', { response });

        // Keep local state in sync
        setSingleCategory(response?.product_category);
      } catch (err) {
        handleError(err, 'Failed to update category');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  /**
   * DELETE /admin/product-categories/:id
   */
  const deleteCategory = useCallback(
    async (categoryId: string) => {
      try {
        setLoading(true);
        setError(null);

        await medusaAdminAPI.removeSingleCategory(categoryId);
        console.log('delete category api response:::: success');

        // Option 1: clear single-category state
        setSingleCategory(null);
        // Option 2: refetch list / invalidate query cache, etc.
      } catch (err) {
        handleError(err, 'Failed to delete category');
      } finally {
        setLoading(false);
      }
    },
    [handleError]
  );

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    singleCategory,

    loading,
    error,

    fetchCategories,
    fetchSingleCategory,
    deleteCategory,
    updateCategory,
    createCategory,
  };
}

export function useMedusaBackendLogin() {
  try {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [token, setToken] = useState<string | null>(null);
    const adminAuth = useCallback(async (email: string, password: string) => {
      try {
        setLoading(true);
        setError(null);

        const response = await medusaAdminAPI.adminLogin({ email, password });
        // console.log('response', response);
        setToken(response?.token);
        return response?.token;
      } catch (err) {
        console.error('Error in admin login:', err);
        if (err instanceof MedusaAPIError) {
          setError(err.message);
        } else if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('Failed to login');
        }
      } finally {
        setLoading(false);
      }
    }, []);
    return {
      adminAuth,
      loading,
      error,
      token,
    };
  } catch (error) {
    console.error('Authentication failed!', error);
  }
}

export function useMedusaBackendCollections() {
  const [collections, setCollections] = useState<MedusaCollection[]>([]);
  const [singleCollection, setSingleCollection] =
    useState<MedusaCollection | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /* --- helpers ---------------------------------------------------- */
  const clearError = useCallback(() => setError(null), []);

  const handleError = useCallback((err: unknown, fallback: string) => {
    console.error('Medusa Backend Collections error:', err);
    if (err instanceof MedusaAPIError || err instanceof Error) {
      setError(err.message);
    } else {
      setError(fallback);
    }
  }, []);

  /* --- CRUD ------------------------------------------------------- */
  const fetchCollections = useCallback(
    async (params?: { limit?: number; offset?: number }) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getCollections(params);
        console.log('fetch collections api response::::', { res });
        setCollections(res.collections);
      } catch (err) {
        handleError(err, 'Failed to fetch collections');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const fetchSingleCollection = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getSingleCollection({ id });
        setSingleCollection(res.collection);
      } catch (err) {
        handleError(err, 'Failed to fetch collection');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const createCollection = useCallback(
    async (payload: any) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.addSingleCollection(payload);
        setSingleCollection(res.product_collection);
      } catch (err) {
        handleError(err, 'Failed to create collection');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const updateCollection = useCallback(
    async (id: string, payload: any) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.updateSingleCollection(id, payload);
        setSingleCollection(res.product_collection);
      } catch (err) {
        handleError(err, 'Failed to update collection');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const deleteCollection = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        clearError();
        await medusaAdminAPI.removeSingleCollection(id);
        setSingleCollection(null);
      } catch (err) {
        handleError(err, 'Failed to delete collection');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /* --- auto-initialisation ---------------------------------------- */
  useEffect(() => {
    fetchCollections();
  }, [fetchCollections]);

  return {
    collections,
    singleCollection,
    loading,
    error,
    fetchCollections,
    fetchSingleCollection,
    createCollection,
    updateCollection,
    deleteCollection,
  };
}

/* =========================================================
   Hook: useMedusaBackendTags
   ========================================================= */

export function useMedusaBackendTags() {
  const [tags, setTags] = useState<MedusaTag[]>([]);
  const [singleTag, setSingleTag] = useState<MedusaTag | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => setError(null), []);

  const handleError = useCallback((err: unknown, fallback: string) => {
    console.error('Medusa Backend Tags error:', err);
    if (err instanceof MedusaAPIError || err instanceof Error) {
      setError(err.message);
    } else {
      setError(fallback);
    }
  }, []);

  const fetchTags = useCallback(
    async (params?: { limit?: number; offset?: number }) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getTags(params);
        setTags(res.product_tags);
      } catch (err) {
        handleError(err, 'Failed to fetch tags');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const fetchSingleTag = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getSingleTag({ id });
        setSingleTag(res.product_tag);
      } catch (err) {
        handleError(err, 'Failed to fetch tag');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const createTag = useCallback(
    async (payload: any) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.addSingleTag(payload);
        setSingleTag(res.product_tag);
      } catch (err) {
        handleError(err, 'Failed to create tag');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const updateTag = useCallback(
    async (id: string, payload: any) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.updateSingleTag(id, payload);
        setSingleTag(res.product_tag);
      } catch (err) {
        handleError(err, 'Failed to update tag');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const deleteTag = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        clearError();
        await medusaAdminAPI.removeSingleTag(id);
        setSingleTag(null);
      } catch (err) {
        handleError(err, 'Failed to delete tag');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  return {
    tags,
    singleTag,
    loading,
    error,
    fetchTags,
    fetchSingleTag,
    createTag,
    updateTag,
    deleteTag,
  };
}

/* =====================================================================
   Hook: useMedusaBackendOrders  (GET all + GET single)
   ===================================================================== */
export function useMedusaBackendOrders() {
  const [orders, setOrders] = useState<MedusaOrder[]>([]);
  const [singleOrder, setSingleOrder] = useState<MedusaOrder | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /* --- shared helpers -------------------------------------------- */
  const clearError = useCallback(() => setError(null), []);
  const handleError = useCallback((err: unknown, fallback: string) => {
    console.error('Medusa Backend Orders error:', err);
    if (err instanceof MedusaAPIError || err instanceof Error) {
      setError(err.message);
    } else {
      setError(fallback);
    }
  }, []);

  /* --- list & single fetch --------------------------------------- */
  const fetchOrders = useCallback(
    async (params?: {
      limit?: number;
      offset?: number;
      customer_id?: string[];
    }) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getAllOrders(params);
        setOrders(res.orders); // adjust key if API differs
      } catch (err) {
        handleError(err, 'Failed to fetch orders');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const fetchSingleOrder = useCallback(
    async (orderId: string) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getSingleOrder(orderId);
        setSingleOrder(res.order); // adjust key if API differs
      } catch (err) {
        handleError(err, 'Failed to fetch order');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /* auto-load list on mount */
  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  return {
    orders,
    singleOrder,
    loading,
    error,
    fetchOrders,
    fetchSingleOrder,
  };
}

/* =====================================================================
   Hook: useMedusaBackendCustomers  (full CRUD)
   ===================================================================== */
export function useMedusaBackendCustomers() {
  const [customers, setCustomers] = useState<MedusaCustomer[]>([]);
  const [singleCustomer, setSingleCustomer] = useState<MedusaCustomer | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => setError(null), []);
  const handleError = useCallback((err: unknown, msg: string) => {
    console.error('Medusa Backend Customers error:', err);
    if (err instanceof MedusaAPIError || err instanceof Error) {
      setError(err.message);
    } else {
      setError(msg);
    }
  }, []);

  /* ----------- READ ---------------------------------------------- */
  const fetchCustomers = useCallback(
    async (params?: { limit?: number; offset?: number; q?: string }) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getCustomers(params);
        setCustomers(res.customers);
      } catch (err) {
        handleError(err, 'Failed to fetch customers');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  const fetchSingleCustomer = useCallback(
    async (customerId: string) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getSingleCustomer(customerId);
        setSingleCustomer(res.customer);
      } catch (err) {
        handleError(err, 'Failed to fetch customer');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /* ----------- CREATE -------------------------------------------- */
  const createCustomer = useCallback(
    async (payload: any) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.addSingleCustomer(payload);
        setSingleCustomer(res.customer);
      } catch (err) {
        handleError(err, 'Failed to create customer');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /* ----------- UPDATE -------------------------------------------- */
  const updateCustomer = useCallback(
    async (id: string, payload: any) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.updateSingleCustomer(id, payload);
        setSingleCustomer(res.customer);
      } catch (err) {
        handleError(err, 'Failed to update customer');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /* ----------- DELETE -------------------------------------------- */
  const deleteCustomer = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        clearError();
        await medusaAdminAPI.removeSingleCustomer(id);
        setSingleCustomer(null);
      } catch (err) {
        handleError(err, 'Failed to delete customer');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /* auto-load customer list on mount */
  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  return {
    customers,
    singleCustomer,
    loading,
    error,
    fetchCustomers,
    fetchSingleCustomer,
    createCustomer,
    updateCustomer,
    deleteCustomer,
  };
}

export function useMedusaBackendPromotions() {
  const [promotions, setPromotions] = useState<MedusaPromotion[]>([]);
  const [singlePromotion, setSinglePromotion] =
    useState<MedusaPromotion | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /* ---------- helpers ---------- */

  const clearError = useCallback(() => setError(null), []);

  const handleError = useCallback((err: unknown, fallback: string) => {
    console.error('Medusa Backend Promotions error:', err);
    if (err instanceof MedusaAPIError || err instanceof Error) {
      setError(err.message);
    } else {
      setError(fallback);
    }
  }, []);

  /* ---------- CRUD ---------- */

  /** GET /admin/promotions */
  const fetchPromotions = useCallback(
    async (params?: { limit?: number; offset?: number }) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getPromotions(params); // << implement in your API wrapper
        setPromotions(res.promotions as MedusaPromotion[]);
      } catch (err) {
        handleError(err, 'Failed to fetch promotions');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /** GET /admin/promotions/:id */
  const fetchSinglePromotion = useCallback(
    async (id: string, fields?: string[]) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.getSinglePromotion({ id, fields });
        setSinglePromotion(res.promotion as MedusaPromotion);
      } catch (err) {
        handleError(err, 'Failed to fetch promotion');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /** POST /admin/promotions */
  const createPromotion = useCallback(
    async (payload: Record<string, any>) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.addPromotion(payload);
        setSinglePromotion(res.promotion as MedusaPromotion);
      } catch (err) {
        handleError(err, 'Failed to create promotion');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /** POST /admin/promotions/:id */
  const updatePromotion = useCallback(
    async (id: string, payload: Record<string, any>) => {
      try {
        setLoading(true);
        clearError();
        const res = await medusaAdminAPI.updatePromotion(id, payload);
        setSinglePromotion(res.promotion as MedusaPromotion);
      } catch (err) {
        handleError(err, 'Failed to update promotion');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /** DELETE /admin/promotions/:id */
  const deletePromotion = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        clearError();
        await medusaAdminAPI.removePromotion(id);
        setSinglePromotion(null);
      } catch (err) {
        handleError(err, 'Failed to delete promotion');
      } finally {
        setLoading(false);
      }
    },
    [clearError, handleError]
  );

  /* ---------- auto-fetch list on mount ---------- */
  useEffect(() => {
    fetchPromotions();
  }, [fetchPromotions]);

  /* ---------- return API ---------- */
  return {
    promotions,
    singlePromotion,
    loading,
    error,
    fetchPromotions,
    fetchSinglePromotion,
    createPromotion,
    updatePromotion,
    deletePromotion,
  };
}
