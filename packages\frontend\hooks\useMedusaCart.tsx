'use client';

import {
  useState,
  useEffect,
  useCallback,
  useContext,
  create<PERSON>ontext,
  ReactNode,
} from 'react';
import {
  medusaAPI,
  MedusaCart,
  MedusaCartItem,
  MedusaAPIError,
} from '../lib/medusa-backend-api';
import { useAuth } from '../contexts/AuthContext';
import { useCartStore } from '@/stores/cartStore';
import { useCategoryStore } from '@/stores/categoriesStore';

// Enhanced cart item interface based on Medusa API
export interface MedusaCartItemUI {
  id: string;
  product_id: string;
  variant_id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  quantity: number;
  unit_price: number;
  total: number;
  product?: {
    id: string;
    title: string;
    thumbnail?: string;
    handle: string;
  };
  variant?: {
    id: string;
    title: string;
    sku: string;
  };
  metadata?: Record<string, any>;
}

interface UseMedusaCartReturn {
  // Cart state
  cart: MedusaCart | null;
  items: MedusaCartItemUI[];
  cartId: string | null;

  // Computed values
  totalItems: number;
  totalPrice: number;
  subtotal: number;

  // Actions
  createCart: () => Promise<void>;
  addItem: (
    variantId: string,
    quantity?: number,
    metadata?: Record<string, any>
  ) => Promise<void>;
  removeItem: (itemId: string, cartId?: string) => Promise<void>;
  updateQuantity: (
    itemId: string,
    quantity: number,
    cartId?: string
  ) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;

  // Loading states
  isLoading: boolean;
  isCreatingCart: boolean;
  isAddingItem: boolean;
  isUpdatingItem: boolean;

  // Error handling
  error: string | null;
  clearError: () => void;
}

// Cart Context
const MedusaCartContext = createContext<UseMedusaCartReturn | null>(null);

export function MedusaCartProvider({ children }: { children: ReactNode }) {
  const cartState = useMedusaCart();
  return (
    <MedusaCartContext.Provider value={cartState}>
      {children}
    </MedusaCartContext.Provider>
  );
}

export function useMedusaCartContext(): UseMedusaCartReturn {
  const context = useContext(MedusaCartContext);
  if (!context) {
    throw new Error(
      'useMedusaCartContext must be used within a MedusaCartProvider'
    );
  }
  return context;
}

export function useMedusaCart(): UseMedusaCartReturn {
  const { user, isAuthenticated } = useAuth(); // Get current authenticated user
  const [cart, setCart] = useState<MedusaCart | null>(null);
  const [cartId, setCartId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingCart, setIsCreatingCart] = useState(false);
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [isUpdatingItem, setIsUpdatingItem] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const setUserCartId = useCategoryStore(state => state.setCartId);
  const setCartProduct = useCategoryStore(state => state.setCartProduct);
  // Load cart ID from localStorage on mount
  useEffect(() => {
    const savedCartId = localStorage.getItem('medusa_cart_id');
    if (savedCartId) {
      setCartId(savedCartId);
    }
  }, []);

  // Save cart ID to localStorage whenever it changes
  useEffect(() => {
    if (cartId) {
      localStorage.setItem('medusa_cart_id', cartId);
    } else {
      localStorage.removeItem('medusa_cart_id');
    }
  }, [cartId]);

  // Update cart customer information when user authentication changes
  useEffect(() => {
    const updateCartCustomer = async () => {
      if (cartId && isAuthenticated && user) {
        try {
          console.log('🔄 Updating cart with customer info:', {
            cartId,
            // customerId: user.id,
            email: user.email,
          });

          const response = await medusaAPI.updateCartCustomer(cartId, {
            // customer_id: user.id,
            email: user.email,
          });

          setCart(response.cart);
          console.log('✅ Cart updated with customer info');
        } catch (error) {
          console.error('❌ Failed to update cart customer info:', error);
        }
      }
    };

    // Only update if we have all required info and haven't updated yet
    if (cartId && isAuthenticated && user) {
      updateCartCustomer();
    }
  }, [cartId, isAuthenticated, user?.id, user?.email]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback((err: unknown, defaultMessage: string) => {
    console.error('Medusa Cart error:', err);
    if (err instanceof MedusaAPIError) {
      setError(err.message);
    } else if (err instanceof Error) {
      setError(err.message);
    } else {
      setError(defaultMessage);
    }
  }, []);

  const loadCart = useCallback(
    async (id: string) => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await medusaAPI.getCart(id);
        setCart(response.cart);
        setCartProduct(response.cart);
      } catch (err) {
        handleError(err, 'Failed to load cart');
        // If cart not found, clear the stored cart ID
        if (err instanceof MedusaAPIError && err.status === 404) {
          setCartId(null);
          setCart(null);
        }
      } finally {
        setIsLoading(false);
      }
    },
    [handleError]
  );

  // Load cart when cartId changes
  useEffect(() => {
    if (cartId) {
      loadCart(cartId);
    }
  }, [cartId, loadCart]);

  const createCart = useCallback(async () => {
    try {
      setIsCreatingCart(true);
      setError(null);
      const response = await medusaAPI.createCart();
      setCart(response.cart);
      setCartId(response.cart.id);
    } catch (err) {
      handleError(err, 'Failed to create cart');
    } finally {
      setIsCreatingCart(false);
    }
  }, [handleError]);

  const addItem = useCallback(
    async (variantId: string, quantity = 1, metadata?: Record<string, any>) => {
      try {
        setIsAddingItem(true);
        setError(null);

        // Create cart if it doesn't exist
        let currentCartId = cartId;
        console.log('add item api call:::::::', { cartId });

        if (!currentCartId) {
          // Include customer information when creating cart
          const cartData =
            isAuthenticated && user
              ? {
                  // customer_id: user.id,
                  email: user.email,
                }
              : {};

          const cartResponse = await medusaAPI.createCart(cartData);
          setCart(cartResponse.cart);
          setCartId(cartResponse.cart.id);
          setUserCartId(cartResponse.cart.id);
          setCartProduct(cartResponse.cart);
          currentCartId = cartResponse.cart.id;

          console.log('🛒 Cart created with customer info:', {
            cartId: cartResponse.cart.id,
            customerId: cartResponse.cart.customer_id,
            email: cartResponse.cart.email,
          });
        }

        // Add item to cart
        const response = await medusaAPI.addToCart(currentCartId, {
          variant_id: variantId,
          quantity,
          metadata,
        });
        console.log('response::::::::::', response);
        // setProductCartId(response.id);
        // setCartProducts(response.items);
        setUserCartId(response.cart.id);
        setCartProduct(response.cart);
        setCart(response.cart);
        return response;
      } catch (err) {
        handleError(err, 'Failed to add item to cart');
      } finally {
        setIsAddingItem(false);
      }
    },
    [cartId, handleError]
  );

  const removeItem = useCallback(
    async (itemId: string, cartId?: string) => {
      if (!cartId) return;

      try {
        setIsUpdatingItem(true);
        setError(null);
        const response = await medusaAPI.removeCartItem(cartId, itemId);
        console.log('remove item response:::::', response);
        await loadCart(cartId);
        // setCart(response.cart);
      } catch (err) {
        handleError(err, 'Failed to remove item from cart');
      } finally {
        setIsUpdatingItem(false);
      }
    },
    [cartId, handleError]
  );

  const updateQuantity = useCallback(
    async (itemId: string, quantity: number, cartId?: string) => {
      if (!cartId) return;

      if (quantity <= 0) {
        await removeItem(itemId, cartId);
        return;
      }

      try {
        setIsUpdatingItem(true);
        setError(null);
        const response = await medusaAPI.updateCartItem(cartId, itemId, {
          quantity,
        });
        setCartProduct(response.cart);

        setCart(response.cart);
      } catch (err) {
        handleError(err, 'Failed to update item quantity');
      } finally {
        setIsUpdatingItem(false);
      }
    },
    [cartId, removeItem, handleError]
  );

  const clearCart = useCallback(async () => {
    setCart(null);
    setCartId(null);
    setError(null);
  }, []);

  const refreshCart = useCallback(
    async (cartId?: string) => {
      if (cartId) {
        await loadCart(cartId);
      }
    },
    [cartId, loadCart]
  );

  // Convert Medusa cart items to our UI interface
  const items: MedusaCartItemUI[] =
    cart?.items?.map((item: MedusaCartItem) => ({
      id: item.id,
      product_id: item.product_id,
      variant_id: item.variant_id,
      title: item.title,
      description: item.description,
      thumbnail: item.thumbnail || undefined,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total: item.total,
      product: item.product
        ? {
            id: item.product.id,
            title: item.product.title,
            thumbnail: item.product.thumbnail || undefined,
            handle: item.product.handle,
          }
        : undefined,
      variant: item.variant
        ? {
            id: item.variant.id,
            title: item.variant.title,
            sku: item.variant.sku,
          }
        : undefined,
      metadata: item.metadata,
    })) || [];

  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = cart?.total || 0;
  const subtotal = cart?.subtotal || 0;

  return {
    // Cart state
    cart,
    items,
    cartId,

    // Computed values
    totalItems,
    totalPrice,
    subtotal,

    // Actions
    createCart,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    refreshCart,

    // Loading states
    isLoading,
    isCreatingCart,
    isAddingItem,
    isUpdatingItem,

    // Error handling
    error,
    clearError,
  };
}
