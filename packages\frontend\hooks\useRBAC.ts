/**
 * <PERSON><PERSON> Hooks for React Components
 * 
 * Provides easy-to-use hooks for permission checking in React components
 */

import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getRolePermissions,
  canAccessRoute,
  hasMinimumRoleLevel,
  getAccessibleMenuItems,
  PERMISSIONS,
  ROLES,
  type Permission,
  type RoleName,
} from '@/lib/rbac/permissions';

/**
 * Main RBAC hook providing all permission checking functions
 */
export function useRBAC() {
  const { user } = useAuth();
  const userRole = user?.role || 'customer';

  return useMemo(() => ({
    // User info
    user,
    userRole,
    
    // Permission checking functions
    hasPermission: (permission: string) => hasPermission(userRole, permission),
    hasAnyPermission: (permissions: string[]) => hasAnyPermission(userRole, permissions),
    hasAllPermissions: (permissions: string[]) => hasAllPermissions(userRole, permissions),
    
    // Route access
    canAccessRoute: (route: string) => canAccessRoute(userRole, route),
    
    // Role level checking
    hasMinimumRoleLevel: (level: number) => hasMinimumRoleLevel(userRole, level),
    
    // Get user permissions
    getUserPermissions: () => getRolePermissions(userRole),
    
    // Menu filtering
    getAccessibleMenuItems: (menuItems: any[]) => getAccessibleMenuItems(userRole, menuItems),
    
    // Role information
    isAdmin: () => ['super_admin', 'admin'].includes(userRole.toLowerCase()),
    isSeller: () => ['seller', 'seller_staff'].includes(userRole.toLowerCase()),
    isCustomer: () => userRole.toLowerCase() === 'customer',
    
    // Specific permission checks
    canManageProducts: () => hasAnyPermission(userRole, [
      PERMISSIONS.PRODUCTS.CREATE,
      PERMISSIONS.PRODUCTS.UPDATE,
      PERMISSIONS.PRODUCTS.DELETE,
    ]),
    
    canManageOrders: () => hasAnyPermission(userRole, [
      PERMISSIONS.ORDERS.UPDATE,
      PERMISSIONS.ORDERS.CANCEL,
      PERMISSIONS.ORDERS.FULFILL,
    ]),
    
    canViewAnalytics: () => hasPermission(userRole, PERMISSIONS.DASHBOARD.ANALYTICS),
    
    canManageCustomers: () => hasAnyPermission(userRole, [
      PERMISSIONS.CUSTOMERS.UPDATE,
      PERMISSIONS.CUSTOMERS.DELETE,
    ]),
    
    canManageSystem: () => hasAnyPermission(userRole, [
      PERMISSIONS.SYSTEM.MANAGE_USERS,
      PERMISSIONS.SYSTEM.MANAGE_ROLES,
      PERMISSIONS.SYSTEM.MANAGE_SETTINGS,
    ]),
  }), [user, userRole]);
}

/**
 * Hook for checking a specific permission
 */
export function usePermission(permission: string): boolean {
  const { hasPermission } = useRBAC();
  return hasPermission(permission);
}

/**
 * Hook for checking multiple permissions (any)
 */
export function useAnyPermission(permissions: string[]): boolean {
  const { hasAnyPermission } = useRBAC();
  return hasAnyPermission(permissions);
}

/**
 * Hook for checking multiple permissions (all)
 */
export function useAllPermissions(permissions: string[]): boolean {
  const { hasAllPermissions } = useRBAC();
  return hasAllPermissions(permissions);
}

/**
 * Hook for route access checking
 */
export function useRouteAccess(route: string): boolean {
  const { canAccessRoute } = useRBAC();
  return canAccessRoute(route);
}

/**
 * Hook for role-based conditional rendering
 */
export function useRoleCheck() {
  const { userRole, isAdmin, isSeller, isCustomer } = useRBAC();
  
  return {
    userRole,
    isAdmin: isAdmin(),
    isSeller: isSeller(),
    isCustomer: isCustomer(),
    isStaff: isAdmin() || isSeller(),
  };
}

/**
 * Hook for getting accessible menu items
 */
export function useAccessibleMenu(menuItems: any[]) {
  const { getAccessibleMenuItems } = useRBAC();
  return useMemo(() => getAccessibleMenuItems(menuItems), [menuItems, getAccessibleMenuItems]);
}

/**
 * Hook for product management permissions
 */
export function useProductPermissions() {
  const { hasPermission } = useRBAC();
  
  return useMemo(() => ({
    canView: hasPermission(PERMISSIONS.PRODUCTS.VIEW),
    canCreate: hasPermission(PERMISSIONS.PRODUCTS.CREATE),
    canUpdate: hasPermission(PERMISSIONS.PRODUCTS.UPDATE),
    canDelete: hasPermission(PERMISSIONS.PRODUCTS.DELETE),
    canPublish: hasPermission(PERMISSIONS.PRODUCTS.PUBLISH),
    canManageInventory: hasPermission(PERMISSIONS.PRODUCTS.MANAGE_INVENTORY),
    canManagePricing: hasPermission(PERMISSIONS.PRODUCTS.MANAGE_PRICING),
    canBulkOperations: hasPermission(PERMISSIONS.PRODUCTS.BULK_OPERATIONS),
  }), [hasPermission]);
}

/**
 * Hook for order management permissions
 */
export function useOrderPermissions() {
  const { hasPermission } = useRBAC();
  
  return useMemo(() => ({
    canView: hasPermission(PERMISSIONS.ORDERS.VIEW),
    canViewAll: hasPermission(PERMISSIONS.ORDERS.VIEW_ALL),
    canUpdate: hasPermission(PERMISSIONS.ORDERS.UPDATE),
    canCancel: hasPermission(PERMISSIONS.ORDERS.CANCEL),
    canRefund: hasPermission(PERMISSIONS.ORDERS.REFUND),
    canFulfill: hasPermission(PERMISSIONS.ORDERS.FULFILL),
    canManageShipping: hasPermission(PERMISSIONS.ORDERS.MANAGE_SHIPPING),
    canExport: hasPermission(PERMISSIONS.ORDERS.EXPORT),
  }), [hasPermission]);
}

/**
 * Hook for customer management permissions
 */
export function useCustomerPermissions() {
  const { hasPermission } = useRBAC();
  
  return useMemo(() => ({
    canView: hasPermission(PERMISSIONS.CUSTOMERS.VIEW),
    canViewDetails: hasPermission(PERMISSIONS.CUSTOMERS.VIEW_DETAILS),
    canUpdate: hasPermission(PERMISSIONS.CUSTOMERS.UPDATE),
    canDelete: hasPermission(PERMISSIONS.CUSTOMERS.DELETE),
    canManageGroups: hasPermission(PERMISSIONS.CUSTOMERS.MANAGE_GROUPS),
    canExport: hasPermission(PERMISSIONS.CUSTOMERS.EXPORT),
  }), [hasPermission]);
}

/**
 * Hook for system administration permissions
 */
export function useSystemPermissions() {
  const { hasPermission } = useRBAC();
  
  return useMemo(() => ({
    canManageUsers: hasPermission(PERMISSIONS.SYSTEM.MANAGE_USERS),
    canManageRoles: hasPermission(PERMISSIONS.SYSTEM.MANAGE_ROLES),
    canManageSettings: hasPermission(PERMISSIONS.SYSTEM.MANAGE_SETTINGS),
    canViewLogs: hasPermission(PERMISSIONS.SYSTEM.VIEW_LOGS),
    canManageIntegrations: hasPermission(PERMISSIONS.SYSTEM.MANAGE_INTEGRATIONS),
  }), [hasPermission]);
}

/**
 * Hook for ONDC specific permissions
 */
export function useONDCPermissions() {
  const { hasPermission } = useRBAC();
  
  return useMemo(() => ({
    canManageCatalog: hasPermission(PERMISSIONS.ONDC.MANAGE_CATALOG),
    canManageOrders: hasPermission(PERMISSIONS.ONDC.MANAGE_ORDERS),
    canViewNetwork: hasPermission(PERMISSIONS.ONDC.VIEW_NETWORK),
    canManageLogistics: hasPermission(PERMISSIONS.ONDC.MANAGE_LOGISTICS),
  }), [hasPermission]);
}

// Export permission constants for easy access
export { PERMISSIONS, ROLES } from '@/lib/rbac/permissions';
