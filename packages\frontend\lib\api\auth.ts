/**
 * Authentication API Service
 * Handles user authentication, profile management, and session management
 */

import { BaseAPIService, APIItemResponse } from './base';
import { APIClient, apiClient } from '../api-client';

// Types
export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
  refresh_token: string;
  expires_in: number;
}

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  tenant_id?: string;
  avatar?: string;
  phone?: string;
  status: 'active' | 'inactive' | 'pending';
  last_login?: string;
  created_at: string;
  updated_at: string;
  isNewUser?: boolean; // Flag to identify new users for onboarding
  // Onboarding metadata
  onboarding_status?: 'pending' | 'completed';
  onboarding_store_config?: boolean;
  onboarding_add_product?: boolean;
  onboarding_bulk_upload?: boolean;
}

export interface UserProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar?: string;
  preferences?: {
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
}

export interface UpdateProfileRequest {
  first_name?: string;
  last_name?: string;
  phone?: string;
  avatar?: string;
  preferences?: UserProfile['preferences'];
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirm_password: string;
}

export interface OnboardingStatusRequest {
  onboarding_status?: 'pending' | 'completed';
  onboarding_store_config?: boolean;
  onboarding_add_product?: boolean;
  onboarding_bulk_upload?: boolean;
}

export interface OnboardingStatusResponse {
  user: User;
  current_step: number;
  completed_steps: number[];
  has_saved_data: boolean;
}

/**
 * Authentication API Service
 */
export class AuthAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('auth', client);
  }

  /**
   * User login
   */
  async login(
    credentials: LoginRequest
  ): Promise<APIItemResponse<LoginResponse>> {
    return this.client.request<APIItemResponse<LoginResponse>>(
      '/api/auth/login',
      {
        method: 'POST',
        body: JSON.stringify(credentials),
      },
      'none'
    );
  }

  /**
   * User logout
   */
  async logout(): Promise<APIItemResponse<{ message: string }>> {
    return this.client.request<APIItemResponse<{ message: string }>>(
      '/api/auth/logout',
      {
        method: 'POST',
      },
      'bearer'
    );
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(
    request: RefreshTokenRequest
  ): Promise<APIItemResponse<LoginResponse>> {
    return this.client.request<APIItemResponse<LoginResponse>>(
      '/api/auth/refresh',
      {
        method: 'POST',
        body: JSON.stringify(request),
      },
      'none'
    );
  }

  /**
   * Get current user profile
   */
  async getMe(): Promise<APIItemResponse<User>> {
    return this.client.request<APIItemResponse<User>>(
      '/api/auth/me',
      {
        method: 'GET',
      },
      'bearer'
    );
  }

  /**
   * Get user profile details
   */
  async getProfile(): Promise<APIItemResponse<UserProfile>> {
    return this.client.request<APIItemResponse<UserProfile>>(
      '/api/users/profile',
      {
        method: 'GET',
      },
      'bearer'
    );
  }

  /**
   * Update user profile
   */
  async updateProfile(
    data: UpdateProfileRequest
  ): Promise<APIItemResponse<UserProfile>> {
    return this.client.request<APIItemResponse<UserProfile>>(
      '/api/users/profile',
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
      'bearer'
    );
  }

  /**
   * Change user password
   */
  async changePassword(
    data: ChangePasswordRequest
  ): Promise<APIItemResponse<{ message: string }>> {
    return this.client.request<APIItemResponse<{ message: string }>>(
      '/api/users/change-password',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      'bearer'
    );
  }

  /**
   * Forgot password
   */
  async forgotPassword(
    data: ForgotPasswordRequest
  ): Promise<APIItemResponse<{ message: string }>> {
    return this.client.request<APIItemResponse<{ message: string }>>(
      '/api/auth/forgot-password',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      'none'
    );
  }

  /**
   * Reset password
   */
  async resetPassword(
    data: ResetPasswordRequest
  ): Promise<APIItemResponse<{ message: string }>> {
    return this.client.request<APIItemResponse<{ message: string }>>(
      '/api/auth/reset-password',
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      'none'
    );
  }

  /**
   * Verify email
   */
  async verifyEmail(
    token: string
  ): Promise<APIItemResponse<{ message: string }>> {
    return this.client.request<APIItemResponse<{ message: string }>>(
      `/api/auth/verify-email?token=${token}`,
      {
        method: 'GET',
      },
      'none'
    );
  }

  /**
   * Resend verification email
   */
  async resendVerification(
    email: string
  ): Promise<APIItemResponse<{ message: string }>> {
    return this.client.request<APIItemResponse<{ message: string }>>(
      '/api/auth/resend-verification',
      {
        method: 'POST',
        body: JSON.stringify({ email }),
      },
      'none'
    );
  }

  /**
   * Get user onboarding status
   */
  async getOnboardingStatus(): Promise<
    APIItemResponse<OnboardingStatusResponse>
  > {
    return this.client.request<APIItemResponse<OnboardingStatusResponse>>(
      '/api/auth/onboarding-status',
      {
        method: 'GET',
      }
    );
  }

  /**
   * Update user onboarding status
   */
  async updateOnboardingStatus(
    data: OnboardingStatusRequest
  ): Promise<APIItemResponse<User>> {
    return this.client.request<APIItemResponse<User>>(
      '/api/auth/onboarding-status',
      {
        method: 'PUT',
        body: JSON.stringify(data),
      }
    );
  }

  /**
   * Complete onboarding (sets status to completed)
   */
  async completeOnboarding(): Promise<APIItemResponse<User>> {
    return this.client.request<APIItemResponse<User>>(
      '/api/auth/complete-onboarding',
      {
        method: 'POST',
      }
    );
  }
}

// Export service instance
export const authAPI = new AuthAPIService(apiClient);
