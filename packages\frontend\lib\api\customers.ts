/**
 * Customers API Service
 * Handles all customer-related API operations
 */

import {
  BaseAPIService,
  APIListResponse,
  APIItemResponse,
  ListParams,
} from './base';
import { APIClient, apiClient } from '../api-client';
import { Address } from './orders';

export interface Customer {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  billing_address_id?: string;
  billing_address?: Address;
  shipping_addresses: Address[];
  phone?: string;
  has_account: boolean;
  orders?: CustomerOrder[];
  groups: CustomerGroup[];
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  metadata?: Record<string, any>;
}

export interface CustomerOrder {
  id: string;
  display_id: number;
  status: string;
  fulfillment_status: string;
  payment_status: string;
  total: number;
  currency_code: string;
  created_at: string;
}

export interface CustomerGroup {
  id: string;
  name: string;
  customers?: Customer[];
  price_lists: PriceList[];
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  metadata?: Record<string, any>;
}

export interface PriceList {
  id: string;
  name: string;
  description: string;
  type: 'sale' | 'override';
  status: 'active' | 'draft';
  starts_at?: string;
  ends_at?: string;
  customer_groups: CustomerGroup[];
  prices: Price[];
  includes_tax: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Price {
  id: string;
  currency_code: string;
  amount: number;
  min_quantity?: number;
  max_quantity?: number;
  price_list_id: string;
  variant_id: string;
  region_id?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface CreateCustomerData {
  email: string;
  first_name: string;
  last_name: string;
  password?: string;
  phone?: string;
  metadata?: Record<string, any>;
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {
  billing_address?: Partial<Address>;
  shipping_addresses?: Partial<Address>[];
}

export interface CustomerListParams extends ListParams {
  groups?: string[];
  created_at?: {
    lt?: string;
    gt?: string;
    gte?: string;
    lte?: string;
  };
  updated_at?: {
    lt?: string;
    gt?: string;
    gte?: string;
    lte?: string;
  };
  has_account?: boolean;
}

export interface CustomerStats {
  total_customers: number;
  new_customers_today: number;
  new_customers_this_week: number;
  new_customers_this_month: number;
  active_customers: number;
  customers_with_orders: number;
  average_order_value: number;
  total_customer_value: number;
}

/**
 * Admin Customers API Service
 */

export class AdminCustomersAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    // CRITICAL FIX: Use backend API URL instead of frontend
    const medusaUrl =
      process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9000';
    const useBackendAPI = process.env.NEXT_PUBLIC_USE_BACKEND_API === 'true';

    console.log('Initializing AdminCustomersAPIService with URL:', medusaUrl);
    console.log('Use Backend API:', useBackendAPI);

    const apiClient =
      client ||
      new APIClient({
        baseURL: medusaUrl, // Use backend URL, not frontend
        useFrontendAPI: false, // CRITICAL: Set to false to use backend
        adminToken: 'demo-admin-token', // Demo token for development
      });

    super('admin/customers', apiClient);

    // Log initialization
    console.log('AdminCustomersAPIService initialized with client config:', {
      baseURL: this.client['baseURL'],
      useFrontendAPI: this.client['useFrontendAPI'],
      medusaUrl,
      useBackendAPI,
    });

    // Add request interceptor for logging
    if (typeof window !== 'undefined') {
      // Client-side only
      const originalRequest = this.client['requestDirect'].bind(this.client);

      // Override requestDirect for better debugging
      this.client['requestDirect'] = async <T>(
        url: string,
        options: RequestInit = {},
        auth: 'none' | 'publishable' | 'admin' | 'bearer' = 'none'
      ) => {
        console.log(
          `[CustomerAPI] Preparing request to ${options.method || 'GET'} ${url}`
        );
        console.log('[CustomerAPI] Request options:', {
          method: options.method,
          headers: options.headers,
          body: options.body ? JSON.parse(options.body as string) : null,
          auth,
        });

        try {
          const response = await originalRequest<T>(
            url,
            {
              ...options,
              headers: {
                'Content-Type': 'application/json',
                ...(options.headers || {}),
              },
              credentials: 'include' as RequestCredentials,
            },
            auth
          );

          console.log(
            `[CustomerAPI] Request successful: ${
              options.method || 'GET'
            } ${url}`,
            {
              status: response.status,
              data: response.data,
            }
          );

          return response;
        } catch (error: any) {
          console.error(
            `[CustomerAPI] Request failed: ${options.method || 'GET'} ${url}`,
            {
              error: error.message,
              status: error.status,
              data: error.response?.data,
            }
          );
          throw error;
        }
      };
    }
  }
  /**
   * Get all customers (admin)
   */
  async getCustomers(
    params: CustomerListParams = {}
  ): Promise<APIListResponse<Customer>> {
    try {
      console.log('[AdminCustomersAPI] Getting customers with params:', params);

      const response = await this.client.callEndpoint<any>(
        'adminCustomers',
        'list',
        {
          page: params.page,
          limit: params.limit,
          q: params.search,
          status: params.status,
          group_id: params.group_id,
          has_account: params.has_account,
          date_from: params.date_from,
          date_to: params.date_to,
          sort_by: params.sortBy,
          sort_order: params.sortOrder,
        }
      );

      console.log('[AdminCustomersAPI] Get customers response:', response);

      // Transform backend response format to frontend expected format
      // Backend returns: { customers: [...], count: X, total: Y }
      // Frontend expects: { data: [...], count: X, total: Y }
      if (response.data && response.data.customers) {
        return {
          data: response.data.customers,
          count: response.data.count || response.data.customers.length,
          total:
            response.data.total ||
            response.data.count ||
            response.data.customers.length,
          page: params.page || 1,
          limit: params.limit || 20,
          hasMore: false,
        };
      }

      // Fallback to standard transformation
      return this.transformListResponse<Customer>(response);
    } catch (error) {
      console.error('Error getting customers from backend:', error);

      // Return empty result instead of throwing error to prevent UI crashes
      return {
        data: [],
        count: 0,
        total: 0,
        page: params.page || 1,
        limit: params.limit || 20,
        hasMore: false,
        error:
          error instanceof Error ? error.message : 'Failed to fetch customers',
      };
    }
  }
  /**
   * Get single customer (admin)
   */
  async getCustomer(id: string): Promise<APIItemResponse<Customer>> {
    try {
      console.log('[AdminCustomersAPI] Getting customer:', id);

      const response = await this.client.callEndpoint<{ customer: Customer }>(
        'adminCustomers',
        'get',
        { id }
      );

      console.log('[AdminCustomersAPI] Get customer response:', response);

      if (!response.data?.customer) {
        throw new Error('Customer not found');
      }
      return { data: response.data.customer };
    } catch (error) {
      console.error('Error getting customer:', error);
      throw error;
    }
  }

  /**
   * Create new customer
   */
  async createCustomer(
    data: CreateCustomerData
  ): Promise<APIItemResponse<Customer>> {
    try {
      console.log('[AdminCustomersAPI] Creating customer:', data);

      const response = await this.client.callEndpoint<{ customer: Customer }>(
        'adminCustomers',
        'create',
        {},
        data
      );

      console.log('[AdminCustomersAPI] Create response:', response);

      if (!response.data?.customer) {
        throw new Error('Failed to create customer: No customer data returned');
      }

      return { data: response.data.customer };
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  /**
   * Update customer
   */
  async updateCustomer(
    id: string,
    data: UpdateCustomerData
  ): Promise<APIItemResponse<Customer>> {
    try {
      console.log('[AdminCustomersAPI] Updating customer:', id, data);

      const response = await this.client.callEndpoint<{
        customer: Customer;
        message: string;
      }>('adminCustomers', 'update', { id }, data);

      console.log('[AdminCustomersAPI] Update response:', response);

      if (!response.data?.customer) {
        throw new Error('Failed to update customer: No customer data returned');
      }

      return { data: response.data.customer };
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  /**
   * Delete customer
   */
  async deleteCustomer(id: string): Promise<{ success: boolean }> {
    try {
      console.log('[AdminCustomersAPI] Deleting customer:', id);

      await this.client.callEndpoint('adminCustomers', 'delete', { id });

      console.log('[AdminCustomersAPI] Customer deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw error;
    }
  }

  /**
   * Get customer orders
   */
  async getCustomerOrders(
    id: string,
    params: { limit?: number; offset?: number; status?: string } = {}
  ): Promise<APIListResponse<CustomerOrder>> {
    try {
      console.log('[AdminCustomersAPI] Getting customer orders:', id, params);

      const response = await this.client.callEndpoint<{
        orders: CustomerOrder[];
        count: number;
      }>('adminCustomers', 'orders', {
        id,
        limit: params.limit,
        offset: params.offset,
        status: params.status,
      });

      console.log(
        '[AdminCustomersAPI] Get customer orders response:',
        response
      );

      return {
        data: response.data?.orders || [],
        count: response.data?.count || 0,
        total: response.data?.count || 0,
        page: params.offset
          ? Math.floor(params.offset / (params.limit || 10)) + 1
          : 1,
        limit: params.limit || 10,
        hasMore: false,
      };
    } catch (error) {
      console.error('Error getting customer orders:', error);
      return {
        data: [],
        count: 0,
        total: 0,
        page: 1,
        limit: params.limit || 10,
        hasMore: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch customer orders',
      };
    }
  }

  /**
   * Add customer to group
   */
  async addToGroup(
    customerId: string,
    groupId: string
  ): Promise<APIItemResponse<Customer>> {
    try {
      const response = await this.client.callEndpoint<{ customer: Customer }>(
        'admin/customers',
        'addToGroup',
        { customerId },
        { groupId }
      );
      if (!response.data?.customer) {
        throw new Error('Failed to add customer to group');
      }
      return { data: response.data.customer };
    } catch (error) {
      console.error('Error adding customer to group:', error);
      throw error;
    }
  }

  /**
   * Remove customer from group
   */
  async removeFromGroup(
    customerId: string,
    groupId: string
  ): Promise<APIItemResponse<Customer>> {
    try {
      const response = await this.client.callEndpoint<{ customer: Customer }>(
        'admin/customers',
        'removeFromGroup',
        { customerId, groupId }
      );
      if (!response.data?.customer) {
        throw new Error('Failed to remove customer from group');
      }
      return { data: response.data.customer };
    } catch (error) {
      console.error('Error removing customer from group:', error);
      throw error;
    }
  }

  /**
   * Get customer statistics
   */
  async getCustomerStats(customerId: string): Promise<CustomerStats> {
    try {
      const response = await this.client.callEndpoint<{ stats: CustomerStats }>(
        'admin/customers',
        'stats',
        { customerId }
      );

      // Return the stats or default values if not available
      return (
        response.data?.stats || {
          total_customers: 0,
          new_customers_today: 0,
          new_customers_this_week: 0,
          new_customers_this_month: 0,
          active_customers: 0,
          customers_with_orders: 0,
          average_order_value: 0,
          total_customer_value: 0,
        }
      );
    } catch (error) {
      console.error('Error getting customer stats:', error);
      return {
        total_customers: 0,
        new_customers_today: 0,
        new_customers_this_week: 0,
        new_customers_this_month: 0,
        active_customers: 0,
        customers_with_orders: 0,
        average_order_value: 0,
        total_customer_value: 0,
      };
    }
  }

  /**
   * Search customers
   */
  async searchCustomers(
    query: string,
    params: CustomerListParams = {}
  ): Promise<APIListResponse<Customer>> {
    return this.getCustomers({ ...params, q: query });
  }

  /**
   * Export customers
   */
  async exportCustomers(
    params: CustomerListParams = {}
  ): Promise<{ downloadUrl: string }> {
    try {
      const response = await this.client.callEndpoint<{ downloadUrl: string }>(
        'admin/customers',
        'export',
        {},
        params
      );
      if (!response.data?.downloadUrl) {
        throw new Error('Failed to generate export');
      }
      return { downloadUrl: response.data.downloadUrl };
    } catch (error) {
      console.error('Error exporting customers:', error);
      throw error;
    }
  }

  /**
   * Import customers
   */
  async importCustomers(file: File): Promise<{ jobId: string }> {
    try {
      // Create a custom request config with FormData
      const formData = new FormData();
      formData.append('file', file);

      // Use callEndpoint with the form data as the body
      const response = await this.client.callEndpoint<{ jobId: string }>(
        'admin/customers',
        'import',
        {},
        formData
      );

      if (!response.data?.jobId) {
        throw new Error('Failed to start import job');
      }
      return { jobId: response.data.jobId };
    } catch (error) {
      console.error('Error importing customers:', error);
      throw error;
    }
  }
}

/**
 * Customer Groups API Service
 */
export class CustomerGroupsAPIService extends BaseAPIService {
  constructor(client?: APIClient) {
    super('adminCustomers', client);
  }

  /**
   * Get all customer groups
   */
  async getCustomerGroups(
    params: ListParams = {}
  ): Promise<APIListResponse<CustomerGroup>> {
    try {
      const response = await this.client.callEndpoint<{
        groups: CustomerGroup[];
        count: number;
      }>('admin/customer-groups', 'list', params);

      return {
        data: response.data?.groups || [],
        count: response.data?.count || 0,
        total: response.data?.count || 0,
        page: params.offset
          ? Math.floor((params.offset || 0) / (params.limit || 10)) + 1
          : 1,
        limit: params.limit || 10,
        hasMore: false,
      };
    } catch (error) {
      console.error('Error fetching customer groups:', error);
      return {
        data: [],
        count: 0,
        total: 0,
        page: 1,
        limit: params.limit || 10,
        hasMore: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch customer groups',
      };
    }
  }

  /**
   * Get single customer group
   */
  async getCustomerGroup(id: string): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.callEndpoint<{ group: CustomerGroup }>(
        'admin/customer-groups',
        'get',
        { id }
      );
      if (!response.data?.group) {
        throw new Error('Customer group not found');
      }
      return { data: response.data.group };
    } catch (error) {
      console.error('Error getting customer group:', error);
      throw error;
    }
  }

  /**
   * Create customer group
   */
  async createCustomerGroup(data: {
    name: string;
    metadata?: Record<string, any>;
  }): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.callEndpoint<{ group: CustomerGroup }>(
        'admin/customer-groups',
        'create',
        {},
        data
      );
      if (!response.data?.group) {
        throw new Error('Failed to create customer group');
      }
      return { data: response.data.group };
    } catch (error) {
      console.error('Error creating customer group:', error);
      throw error;
    }
  }

  /**
   * Update customer group
   */
  async updateCustomerGroup(
    id: string,
    data: {
      name?: string;
      metadata?: Record<string, any>;
    }
  ): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.callEndpoint<{ group: CustomerGroup }>(
        'admin/customer-groups',
        'update',
        { id },
        data
      );

      if (!response.data?.group) {
        throw new Error('Failed to update customer group');
      }

      return { data: response.data.group };
    } catch (error) {
      console.error('Error updating customer group:', error);
      throw error;
    }
  }

  /**
   * Delete customer group
   */
  async deleteGroup(id: string): Promise<{ success: boolean }> {
    try {
      await this.client.callEndpoint('admin/customer-groups', 'delete', { id });
      return { success: true };
    } catch (error) {
      console.error('Error deleting customer group:', error);
      throw error;
    }
  }

  /**
   * Add customers to group
   */
  async addCustomersToGroup(
    groupId: string,
    customerIds: string[]
  ): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.callEndpoint<{ group: CustomerGroup }>(
        'admin/customer-groups',
        'addCustomers',
        { id: groupId },
        { customerIds }
      );

      if (!response.data?.group) {
        throw new Error('Failed to add customers to group');
      }

      return { data: response.data.group };
    } catch (error) {
      console.error('Error adding customers to group:', error);
      throw error;
    }
  }

  /**
   * Remove customers from group
   */
  async removeCustomersFromGroup(
    groupId: string,
    customerIds: string[]
  ): Promise<APIItemResponse<CustomerGroup>> {
    try {
      const response = await this.client.callEndpoint<{ group: CustomerGroup }>(
        'admin/customer-groups',
        'removeCustomers',
        { id: groupId },
        { customerIds }
      );

      if (!response.data?.group) {
        throw new Error('Failed to remove customers from group');
      }

      return { data: response.data.group };
    } catch (error) {
      console.error('Error removing customers from group:', error);
      throw error;
    }
  }
}

// Export service instances - let AdminCustomersAPIService create its own correctly configured client
export const adminCustomersAPI = new AdminCustomersAPIService();
export const customerGroupsAPI = new CustomerGroupsAPIService(apiClient);
