import { StoreConfiguration } from '../types/store-config';

export async function createStoreConfiguration(
  data: StoreConfiguration,
  jwt?: string
): Promise<any> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/store-configurations`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(jwt ? { Authorization: `Bearer ${jwt}` } : {}),
      },
      body: JSON.stringify({ data }),
    }
  );

  const result = await response.json();

  if (!response.ok) {
    throw new Error(
      result.error?.message || 'Failed to create store configuration'
    );
  }
  return result;
}
