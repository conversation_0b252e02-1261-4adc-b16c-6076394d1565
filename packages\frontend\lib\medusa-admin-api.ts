/**
 * Medusa Backend API Client
 * Integration with confirmed working backend endpoints from 06-Store-API-WORKING.postman_collection.json
 * All endpoints tested and verified working with real database operations
 */

import { toSortKey } from '@/utils/utils';

// API Configuration from confirmed working setup
// const admin_token = localStorage.getItem("ondc_auth_user");

// Type definitions based on confirmed API responses
export interface MedusaProduct {
  id: string;
  title: string;
  description: string;
  handle: string;
  status: string;
  thumbnail: string | null;
  variants: MedusaProductVariant[];
  categories?: MedusaCategory[];
  images?: MedusaImage[];
  options?: MedusaProductOption[];
  tags?: MedusaTag[];
  type?: MedusaProductType;
  collection?: MedusaCollection;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaProductVariant {
  id: string;
  title: string;
  sku: string;
  barcode?: string;
  ean?: string;
  upc?: string;
  inventory_quantity: number;
  allow_backorder: boolean;
  manage_inventory: boolean;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  metadata?: Record<string, any>;
  options?: MedusaProductOptionValue[];
  prices?: MedusaPrice[];
}

export interface MedusaCategory {
  id: string;
  name: string;
  description: string;
  handle: string;
  rank: number;
  parent_category_id: string | null;
  parent_category?: MedusaCategory;
  category_children?: MedusaCategory[];
  products?: MedusaProduct[];
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaCart {
  id: string;
  currency_code: string;
  email?: string;
  region_id: string;
  region?: MedusaRegion;
  items: MedusaCartItem[];
  total: number;
  subtotal: number;
  tax_total?: number;
  shipping_total?: number;
  discount_total?: number;
  shipping_address?: MedusaAddress;
  billing_address?: MedusaAddress;
  customer_id?: string;
  payment_sessions?: any[];
  created_at: string;
  updated_at: string;
  completed_at?: string;
  metadata?: Record<string, any>;
}

export interface MedusaCartItem {
  id: string;
  cart_id: string;
  product_id: string;
  variant_id: string;
  product?: MedusaProduct;
  variant?: MedusaProductVariant;
  title: string;
  description?: string;
  thumbnail?: string;
  quantity: number;
  unit_price: number;
  total: number;
  original_total?: number;
  original_unit_price?: number;
  metadata?: Record<string, any>;
}

export interface MedusaRegion {
  id: string;
  name: string;
  currency_code: string;
  tax_rate?: number;
  countries?: MedusaCountry[];
  payment_providers?: any[];
  fulfillment_providers?: any[];
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaCustomer {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  has_account: boolean;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaOrder {
  id: string;
  cart_id?: string;
  email: string;
  total: number;
  subtotal: number;
  tax_total?: number;
  shipping_total?: number;
  currency_code: string;
  payment_status: string;
  fulfillment_status: string;
  payment_method?: string;
  items: MedusaOrderItem[];
  shipping_address?: MedusaAddress;
  billing_address?: MedusaAddress;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaOrderItem {
  id: string;
  title: string;
  quantity: number;
  unit_price: number;
  total: number;
  variant_id?: string;
  product_id?: string;
  metadata?: Record<string, any>;
}

export interface MedusaAddress {
  id?: string;
  first_name: string;
  last_name: string;
  address_1: string;
  address_2?: string;
  city: string;
  postal_code: string;
  province?: string;
  country_code: string;
  phone?: string;
  company?: string;
  metadata?: Record<string, any>;
}

// Additional interfaces
export interface MedusaImage {
  id: string;
  url: string;
  metadata?: Record<string, any>;
}

export interface MedusaProductOption {
  id: string;
  title: string;
  values: MedusaProductOptionValue[];
}

export interface MedusaProductOptionValue {
  id: string;
  value: string;
  option_id: string;
}

export interface MedusaPrice {
  id: string;
  currency_code: string;
  amount: number;
  min_quantity?: number;
  max_quantity?: number;
}

export interface MedusaTag {
  id: string;
  value: string;
}

export interface MedusaProductType {
  id: string;
  value: string;
}

export interface MedusaCollection {
  id: string;
  title: string;
  handle: string;
}

export interface MedusaCountry {
  id: string;
  iso_2: string;
  iso_3: string;
  name: string;
  display_name: string;
}

// API Response interfaces
export interface ApiResponse<T> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ProductsResponse {
  products: MedusaProduct[];
  count: number;
  offset: number;
  limit: number;
}

export interface CategoriesResponse {
  product_categories: MedusaCategory[];
  count: number;
  offset: number;
  limit: number;
}

export interface RegionsResponse {
  regions: MedusaRegion[];
}

export interface CartResponse {
  cart: MedusaCart;
}
export interface AdminLoginResponse {
  token: string;
}

export interface CustomerResponse {
  customer: MedusaCustomer;
  success: boolean;
  message?: string;
}

export interface OrdersResponse {
  success: boolean;
  orders: MedusaOrder[];
  count: number;
  total_count: number;
  limit: number;
  offset: number;
}

export interface OrderResponse {
  success: boolean;
  order: MedusaOrder;
}

export interface CODOrderResponse {
  success: boolean;
  order: MedusaOrder;
  message: string;
}

// Error handling
export class MedusaAPIError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'MedusaAPIError';
  }
}

// API Client Class
export class MedusaBackendAPI {
  private baseURL: string;
  private headers: Record<string, string>;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL as string;
    let token = '';
    if (typeof window !== 'undefined') {
      token = localStorage.getItem('ondc_auth_token') || '';
    }
    console.log('token:::::::', token);
    this.headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token || process.env.NEXT_PUBLIC_ADMIN_TOKEN}`,
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.headers,
          ...options.headers,
        },
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        // throw new MedusaAPIError(
        //   errorData.message ||
        //     `HTTP ${response.status}: ${response.statusText}`,
        //   response.status,
        //   errorData
        // );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof MedusaAPIError) {
        throw error;
      }
      // throw new MedusaAPIError(
      //   `Network error: ${
      //     error instanceof Error ? error.message : 'Unknown error'
      //   }`
      // );
    }
  }

  // Admin Login
  async adminLogin(data?: {
    email?: string;
    password?: string;
  }): Promise<AdminLoginResponse> {
    const adminData: any = {
      email: data?.email,
      password: data?.password,
    };

    return await this.request<AdminLoginResponse>('/auth/user/emailpass', {
      method: 'POST',
      body: JSON.stringify(adminData),
    });
  }

  async adminRegisteration(data: any): Promise<any> {
    return await this.request<any>('/public/admin-signup', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // {{base_url}}/admin/users/me
  async getAdminUserDetails(): Promise<AdminLoginResponse> {
    return await this.request<AdminLoginResponse>('/admin/users/me', {});
  }

  // Health Check
  async healthCheck(): Promise<string> {
    return await this.request<string>('/health');
  }

  // Get Regions
  async getRegions(): Promise<RegionsResponse> {
    return await this.request<RegionsResponse>('/store/regions');
  }

  // Get Products with enhanced expand options
  async getProducts(params?: {
    limit?: number;
    offset?: number;
    q?: string;
    category_id?: string[];
    collection_id?: string[];
    tags?: string[];
    type_id?: string[];
    expand?: string[];
    fields?: string[];
    sort?: string;
  }): Promise<ProductsResponse> {
    const searchParams = new URLSearchParams();

    // Optimize default parameters for faster loading
    const limit = params?.limit || 50;
    const offset = params?.offset || 0;

    searchParams.append('limit', limit.toString());
    searchParams.append('offset', offset.toString());

    // Default expand parameters for products list
    const defaultExpand = [
      'variants.*',
      'variants.prices.*',
      'variants.options',
      'options',
      'options.values',
      'images.*',
      'thumbnail',
      'tags.*',
      'type',
      'collection.*',
      'categories.*',
      'profiles',
      'metadata',
      'collection_id',
      'description',
      'status',
      'updated_at',
      'handle',
      'created_at',
      'title',
    ];

    const expandFields = params?.expand || defaultExpand;
    if (expandFields.length > 0) {
      searchParams.append('fields', expandFields.join(','));
    }

    // Add specific fields if requested
    // if (params?.fields && params.fields.length > 0) {
    //   searchParams.append('fields', params.fields.join(','));
    // }

    // Add other parameters only if provided
    if (params?.q) searchParams.append('q', params.q);
    if (params?.category_id?.length) {
      params.category_id.forEach(id =>
        searchParams.append('category_id[]', id)
      );
    }
    if (params?.collection_id?.length) {
      params.collection_id.forEach(id =>
        searchParams.append('collection_id[]', id)
      );
    }
    if (params?.tags?.length) {
      params.tags.forEach(tag => searchParams.append('tags[]', tag));
    }
    if (params?.type_id?.length) {
      params.type_id.forEach(id => searchParams.append('type_id[]', id));
    }

    if (params?.sort) searchParams.append('order', toSortKey(params?.sort));

    const queryString = searchParams.toString();
    const endpoint = `/admin/products?${queryString}`;

    try {
      const response = await this.request<ProductsResponse>(endpoint);

      // Ensure we always return a valid response structure
      return {
        products: response.products || [],
        count: response.count || response.products?.length || 0,
        offset: offset,
        limit: limit,
      };
    } catch (error) {
      console.error('Failed to fetch products:', error);
      // Return empty response on error to prevent UI crashes
      return {
        products: [],
        count: 0,
        offset: offset,
        limit: limit,
      };
    }
  }

  // Get Single Product with complete data
  async getProduct(
    productId: string,
    options?: {
      expand?: string[];
      fields?: string[];
    }
  ): Promise<{ product: MedusaProduct }> {
    const params = new URLSearchParams();

    // Default expand parameters to get complete product data
    const defaultExpand = [
      'variants.*',
      'variants.prices.*',
      'variants.options',
      'options',
      'options.values',
      'images.*',
      'thumbnail',
      'tags.*',
      'type',
      'collection',
      'categories.*',
      'profiles',
      'metadata',
      'collection_id',
      'description',
      'status',
      'updated_at',
      'handle',
      'title',
    ];

    const expandFields = options?.expand || defaultExpand;
    if (expandFields.length > 0) {
      params.append('fields', expandFields.join(','));
    }

    // Add specific fields if requested
    // if (options?.fields && options.fields.length > 0) {
    //   params.append('fields', options.fields.join(','));
    // }

    const queryString = params.toString();
    const url = `/admin/products/${productId}${
      queryString ? `?${queryString}` : ''
    }`;

    console.log('🔍 Fetching product with expand params:', {
      productId,
      url,
    });

    return await this.request<{ product: MedusaProduct }>(url);
  }

  // Get Product Categories
  async getCategories(params?: {
    limit?: number;
    offset?: number;
    parent_category_id?: string;
    include_descendants_tree?: boolean;
  }): Promise<CategoriesResponse> {
    const searchParams = new URLSearchParams();

    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());
    if (params?.parent_category_id)
      searchParams.append('parent_category_id', params.parent_category_id);
    if (params?.include_descendants_tree)
      searchParams.append('include_descendants_tree', 'true');

    const queryString = searchParams.toString();
    const endpoint = `/admin/product-categories${
      queryString ? `?${queryString}` : ''
    }`;

    return await this.request<CategoriesResponse>(endpoint);
  }

  // Add Single Product
  async addSingleProduct(data: any): Promise<MedusaProduct> {
    return await this.request<MedusaProduct>('/admin/products', {
      method: 'POST',
      body: JSON.stringify({
        ...data,
      }),
    });
  }

  // Update Single Product
  async updateSingleProduct(
    productId: string,
    data: any
  ): Promise<MedusaProduct> {
    return await this.request<MedusaProduct>(`/admin/products/${productId}`, {
      method: 'POST',
      body: JSON.stringify({
        ...data,
      }),
    });
  }

  // Remove Single Product
  async removeSingleProduct(productId: string): Promise<MedusaProduct> {
    return await this.request<MedusaProduct>(`/admin/products/${productId}`, {
      method: 'DELETE',
    });
  }

  // ---------------------------------------------------------------------------------------------------------------

  // Get Single Product Category
  async getSingleCategory(params: { id: string }): Promise<CategoriesResponse> {
    return await this.request<CategoriesResponse>(
      `/admin/product-categories/${params.id}`
    );
  }

  // Remove Single Product Category
  async removeSingleCategory(categoryId: string): Promise<CategoriesResponse> {
    return await this.request<CategoriesResponse>(
      `/admin/product-categories/${categoryId}`,
      {
        method: 'DELETE',
      }
    );
  }

  // Add Single Product Category
  async addSingleCategory(data: any): Promise<CategoriesResponse> {
    return await this.request<CategoriesResponse>('/admin/product-categories', {
      method: 'POST',
      body: JSON.stringify({
        ...data,
      }),
    });
  }
  // Update Single Product Category
  async updateSingleCategory(
    categoryId: string,
    data: any
  ): Promise<CategoriesResponse> {
    return await this.request<CategoriesResponse>(
      `/admin/product-categories/${categoryId}`,
      {
        method: 'POST',
        body: JSON.stringify({
          ...data,
        }),
      }
    );
  }

  // COLLECTION API CALLS -------------------------------------------------------------------------------------------------

  async getCollections(params?: {
    limit?: number;
    offset?: number;
    parent_category_id?: string;
    include_descendants_tree?: boolean;
  }): Promise<CategoriesResponse> {
    const searchParams = new URLSearchParams();

    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());
    if (params?.parent_category_id)
      searchParams.append('parent_category_id', params.parent_category_id);
    if (params?.include_descendants_tree)
      searchParams.append('include_descendants_tree', 'true');

    const queryString = searchParams.toString();
    const endpoint = `/admin/collections${
      queryString ? `?${queryString}` : ''
    }`;

    return await this.request<CategoriesResponse>(endpoint);
  }

  // Get Single Product Collection
  async getSingleCollection(params: {
    id: string;
  }): Promise<CategoriesResponse> {
    return await this.request<CategoriesResponse>(
      `/admin/collections/${params.id}`
    );
  }

  // Remove Single Product Collection
  async removeSingleCollection(
    collectionId: string
  ): Promise<CategoriesResponse> {
    return await this.request<CategoriesResponse>(
      `/admin/collections/${collectionId}`,
      {
        method: 'DELETE',
      }
    );
  }

  // Add Single Product Collection
  async addSingleCollection(data: any): Promise<CategoriesResponse> {
    return await this.request<CategoriesResponse>('/admin/collections', {
      method: 'POST',
      body: JSON.stringify({
        ...data,
      }),
    });
  }
  // Update Single Product Collection
  async updateSingleCollection(
    collectionId: string,
    data: any
  ): Promise<CategoriesResponse> {
    return await this.request<CategoriesResponse>(
      `/admin/collections/${collectionId}`,
      {
        method: 'POST',
        body: JSON.stringify({
          ...data,
        }),
      }
    );
  }

  // TAG API CALLS -------------------------------------------------------------------------------------------------

  async getTags(params?: {
    limit?: number;
    offset?: number;
  }): Promise<CategoriesResponse> {
    const searchParams = new URLSearchParams();

    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());

    const queryString = searchParams.toString();
    const endpoint = `/admin/product-tags${
      queryString ? `?${queryString}` : ''
    }`;

    return await this.request<any>(endpoint);
  }

  // Get Single Product Tag
  async getSingleTag(params: { id: string }): Promise<any> {
    return await this.request<any>(`/admin/product-tags/${params.id}`);
  }

  // Remove Single Product Tag
  async removeSingleTag(tagId: string): Promise<any> {
    return await this.request<any>(`/admin/product-tags/${tagId}`, {
      method: 'DELETE',
    });
  }

  // Add Single Product Tag
  async addSingleTag(data: any): Promise<any> {
    return await this.request<any>('/admin/product-tags', {
      method: 'POST',
      body: JSON.stringify({
        ...data,
      }),
    });
  }
  // Update Single Product Tag
  async updateSingleTag(tagId: string, data: any): Promise<any> {
    return await this.request<any>(`/admin/product-tags/${tagId}`, {
      method: 'POST',
      body: JSON.stringify({
        ...data,
      }),
    });
  }
  // ---------------------------------------------------------------------------------------------------------------

  /* ------------------------------------------------------------------ */
  /* ------------------  ADMIN PROMOTION ENDPOINTS  ------------------- */
  /* ------------------------------------------------------------------ */

  /**
   * List promotions
   * GET /admin/promotions
   *
   * @param params.limit   results per page
   * @param params.offset  skip N results
   * @param params.fields  comma-list of scalar columns (id,code,status,…)
   * @param params.expand  comma-list of relations (application_method,rules,…)
   * @param params.code    filter by exact code
   */
  async getPromotions(params?: {
    limit?: number;
    offset?: number;
    fields?: string;
    expand?: string;
    code?: string;
  }): Promise<any> {
    const search = new URLSearchParams();

    if (params?.limit) search.append('limit', params.limit.toString());
    if (params?.offset) search.append('offset', params.offset.toString());
    if (params?.fields) search.append('fields', params.fields);
    if (params?.expand) search.append('expand', params.expand);
    if (params?.code) search.append('code', params.code);

    const qs = search.toString();
    const endpoint = `/admin/promotions${qs ? `?${qs}` : ''}`;

    return await this.request<any>(endpoint);
  }

  /**
   * Retrieve a single promotion
   * GET /admin/promotions/:id
   *
   * @param id       promotion ID
   * @param fields   optional projection
   * @param expand   optional relations
   */
  async getSinglePromotion(opts: {
    id: string;
    fields?: string;
    expand?: string;
  }): Promise<any> {
    const search = new URLSearchParams();
    if (opts.fields) search.append('fields', opts.fields);
    if (opts.expand) search.append('expand', opts.expand);

    const qs = search.toString();
    const endpoint = `/admin/promotions/${opts.id}${qs ? `?${qs}` : ''}`;

    return await this.request<any>(endpoint);
  }

  /**
   * Delete a promotion
   * DELETE /admin/promotions/:id
   */
  async removePromotion(id: string): Promise<any> {
    return await this.request<any>(`/admin/promotions/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Create a promotion
   * POST /admin/promotions
   *
   * Body must match Medusa’s CreatePromotion schema.
   */
  async addPromotion(payload: Record<string, any>): Promise<any> {
    return await this.request<any>('/admin/promotions', {
      method: 'POST',
      body: JSON.stringify(payload),
    });
  }

  /**
   * Update an existing promotion
   * POST /admin/promotions/:id
   *
   * Body = partial patch of fields you want to change.
   */
  async updatePromotion(id: string, body: Record<string, any>): Promise<any> {
    return await this.request<any>(`/admin/promotions/${id}`, {
      method: 'POST',
      body: JSON.stringify(body),
    });
  }

  // Create Cart (with customer support)
  async createCart(data?: {
    region_id?: string;
    // customer_id?: string;
    email?: string;
  }): Promise<CartResponse> {
    const cartData: any = {
      region_id: data?.region_id || process.env.NEXT_PUBLIC_REGION_ID,
    };

    console.log('data:::::::', data, cartData);

    if (data?.email) {
      cartData.email = data.email;
    }

    return await this.request<CartResponse>('/store/carts', {
      method: 'POST',
      body: JSON.stringify(cartData),
    });
  }

  // Get Cart Details
  async getCart(cartId: string): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}`);
  }

  // Update Cart Customer Information
  async updateCartCustomer(
    cartId: string,
    data: {
      // customer_id?: string;
      email?: string;
    }
  ): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Add Product to Cart
  async addToCart(
    cartId: string,
    data: {
      variant_id: string;
      quantity: number;
      metadata?: Record<string, any>;
    }
  ): Promise<CartResponse> {
    return await this.request<CartResponse>(
      `/store/carts/${cartId}/line-items`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      }
    );
  }

  // Update Cart Item Quantity
  async updateCartItem(
    cartId: string,
    itemId: string,
    data: {
      quantity: number;
    }
  ): Promise<CartResponse> {
    return await this.request<CartResponse>(
      `/store/carts/${cartId}/line-items/${itemId}`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      }
    );
  }

  // Remove Cart Item
  async removeCartItem(cartId: string, itemId: string): Promise<CartResponse> {
    return await this.request<CartResponse>(
      `/store/carts/${cartId}/line-items/${itemId}`,
      {
        method: 'DELETE',
      }
    );
  }

  // ---------------------------------------------------------------------------------------------------------------

  // Customer Registration (Simplified)
  async registerCustomer(data: {
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
  }): Promise<CustomerResponse> {
    return await this.request<CustomerResponse>('/store/customers/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Create Order from Cart with COD
  async createOrderFromCart(data: {
    cart_id: string;
    email: string;
    shipping_address: {
      first_name: string;
      last_name: string;
      address_1: string;
      address_2?: string;
      city: string;
      postal_code: string;
      country_code: string;
      phone: string;
    };
    billing_address?: any;
    payment_method?: string;
  }): Promise<CODOrderResponse> {
    return await this.request<CODOrderResponse>('/store/orders/create', {
      method: 'POST',
      body: JSON.stringify({
        ...data,
        payment_method: data.payment_method || 'cod',
      }),
    });
  }

  // Complete Cart with Cash on Delivery (Legacy)
  async completeCartWithCOD(cartId: string): Promise<CODOrderResponse> {
    return await this.request<CODOrderResponse>(
      `/store/carts/${cartId}/complete-cod`,
      {
        method: 'POST',
        body: JSON.stringify({}),
      }
    );
  }

  async getAllOrders(params?: {
    limit?: number;
    offset?: number;
    customer_id?: string[];
  }): Promise<OrdersResponse> {
    const searchParams = new URLSearchParams();

    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());
    if (params?.customer_id)
      searchParams.append('customer_id', params.customer_id.toString());

    const queryString = searchParams.toString();
    const endpoint = `/admin/orders${queryString ? `?${queryString}` : ''}`;

    return await this.request<OrdersResponse>(endpoint);
  }

  // ✅ 12. Get Single Order
  async getSingleOrder(orderId: string): Promise<OrderResponse> {
    return await this.request<OrderResponse>(`/admin/orders/${orderId}`);
  }

  async getCustomers(params?: {
    limit?: number;
    offset?: number;
  }): Promise<OrdersResponse> {
    const searchParams = new URLSearchParams();

    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());

    const queryString = searchParams.toString();
    const endpoint = `/admin/customers${queryString ? `?${queryString}` : ''}`;

    return await this.request<OrdersResponse>(endpoint);
  }

  async getSingleCustomer(customerId: string): Promise<OrderResponse> {
    return await this.request<OrderResponse>(`/admin/customers/${customerId}`);
  }

  // ✅ 13. Customer Registration
  // async registerCustomer(data: {
  //   email: string;
  //   first_name: string;
  //   last_name: string;
  //   phone?: string;
  //   password?: string;
  // }): Promise<CustomerResponse> {
  //   return await this.request<CustomerResponse>('/store/customers/create', {
  //     method: 'POST',
  //     body: JSON.stringify(data),
  //   });
  // }

  // ✅ 14. Customer Login
  async loginCustomer(data: { email: string; password: string }): Promise<{
    success: boolean;
    customer: MedusaCustomer;
    token: string;
    token_type: string;
    expires_in: number;
    message: string;
  }> {
    return await this.request('/store/customers/login', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Utility Methods

  // Get default region ID
  getDefaultRegionId(): string {
    return process.env.NEXT_PUBLIC_REGION_ID as string;
  }

  // Check if server is healthy
  async isServerHealthy(): Promise<boolean> {
    try {
      const response = await this.healthCheck();
      return response === 'OK';
    } catch {
      return false;
    }
  }

  // Search products by query
  async searchProducts(query: string, limit = 20): Promise<ProductsResponse> {
    return await this.getProducts({ q: query, limit });
  }

  // Get products by category
  async getProductsByCategory(
    categoryId: string,
    limit = 20
  ): Promise<ProductsResponse> {
    return await this.getProducts({ category_id: [categoryId], limit });
  }

  // Get featured products (using all products since Medusa v2 doesn't support tag filtering)
  async getFeaturedProducts(limit = 10): Promise<ProductsResponse> {
    // Since Medusa v2 store API doesn't support tag filtering, return all products
    // In a real implementation, you would filter by metadata or use a different approach
    console.info(
      'Medusa v2 store API does not support tag filtering, showing all products as featured'
    );
    return await this.getProducts({ limit });
  }

  // Get top deals (using all products since Medusa v2 doesn't support tag filtering)
  async getTopDeals(limit = 10): Promise<ProductsResponse> {
    // Since Medusa v2 store API doesn't support tag filtering, return all products
    // In a real implementation, you would filter by metadata or use a different approach
    console.info(
      'Medusa v2 store API does not support tag filtering, showing all products as top deals'
    );
    return await this.getProducts({ limit });
  }

  // Get hot picks (using all products since Medusa v2 doesn't support tag filtering)
  async getHotPicks(limit = 10): Promise<ProductsResponse> {
    // Since Medusa v2 store API doesn't support tag filtering, return all products
    // In a real implementation, you would filter by metadata or use a different approach
    console.info(
      'Medusa v2 store API does not support tag filtering, showing all products as hot picks'
    );
    return await this.getProducts({ limit });
  }

  // ===== ANALYTICS ENDPOINTS =====

  /**
   * Get comprehensive dashboard analytics
   */
  async getDashboardAnalytics(params?: {
    period?: '7d' | '30d' | '90d' | '1y';
    sales_channel_id?: string;
    tenant_id?: string;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.period) queryParams.append('period', params.period);
    if (params?.sales_channel_id)
      queryParams.append('sales_channel_id', params.sales_channel_id);
    if (params?.tenant_id) queryParams.append('tenant_id', params.tenant_id);

    const url = `/admin/analytics/dashboard${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    return await this.request<any>(url);
  }

  /**
   * Get detailed sales analytics
   */
  async getSalesAnalytics(params?: {
    period?: '7d' | '30d' | '90d' | '1y';
    group_by?: 'day' | 'week' | 'month';
    sales_channel_id?: string;
    tenant_id?: string;
    category_id?: string;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.period) queryParams.append('period', params.period);
    if (params?.group_by) queryParams.append('group_by', params.group_by);
    if (params?.sales_channel_id)
      queryParams.append('sales_channel_id', params.sales_channel_id);
    if (params?.tenant_id) queryParams.append('tenant_id', params.tenant_id);
    if (params?.category_id)
      queryParams.append('category_id', params.category_id);

    const url = `/admin/analytics/sales${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    return await this.request<any>(url);
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(params?: {
    period?: '7d' | '30d' | '90d' | '1y';
    segment?: 'all' | 'new' | 'repeat' | 'vip' | 'inactive';
    tenant_id?: string;
    sales_channel_id?: string;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.period) queryParams.append('period', params.period);
    if (params?.segment) queryParams.append('segment', params.segment);
    if (params?.tenant_id) queryParams.append('tenant_id', params.tenant_id);
    if (params?.sales_channel_id)
      queryParams.append('sales_channel_id', params.sales_channel_id);

    const url = `/admin/analytics/customers${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    return await this.request<any>(url);
  }

  /**
   * Get product analytics
   */
  async getProductAnalytics(params?: {
    period?: '7d' | '30d' | '90d' | '1y';
    limit?: number;
    sort_by?: 'revenue' | 'units_sold' | 'views' | 'conversion_rate';
    category_id?: string;
    tenant_id?: string;
    sales_channel_id?: string;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.period) queryParams.append('period', params.period);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params?.category_id)
      queryParams.append('category_id', params.category_id);
    if (params?.tenant_id) queryParams.append('tenant_id', params.tenant_id);
    if (params?.sales_channel_id)
      queryParams.append('sales_channel_id', params.sales_channel_id);

    const url = `/admin/analytics/products${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    return await this.request<any>(url);
  }

  /**
   * Get inventory analytics
   */
  async getInventoryAnalytics(params?: {
    location_id?: string;
    tenant_id?: string;
    low_stock_threshold?: number;
    category_id?: string;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.location_id)
      queryParams.append('location_id', params.location_id);
    if (params?.tenant_id) queryParams.append('tenant_id', params.tenant_id);
    if (params?.low_stock_threshold)
      queryParams.append(
        'low_stock_threshold',
        params.low_stock_threshold.toString()
      );
    if (params?.category_id)
      queryParams.append('category_id', params.category_id);

    const url = `/admin/analytics/inventory${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    return await this.request<any>(url);
  }

  /**
   * Get KPI dashboard data
   */
  async getKPIAnalytics(params?: {
    period?: '7d' | '30d' | '90d' | '1y';
    tenant_id?: string;
    sales_channel_id?: string;
    compare_previous?: boolean;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.period) queryParams.append('period', params.period);
    if (params?.tenant_id) queryParams.append('tenant_id', params.tenant_id);
    if (params?.sales_channel_id)
      queryParams.append('sales_channel_id', params.sales_channel_id);
    if (params?.compare_previous !== undefined)
      queryParams.append(
        'compare_previous',
        params.compare_previous.toString()
      );

    const url = `/admin/analytics/kpi${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    return await this.request<any>(url);
  }
}

// Create singleton instance
export const medusaAdminAPI = new MedusaBackendAPI();

// Export default instance
export default medusaAdminAPI;
