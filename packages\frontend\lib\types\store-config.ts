// types/store.ts

export interface StoreConfiguration {
  store_theme: string;
  country: string;
  onboarding_completed: boolean;
  onboarding_step: number;
  store_status: string;
  store_handle: string;
  store_name: string;
  store_description: string;
  gst_number: string;
  address_line_1: string;
  address_line_2: string;
  city: string;
  state: string;
  pincode: string;
  phone: string;
  email: string;
  business_type: string;
  business_category: string;
  user_id: string;
  created_by_user: string;
}
