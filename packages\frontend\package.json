{"name": "@ondc-seller/frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:airbnb": "eslint --ext .js,.jsx,.ts,.tsx .", "lint:airbnb:fix": "eslint --ext .js,.jsx,.ts,.tsx . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\" --ignore-path .prettierignore", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\" --ignore-path .prettierignore", "type-check": "tsc --noEmit", "quality": "npm run type-check && npm run lint:airbnb && npm run format:check", "quality:fix": "npm run lint:airbnb:fix && npm run format", "test": "jest", "test:unit": "jest --testPathPattern=__tests__/components --watchAll=false", "test:integration": "jest --testPathPattern=__tests__/integration --watchAll=false", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --ci --coverage --watchAll=false --maxWorkers=1", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "playwright:install": "playwright install", "clean": "rimraf .turbo node_modules .next coverage test-results", "prepare": "husky", "build:analyze": "ANALYZE=true npm run build", "build:production": "NODE_ENV=production npm run build", "dev:debug": "NODE_OPTIONS='--inspect' npm run dev", "dev:https": "npm run dev -- --experimental-https", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=CHROMATIC_PROJECT_TOKEN", "security:audit": "npm audit --audit-level=high", "security:fix": "npm audit fix", "deps:check": "npm outdated", "deps:update": "npm update", "license:check": "license-checker --summary", "precommit": "lint-staged", "prepush": "npm run quality && npm run test:ci", "ci": "npm run quality && npm run test:ci && npm run build", "deploy:staging": "vercel --target staging", "deploy:production": "vercel --prod"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.0.18", "@medusajs/medusa-js": "^6.1.10", "@modelcontextprotocol/sdk": "^1.16.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@ondc-seller/prisma": "*", "@radix-ui/react-slot": "^1.2.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@types/js-cookie": "^3.0.6", "@types/keycloak-js": "^2.5.4", "@types/pg": "^8.15.4", "@vercel/mcp-adapter": "^1.0.0", "axios": "^1.10.0", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^4.3.0", "date-fns": "^3.6.0", "express": "^5.1.0", "js-cookie": "^3.0.5", "keycloak-js": "^26.2.0", "lucide-react": "^0.522.0", "next": "^15.4.5", "pg": "^8.16.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-query": "^3.39.3", "react-quill": "^2.0.0", "recharts": "^2.12.7", "swiper": "^11.2.8", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.14", "@types/node": "^20.19.9", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.1.2", "msw": "^1.3.5", "postcss": "^8.5.3", "prettier": "^3.1.1", "puppeteer": "^24.10.0", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "msw": {"workerDirectory": ["public"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}