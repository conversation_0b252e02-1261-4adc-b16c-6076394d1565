// stores/useCategoryStore.ts
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  productCount?: number;
  subcategories?: Category[];
}

interface CategoryStore {
  categories: Category[];
  selectedCategoryId: string;
  selectedSubCategoryId: string;
  selectedProductId: string;
  cartId: string;
  cartProduct: any;

  setSelectedCategoryId: (selectedCategoryId: string) => void;
  setSelectedSubCategoryId: (selectedSubCategoryId: string) => void;
  setCategories: (categories: Category[]) => void;
  setSelectedProductId: (selectedProductId: string) => void;
  getCategoryById: (id: string) => Category | undefined;
  setCartId: (cartId: string) => void;
  setCartProduct: (cartProduct: any) => void;
}

export const useCategoryStore = create<CategoryStore>()(
  persist(
    (set, get) => ({
      categories: [],
      selectedCategoryId: '',
      selectedSubCategoryId: '',
      selectedProductId: '',
      cartId: '',
      cartProduct: [],
      // /
      setCartId: cartId => set({ cartId }),
      setCartProduct: cartProduct => set({ cartProduct }),
      setCategories: categories => set({ categories }),
      setSelectedCategoryId: selectedCategoryId => set({ selectedCategoryId }),
      setSelectedSubCategoryId: selectedSubCategoryId =>
        set({ selectedSubCategoryId }),
      setSelectedProductId: selectedProductId => set({ selectedProductId }),
      getCategoryById: id => {
        const findCategory = (cats: Category[]): Category | undefined => {
          for (const cat of cats) {
            if (cat.id === id) return cat;
            if (cat.subcategories?.length) {
              const found = findCategory(cat.subcategories);
              if (found) return found;
            }
          }
          return undefined;
        };
        console.log('List categories::::::', get().categories);
        return findCategory(get().categories);
      },
    }),
    {
      name: 'category-store', // LocalStorage key
      storage: createJSONStorage(() => {
        // Check if we're in a browser environment
        if (typeof window !== 'undefined') {
          try {
            // Test localStorage availability
            const testKey = '__localStorage_test__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return localStorage;
          } catch (e) {
            console.warn(
              'localStorage is not available, using fallback storage'
            );
            // Fallback to in-memory storage
            const memoryStorage = new Map();
            return {
              getItem: (key: string) => memoryStorage.get(key) || null,
              setItem: (key: string, value: string) =>
                memoryStorage.set(key, value),
              removeItem: (key: string) => memoryStorage.delete(key),
            };
          }
        }
        // Server-side fallback
        return {
          getItem: () => null,
          setItem: () => {},
          removeItem: () => {},
        };
      }),
      onRehydrateStorage: () => state => {
        console.log('Category store hydration finished', state);
      },
    }
  )
);
