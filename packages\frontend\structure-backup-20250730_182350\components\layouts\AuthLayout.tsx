'use client';

import React from 'react';
import Link from 'next/link';

interface AuthLayoutProps {
  children: React.ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className='min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8'>
      <div className='sm:mx-auto sm:w-full w-auto'>
        <div className='text-center'>
          <h2 className='text-3xl font-bold text-blue-600'>One Store</h2>
          <h2 className='mt-6 text-center text-3xl font-extrabold text-gray-900'>
            Welcome back
          </h2>
        </div>
      </div>

      <div className='mt-8 sm:mx-auto sm:w-full w-auto'>
        <div className='bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10'>
          {children}
        </div>
      </div>

      <div className='mt-8 text-center'>
        <p className='text-sm text-gray-600'>
          © {new Date().getFullYear()} ONDC Seller. All Rights Reserved.
          <br />
          <span className='text-xs text-gray-400'>Version 1.2.2</span>
        </p>
      </div>
    </div>
  );
}
