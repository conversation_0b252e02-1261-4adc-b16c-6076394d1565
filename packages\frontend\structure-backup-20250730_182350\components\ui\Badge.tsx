'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  outline?: boolean;
  removable?: boolean;
  onRemove?: () => void;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      className,
      variant = 'default',
      size = 'md',
      rounded = true,
      outline = false,
      removable = false,
      onRemove,
      leftIcon,
      rightIcon,
      children,
      ...props
    },
    ref
  ) => {
    const baseClasses =
      'inline-flex items-center font-medium transition-colors duration-200';

    const sizeClasses = {
      sm: 'px-2 py-0.5 text-xs',
      md: 'px-2.5 py-1 text-sm',
      lg: 'px-3 py-1.5 text-base',
    };

    const roundedClasses = rounded ? 'rounded-full' : 'rounded-md';

    const variantClasses = {
      default: outline
        ? 'text-gray-700 border border-gray-300 bg-transparent'
        : 'text-gray-700 bg-gray-100',
      primary: outline
        ? 'text-blue-700 border border-blue-300 bg-transparent'
        : 'text-blue-700 bg-blue-100',
      secondary: outline
        ? 'text-purple-700 border border-purple-300 bg-transparent'
        : 'text-purple-700 bg-purple-100',
      success: outline
        ? 'text-green-700 border border-green-300 bg-transparent'
        : 'text-green-700 bg-green-100',
      warning: outline
        ? 'text-yellow-700 border border-yellow-300 bg-transparent'
        : 'text-yellow-700 bg-yellow-100',
      error: outline
        ? 'text-red-700 border border-red-300 bg-transparent'
        : 'text-red-700 bg-red-100',
      info: outline
        ? 'text-cyan-700 border border-cyan-300 bg-transparent'
        : 'text-cyan-700 bg-cyan-100',
    };

    const iconSizeClasses = {
      sm: 'w-3 h-3',
      md: 'w-4 h-4',
      lg: 'w-5 h-5',
    };

    return (
      <span
        ref={ref}
        className={cn(
          baseClasses,
          sizeClasses[size],
          roundedClasses,
          variantClasses[variant],
          className
        )}
        {...props}
      >
        {leftIcon && (
          <span className={cn('mr-1', iconSizeClasses[size])}>{leftIcon}</span>
        )}
        
        {children}
        
        {rightIcon && !removable && (
          <span className={cn('ml-1', iconSizeClasses[size])}>{rightIcon}</span>
        )}
        
        {removable && (
          <button
            type="button"
            onClick={onRemove}
            className={cn(
              'ml-1 inline-flex items-center justify-center rounded-full hover:bg-black hover:bg-opacity-10 focus:outline-none focus:bg-black focus:bg-opacity-10',
              iconSizeClasses[size]
            )}
          >
            <svg
              className={cn('w-2 h-2', size === 'lg' && 'w-3 h-3')}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        )}
      </span>
    );
  }
);

Badge.displayName = 'Badge';

export default Badge;
